Imports System.Data
Imports System.DirectoryServices
Namespace AccountManager
    Public Class Helper
        Sub AddBlank(ByVal ddlCur As System.Web.UI.WebControls.DropDownList)
            Dim lNew As New System.Web.UI.WebControls.ListItem

            lNew.Text = "--Select Value--"
            lNew.Value = ""
            lNew.Selected = True

            ddlCur.Items.Insert(0, lNew)
            ddlCur.SelectedIndex = 0

        End Sub

        Sub FillAccessDropDown(ByVal ddlNew As System.Web.UI.WebControls.DropDownList, Optional ByVal blnShowOwner As Boolean = False) 'As DropDownList

            Dim strSQL As String
            Dim dbGet As New DataAccess

            If blnShowOwner Then
                strSQL = "SELECT AccessID,Access FROM tbUMAccess Order by Access desc"
            Else
                strSQL = "SELECT AccessID,Access FROM tbUMAccess WHERE Access <> 'Owner' Order by Access desc"
            End If


            ddlNew.DataValueField = "AccessID"
            ddlNew.DataTextField = "Access"

            ddlNew.DataSource = dbGet.GetDataReaderByStringId(strSQL)
            ddlNew.DataBind()

            ' Return ddlNew
        End Sub

        Function IsValidOwnerID(ByVal strOwnerID As String) As Boolean

            Select Case Left(LCase(strOwnerID), 1)
                Case "c", "d", "r", "n", "p", "q"
                    IsValidOwnerID = False
                Case Else
                    IsValidOwnerID = True
            End Select

            Return IsValidOwnerID

        End Function

 	Function IsValidOwnerIDNew(ByVal strOwnerID As String) As string

            Dim dEntry As DirectoryEntry = New DirectoryEntry("LDAP://DC=kcc,DC=com")
            Dim dSearcher As DirectorySearcher = New DirectorySearcher(dEntry)
	    
		Dim OwnerType as boolean
            Dim employee As String = ""
            dSearcher.ReferralChasing = &H40
            dSearcher.PageSize = 2000
            dSearcher.SearchScope = 2
            Try
                dSearcher.Filter = "((samaccountname=" & strOwnerID & "))"
                Dim results As System.DirectoryServices.SearchResultCollection = dSearcher.FindAll()
                For Each result As System.DirectoryServices.SearchResult In results
                    Dim employeeType As String = (result.Properties("employeeType")(0))
                    IsValidOwnerIDNew = employeeType 
                Next

                return IsValidOwnerIDNew 

            Catch ex As Exception

            End Try


        End Function

        Function ValidOwnerMessage() As String
            'Return "An owner can not have a C, D, R, N, P or Q ID."
		Return "Owner for Non-User Account is not Permanenet KC Employee."
        End Function

        Function GetOwner(ByVal strAccount As String) As String

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            strSQL = "SELECT AccountID,Username FROM vUsr " & _
                     "WHERE AccountID = '" & Trim(strAccount) & "' and Type=1"

            srRead = dbGet.GetDataReaderByStringId(strSQL)
            GetOwner = ""

            If srRead.Read Then
                GetOwner = srRead.Item("UserName")
            End If

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()
            dbGet = Nothing

            Return GetOwner

        End Function

        Function IsOwner(ByVal strAccount As String, ByVal strUserID As String) As Boolean

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            strSQL = "SELECT AccountID,Username FROM vUsr " & _
                     "WHERE AccountID = '" & Trim(strAccount) & "' and Access='Owner'"

            srRead = dbGet.GetDataReaderByStringId(strSQL)
            IsOwner = False

            Do While srRead.Read

                If LCase(Trim(srRead.Item("UserName"))) = LCase(Trim(strUserID)) Then
                    IsOwner = True
                    Exit Do
                End If
            Loop

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()
            dbGet = Nothing

        End Function

        Function IsDelegate(ByVal strAccount As String, ByVal strUserID As String) As Boolean

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            strSQL = "SELECT AccountID,Username FROM vUsr " & _
                     "WHERE AccountID = '" & Trim(strAccount) & "' and Access='Delegate'"

            srRead = dbGet.GetDataReaderByStringId(strSQL)
            IsDelegate = False

            Do While srRead.Read

                If LCase(Trim(srRead.Item("UserName"))) = LCase(Trim(strUserID)) Then
                    IsDelegate = True
                    Exit Do
                End If
            Loop

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()
            dbGet = Nothing

        End Function

        Function IsOwnerOrDelegate(ByVal strAccount As String, ByVal strUserID As String) As Boolean

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            strSQL = "SELECT AccountID,Username FROM vUsr " & _
                     "WHERE AccountID = '" & Trim(strAccount) & "' and (Access='Owner' or Access='Delegate')"

            srRead = dbGet.GetDataReaderByStringId(strSQL)
            IsOwnerOrDelegate = False

            Do While srRead.Read

                If LCase(Trim(srRead.Item("UserName"))) = LCase(Trim(strUserID)) Then
                    IsOwnerOrDelegate = True
                    Exit Do
                End If
            Loop

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()
            dbGet = Nothing

        End Function

        Function IsOwnerOrBackup(ByVal strAccount As String, ByVal strUserID As String) As Boolean

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            strSQL = "SELECT AccountID,Username FROM vUsr " & _
                     "WHERE AccountID = '" & Trim(strAccount) & "' "

            srRead = dbGet.GetDataReaderByStringId(strSQL)
            IsOwnerOrBackup = False

            Do While srRead.Read

                If LCase(Trim(srRead.Item("UserName"))) = LCase(Trim(strUserID)) Then
                    IsOwnerOrBackup = True
                    Exit Do
                End If
            Loop

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()
            dbGet = Nothing

        End Function

        Public Shared Function IsValueNull(ByVal Value) As String

            If Value Is DBNull.Value Then
                Return ""
            ElseIf Trim(Value) = "" Then
                Return ""
            Else
                Return Trim(Value)
            End If

        End Function

        Function dbCleanUpString(ByVal Value As String) As String

            Value = Replace(Value, "'", "`")
            Value = Replace(Value, Chr(34), "`")

            Return Value

        End Function


        Sub InsertLog(ByVal AccountID As String, _
    ByVal UsrAction As String, ByVal DTAction As DateTime, ByVal Comments As String)

            Dim usrcur As New UserInfo
            Dim dbIns As New DataAccess
            Dim strSQL As String

            strSQL = "sp_UMLog_Add '" & AccountID & "','" & usrcur.GetUserID & "','" & UsrAction & "','" & DTAction & "','" & dbCleanUpString(Comments) & "'"

            dbIns.UpdateDBByStringId(strSQL)

            dbIns = Nothing
        End Sub


    End Class

End Namespace
