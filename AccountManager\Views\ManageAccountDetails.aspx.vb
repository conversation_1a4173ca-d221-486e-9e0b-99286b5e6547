Imports System.Data
Namespace AccountManager

    Partial Class ManageAccountDetails
        Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
            If Not Page.IsPostBack Then
                LoadData()
            End If

        End Sub

        Sub UpdateDetails()
            Dim hlpClean As New Helper
            Dim strAppName As String = hlpClean.dbCleanUpString(txtAppName.Text)
            Dim strPurpose As String = hlpClean.dbCleanUpString(txtPurpose.Text)
            If hfAppName.Value <> strAppName Or hfPurpose.Value <> strPurpose Then
                Dim strSQL As String

                Dim dbUpd As New DataAccess
                ' Dim usrAccount As New UserInfo(lblAccountID.Text)

                'usrAccount.SetDescription(usrAccount.LdapPath, txtDescription.Text)

                strSQL = "sp_UM_Update_AccountDetails '" & lblAccountID.Text & "','" & hlpClean.dbCleanUpString(lblDescription.Text) & _
                                                        "','" & strAppName & _
                                                        "','" & strPurpose & "'"
                dbUpd.UpdateDBByStringId(strSQL)
                lblMessage.Text = "Account details updated."
                hlpClean.InsertLog(lblAccountID.Text, "Updated Details", Now, String.Format("Updated from Managed Details Page. Old AppName: {0}, Purpose: {1}", hfAppName.Value, hfPurpose.Value))
                hfAppName.Value = strAppName
                hfPurpose.Value = strPurpose
            Else
                lblMessage.Text = "No changes in Application Name or Purpose."
            End If

        End Sub

        Sub LoadData()

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader
            Dim arUsers As New ArrayList
            Dim hlpClean As New Helper

            strSQL = "SELECT * FROM vUsr " & _
                     "WHERE AccountID = '" & Request.QueryString("AccountId") & "'"
            srRead = dbGet.GetDataReaderByStringId(strSQL)

            If srRead.Read Then

                txtAppName.Text = hlpClean.IsValueNull(srRead.Item("AppName"))
                lblDescription.Text = hlpClean.IsValueNull(srRead.Item("Description"))
                txtPurpose.Text = hlpClean.IsValueNull(srRead.Item("Purpose"))
                lblAccountID.Text = hlpClean.IsValueNull(srRead.Item("AccountID"))
                hfAppName.Value = txtAppName.Text
                hfPurpose.Value = txtPurpose.Text
            End If

        End Sub

        Private Sub cmdUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdUpdate.Click
            UpdateDetails()
        End Sub

        Private Sub cmdReturn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdReturn.Click
            Response.Redirect("/AccountManager/MyUsers.aspx")
        End Sub

    End Class

End Namespace
