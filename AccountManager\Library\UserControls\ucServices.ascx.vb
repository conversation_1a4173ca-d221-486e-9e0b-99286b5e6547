Imports System.Data
Namespace AccountManager

    Partial Class ucServices
        Inherits System.Web.UI.UserControl

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Dim strSQL As String

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
            strSQL = "SELECT ID,HOST,AccountName as [Account Name],DisplayName as [Display Name] FROM SA_Common_Services_Default WHERE " & _
                         " AccountName like '%" & txtAccountId.Text & "%' Order BY Host"
        End Sub

        Sub LoadRequests()

            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            srRead = dbGet.GetDataReaderByStringId(strSQL, [Global].GblSauConn)

            If srRead.HasRows Then
                grdServices.DataKeyField = "ID"
                grdServices.DataSource = srRead
                grdServices.DataBind()
            Else
                lblMessage.CssClass = "LabelRed"
                lblMessage.Text = "No services found running as the selected account"
            End If

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()

        End Sub

        Private Sub cmdGet_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdGet.Click
            LoadRequests()
        End Sub

        Private Sub cmdExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdExport.Click
            Dim prtReport As New Print([Global].GblSauConn)

            hlDownload.NavigateUrl = prtReport.CreateCSV(strSQL)
            hlDownload.Text = "Download File"
        End Sub
    End Class

End Namespace
