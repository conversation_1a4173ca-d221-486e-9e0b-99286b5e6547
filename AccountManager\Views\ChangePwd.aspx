﻿<%@ Register TagPrefix="uc1" TagName="Menu" Src="~/Library/UserControls/Menu.ascx" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>

<%@ Page MasterPageFile="~/MasterPage.master" Language="vb" AutoEventWireup="false" Inherits="AccountManager.ChangePwd" CodeFile="ChangePwd.aspx.vb" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table>
        <tr>
            <td>
                <asp:Label ID="lblWelcome" runat="server" CssClass="TableHeader">Change Password</asp:Label><br />
                <br />
                <asp:Label ID="lblMessage" runat="server"></asp:Label><br />
                <table>
                    <tr>
                        <td>
                            <table>
                                <tr>
                                    <td>
                                        <asp:Panel ID="pPasswords" runat="server">
                                            <table class="DisplayTables">
                                                <tr>
                                                    <td colspan="2">You are Changing the Password for Account
                                                        <asp:Label ID="lblAccount" CssClass="LabelRed" runat="server"></asp:Label></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">
                                                        <asp:Label ID="lblChangePwd" CssClass="LabelRed" runat="server"></asp:Label></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">Step 1:  Understand the Password Requirements.  Please refer to Section 6.0 in the Access Management Standard linked below. <br/>
                                    Step 2:  Enter a new password that meets the password requirements.  <br/> 
Step 3:  Re-enter the new password to verify. <br/>
Step 4:  Document the new password appropriately. <br/>
Step 5:  Add comment on the password change. <br/>
Step 6:  Click Update Password.
</td>
                                                </tr>
                                                <tr>
                                                    <td></td>
                                                    <td>
                                                        <asp:TextBox ID="txtOldPwd" runat="server" TextMode="Password" Visible="False"></asp:TextBox></td>
                                                </tr>
                                                <tr>
                                                    <td>New Password:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtNewPwd" Display="None" ErrorMessage="Please enter new password." SetFocusOnError="True" ValidationGroup="ChgPwd"></asp:RequiredFieldValidator></td>
                                                    <td>
                                                        <asp:TextBox ID="txtNewPwd" runat="server" TextMode="Password"></asp:TextBox></td>
                                                </tr>
                                                <tr>
                                                    <td>Verify New Password:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="txtVerifyNew" Display="None" ErrorMessage="Please enter verify new password." SetFocusOnError="True" ValidationGroup="ChgPwd"></asp:RequiredFieldValidator><asp:CompareValidator ID="CompareValidator1" runat="server"  Display="None" ErrorMessage="New Password and Verify New Password must be same." SetFocusOnError="True" ValidationGroup="ChgPwd" ControlToCompare="txtNewPwd" ControlToValidate="txtVerifyNew"></asp:CompareValidator></td>
                                                    <td>
                                                        <asp:TextBox ID="txtVerifyNew" runat="server" TextMode="Password"></asp:TextBox></td>
                                                </tr>
                                                <tr>
                                                    <td>Comments:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ControlToValidate="txtNewPwdComments" Display="None" ErrorMessage="Please enter comments." SetFocusOnError="True" ValidationGroup="ChgPwd"></asp:RequiredFieldValidator></td>
                                                    <td>
                                                        <asp:TextBox ID="txtNewPwdComments" runat="server" TextMode="MultiLine" Columns="40" Rows="4" MaxLength="500"></asp:TextBox></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">
                                                        <table>
                                                            <tr>
                                                                <td style="width: 175px; height: 38px">
                                                                    <dx:ASPxButton ID="cmdUpdPwd" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                        CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                                        Text="Update Password" ValidationGroup="ChgPwd">
                                                                    </dx:ASPxButton>
                                                                </td>
                                                                <td style="width: 227px; height: 38px">
                                                                    <dx:ASPxButton ID="cmdReturn" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                        CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                                        Text="Return to My Accounts">
                                                                    </dx:ASPxButton>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">
                                                        <asp:ValidationSummary ID="ValidationSummary1" runat="server" ValidationGroup="ChgPwd" />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="2">
                                                        <!-- Begin Alert Code -->
                                                        <script language="JavaScript">
																<!--
    window.alert("Using this tool to change the ID's password will only change it in Active Directory. Additional steps may be required to update the password within the process or application using the ID. Contact your ITS support team and/or key application users to verify any additional action required to complete a password change for this ID.")
    // -->
                                                        </script>
                                                        <!-- End Alert Code -->
                                                        <table>
                                                            <tr>
                                                                <td class="FunctionStep">Non-User ID Password Help Documents</td>
                                                            </tr>
                                                            <tr>
                                                                <td><a href="https://kimberlyclark.sharepoint.com/Sites/F121/services/SitePages/DTS-Policies-and-Procedures.aspx?OR=Teams-HL&CT=1684744772756&clickparams=eyJBcHBOYW1lIjoiVGVhbXMtRGVza3RvcCIsIkFwcFZlcnNpb24iOiIyNy8yMzA0MDIwMjcwNSIsIkhhc0ZlZGVyYXRlZFVzZXIiOmZhbHNlfQ%3D%3D"
                                                                    target="_blank">Please refer to Access Management Standard</a><asp:Label ID="lblLinkMsg" runat="server" CssClass="LinkMsg" ></asp:Label><br />
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><a href="https://kcc.service-now.com/kc_sp?sys_kb_id=6b69362edb13e740ada5ae441b961905&id=kb_article_view&sysparm_rank=6&sysparm_tsqueryId=6f36672cdb247784d9b5e7b51b9619a5"
                                                                    target="_blank">How to Perform Password Reset for Non-User ID</a><asp:Label ID="lblLinkMsg1" runat="server" CssClass="LinkMsg" ></asp:Label><br />
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><a href="https://kcc.service-now.com/kc_sp?sys_kb_id=c372f273db3a1fc0e790d2cb4b961967&id=kb_article_view&sysparm_rank=1&sysparm_tsqueryId=1c695e6ddbac3f84d9b5e7b51b9619a9" target="_blank">Info: Managing Passwords for Production ID Accounts</a><asp:Label ID="lbllinkMsg2" runat="server" CssClass="LinkMsg" ></asp:Label><br />
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                            <strong><font color="red">Caution: By using this tool to change an ID's password, you will only be changing the password in Active Directory. Additional steps may be required to change the password within the process or application using this ID. Please contact your ITS support team and/or key application users to verify if any additional actions are required to complete a password change for this ID.</font></strong>
                                        </asp:Panel>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>


</asp:Content>
