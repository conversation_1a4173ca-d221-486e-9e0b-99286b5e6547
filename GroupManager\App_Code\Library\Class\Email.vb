﻿Imports Microsoft.VisualBasic
Imports System.Net.Mail

Public Class Email
    Sub SendEmail(ByVal strSubject As String, ByVal strBody As String, ByVal strFrom As String, ByVal strToAddresses As String, Optional ByVal strCCAddresses As String = Nothing, Optional ByVal strBCCAddresses As String = Nothing, Optional ByVal IsBodyHTML As Boolean = False, Optional IsProduction As Boolean = False, Optional ByVal ToAddressesTestingSiphon As String = Nothing, Optional ByVal Priority As Net.Mail.MailPriority = MailPriority.Normal, Optional ByVal AttachmentFilePath As String = Nothing)
        Dim attachmentFile As Attachment
        Dim myMessage As New MailMessage
        myMessage.From = New MailAddress(Trim(strFrom) & "@kcc.com")
        If strCCAddresses IsNot Nothing Then
            For Each strScc As String In strCCAddresses.Split(";")
                If strScc <> "" Then
                    myMessage.CC.Add(New MailAddress(Trim(strScc) & "@kcc.com"))
                End If
            Next
        End If
        If strBCCAddresses IsNot Nothing Then
            For Each strSbcc As String In strBCCAddresses.Split(";")
                If strSbcc <> "" Then
                    myMessage.Bcc.Add(New MailAddress(Trim(strSbcc) & "@kcc.com"))
                End If
            Next
        End If
        Dim strToTemp As String = strToAddresses
        If Not IsProduction Then
            strToTemp = ToAddressesTestingSiphon
        End If

        For Each strSTo As String In strToTemp.Split(";")
            If strSTo <> "" Then
                myMessage.To.Add(New MailAddress(Trim(strSTo) & "@kcc.com"))

            End If
        Next
        myMessage.Body = strBody

        If AttachmentFilePath IsNot Nothing Then
            attachmentFile = New Attachment(AttachmentFilePath)
            myMessage.Attachments.Add(attachmentFile)
        End If
        myMessage.Subject = strSubject
        myMessage.Body = strBody
        myMessage.IsBodyHtml = IsBodyHTML
        myMessage.Priority = Priority
        Dim myclient As New SmtpClient("mailhost.kcc.com")
        myclient.Send(myMessage)
    End Sub
End Class
