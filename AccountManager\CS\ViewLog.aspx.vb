Imports System.Data
Namespace AccountManager

    Partial Class ViewLog
        Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load, Me.Load
            'Put user code to initialize the page here
            Try
                GetLogs()
            Catch ex As Exception

            End Try

        End Sub

        Sub GetLogs()

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            strSQL = "SELECT * FROM tbUMLog WHERE AccountID LIKE '%" & txtAccount.Text & "%' Order By DTAction DESC,UsrAction"
            srRead = dbGet.GetDataReaderByStringId(strSQL)
            Trace.Warn(strSQL)
            If srRead.HasRows Then
                grdRequests.KeyFieldName = "ID"
                grdRequests.DataSource = srRead
                grdRequests.DataBind()
                grdRequests.ExpandAll()
            Else
                lblMessage.Text = "No requests found."
            End If


            'If grdRequests. > 12 Then
            'grdRequests.PageIndexButtonCount = 12
            'Else
            'grdRequests.PageIndexButtonCount = grdRequests.Items.Count
            'End If

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()

        End Sub

        Private Sub cmdGet_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdGet.Click
            GetLogs()
        End Sub
    End Class

End Namespace
