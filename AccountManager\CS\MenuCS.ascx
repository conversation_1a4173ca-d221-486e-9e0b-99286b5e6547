<%@ OutputCache Duration="100" VaryByParam="none" %>
<%@ Control Language="vb" AutoEventWireup="false" Inherits="AccountManager.MenuCS" CodeFile="MenuCS.ascx.vb" %>
<link href="/Library/css/SideMenu.css" type="text/css" rel="stylesheet" />
<%  Response.WriteFile("/Library/Visual/Header/LeftHeader.htm")%>
<script language="JavaScript" src="/Library/scripts/LeftNavMenu.js" type="text/javascript"></script>
<link href="/Styles.css" type="text/css" rel="stylesheet" />
<table cellspacing="0" cellpadding="0" border="0">
	<tr>
		<td><br/>
		</td>
	</tr>
	<tr>
		<td><strong>Account Manager</strong></td>
	</tr>
	<tr>
		<td onmousedown="btnDown(this);" onmouseover="btnUp(this);" onmouseout="btnHide(this);">
			<div class="LeftNavOff" align="right"><a class="leftNavLink" href="~/" target="_self">Home
				</a>&nbsp;&nbsp;</div>
		</td>
	</tr>
	<tr>
		<td onmousedown="btnDown(this);" onmouseover="btnUp(this);" onmouseout="btnHide(this);">
			<div class="LeftNavOff" align="right"><a class="leftNavLink" href="/AccountManager/MyUsers.aspx" target="_self">My 
					Accounts </a>&nbsp;&nbsp;</div>
		</td>
	</tr>
	<tr>
		<td onmousedown="btnDown(this);" onmouseover="btnUp(this);" onmouseout="btnHide(this);">
			<div class="LeftNavOff" align="right"><a class="leftNavLink" href="/AccountManager/Views/AcceptOwnership.aspx" target="_self">Pending 
					Ownership Changes </a>&nbsp;&nbsp;</div>
		</td>
	</tr>
	<tr>
		<td onmousedown="btnDown(this);" onmouseover="btnUp(this);" onmouseout="btnHide(this);">
			<div class="LeftNavOff" align="right"><a class="leftNavLink" href="/AccountManager/Views/FindOwners.aspx" target="_self">Find 
					Owners</a>&nbsp;&nbsp;</div>
		</td>
	</tr>
	<tr>
		<td onmousedown="btnDown(this);" onmouseover="btnUp(this);" onmouseout="btnHide(this);">
			<div class="LeftNavOff" align="right"><a class="leftNavLink" href="mailto:_Enterprise Server Mgmt">Questions/Feedback</a>&nbsp;&nbsp;</div>
		</td>
	</tr>
	<tr>
		<td>
			<br/>
		</td>
	</tr>
	<tr>
		<td><strong>Documentation</strong></td>
	</tr>
	<tr>
		<td onmousedown="btnDown(this);" onmouseover="btnUp(this);" onmouseout="btnHide(this);">
			<div class="LeftNavOff" align="right"><a class="leftNavLink" href="/AccountManager/Tutorials/FAQ.aspx" target="_self">FAQ</a>&nbsp;&nbsp;</div>
		</td>
	</tr>
	<tr>
		<td onmousedown="btnDown(this);" onmouseover="btnUp(this);" onmouseout="btnHide(this);">
			<div class="LeftNavOff" align="right"><a target="_blank" class="leftNavLink" href="http://www.kcc.com/mis/cafe/Munch/reference%20cards/Account%20Manager%20QRC.pdf">
					QRC</a>&nbsp;&nbsp;</div>
		</td>
	</tr>
	<tr>
		<td>
			<br/>
		</td>
	</tr>
	<asp:Panel ID="pAdmin" Runat="server">
		<tr>
			<td>
				<p><b><font color="#ff0000" dir="ltr">Admin Functions</font></b></p>
			</td>
		</tr>
		<tr>
			<td onmousedown="btnDown(this);" onmouseover="btnUp(this);" onmouseout="btnHide(this);">
				<div class="LeftNavOff" align="right"><a class="leftNavLink" href="/AccountManager/CS/Manage.aspx" target="_self">Manage 
						Accounts</a>&nbsp;&nbsp;</div>
				</td>
		</tr>
		<tr>
			<td onmousedown="btnDown(this);" onmouseover="btnUp(this);" onmouseout="btnHide(this);">
				<div class="LeftNavOff" align="right"><a class="leftNavLink" href="/AccountManager/Reports/Stats.aspx" target="_self">User 
						Stats</a>&nbsp;&nbsp;</div>
			</td>
		</tr>
		<tr>
			<td onmousedown="btnDown(this);" onmouseover="btnUp(this);" onmouseout="btnHide(this);">
				<div class="LeftNavOff" align="right"><a class="leftNavLink" href="/AccountManager/CS/Requests.aspx" target="_self">Requests</a>&nbsp;&nbsp;</div>
			</td>
		</tr>
		<tr>
			<td onmousedown="btnDown(this);" onmouseover="btnUp(this);" onmouseout="btnHide(this);">
				<div class="LeftNavOff" align="right"><a class="leftNavLink" href="/AccountManager/Reports/Reports.aspx" target="_self">Reports</a>&nbsp;&nbsp;</div>
			</td>
		</tr>
		<tr>
			<td onmousedown="btnDown(this);" onmouseover="btnUp(this);" onmouseout="btnHide(this);">
				<div class="LeftNavOff" align="right"><a class="leftNavLink" href="/AccountManager/CS/ViewLog.aspx" target="_self">User 
						Logs</a>&nbsp;&nbsp;</div>
			</td>
		</tr>
	</asp:Panel>
</table>
