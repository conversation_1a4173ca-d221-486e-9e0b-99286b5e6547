<%@ Register TagPrefix="uc1" TagName="Menu" Src="~/Library/UserControls/Menu.ascx" %>
<%@ Page Trace="false" MasterPageFile="~/MasterPage.master" Title="Find Owners" Language="vb"
    AutoEventWireup="false" Inherits="AccountManager.FindOwners" CodeFile="FindOwners.aspx.vb" %>

<%@ Register Assembly="DevExpress.Web.ASPxGridView.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
    
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <script>
    function OnGetRowValues(values) {
alblAccountID.SetText(values[0]);
alblDescription.SetText(values[1]);
alblAppName.SetText(values[2]);
alblPurpose.SetText(values[3]);
alblExpirationDate.SetText(values[4].toDateString());
}
    </script>
    <table>
        <tr>
            <td style="width: 726px">
                <asp:Label ID="lblWelcome" runat="server" CssClass="TableHeader"></asp:Label><br />
                <br />
                <asp:Label ID="lblMessage" runat="server" CssClass="LabelRed"></asp:Label><br />
                <table class="DisplayTables" width="100%">
                    <tr>
                        <td colspan="3"><strong>
                            Filtering Options</strong></td>
                    </tr>
                    <tr>
                        <td>
                            Account ID</td>
                        <td>
                            Owner/Delegate/Backup ID</td>
                        <td>
                            Owner/Delegate/Backup Name</td>
                    </tr>
                    <tr>
                        <td>
                            <asp:TextBox ID="txtFAccount" runat="server" ></asp:TextBox></td>
                        <td>
                            <asp:TextBox ID="txtOwnerID" runat="server"></asp:TextBox></td>
                        <td>
                            <asp:TextBox ID="txtOwnerName" runat="server"></asp:TextBox>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            Description</td>
                        <td>
                            <asp:Label ID="lblAppNameFilter" runat="server" ToolTip="User entered field describing the application name this account is used for">Application Name</asp:Label></td>
                        <td>
                            <asp:Label ID="lblPurpFilter" runat="server" ToolTip="User entered field describing the purpose of the account">Account Purpose</asp:Label></td>
                    </tr>
                    <tr>
                        <td>
                            <asp:TextBox ID="txtFilterDesc" runat="server"></asp:TextBox></td>
                        <td>
                            <asp:TextBox ID="txtFilterAppName" runat="server"></asp:TextBox></td>
                        <td>
                            <asp:TextBox ID="txtFilterPurpose" runat="server"></asp:TextBox></td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            Access</td>
                    </tr>
                    <tr>
                        <td>
                            <asp:DropDownList ID="ddlFAccess" runat="server">
                            </asp:DropDownList></td>
                        <td colspan="2">
                            <asp:CheckBox ID="chkPastDue" runat="server" Text="Show Only Past Due Accounts" /></td>
                    </tr>
                    <tr>
                        <td colspan="3" style="height: 38px">
                            <table>
                                <tr>
                                    <td style="height: 33px">
                                        <dx:ASPxButton ID="cmdContinue" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                            CssPostfix="Office2003Blue" Font-Size="Small" Height="1px" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                            Text="Search  ">
                                        </dx:ASPxButton>
                                    </td>
    
                                    <td style="height: 33px">
                                        <dx:ASPxButton ID="cmdClearF" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                            CssPostfix="Office2003Blue" Font-Size="Small" Height="16px" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                            Text="Clear Filter">
                                        </dx:ASPxButton>
                                    </td>
            
                                    <td style="height: 33px">
                                        <dx:ASPxButton ID="cmdExport" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                            CssPostfix="Office2003Blue" Font-Size="Small" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                            Text="Export">
                                        </dx:ASPxButton>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td align="center" colspan="3">
                            <asp:HyperLink ID="hlDownload" runat="server" Font-Size="14" ForeColor="#000099"
                                Font-Bold="True" Target="_blank"></asp:HyperLink></td>
                    </tr>
                    <tr>
                        <td colspan="3" style="height: 21px">
                            <strong>Account Information:</strong></td>
                    </tr>
                    <tr>
                        <td colspan="3" style="height: 21px">
                            <span style="font-size: 11pt">
                            Account ID:</span>
                            <dx:ASPxLabel ID="alblAccountID" runat="server" ClientInstanceName="alblAccountID" Font-Size="11pt">
                            </dx:ASPxLabel>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3" style="height: 21px">
                            <span style="font-size: 11pt">
                            Description:</span>
                            <dx:ASPxLabel ID="alblDescription" runat="server" ClientInstanceName="alblDescription" Font-Size="11pt">
                            </dx:ASPxLabel>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3" style="height: 21px">
                            <span style="font-size: 11pt">
                            Application Name:</span>
                            <dx:ASPxLabel ID="alblAppName" runat="server" ClientInstanceName="alblAppName" Font-Size="11pt">
                            </dx:ASPxLabel>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3" style="height: 21px">
                            <span style="font-size: 11pt">
                            Account Purpose:</span>
                            <dx:ASPxLabel ID="alblPurpose" runat="server" ClientInstanceName="alblPurpose" Font-Size="11pt">
                            </dx:ASPxLabel>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <span style="font-size: 11pt">
                            Account Past Due On:</span>
                            <dx:ASPxLabel ID="alblExpirationDate" runat="server" ClientInstanceName="alblExpirationDate" Font-Size="11pt">
                            </dx:ASPxLabel>
                            
                        </td>
                    </tr>
                    </table>
                <table style="width: 719px; height: 240px">
                    <tr>
                        <td colspan="3" style="height: 246px">
                            <dx:ASPxGridView ID="grdusers"  ClientInstanceName="grid" runat="server" AutoGenerateColumns="False" CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css" CssPostfix="PlasticBlue" KeyFieldName="AccountID" Width="100%" PreviewFieldName="Description">
                                <Columns>
                                    <%--<dx:GridViewCommandColumn ShowSelectCheckbox="True" VisibleIndex="0">
                                    </dx:GridViewCommandColumn>--%>
                                    <dx:GridViewDataTextColumn Caption="AccountID" FieldName="AccountID" GroupIndex="0"
                                        SortIndex="0" SortOrder="Ascending" VisibleIndex="1">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Owner/Delegate/Backup ID" FieldName="UserName" VisibleIndex="2">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Owner/Delegate/Backup Name" FieldName="UserNameDN" VisibleIndex="3">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Access" FieldName="Access" VisibleIndex="4">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Description" FieldName="Description" Visible="False" VisibleIndex="5">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Purpose" FieldName="Purpose" VisibleIndex="6" Visible="False">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="AppName" FieldName="AppName" VisibleIndex="7" Visible="False">
                                    </dx:GridViewDataTextColumn>
                                    <dx:GridViewDataTextColumn Caption="Expiration Date" FieldName="ExpirationDate" VisibleIndex="8" Visible="False">
                                    </dx:GridViewDataTextColumn>
                                </Columns>
                                <Settings ShowGroupPanel="True" />
                                <Styles CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css" CssPostfix="PlasticBlue">
                                    <Header ImageSpacing="10px" SortingImageSpacing="10px">
                                    </Header>
                                    <AlternatingRow BackColor="silver">
                                        </AlternatingRow>
                                    <%--<AlternatingRow BackColor="InactiveCaptionText">
                                    </AlternatingRow>--%>
                                </Styles>
                                <ImagesFilterControl>
                                    <LoadingPanel Url="~/App_Themes/PlasticBlue/Editors/Loading.gif">
                                    </LoadingPanel>
                                </ImagesFilterControl>
                                <Images SpriteCssFilePath="~/App_Themes/PlasticBlue/{0}/sprite.css">
                                    <LoadingPanelOnStatusBar Url="~/App_Themes/PlasticBlue/GridView/gvLoadingOnStatusBar.gif">
                                    </LoadingPanelOnStatusBar>
                                    <LoadingPanel Url="~/App_Themes/PlasticBlue/GridView/Loading.gif">
                                    </LoadingPanel>
                                </Images>
                                <StylesEditors>
                                    <ProgressBar Height="25px">
                                    </ProgressBar>
                                    <CalendarHeader Spacing="11px">
                                    </CalendarHeader>
                                </StylesEditors>
                                <SettingsPager ShowDefaultImages="False">
                                    <AllButton Text="All">
                                    </AllButton>
                                    <NextPageButton Text="Next &gt;">
                                    </NextPageButton>
                                    <PrevPageButton Text="&lt; Prev">
                                    </PrevPageButton>
                                </SettingsPager>
                                <Templates>
                                    <DetailRow>
                                        Description
                                    </DetailRow>
                                </Templates>
                                <SettingsBehavior AllowFocusedRow="True" />
                                <ClientSideEvents FocusedRowChanged="function(s, e) {
	grid.GetRowValues(grid.GetFocusedRowIndex(), 'AccountID;Description;AppName;Purpose;ExpirationDate', OnGetRowValues);
	}" />                            </dx:ASPxGridView>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</asp:Content>
