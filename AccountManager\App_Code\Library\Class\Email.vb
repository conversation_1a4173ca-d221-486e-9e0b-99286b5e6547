'Title: Send a html email
'Created by: <PERSON> on 4/5/2002
'Purpose:  Format and send an email
'Assumptions:  Assuming you want to send a html based email with tables
'Effects:  Sens email
'Inputs:  The class receives the To, From, Subject and Body fields for the email.
'Returns:  None
'Imports System.Web.Mail
Imports System.Data
Imports System.Data.SqlClient
Imports AccountManager.[Global]
Imports AccountManager.UserInfo
Imports System.Net.Mail

Namespace AccountManager

    Public Class Email

        Dim sMailBody As String
        Dim strTo As String
        Dim strCC As String

        Sub New()

            Reinit()
        End Sub

        Sub New(ByVal SendTo As String)

            Dim usrcUr As New UserInfo(SendTo)
            strTo = usrcUr.GetUserEmail

            Reinit()

        End Sub

        Sub Reinit()
            sMailBody = "<!DOCTYPE html PUBLIC""-//IETF//DTD html//EN"">"
            sMailBody = sMailBody & "<html><head><TITLE>Account Manager Automated Email</TITLE><STYLE TYPE='text/css'><!--BODY {font:normal 100% verdana,arial,helvetica;color:#000000;}TD.clsRaised {border-top: solid #99CCFF 1px;border-bottom: solid #003366 1px;padding: 5px 10px;}TD { font-size:68%; }DIV.clsDocBody { margin-left:10px; margin-right:10px; margin-top:10px; }--></STYLE></head>"
            sMailBody = sMailBody & "<BODY TOPMARGIN='0' LEFTMARGIN='0' MARGINWIDTH='0' MARGINHEIGHT='0' BGCOLOR='#ffffff' TEXT='#000000'>"
            sMailBody = sMailBody & "<table CLASS='clsNavTop' BGCOLOR='#6699cc' cellpadding='0' cellspacing='0' BORDER='0' WIDTH='100%'><tr><td CLASS='clsRaised' ALIGN='left' STYLE='PADDING-BOTTOM: 0px; PADDING-LEFT: 5px; PADDING-RIGHT: 5px; PADDING-TOP: 0px'>"
            sMailBody = sMailBody & "<table cellpadding='5' cellspacing='0' BORDER='0' WIDTH='100%'><tr><td><FONT COLOR='#ffffff'><B>Account Manager Automated Email</B></FONT></td><td ALIGN='right'>"
            sMailBody = sMailBody & "<FONT COLOR='#ffffff'>Date:&nbsp;" & FormatDateTime(Now, 2) & "</FONT>"
            sMailBody = sMailBody & "</td></tr></table></td></tr></table><DIV CLASS='clsDocBody'><table>"

        End Sub

        Sub CopyAllBackups(ByVal strAccount As String)

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader
            Dim arUsers As New ArrayList
            Dim hlpClean As New Helper

            strSQL = "SELECT * FROM vUsr " & _
                     "WHERE AccountID = '" & strAccount & "'"
            srRead = dbGet.GetDataReaderByStringId(strSQL)

            Do While srRead.Read
                'Dim usrBackup As New UserInfo(srRead.Item("UserName"), True)
                If srRead.Item("UserName").ToString <> "" Then
                    strCC = strCC & "," & Trim(srRead.Item("UserName").ToString) & "@kcc.com"
                End If

            Loop

            strCC = strCC.Trim(",")
        End Sub

        Sub AddPlainText(ByVal Value As String)


            sMailBody = sMailBody & Value

        End Sub

        Sub CreateEmailRow(ByVal Value As String, ByVal FirstCol As Boolean, ByVal LastCol As Boolean, Optional ByVal colSpan As Integer = 1)

            If FirstCol Then
                sMailBody = sMailBody & "<tr>"
            End If

            sMailBody = sMailBody & "<td colspan=" & colSpan & ">" & Value & "</td>"

            If LastCol Then
                sMailBody = sMailBody & "</tr>"
            End If
        End Sub

        Sub CreateEmailHeader(ByVal ColName As String, ByVal FirstCol As Boolean, ByVal LastCol As Boolean, Optional ByVal colSpan As Integer = 1)

            If FirstCol Then
                sMailBody = sMailBody & "<tr>"
            End If

            sMailBody = sMailBody & "<td colspan=" & colSpan & "><b>" & ColName & "</b></td>"

            If LastCol Then
                sMailBody = sMailBody & "</tr>"
            End If

        End Sub

        Sub EndEmailText()
            sMailBody = sMailBody & "</table></DIV></BODY></html>"
        End Sub


        'Title: Send Email
        'Created by: Shane Z Smith on 5/10/02
        'Purpose:  Send an Email 
        'Assumptions:  Every email will look the same
        'Effects:  Generates an email
        'Inputs:  strTo (To Field for Email), strFrom (From Field for Email),
        '         strSubject (Subject of Email), and strBody (Body of the email)
        'Returns:  None
        Function SendEmail(ByVal strTo As String, ByVal strFrom As String, ByVal strSubject As String, _
                            ByVal strBody As String, ByVal Attachment As String)
            Dim attachmentFile As Attachment
            Dim myMessage As New MailMessage

            If UCase(GblTestMode) = "TRUE" Then


                myMessage.To.Add(New MailAddress(GblTestModeEmail))
                myMessage.From = New MailAddress(GblTestModeEmail)
                strSubject = strSubject & " ** TEST MODE ** "
                myMessage.Body = "<html><body><table><font size=-2>" & strBody & "</font></table></body></html> <br>**TEST MODE**<br>Email Would have been sent TO: " & strTo & " AND CC:" & strCC
            Else
                myMessage.From = New MailAddress(strFrom)
                If strCC <> "" Then
                    For Each strScc As String In strCC.Split(",")
                        If strScc <> "" Then
                            myMessage.CC.Add(New MailAddress(strScc))
                        End If
                    Next
                End If

                    myMessage.To.Add(New MailAddress(strTo))
                    myMessage.Body = "<html><body><table><font size=-2>" & strBody & "</font></table></body></html>"
            End If

            If Attachment <> "" Then
                attachmentFile = New Attachment(Attachment)
                myMessage.Attachments.Add(attachmentFile)
            End If
            myMessage.Subject = strSubject
            myMessage.Body = strBody
            myMessage.IsBodyHtml = True
            Dim myclient As New SmtpClient("mailhost.kcc.com")
            myclient.Send(myMessage)
            'With myMail

            '    .From = strFrom
            '    .To = strTo


            '    If UCase(GblTestMode) = "TRUE" Then

            '        Dim usrTest As New UserInfo
            '        .To = usrTest.GetUserEmail

            '        .Subject = strSubject & " ** TEST MODE ** " & usrTest.GetUserEmail
            '        usrTest = Nothing
            '    Else
            '        .Subject = strSubject
            '        .Cc = strCC
            '        .To = strTo
            '    End If

            '    '.To = "<EMAIL>"
            '    .Priority = System.Web.Mail.MailPriority.Normal
            '    .BodyFormat = MailFormat.Html

            '    '.Bcc = "<EMAIL>"

            '    If Trim(Attachment) <> "" Then
            '        .Attachments.Add(New MailAttachment(Trim(Attachment)))
            '    End If

            'If GblTestMode = "TRUE" Then
            '    .Body = "<html><body><table><font size=-2>" & strBody & "</font></table></body></html> <br>**TEST MODE**<br>Email Would have been sent TO: " & strTo & " AND CC:" & strCC
            'Else
            '    .Body = "<html><body><table><font size=-2>" & strBody & "</font></table></body></html>"
            'End If

            'End With

            ''  Try
            ''SmtpMail.Send(myMail)
            'Dim myclient As New SmtpClient("mailhost.kcc.com")
            'myclient.Send(myMail)
            ''Catch e As Exception

            ''End Try

        End Function


        'Title: CreateAndSend
        'Created by: Shane Z Smith on 8/23/02
        'Purpose:  Creats teh body of the email and calls the sending function
        'Assumptions:  None
        'Effects:  Creates a standard email so all the emails look the same but have different information.
        'Inputs:  Request Number, To Field, Subject line of email, and the Link Address so the user can navigate to the request
        'Returns:  None
        Function CreateAndSend(ByVal strSubject As String, ByVal strAccounts As String()) As String

            Dim hpGet As New Helper
            Dim intIndex As Integer = 0

            Dim CurrentUser As New UserInfo
            Dim strBody, strFrom As String

            If Trim(CurrentUser.GetUserEmail) = "" Then
                strFrom = "<EMAIL>"
            Else
                strFrom = CurrentUser.GetUserEmail
                strCC = CurrentUser.GetUserID & "@kcc.com"
            End If

            strBody = AddHeader(strBody, "Account Manager Automated Email - Owner Change Request")

            CreateEmailRow("", True, True)
            CreateEmailRow("The current owner of the account listed below has requested the ownership responsibilities be transferred to you.", True, True, 2)
            CreateEmailRow("", True, True)

            CreateEmailHeader("Current Owner", True, False)
            CreateEmailRow(CurrentUser.DisplayName & " (" & CurrentUser.GetUserID & ")", False, True)
            CreateEmailHeader("Account ID", True, True, 2)

            Do While intIndex < strAccounts.Length
                CreateEmailRow(strAccounts(intIndex), True, True, 2)
                intIndex = intIndex + 1
            Loop

            CreateEmailHeader("Do you Accept this Ownership change?", True, True, 2)
            CreateEmailHeader("<a href=" & [Global].GblHttp & "AccountManager/Views/AcceptOwnership.aspx>Review Changes</a>", True, True, 2)

            strSubject = strSubject

            If Trim(strTo) <> "" Then
                SendEmail(strTo, strFrom, strSubject, sMailBody, "")
            End If


            Return strTo

        End Function

        'Title: CreateAndSend
        'Created by: Shane Z Smith on 8/23/02
        'Purpose:  Creats teh body of the email and calls the sending function
        'Assumptions:  None
        'Effects:  Creates a standard email so all the emails look the same but have different information.
        'Inputs:  Request Number, To Field, Subject line of email, and the Link Address so the user can navigate to the request
        'Returns:  None
        Function CreateAndSendReject(ByVal strSubject As String, ByVal strAccount As String, ByVal strComments As String) As String

            Dim hpGet As New Helper

            Dim CurrentUser As New UserInfo
            Dim strBody, strFrom As String

            If Trim(CurrentUser.GetUserEmail) = "" Then
                strFrom = "<EMAIL>"
            Else
                strFrom = CurrentUser.GetUserEmail
            End If

            strBody = AddHeader(strBody, "Account Manager Automated Email - Owner Change Request Rejected")
            CreateEmailHeader("The new owner rejected your requset to change ownership.", True, True, 2)
            CreateEmailHeader("Current Owner", True, False)
            CreateEmailRow(CurrentUser.DisplayName & " (" & CurrentUser.GetUserID & ")", False, True)

            CreateEmailHeader("Account ID", True, False)
            CreateEmailRow(strAccount, False, True)

            CreateEmailHeader("Comments", True, False)
            CreateEmailHeader(strComments, False, True)

            strSubject = strSubject

            If Trim(strTo) <> "" Then
                SendEmail(strTo, strFrom, strSubject, sMailBody, "")
            End If

            Return strTo

        End Function

        'Title: CreateAndSend
        'Created by: Shane Z Smith on 8/23/02
        'Purpose:  Creats teh body of the email and calls the sending function
        'Assumptions:  None
        'Effects:  Creates a standard email so all the emails look the same but have different information.
        'Inputs:  Request Number, To Field, Subject line of email, and the Link Address so the user can navigate to the request
        'Returns:  None
        Function SendDeleteRequest(ByVal strSendTo As String, ByVal strSubject As String, ByVal strAccount As String, ByVal strComments As String) As String

            Dim hpGet As New Helper

            Dim CurrentUser As New UserInfo
            Dim strBody, strFrom As String

            If Trim(CurrentUser.GetUserEmail) = "" Then
                strFrom = "<EMAIL>"
            Else
                strFrom = CurrentUser.GetUserEmail
            End If

            strBody = AddHeader(strBody, "Account Manager Automated Email - Account Delete Request")
            CreateEmailHeader("A request for deletion of the following account has been received:", True, True, 2)

            'CreateEmailHeader("Current Owner", True, False)
            CreateEmailHeader("Requested By", True, False)
            CreateEmailRow(CurrentUser.DisplayName & " (" & CurrentUser.GetUserID & ")", False, True)

            CreateEmailHeader("Account ID", True, False)
            CreateEmailRow(strAccount, False, True)

            CreateEmailHeader("Comments", True, False)
            CreateEmailHeader(strComments, False, True)
            CreateEmailRow("<font color=red>Account will be disabled for two months and then deleted without any further communication.</font>", True, True, 2)

            strSubject = strSubject

            If Trim(strSendTo) <> "" Then
                SendEmail(strSendTo, strFrom, strSubject, sMailBody, "")
            End If

            Return strSendTo

        End Function

        Function SendNoPasswordNetworkNotice(ByVal strSendTo As String, ByVal strSubject As String, ByVal strAccount As String, ByVal straction As String) As String

            Dim hpGet As New Helper

            Dim CurrentUser As New UserInfo
            Dim strBody, strFrom As String

            If Trim(CurrentUser.GetUserEmail) = "" Then
                strFrom = "<EMAIL>"
            Else
                strFrom = CurrentUser.GetUserEmail
            End If

            strBody = AddHeader(strBody, "Account Manager Automated Email - Account Setting Changed by " & CurrentUser.DisplayName)
            If straction.Contains("enable") Then
                CreateEmailHeader("Please add " & strAccount & " to Envision for monitoring of interactive logins.Contact the account owner if any logs are received.", True, True, 2)
            Else
                CreateEmailHeader("Please remove " & strAccount & " from Envision for interactive logins monitoring.", True, True, 2)
            End If


            strSubject = strSubject

            If Trim(strSendTo) <> "" Then
                SendEmail(strSendTo, strFrom, strSubject, sMailBody, "")
            End If

            Return strSendTo

        End Function

        'Title: SendPasswordChange
        'Created by: Shane Z Smith on 8/23/02
        'Purpose:  Creats teh body of the email and calls the sending function
        'Assumptions:  None
        'Effects:  Creates a standard email so all the emails look the same but have different information.
        'Inputs:  Request Number, To Field, Subject line of email, and the Link Address so the user can navigate to the request
        'Returns:  None
        Function SendPasswordChange(ByVal strSendTo As String, ByVal strSubject As String, ByVal strAccount As String, ByVal strComments As String) As String

            Dim hpGet As New Helper

            Dim CurrentUser As New UserInfo
            Dim strBody, strFrom As String

            If Trim(CurrentUser.GetUserEmail) = "" Then
                strFrom = "<EMAIL>"
            Else
                strFrom = CurrentUser.GetUserEmail
            End If

            strBody = AddHeader(strBody, "Account Manager Automated Email - Account Passworc Changed By " & CurrentUser.DisplayName)
            CreateEmailHeader("An owner has changed the password on this account.", True, True, 2)
            CreateEmailHeader("Change Made By", True, False)
            CreateEmailRow(CurrentUser.DisplayName & " (" & CurrentUser.GetUserID & ")", False, True)

            CreateEmailHeader("Account ID", True, False)
            CreateEmailRow(strAccount, False, True)

            CreateEmailHeader("Comments", True, False)
            CreateEmailHeader(strComments, False, True)
            AddStandard(2)

            strSubject = strSubject

            If Trim(strSendTo) <> "" Then
                SendEmail(strSendTo, strFrom, strSubject, sMailBody, "")
            End If

            Return strSendTo

        End Function

        Function SendAccessChange(ByVal strSubject As String, _
                ByVal alAccounts As ArrayList, _
                ByVal strComments As String, _
                ByVal strAction As String, Optional ByVal strUserIDDeleted As String = Nothing) As String

            Dim hpGet As New Helper
            Dim CurrentUser As New UserInfo
            Dim strBody, strFrom, strSendTo As String

            If Trim(CurrentUser.GetUserEmail) = "" Then
                strFrom = "<EMAIL>"
            Else
                strFrom = CurrentUser.GetUserEmail
            End If

            Dim srOwners As SqlClient.SqlDataReader
            Dim dbGet As New DataAccess
            Dim strSQL As String = ""
            Dim strIN As String = ""
            Dim intIndex As Integer = 0

            'Get owners/delegates for accounts changed
            Do While intIndex < alAccounts.Count
                'Get's account ID
                strIN = "'" & alAccounts.Item(intIndex) & "', " & strIN
                intIndex = intIndex + 1
            Loop

            strIN = strIN.Remove(strIN.Length - 2, 2)
            strSQL = "SELECT distinct UserName FROM vUsr " & _
                     "WHERE UserName <> '" & CurrentUser.GetUserID & "' " & _
                     "AND AccountID IN (" & strIN & ")"

            srOwners = dbGet.GetDataReaderByStringId(strSQL)

            Do While srOwners.Read

                Dim dbAcct As New DataAccess
                Dim srAcct As SqlClient.SqlDataReader

                Reinit()
                strBody = AddHeader(strBody, "Account Manager Automated Email - Backup/Delegate Changes")
                CreateEmailHeader("Change Made By", True, False)
                CreateEmailRow(CurrentUser.DisplayName & " (" & CurrentUser.GetUserID & ")", False, True, 2)
                CreateEmailHeader("Comments", True, False)
                CreateEmailRow(strComments, False, True, 2)
                CreateEmailHeader("", True, True)
                CreateEmailHeader("", True, True)
                CreateEmailHeader(strAction, True, True, 3)

                If strUserIDDeleted <> "" Then
                    CreateEmailHeader("Account ID", True, False)
                    CreateEmailHeader("User Removed", False, True)
                Else
                    CreateEmailHeader("Account ID", True, True)
                End If

                strSQL = "SELECT * FROM vUSR " & _
                         "WHERE AccountID IN (" & strIN & ") " & _
                         "AND UserName = '" & srOwners.Item("UserName") & "' "


                srAcct = dbAcct.GetDataReaderByStringId(strSQL)

                Dim usrGetEmail As New UserInfo(srOwners.Item("UserName"))
                strSendTo = usrGetEmail.GetUserEmail

                'For all the accounts for each the current user in srAcct do the following
                '
                Do While srAcct.Read
                    CreateEmailRow(srAcct.Item("AccountID"), True, False)

                    If strUserIDDeleted <> "" Then

                        Dim strName As String = ""

                        Try
                            Dim usrGet As New UserInfo(strUserIDDeleted)
                            strName = usrGet.UserNameandID
                            usrGet = Nothing

                        Catch ex As Exception
                            strName = strUserIDDeleted
                        End Try

                        CreateEmailRow(strName, False, True)

                    End If

                Loop

                srAcct.Close()
                srAcct = Nothing
                dbAcct.CloseConnections()
                dbAcct = Nothing

                CreateEmailHeader("", True, True)
                CreateEmailRow("Click the following link to visit Account Manager and view accounts you have access to manage.", True, True, 2)
                CreateEmailRow("<a href=" & [Global].GblHttp & "AccountManager/Default.aspx>Account Manager</a>", True, True, 2)
                CreateEmailHeader("", True, True)

                AddStandard(2)

                strSubject = strSubject

                'If Trim(strSendTo) <> "" Then
                ' SendEmail(strSendTo, strFrom, strSubject, sMailBody, "")
                'End If
            Loop
            srOwners.Close()
            srOwners = Nothing
            dbGet.CloseConnections()
            dbGet = Nothing

            Return strSendTo

        End Function

        Function SendDelAccessChange(ByVal strSubject As String, _
                        ByVal alAccounts As ArrayList, _
                        ByVal strComments As String, _
                            ByVal strAction As String, ByVal stremailbody As String) As String

            Dim hpGet As New Helper
            Dim CurrentUser As New UserInfo
            Dim strBody, strFrom, strSendTo As String

            If Trim(CurrentUser.GetUserEmail) = "" Then
                strFrom = "<EMAIL>"
            Else
                strFrom = CurrentUser.GetUserEmail
            End If

            Dim srOwners As SqlClient.SqlDataReader
            Dim dbGet As New DataAccess
            Dim strSQL As String = ""
            Dim strIN As String = ""
            Dim intIndex As Integer = 0
            Dim intCIndex As Integer = 0
            'Get owners/delegates for accounts changed
            Do While intIndex < alAccounts.Count
                'Get's account ID
                strIN = "'" & alAccounts.Item(intIndex) & "', " & strIN
                intIndex = intIndex + 1
            Loop

            strIN = strIN.Remove(strIN.Length - 2, 2)
            strSQL = "SELECT distinct UserName FROM vUsr " & _
                     "WHERE UserName <> '" & CurrentUser.GetUserID & "' " & _
                     "AND AccountID IN (" & strIN & ")"

            srOwners = dbGet.GetDataReaderByStringId(strSQL)

            Do While srOwners.Read

                Dim dbAcct As New DataAccess
                Dim srAcct As SqlClient.SqlDataReader

                Reinit()
                strBody = AddHeader(strBody, "Account Manager Automated Email - Backup/Delegate Changes")
                CreateEmailHeader("Change Made By", True, False)
                CreateEmailRow(CurrentUser.DisplayName & " (" & CurrentUser.GetUserID & ")", False, True, 2)
                CreateEmailHeader("Comments", True, False)
                CreateEmailRow(strComments, False, True, 2)
                CreateEmailHeader("", True, True)
                CreateEmailHeader("", True, True)
                CreateEmailHeader(strAction, True, True, 3)
                CreateEmailHeader("Account ID", True, False)
                CreateEmailHeader("User Removed", False, True)

                Dim usrGetEmail As New UserInfo(srOwners.Item("UserName"))
                strSendTo = usrGetEmail.GetUserEmail

                'For all the accounts for each the current user in srAcct do the following
                '


                AddPlainText(stremailbody)

                CreateEmailHeader("", True, True)
                CreateEmailRow("Click the following link to visit Account Manager and view accounts you have access to manage.", True, True, 2)
                CreateEmailRow("<a href=" & [Global].GblHttp & "AccountManager/Default.aspx>Account Manager</a>", True, True, 2)
                CreateEmailHeader("", True, True)

                AddStandard(2)

                strSubject = strSubject

                'If Trim(strSendTo) <> "" Then
                SendEmail(strSendTo, strFrom, strSubject, sMailBody, "")
                'End If
            Loop
            srOwners.Close()
            srOwners = Nothing
            dbGet.CloseConnections()
            dbGet = Nothing

            Return strSendTo

        End Function

        Function AddStandard(ByVal colSpan As Integer) As String

            sMailBody = sMailBody & "<tr><td align=Left colspan=" & colSpan & "><br/><br/><strong>Non-User ID Password Help Documents</strong><br/>" & _
                         "<table>" & _
                         "<tr><td><a target=_blank href=" & Chr(34) & "https://archer.kcc.com/apps/ArcherApp/Home.aspx?workspaceId=-1&requestUrl=..%2fGenericContent%2fRecord.aspx%3fid%3d224582%26moduleId%3d65#redirect" & Chr(34) & ">Please refer to IT 270-01 Access Management Standard</a><br/></td></tr>" & _
                         "<tr><td><a target=_blank href=" & Chr(34) & "https://kcc.service-now.com/kc_sp?sys_kb_id=6b69362edb13e740ada5ae441b961905&id=kb_article_view&sysparm_rank=6&sysparm_tsqueryId=6f36672cdb247784d9b5e7b51b9619a5" & Chr(34) & ">How To: Reset Password for Non-User ID from Account Manager</a><br/></td></tr></table></td></tr>" & _
                         "<tr><td><a target=_blank href=" & Chr(34) & "https://kcc.service-now.com/kc_sp?sys_kb_id=c372f273db3a1fc0e790d2cb4b961967&id=kb_article_view&sysparm_rank=1&sysparm_tsqueryId=1c695e6ddbac3f84d9b5e7b51b9619a9" & Chr(34) & ">Info: Managing Passwords for Production ID Accounts</a><br/></td></tr></table></td></tr>"
            Return sMailBody

        End Function

        'Title: Add a Header Line
        'Created by: Shane Z Smith on 5/10/02
        'Purpose:  Creates a single html header line (row) at the top of the email
        'Assumptions:  None
        'Effects:  Creates a line at the top of the email
        'Inputs:  Body is the original contents of the email, so the email doesn't lose anything previously added,
        '         NewLine is the header text
        'Returns:  The line created
        Function AddHeader(ByVal Body As String, ByVal NewLine As String)
            AddHeader = Body & "<tr><td colspan=2><font size=+2><b>" & NewLine & ": </b></font></td></tr>"
        End Function

    End Class

End Namespace


