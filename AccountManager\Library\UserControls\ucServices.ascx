﻿<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Control Language="vb" AutoEventWireup="false" Inherits="AccountManager.ucServices"
    CodeFile="ucServices.ascx.vb" %>
<table class="DisplayTables">
    <tr>
        <td class="FunctionStep">
            Search Services</td>
    </tr>
    <tr>
        <td>
            <asp:Label id="lblMessage" runat="server" cssclass="LabelRed"></asp:Label></td>
    </tr>
    <tr>
        <td>
            <asp:TextBox id="txtAccountId" runat="server"></asp:TextBox></td>
    </tr>
    <tr>
        <td>
            <dxwdc:aspxbutton id="cmdGet" tabindex="0" runat="server" text="Get Data">
                <LookAndFeel Kind="Office2003">
                    <EditorStyle font-size="8pt" Font-Names="Verdana" bordercolor="#6787B8" forecolor="Black"
                        backcolor="White">
                    </EditorStyle>
                    <LabelStyle font-size="8pt" Font-Names="Verdana" forecolor="Black"></LabelStyle>
                    <ScrollBarButtonStyle backcolor="#84ABE3">
                        <Filters>
                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal"
                                StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
                        </Filters>
                    </ScrollBarButtonStyle>
                    <ElementsSettings scrollbarsize="17px" dropdownbuttonwidth="17px" scrollbarbackcolor="247, 245, 241"
                        scrollbarmargin="1"></ElementsSettings>
                    <PopupStyle font-size="8pt" font-Names="Verdana" bordercolor="#6787B8">
                    </PopupStyle>
                    <ButtonStyle usehottrackstyle="True" font-size="8pt" Font-Names="Verdana" bordercolor="#6787B8"
                        usepressedstyle="True" forecolor="Black" backcolor="#84ABE3" Wrap="False" Margin="1">
                        <HotTrackStyle bordercolor="Navy" forecolor="Black" backcolor="#FFD599">
                            <Filters>
                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202">
                                </dxwdc:LookAndFeelStyleGradientFilter>
                            </Filters>
                        </HotTrackStyle>
                        <PressedStyle bordercolor="Navy" forecolor="Black" backcolor="#FFCA86">
                            <Filters>
                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80">
                                </dxwdc:LookAndFeelStyleGradientFilter>
                            </Filters>
                        </PressedStyle>
                        <Filters>
                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254">
                            </dxwdc:LookAndFeelStyleGradientFilter>
                        </Filters>
                    </ButtonStyle>
                </LookAndFeel>
            </dxwdc:ASPxButton>
            <lookandfeel kind="Office2003"><EDITORSTYLE backcolor="White" forecolor="Black" bordercolor="#6787B8" font-aames="Verdana" font-size="8pt"></EDITORSTYLE>
				<LABELSTYLE forecolor="Black" font-aames="Verdana" font-size="8pt"></LABELSTYLE>
				<SCROLLBARBUTTONSTYLE backcolor="#84ABE3">
					<FILTERS>
						<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="221, 236, 254" GradientMode="Horizontal" EndColor="132, 171, 227"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
					</FILTERS>
				</SCROLLBARBUTTONSTYLE>
				<ELEMENTSSETTINGS scrollbarmargin="1" scrollbarbackcolor="247, 245, 241" dropdownbuttonwidth="17px"
					scrollbarsize="17px"></ELEMENTSSETTINGS>
				<POPUPSTYLE bordercolor="#6787B8" font-aames="Verdana" font-size="8pt"></POPUPSTYLE>
				<BUTTONSTYLE backcolor="#84ABE3" forecolor="Black" bordercolor="#6787B8" font-aames="Verdana"
					font-size="8pt" margin="1" wrap="False" usepressedstyle="True" usehottrackstyle="True">
					<HOTTRACKSTYLE backcolor="#FFD599" forecolor="Black" bordercolor="Navy">
						<FILTERS>
							<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="255, 243, 202" EndColor="255, 213, 153"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
						</FILTERS>
					</HOTTRACKSTYLE>
					<PRESSEDSTYLE backcolor="#FFCA86" forecolor="Black" bordercolor="Navy">
						<FILTERS>
							<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="254, 148, 80" EndColor="255, 202, 134"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
						</FILTERS>
					</PRESSEDSTYLE>
					<FILTERS>
						<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="221, 236, 254" EndColor="132, 171, 227"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
					</FILTERS>
				</BUTTONSTYLE>
			</lookandfeel>
            &nbsp;<dxwdc:ASPxButton id="cmdExport" TabIndex="0" runat="server" Text="Export">
                <LookAndFeel Kind="Office2003">
                    <EditorStyle font-size="8pt" Font-Names="Verdana" bordercolor="#6787B8" forecolor="Black"
                        backcolor="White">
                    </EditorStyle>
                    <LabelStyle font-size="8pt" Font-Names="Verdana" forecolor="Black"></LabelStyle>
                    <ScrollBarButtonStyle backcolor="#84ABE3">
                        <Filters>
                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal"
                                StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
                        </Filters>
                    </ScrollBarButtonStyle>
                    <ElementsSettings scrollbarsize="17px" dropdownbuttonwidth="17px" scrollbarbackcolor="247, 245, 241"
                        scrollbarmargin="1"></ElementsSettings>
                    <PopupStyle font-size="8pt" Font-Names="Verdana" bordercolor="#6787B8">
                    </PopupStyle>
                    <ButtonStyle usehottrackstyle="True" font-size="8pt" Font-Names="Verdana" bordercolor="#6787B8"
                        usepressedstyle="True" forecolor="Black" backcolor="#84ABE3" Wrap="False" Margin="1">
                        <HotTrackStyle bordercolor="Navy" forecolor="Black" backcolor="#FFD599">
                            <Filters>
                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202">
                                </dxwdc:LookAndFeelStyleGradientFilter>
                            </Filters>
                        </HotTrackStyle>
                        <PressedStyle bordercolor="Navy" forecolor="Black" backcolor="#FFCA86">
                            <Filters>
                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80">
                                </dxwdc:LookAndFeelStyleGradientFilter>
                            </Filters>
                        </PressedStyle>
                        <Filters>
                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254">
                            </dxwdc:LookAndFeelStyleGradientFilter>
                        </Filters>
                    </ButtonStyle>
                </LookAndFeel>
            </dxwdc:ASPxButton>
            <lookandfeel kind="Office2003"><EDITORSTYLE backcolor="White" forecolor="Black" bordercolor="#6787B8" font-aames="Verdana" font-size="8pt"></EDITORSTYLE>
				<LABELSTYLE forecolor="Black" font-aames="Verdana" font-size="8pt"></LABELSTYLE>
				<SCROLLBARBUTTONSTYLE backcolor="#84ABE3">
					<FILTERS>
						<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="221, 236, 254" GradientMode="Horizontal" EndColor="132, 171, 227"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
					</FILTERS>
				</SCROLLBARBUTTONSTYLE>
				<ELEMENTSSETTINGS scrollbarmargin="1" scrollbarbackcolor="247, 245, 241" dropdownbuttonwidth="17px"
					scrollbarsize="17px"></ELEMENTSSETTINGS>
				<POPUPSTYLE bordercolor="#6787B8" font-aames="Verdana" font-size="8pt"></POPUPSTYLE>
				<BUTTONSTYLE backcolor="#84ABE3" forecolor="Black" bordercolor="#6787B8" font-aames="Verdana"
					font-size="8pt" margin="1" wrap="False" usepressedstyle="True" usehottrackstyle="True">
					<HOTTRACKSTYLE backcolor="#FFD599" forecolor="Black" bordercolor="Navy">
						<FILTERS>
							<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="255, 243, 202" EndColor="255, 213, 153"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
						</FILTERS>
					</HOTTRACKSTYLE>
					<PRESSEDSTYLE backcolor="#FFCA86" forecolor="Black" bordercolor="Navy">
						<FILTERS>
							<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="254, 148, 80" EndColor="255, 202, 134"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
						</FILTERS>
					</PRESSEDSTYLE>
					<FILTERS>
						<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="221, 236, 254" EndColor="132, 171, 227"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
					</FILTERS>
				</BUTTONSTYLE>
			</lookandfeel>
        </td>
    </tr>
    <tr>
        <td>
            <asp:hyperlink id="hlDownload" target="_blank" runat="server" font-bold="True" forecolor="#000099"
                font-size="14"></asp:HyperLink></td>
    </tr>
    <tr>
        <td>
            <dxwg:aspxgrid id="grdServices" tabindex="0" runat="server" bordercolor="#6787B8"
                borderstyle="Solid" expandbtnwidth="11px" headerheight="25px" rowbtnwidth="18px"
                selectedbackcolor="49, 106, 197" expandbtnheight="11px" searchbtnwidth="17px"
                statusbaritemspacing="0" pageindexbuttoncount="30" autogeneratecolumns="False">
                <AlternatingItemStyle backcolor="#E7F2FE"></AlternatingItemStyle>
                <ExpandBtnStyle borderwidth="1px" bordercolor="#6787B8"  backcolor="#F9F9F9">
                    <HotTrackStyle bordercolor="Navy" backcolor="White">
                    </HotTrackStyle>
                    <PressedStyle bordercolor="Navy" forecolor="Black" backcolor="#D0D0D0">
                    </PressedStyle>
                </ExpandBtnStyle>
                <ButtonBarStyle borderwidth="1px" bordercolor="#6787B8" borderstyle="Solid">
                </ButtonBarStyle>
                <GroupPanelStyle font-bold="True" forecolor="#DDECFE" backcolor="#3E6DB9">
                </GroupPanelStyle>
                <SearchBtnStyle FixedWidth="True">
                </SearchBtnStyle>
                <TitleStyle forecolor="White" backcolor="#6787B8"></TitleStyle>
                <GroupItemStyle fixedWidth="True" bordercolor="#6787B8" backcolor="#C1D8F7" Wrap="False">
                </GroupItemStyle>
                <RowBtnStyle BorderStyle="None">
                </RowBtnStyle>
                <ButtonBars>
                    <dxwg:ButtonBar ButtonBarType="Navigator">
                        <BarItems>
                            <dxwdc:BarButton ButtonType="MoveFirst">
                            </dxwdc:BarButton>
                            <dxwdc:BarButton ButtonType="MovePrevPage">
                            </dxwdc:BarButton>
                            <dxwdc:BarButton ButtonType="MovePrev">
                            </dxwdc:BarButton>
                            <dxwdc:BarTwoStateEditorButton ButtonType="ChangePageSize">
                            </dxwdc:BarTwoStateEditorButton>
                            <dxwdc:BarButton ButtonType="MoveNext">
                            </dxwdc:BarButton>
                            <dxwdc:BarButton ButtonType="MoveNextPage">
                            </dxwdc:BarButton>
                            <dxwdc:BarButton ButtonType="MoveLast">
                            </dxwdc:BarButton>
                            <dxwdc:BarButton ButtonType="Refresh">
                            </dxwdc:BarButton>
                        </BarItems>
                    </dxwg:ButtonBar>
                </ButtonBars>
                <HeaderStyle FixedWidth="True" Font-Bold="True" BorderStyle="None" FixedHeight="True"
                    Wrap="False"></HeaderStyle>
                <StatusBars>
                    <dxwg:StatusBar Height="20px" StatusBarType="Regular">
                        <BarItems>
                            <dxwdc:BarStatusSection StatusSectionType="Status">
                            </dxwdc:BarStatusSection>
                            <dxwdc:BarStatusSection StatusSectionType="VisibleInterval">
                            </dxwdc:BarStatusSection>
                            <dxwdc:BarStatusSection StatusSectionType="TotalVisible">
                            </dxwdc:BarStatusSection>
                            <dxwdc:BarStatusSection StatusSectionType="TotalRows">
                            </dxwdc:BarStatusSection>
                        </BarItems>
                    </dxwg:StatusBar>
                </StatusBars>
                <ItemStyle FixedWidth="True" VerticalAlign="Middle" font-size="7.5pt" backcolor="White"
                    Wrap="False"></ItemStyle>
                <HeaderDraggedStyle BorderWidth="1px" bordercolor="LightGray" BorderStyle="Solid">
                    <Filters>
                        <dxwdc:LookAndFeelStyleAlphaFilter FinishOpacity="50" FinishX="50">
                        </dxwdc:LookAndFeelStyleAlphaFilter>
                    </Filters>
                </HeaderDraggedStyle>
                <LookAndFeel Kind="Office2003">
                    <EditorStyle font-size="8pt" Font-Names="Verdana" bordercolor="#6787B8" forecolor="Black"
                        backcolor="White">
                    </EditorStyle>
                    <LabelStyle font-size="8pt" Font-Names="Verdana" forecolor="Black"></LabelStyle>
                    <ScrollBarButtonStyle backcolor="#84ABE3">
                        <Filters>
                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal"
                                StartColor="221, 236, 254">
                            </dxwdc:LookAndFeelStyleGradientFilter>
                        </Filters>
                    </ScrollBarButtonStyle>
                    <ElementsSettings scrollbarsize="17px" dropdownbuttonwidth="17px" scrollbarbackcolor="247, 245, 241"
                        scrollbarmargin="1"></ElementsSettings>
                    <PopupStyle font-size="8pt" Font-Names="Verdana" bordercolor="#6787B8">
                    </PopupStyle>
                    <ButtonStyle usehottrackstyle="True" font-size="8pt" Font-Names="Verdana" bordercolor="#6787B8"
                        usepressedstyle="True" forecolor="Black" backcolor="#84ABE3" Wrap="False" Margin="1">
                        <HotTrackStyle bordercolor="Navy" forecolor="Black" backcolor="#FFD599">
                            <Filters>
                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202">
                                </dxwdc:LookAndFeelStyleGradientFilter>
                            </Filters>
                        </HotTrackStyle>
                        <PressedStyle bordercolor="Navy" forecolor="Black" backcolor="#FFCA86">
                            <Filters>
                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80">
                                </dxwdc:LookAndFeelStyleGradientFilter>
                            </Filters>
                        </PressedStyle>
                        <Filters>
                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254">
                            </dxwdc:LookAndFeelStyleGradientFilter>
                        </Filters>
                    </ButtonStyle>
                </LookAndFeel>
                <GroupedHeaderStyle BorderWidth="1px" bordercolor="#6787B8" BorderStyle="Solid">
                    <HotTrackStyle bordercolor="Navy">
                    </HotTrackStyle>
                    <PressedStyle bordercolor="Navy">
                    </PressedStyle>
                </GroupedHeaderStyle>
                <BarBtnStyle BorderStyle="None">
                    <HotTrackStyle BorderStyle="Solid">
                    </HotTrackStyle>
                    <PressedStyle BorderStyle="Solid">
                    </PressedStyle>
                </BarBtnStyle>
                <FooterStyle FixedWidth="True" Font-Bold="True" FixedHeight="True" backcolor="#B0CBF1"
                    Wrap="False"></FooterStyle>
                <PreviewStyle forecolor="#5881B9" backcolor="#F9FCFF">
                </PreviewStyle>
                <BarBtnEditorStyle BorderStyle="None">
                </BarBtnEditorStyle>
                <SearchEditorStyle bordercolor="White">
                </SearchEditorStyle>
                <SearchItemStyle backcolor="#C1D8F7">
                </SearchItemStyle>
                <Columns>
                    <dxwg:BoundColumn VisibleIndex="0" DataField="HOST" Width="100px">
                    </dxwg:BoundColumn>
                    <dxwg:BoundColumn VisibleIndex="1" DataField="Display Name" Width="200px">
                    </dxwg:BoundColumn>
                    <dxwg:BoundColumn VisibleIndex="2" DataField="Account Name" Width="200px">
                    </dxwg:BoundColumn>
                </Columns>
                <NavigatorButtons Ok="False" InsertRow="False" DeleteRow="False" Cancel="False" EditRow="False">
                </NavigatorButtons>
                <FooterItemStyle backcolor="#B0CBF1">
                </FooterItemStyle>
                <StatusBarStyle BorderStyle="None" backcolor="#DDECFE">
                    <Filters>
                        <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254">
                        </dxwdc:LookAndFeelStyleGradientFilter>
                    </Filters>
                </StatusBarStyle>
                <GroupIndentStyle backcolor="#C1D8F7">
                </GroupIndentStyle>
            </dxwg:ASPxGrid>
        </td>
    </tr>
</table>
