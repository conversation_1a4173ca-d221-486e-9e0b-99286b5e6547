Imports System
Imports System.Diagnostics
Imports KCC.Common
Imports KCC.Common.Data
Imports GroupManager.Global
Imports System.Text
Imports System.IO
Imports System.Configuration
Imports System.Data.SqlClient
Namespace GroupManager

    Public Class GroupManager

#Region "Member Variables"
        Private m_dtbClassificationCodes As DataTable
        Private m_strUserID As String
        Private m_dtbUserGroupDetails As DataTable
        Private m_dtbUserGroupStatus As DataTable
        Private m_dtbUserMemberShipDetails As DataTable
        Private m_dtbUserName As DataTable
        Private m_dtbSelectedGroup As DataTable
        Private m_dtbSelectedGroupList As DataTable
        Private m_dtbSelectedGroupAuth As DataTable
        Private m_dvRemoveAuth As DataView
        Private m_strSelectedAuthID As String
        Private m_dvSelectedGroups As DataView
        Private m_dvMyMemberShip As DataView
        Private m_dvSelectedGroupsList As DataView
        Private m_dtSelectRequest As DataTable
        Private m_dtbSearchUser As DataTable
        Private m_strAccessId As String
        Private m_strGroupName As String
        Private m_strFilterGroupName As String
        Private m_strFilterGroupDesc As String
        Private m_iFilterClassId As Int16
        Private m_iFilterAccessId As Int16
        Private m_strFilterOwnerComments As String

#End Region


#Region "Properties Used in Mygroup"
        'Property : MyGroupsList
        Public Property UserGroupsStatus() As DataTable
            Get
                Return m_dtbUserGroupStatus
            End Get
            Set(ByVal value As DataTable)
                m_dtbUserGroupStatus = value
            End Set
        End Property
        'property added to get list of user and their authorising status
        Public Property MyGroupsList() As DataTable
            Get
                Return m_dtbUserGroupDetails
            End Get
            Set(ByVal value As DataTable)
                m_dtbUserGroupDetails = value
            End Set
        End Property

        'properrty added to get membership details separately
        Public Property MyMemberShipList() As DataTable
            Get
                Return m_dtbUserMemberShipDetails
            End Get
            Set(ByVal value As DataTable)
                m_dtbUserMemberShipDetails = value
            End Set
        End Property

        'Property : UserName
        Public Property UserName() As DataTable
            Get
                Return m_dtbUserName
            End Get
            Set(ByVal value As DataTable)
                m_dtbUserName = value
            End Set
        End Property
#End Region

#Region "Properties Used in AddRemoveAuthorizers"
        'Property : SelectedMyGroupsList
        Public Property SelectedMyGroupsList() As DataTable
            Get
                Return m_dtbSelectedGroup
            End Get
            Set(ByVal value As DataTable)
                m_dtbSelectedGroup = value
            End Set
        End Property

        'Property : SelectedGroupsList
        Public Property SelectedGroupsList() As DataTable
            Get
                Return m_dtbSelectedGroupList
            End Get
            Set(ByVal value As DataTable)
                m_dtbSelectedGroupList = value
            End Set
        End Property

        'Property : SelectedGroupsAuthList
        Public Property SelectedGroupsAuthList() As DataTable
            Get
                Return m_dtbSelectedGroupAuth
            End Get
            Set(ByVal value As DataTable)
                m_dtbSelectedGroupAuth = value
            End Set
        End Property

        'Property : RemoveAuthorizerList
        Public Property RemoveAuthList() As DataView
            Get
                Return m_dvRemoveAuth
            End Get
            Set(ByVal value As DataView)
                m_dvRemoveAuth = value
            End Set
        End Property
        'Property : SelectedGroups
        Public Property SelectedMyGroups() As DataView
            Get
                Return m_dvSelectedGroups
            End Get
            Set(ByVal value As DataView)
                m_dvSelectedGroups = value
            End Set
        End Property
        'Property : SelectedGroups
        Public Property SelectedGroups() As DataView
            Get
                Return m_dvSelectedGroupsList
            End Get
            Set(ByVal value As DataView)
                m_dvSelectedGroupsList = value
            End Set
        End Property

        'Property : MyMemberShip
        'properrty added to get membership details separately
        Public Property MyMemberShip() As DataView
            Get
                Return m_dvMyMemberShip
            End Get
            Set(ByVal value As DataView)
                m_dvMyMemberShip = value
            End Set
        End Property

        'Property : SelectedAuthorizerID
        Public Property SelectedAuthID() As String
            Get
                Return m_strSelectedAuthID
            End Get
            Set(ByVal value As String)
                m_strSelectedAuthID = value
            End Set
        End Property

        'Property : SearchFilterForGroupName
        Public Property FilterGroupName() As String
            Get
                Return m_strFilterGroupName
            End Get
            Set(ByVal value As String)
                m_strFilterGroupName = value
            End Set
        End Property

        'Property : SearchFilterForGroupDesc
        Public Property FilterOwnerComments() As String
            Get
                Return m_strFilterOwnerComments
            End Get
            Set(ByVal value As String)
                m_strFilterOwnerComments = value
            End Set
        End Property

        'Property : SearchFilterForClassid
        Public Property FilterClassId() As Int16
            Get
                Return m_iFilterClassId
            End Get
            Set(ByVal value As Int16)
                m_iFilterClassId = value
            End Set
        End Property

        'Property : SearchFilterForAccessId
        Public Property FilterAccessId() As Int16
            Get
                Return m_iFilterAccessId
            End Get
            Set(ByVal value As Int16)
                m_iFilterAccessId = value
            End Set
        End Property

        'Property : SearchFilterForOwnerComments
        Public Property FilterGroupDesc() As String
            Get
                Return m_strFilterGroupDesc
            End Get
            Set(ByVal value As String)
                m_strFilterGroupDesc = value
            End Set
        End Property

#End Region

#Region "Properties Used in ReassignOwner"
        ' Property : ReassignRequest
        Public Property ReassignRequest() As DataTable
            Get
                Return m_dtSelectRequest
            End Get
            Set(ByVal Value As DataTable)
                m_dtSelectRequest = Value
            End Set
        End Property

        'Property : ClassificationCodes
        Public Property ClassificationCodes() As DataTable
            Get
                Return m_dtbClassificationCodes
            End Get
            Set(ByVal value As DataTable)
                m_dtbClassificationCodes = value
            End Set
        End Property
#End Region

#Region "Properties Used In OverdueGroups"
        'Property : FilteredOverdueGroups
        Public Shared Property FilteredOverdueGroups() As DataView
            Get
                Dim dvOverdueGroups As DataView = CType(HttpContext.Current.Session(Constants.OVERDUE_GROUPS_VIEW_SESSION), DataView)
                Return dvOverdueGroups
            End Get
            Set(ByVal Value As DataView)
                HttpContext.Current.Session(Constants.OVERDUE_GROUPS_VIEW_SESSION) = Value
            End Set
        End Property
#End Region

#Region "Properties Used in UserPopUP"
        Public Property SearchUserResult() As DataTable
            Get
                Return m_dtbSearchUser
            End Get
            Set(ByVal Value As DataTable)
                m_dtbSearchUser = Value
            End Set
        End Property
#End Region

#Region "Properties Used in My Special Filter"
        'Property : Access ID
        Public Property AccessID() As String
            Get
                Return m_strAccessId
            End Get
            Set(ByVal Value As String)
                m_strAccessId = Value
            End Set
        End Property

        'Property : Group Name
        Public Property GroupName() As String
            Get
                Return m_strGroupName
            End Get
            Set(ByVal Value As String)
                m_strGroupName = Value
            End Set
        End Property
#End Region

#Region "Methods Used In Mygroups"
        ' function to get the User groups from the active directory

        Public Function GetUserGroups(ByVal strUserID As String) As DataSet
            Dim oAD As New AD
            Dim dtbUserGroup As DataTable = oAD.GetUserGroups(strUserID)
            Dim dsUserGroup As New DataSet
            dsUserGroup.DataSetName = "UserGroupsDataSet"
            dsUserGroup.Tables.Add(dtbUserGroup)
            GetUserGroups = dsUserGroup
        End Function
        'Get the UserGroupDetails from the database ( executing the stored proc)
        Public Sub GetUserGroupDetails(ByVal strUserID As String)
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_SelectUserGroupDetails
            Try
                dbGM.Parameters.Add("@in_UserID", strUserID, DbType.String)
                dbGM.Open()
                dbGM.CommandTimeout = 600
                m_dtbUserGroupDetails = dbGM.ExecuteQuerySP(strProcRequest, "UserGroupDetails")
                m_dtbUserGroupDetails.Columns.Add("CHKLFLAG")
                'm_dtbUserGroupDetails.Columns("dateReviewed").DATAtYPE() = Date.Now.GetType()
                m_dtbUserGroupDetails.AcceptChanges()
                Dim dtCloned As DataTable = m_dtbUserGroupDetails.Clone()
                dtCloned.Columns("dateReviewed").DataType = Date.Now.GetType()
                For Each row As DataRow In m_dtbUserGroupDetails.Rows
                    dtCloned.ImportRow(row)
                Next
                m_dvSelectedGroups = m_dtbUserGroupDetails.DefaultView
                dtCloned.Dispose()
            Finally
                If Not m_dtbUserGroupDetails Is Nothing Then
                    m_dtbUserGroupDetails.Dispose()

                End If
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try

        End Sub


        'Get the UserMemberShipDetails from the database ( executing the stored proc)
        'function added to get membership details separately
        Public Sub GetUserMemberShipDetails(ByVal dsUserGroups As DataSet)
            'Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            'Dim strProcRequest As String = Constants.sp_SelectUserMembershipDetails

            'Dim strXML As String = dsUserGroups.GetXml()
            Try
                'dbGM.Parameters.Add("@szXml", strXML, DbType.String)
                'dbGM.Parameters.Add("@in_UserID", strUserID, DbType.String)
                'dbGM.Open()
                'dbGM.CommandTimeout = 600
                m_dtbUserMemberShipDetails = dsUserGroups.Tables(0)
                m_dvMyMemberShip = m_dtbUserMemberShipDetails.DefaultView
            Finally
                If Not m_dtbUserMemberShipDetails Is Nothing Then
                    m_dtbUserMemberShipDetails.Dispose()
                End If

            End Try

        End Sub

        'Update the Group details in the database
        Public Sub UpdateMyGroupDetails(ByVal strADSPath As String, ByVal strOwnerComments As String, ByVal iClassID As Integer, ByVal strChangedUserID As String)
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_UpdateMyGroups
            Try
                dbGM.Parameters.Add("@in_ADSPath", strADSPath, DbType.String)
                dbGM.Parameters.Add("@in_OwnerComments", strOwnerComments, DbType.String)
                dbGM.Parameters.Add("@in_ClassID", iClassID, DbType.Int32)
                dbGM.Parameters.Add("@in_LastChangedBy", strChangedUserID, DbType.String)
                dbGM.Open()
                dbGM.CommandTimeout = 600
                dbGM.ExecuteNonQuerySP(strProcRequest)
            Finally
                If Not m_dtbUserGroupDetails Is Nothing Then
                    m_dtbUserGroupDetails.Dispose()
                End If
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try
        End Sub

        'BOC Prateek RC
        Public Sub UpdateMyGroupDetailswithoutcomment(ByVal strADSPath As String, ByVal iClassID As Integer, ByVal strChangedUserID As String)
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_UpdateMyGroupswithoutcomment
            Try
                dbGM.Parameters.Add("@in_ADSPath", strADSPath, DbType.String)
                dbGM.Parameters.Add("@in_ClassID", iClassID, DbType.Int32)
                dbGM.Parameters.Add("@in_LastChangedBy", strChangedUserID, DbType.String)
                dbGM.Open()
                dbGM.CommandTimeout = 600
                dbGM.ExecuteNonQuerySP(strProcRequest)
            Finally
                If Not m_dtbUserGroupDetails Is Nothing Then
                    m_dtbUserGroupDetails.Dispose()
                End If
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try
        End Sub
        'EOC Prateek RC        

        'Update the Group Classification details in the database
        Public Sub UpdateMyGroupClassDetails(ByVal strADSPath As String, ByVal strOwnerComments As String, ByVal iClassID As Integer, ByVal strChangedUserID As String)
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_UpdateMyGroupsClass
            Try
                dbGM.Parameters.Add("@in_ADSPath", strADSPath, DbType.String)
                dbGM.Parameters.Add("@in_OwnerComments", strOwnerComments, DbType.String)
                dbGM.Parameters.Add("@in_ClassID", iClassID, DbType.Int32)
                dbGM.Parameters.Add("@in_LastChangedBy", strChangedUserID, DbType.String)
                dbGM.Open()
                dbGM.CommandTimeout = 600
                dbGM.ExecuteNonQuerySP(strProcRequest)
            Finally
                If Not m_dtbUserGroupDetails Is Nothing Then
                    m_dtbUserGroupDetails.Dispose()
                End If
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try
        End Sub
#End Region

#Region "Methods Used In My Filtered groups"
        'Get the UserGroupDetails from the database ( executing the stored proc)
        Public Sub GetFilteredUserGroupDetails(ByVal strUserID As String, ByVal iAccesId As Int16)
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String
            If iAccesId = 1 Or iAccesId = 2 Or iAccesId = 3 Then
                strProcRequest = Constants.sp_SelectFilteredUserGroupDetailsExceptMember
            Else
                strProcRequest = Constants.sp_SelectUserGroupDetails
            End If
            Try
                dbGM.Parameters.Add("@in_UserID", strUserID, DbType.String)
                If iAccesId = 1 Or iAccesId = 2 Or iAccesId = 3 Then
                    dbGM.Parameters.Add("@in_AccessID", iAccesId, DbType.Int16)
                End If
                dbGM.Open()
                dbGM.CommandTimeout = 600
                m_dtbUserGroupDetails = dbGM.ExecuteQuerySP(strProcRequest, "UserGroupDetails")
                m_dtbUserGroupDetails.Columns.Add("CHKLFLAG")
                m_dtbUserGroupDetails.AcceptChanges()
                m_dvSelectedGroups = m_dtbUserGroupDetails.DefaultView
            Finally
                If Not m_dtbUserGroupDetails Is Nothing Then
                    m_dtbUserGroupDetails.Dispose()
                End If
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try

        End Sub
#End Region

#Region "methods Used In AddRemoveAuthorizers "
        'function to get the Owner/Authorizer for the group

        Public Function GetOwnerAuthorizersForGroup(ByVal strXML As String, ByVal strGetOwner As String) As DataTable
            Dim dtbOwnerAuthGroups As DataTable = Nothing
            Dim oAD As New AD
            Dim dtbTemp As DataTable
            Dim iRowCount As Integer
            Dim iRowIndex As Integer = 0
            Dim strUserName, strDisplayName As String
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_SelectAuthorizers
            Try
                dbGM.Parameters.Add("@in_InputXML", strXML, DbType.String)
                dbGM.Parameters.Add("@in_GetOwner", strGetOwner, DbType.String)
                dbGM.Open()
                dbGM.CommandTimeout = 600
                dtbOwnerAuthGroups = dbGM.ExecuteQuerySP(strProcRequest, Constants.DTB_OWNER_AUTH_GROUPS)
                iRowCount = dtbOwnerAuthGroups.Rows.Count
                dtbTemp = dtbOwnerAuthGroups.Copy()
                For iRowIndex = 0 To iRowCount - 1
                    strUserName = dtbOwnerAuthGroups.Rows(iRowIndex)("UserName")
                    strDisplayName = oAD.GetDisplayName(strUserName)
                    dtbTemp.Rows(iRowIndex)("DisplayName") = strDisplayName
                Next
            Finally
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try
            Return dtbTemp
        End Function

        'deleting the Authorizer from the database
        Public Sub DeleteAuthorizers(ByVal strDelAuthXML As String)
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_DeleteAuthorizers
            Try
                dbGM.Parameters.Add("@in_InputXML", strDelAuthXML, DbType.String)
                dbGM.Open()
                dbGM.CommandTimeout = 600
                dbGM.ExecuteNonQuerySP(strProcRequest)
            Finally
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try
        End Sub
        'insert the Authorizer in the database
        Public Sub InsertAuthorizer(ByVal strADSPath As String, ByVal strUserID As String)
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_InsertAuthorizer
            Try
                dbGM.Parameters.Add("@in_ADSPath", strADSPath, DbType.String)
                dbGM.Parameters.Add("@in_UserID", strUserID, DbType.String)
                dbGM.Open()
                dbGM.CommandTimeout = 600
                dbGM.ExecuteNonQuerySP(strProcRequest)
            Finally
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try
        End Sub
        'Check the Authorizer in the database
        Public Function CheckAuthorizer(ByVal strADSPath As String, ByVal strUserID As String, ByVal strLoginUser As String) As DataTable
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_CheckAuthorizer
            Dim intReturn, intAccess, intDelCount As Int16
            Dim dtbResult As DataTable = Nothing
            Dim drResult As DataRow = Nothing
            Try
                dbGM.Parameters.Add("@in_ADSPath", strADSPath, DbType.String)
                dbGM.Parameters.Add("@in_UserID", strUserID, DbType.String)
                dbGM.Parameters.Add("@in_LoginID", strLoginUser, DbType.String)
                dbGM.Parameters.Add("@out_Count", intReturn, DbType.Int16)
                dbGM.Parameters.Add("@out_Access", intAccess, DbType.Int16)
                dbGM.Parameters.Add("@out_DelCount", intDelCount, DbType.Int16)

                dbGM.Parameters("@out_Count").Direction = ParameterDirection.Output
                dbGM.Parameters("@out_Access").Direction = ParameterDirection.Output
                dbGM.Parameters("@out_DelCount").Direction = ParameterDirection.Output
                dbGM.Open()
                dbGM.CommandTimeout = 600
                dbGM.ExecuteNonQuerySP(strProcRequest)

                intReturn = dbGM.Parameters("@out_Count").Value()
                intAccess = dbGM.Parameters("@out_Access").Value()
                intDelCount = dbGM.Parameters("@out_DelCount").Value()

                dtbResult = New DataTable
                dtbResult.Columns.Add("RowCount")
                dtbResult.Columns.Add("AccessID")
                dtbResult.Columns.Add("DelCount")

                drResult = dtbResult.NewRow()
                drResult("RowCount") = intReturn
                drResult("AccessID") = intAccess
                drResult("DelCount") = intDelCount

                dtbResult.Rows.Add(drResult)

                Return dtbResult
            Catch ex As Exception

                dbGM.Close()
                dbGM.Dispose()
            Finally
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try
        End Function
        'deleting the Authorizer from the database
        Public Sub InsertAuthorizers(ByVal strInsAuthXML As String)
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_InsertAuthorizers
            Try
                dbGM.Parameters.Add("@in_InputXML", strInsAuthXML, DbType.String)
                dbGM.Open()
                dbGM.CommandTimeout = 600
                dbGM.ExecuteNonQuerySP(strProcRequest)
            Finally
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try
        End Sub
        'Insert Authorizer and replace delegate with logs - Added By Q07233 Gursewak Singh
        Public Function InsertAuthorizerAndReplaceDelegate(ByVal strADSPath As String, ByVal strUserID As String, ByVal intAccessID As Integer, ByVal strGManager As String) As String
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_InsertAuthorizerAndReplaceDelegate
            Dim strPrevDelegate As String = ""

            Try

                dbGM.Parameters.Add("@in_ADSPath", strADSPath, DbType.String)
                dbGM.Parameters.Add("@in_UserID", strUserID, DbType.String)
                dbGM.Parameters.Add("@in_AccessID", intAccessID, DbType.Int16)
                dbGM.Parameters.Add("@in_GManager", strGManager, DbType.String)
                dbGM.Open()
                Dim myTrans As IDbTransaction = dbGM.BeginTransaction()
                dbGM.CommandTimeout = 600
                Try
                    strPrevDelegate = dbGM.ExecuteScalarSP(strProcRequest).ToString()
                    myTrans.Commit()
                Catch ex As Exception
                    myTrans.Rollback()
                    Throw
                End Try
            Catch ex As Exception
                Throw
            Finally
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try
            Return strPrevDelegate
        End Function


        'this method will accept a XML string and return the list of users
        'and for how may groups the user is owner/authorisor/delegate
        Public Sub CheckUserStatus(ByVal strInsAuthXML As String)
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_CheckUserStatus
            Try
                dbGM.Parameters.Add("@in_InputXML", strInsAuthXML, DbType.String)
                dbGM.Open()
                dbGM.CommandTimeout = 600
                m_dtbUserGroupStatus = dbGM.ExecuteQuerySP(strProcRequest, "UserStatus")
            Finally
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try
        End Sub
#End Region

#Region "Methods Used In ReassignOwner"

        'Insert the request in the database
        Public Function InsertRequest(ByVal strADSPathXml As String, ByVal strNewOwner As String)
            Dim strProcRequest As String = Constants.sp_ReassignRequestInsert
            'Dim strCreatedBy As String = "E02238"
            Dim strCreatedBy As String
            Dim intCheck As Integer
            Dim dbGMRequest As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            strCreatedBy = HttpContext.Current.Session("ManagerID")
            Try
                With dbGMRequest
                    .Open()
                    .CommandTimeout = 600
                    .Parameters.Add("@strADSpathsXml", strADSPathXml, DbType.String)
                    .Parameters.Add("@strUserName", strNewOwner, DbType.String)
                    .Parameters.Add("@strCreatedBy", strCreatedBy, DbType.String)

                End With
                intCheck = dbGMRequest.ExecuteScalarSP(strProcRequest)
                Return intCheck

            Catch ex As Exception
                Throw ex
            Finally
                dbGMRequest.Close()
            End Try
        End Function
#End Region

#Region "Methods Used In OverdueGroups"
        'gets the OverDue groups
        Public Shared Function GetOverdueGroups(ByVal strSql As String, ByVal FromReviewDate As Date, ByVal ToReviewDate As Date) As DataTable
            Dim dtbTemp As DataTable
            Try

                'Call the corresponding method to get the and store it in session
                If System.Web.HttpContext.Current.Session(Constants.OVERDUE_GROUPS_SESSION) Is Nothing Then
                    'Create a new session object "OverdueGroups"
                    'Dim oAD As New AD
                    dtbTemp = GetAllOverdueGroups(strSql)
                    'Dim dtbGetDisplayName As DataTable = GetAllOverdueGroups(strSql)
                    'Dim dtbCopyOverdueReviewGroups As DataTable

                    'Dim iRowIndex As Integer = 0
                    'Dim strUserName, strDisplayName As String

                    'For Each dr As DataRow In dtbGetDisplayName.Rows
                    '    If dr("ClassID") = 5 Then
                    '        'If dr("NotificationPeriod") = 0 Then
                    '        dr.Delete()
                    '        'End If
                    '    End If
                    'Next
                    'dtbGetDisplayName.AcceptChanges()


                    'dtbCopyOverdueReviewGroups = dtbGetDisplayName.Copy()

                    ''For Each drCopy As DataRow In dtbCopyOverdueReviewGroups.Rows
                    ''    For Each dr As DataRow In dtbGetDisplayName.Rows
                    ''        If dr("GroupName") = drCopy("GroupName") Then
                    ''            If Convert.ToDateTime(dr("DateReviewed")) < Convert.ToDateTime(drCopy("DateReviewed")) Then
                    ''                dr.Delete()
                    ''            End If
                    ''        End If
                    ''    Next
                    ''    dtbGetDisplayName.AcceptChanges()
                    ''Next
                    'For Each drCopy As DataRow In dtbGetDisplayName.Rows
                    '    If dtbGetDisplayName.Select("GroupName = '" & Convert.ToString(drCopy("GroupName")) & "' AND  DateReviewed > '" & Convert.ToDateTime(drCopy("DateReviewed")) & "'", "DateReviewed").Length > 0 Then
                    '        drCopy.Delete()
                    '    End If
                    'Next
                    'dtbGetDisplayName.AcceptChanges()

                    ''Added By Subhajit ---- To remove Date Reviwed Criterion
                    'If Not FromReviewDate = Date.MinValue Then
                    '    For Each dr As DataRow In dtbGetDisplayName.Rows
                    '        If dr("DateReviewed") <= FromReviewDate Then
                    '            dr.Delete()
                    '        End If
                    '    Next
                    '    dtbGetDisplayName.AcceptChanges()
                    'End If

                    'If Not ToReviewDate = Date.MinValue Then
                    '    For Each dr As DataRow In dtbGetDisplayName.Rows
                    '        If dr("DateReviewed") >= ToReviewDate Then
                    '            dr.Delete()
                    '        End If
                    '    Next
                    '    dtbGetDisplayName.AcceptChanges()
                    'End If



                    ''Dim iRowCount As Integer = dtbGetDisplayName.Rows.Count
                    'dtbTemp = dtbGetDisplayName.Copy()
                    ''For iRowIndex = 0 To iRowCount - 1
                    ''    strUserName = dtbGetDisplayName.Rows(iRowIndex)("UserName")
                    ''    strDisplayName = oAD.GetDisplayName(strUserName)
                    ''    dtbTemp.Rows(iRowIndex)("DisplayName") = strDisplayName
                    ''Next
                    System.Web.HttpContext.Current.Session(Constants.OVERDUE_GROUPS_VIEW_SESSION) = dtbTemp.DefaultView
                End If
                'Return the datatable containing the overdue groups

            Catch ex As Exception
                EventLog.WriteEntry("GroupManager", ex.Message)
                Throw ex
            End Try
            Return dtbTemp
        End Function

        'This gets all the groups that are overdue 
        Private Shared Function GetAllOverdueGroups(ByVal strSql As String) As DataTable
            Dim dbGME As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            'Dim strProcRequest As String = Constants.sp_SelectAllOverdueGroups
            Dim dtbOverdueReviewGroups As DataTable
            Try
                dtbOverdueReviewGroups = New DataTable(Constants.DTB_OVERDUE_GROUPS)
                dbGME.Open()
                dbGME.CommandTimeout = 600
                dbGME.ExecuteQuery(strSql, dtbOverdueReviewGroups)
                'dbGME.ExecuteQuerySP(strProcRequest, dtbOverdueReviewGroups)

                Return dtbOverdueReviewGroups
            Finally
                dbGME.Close()
            End Try
        End Function
        'This gets count of all overdue group
        Public Shared Function GetAllOverdueGroupsCount(ByVal strSql As String) As Integer
            Dim dbGME As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            'Dim strProcRequest As String = Constants.sp_SelectAllOverdueGroups
            Try
                dbGME.Open()
                dbGME.CommandTimeout = 600
                Dim iCount As Int32 = dbGME.ExecuteScalar(strSql)
                Return iCount
            Finally
                dbGME.Close()
            End Try
        End Function
#End Region

#Region "Methods Used In OwnerRequesrApproveReject"

        'select the requests from the database
        Public Sub SelectRequest(ByVal strUserName As String)
            Dim strProcRequest As String = Constants.sp_ReassignRequestSelect

            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Try

                dbGM.Parameters.Add("@strUserName", strUserName, DbType.String)
                dbGM.Open()
                dbGM.CommandTimeout = 600
                m_dtSelectRequest = dbGM.ExecuteQuerySP(strProcRequest, Constants.DTB_REASSIGN_REQUEST)
                m_dtSelectRequest.Columns.Add("CHKLFLAG")
            Catch ex As Exception
                Throw ex
            Finally
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try
        End Sub
        ' function to Update the OwnerName in the database and delete the request in the table
        Public Sub UpdateRequest(ByVal strADSPathXml As String, ByVal strFlag As Char)
            Dim Constants As New Constants
            Dim strProcRequest As String = Constants.sp_ReassignRequestUpdate
            Dim dbGMRequest As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strUserName As String = HttpContext.Current.Session("ManagerID")
            Try
                With dbGMRequest
                    .Open()
                    .CommandTimeout = 600
                    .Parameters.Add("@strADSPathXml", strADSPathXml, DbType.String)
                    .Parameters.Add("@strUserName", strUserName, DbType.String)
                    .Parameters.Add("@strFlag", strFlag, DbType.String)
                    .ExecuteNonQuerySP(strProcRequest)
                End With
            Catch ex As Exception
                Throw ex
            Finally
                dbGMRequest.Close()
            End Try
        End Sub
        ' function to Check whether the ownership is assigned already to another user or not
        Public Function CheckOwnerReassign(ByVal strADSPathXml As String) As Int16
            Dim Constants As New Constants
            Dim intCheck As Int16
            Dim strProcRequest As String = Constants.sp_CheckOwnerReassign
            'Dim strUserName As String = "Q00214"
            Dim dbGMRequest As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            'strCreatedBy = HttpContext.Current.Session("UserName")
            Dim strUserName As String = HttpContext.Current.Session("ManagerID")
            Try
                With dbGMRequest
                    .Open()
                    .CommandTimeout = 600
                    .Parameters.Add("@strADSPathXml", strADSPathXml, DbType.String)
                    .Parameters.Add("@strUserName", strUserName, DbType.String)
                End With
                intCheck = dbGMRequest.ExecuteScalarSP(strProcRequest)
                Return intCheck
            Catch ex As Exception
                Throw ex
            Finally
                dbGMRequest.Close()
            End Try
        End Function
#End Region

#Region "Common Methods"

        'Private constructor.
        Private Sub GroupManager()

        End Sub

        'Method : GetCurrentSingleton
        Public Shared Function GetCurrentSingleton() As GroupManager
            If Not System.Web.HttpContext.Current.Session("GroupManager") Is Nothing Then
                Return CType(System.Web.HttpContext.Current.Session(Constants.GROUP__MANAGER_SESSION), GroupManager)
            Else
                Dim oGroupManager = New GroupManager
                System.Web.HttpContext.Current.Session(Constants.GROUP__MANAGER_SESSION) = oGroupManager
                Return oGroupManager
            End If
        End Function
        'Gets the ClassificationCodes
        Public Sub GetClassificationCodes()

            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_SelectGroupClass
            Try
                dbGM.Open()
                dbGM.CommandTimeout = 600
                m_dtbClassificationCodes = dbGM.ExecuteQuerySP(strProcRequest, "ClassificationCodes")
            Finally
                If Not m_dtbClassificationCodes Is Nothing Then
                    m_dtbClassificationCodes.Dispose()
                End If
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try

        End Sub
        'UserName is reterived from the database
        Public Sub GetUserName(ByVal strADSPath As String)
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_SelectUserName
            Try
                dbGM.Parameters.Add("@in_ADSPath", strADSPath, DbType.String)
                dbGM.Open()
                dbGM.CommandTimeout = 600
                m_dtbUserName = dbGM.ExecuteQuerySP(strProcRequest, "UserName")
            Finally
                If Not m_dtbUserName Is Nothing Then
                    m_dtbUserName.Dispose()
                End If
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try

        End Sub

        Public Shared Function CheckUserInRole(ByVal strRoleName As String) As Boolean
            Dim bIsValidUser As Boolean = System.Web.HttpContext.Current.User.IsInRole(Constants.DOMAIN_NAME & "\\" & strRoleName)
            Return bIsValidUser
        End Function

#End Region

#Region "Methods used in UserControls"
        'function to find the user from the Active Directory
        Public Function FindUsers(ByVal strUserDetails As String, ByVal bUserID As Boolean) As DataSet
            Dim dtbUsers As DataTable
            Dim dsUsers As DataSet


            Dim strLdap As String
            strLdap = "LDAP://OU=Accounts,DC=kcc,DC=com"

            Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLdap)
            Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
            Dim objSearchRes As System.DirectoryServices.SearchResult

            Try
                objSearcher.Filter = ("(objectClass=user)")

                If bUserID Then
                    If Not strUserDetails = "" Then
                        objSearcher.Filter = ("(sAMAccountName=" & strUserDetails & ")")
                    End If
                Else
                    If Not strUserDetails = "" Then
                        Dim strUserNames As String()
                        strUserNames = strUserDetails.Split(",")
                        If strUserNames.Length > 1 Then
                            objSearcher.Filter = ("(sn=" & strUserNames(0).ToString() & ")")
                            objSearcher.Filter = ("(givenname=" & strUserNames(1).ToString() & ")")
                        Else
                            objSearcher.Filter = ("(sn=" & strUserNames(0).ToString() & ")")
                        End If
                    End If
                End If

                dtbUsers = New DataTable("DataTableUsers")
                dtbUsers.Columns.Add("UserID")
                dtbUsers.Columns.Add("UserName")

                Dim dr As DataRow
                If objSearcher.FindAll.Count > 0 Then
                    For Each objSearchRes In objSearcher.FindAll
                        dr = dtbUsers.NewRow()
                        dr("UserID") = objSearchRes.GetDirectoryEntry.Properties.Item("sAMAccountName").Value
                        dr("UserName") = objSearchRes.GetDirectoryEntry.Properties.Item("Description").Value
                        dtbUsers.Rows.Add(dr)
                    Next
                End If
                dsUsers = New DataSet
                dsUsers.Tables.Add(dtbUsers)
                dsUsers.AcceptChanges()
                Return dsUsers
            Catch ex As Exception
                If Not dtbUsers Is Nothing Then
                    dtbUsers.Dispose()
                End If
            Finally
                If Not objDirEnt Is Nothing Then
                    objDirEnt.Dispose()
                End If

                If Not objSearcher Is Nothing Then
                    objSearcher.Dispose()
                End If
            End Try
        End Function

#End Region

#Region "Function used in asp pages"
        Public Function ListMgrsForGrp(ByVal strGroupADSPath As String) As DataTable
            Try
                Dim dtbGroupManagers As DataTable = GetGroupManagers(strGroupADSPath)
                Dim oAD As New AD
                Dim strUserName As String
                Dim strDisplayName As String
                Dim iRowIndex As Integer
                Dim dtbRowcount As Integer = dtbGroupManagers.Rows.Count
                dtbGroupManagers.Columns.Add("DisplayName", GetType(String))
                For iRowIndex = 0 To dtbRowcount - 1
                    Try
                        strUserName = dtbGroupManagers.Rows(iRowIndex)("UserName")
                        strDisplayName = oAD.GetDisplayName(strUserName)
                        dtbGroupManagers.Rows(iRowIndex)("DisplayName") = strDisplayName
                    Catch ex As Exception
                        dtbGroupManagers.Rows(iRowIndex)("DisplayName") = ""
                    End Try
                    
                    'If Not strDisplayName.Equals(strUserName) Then
                    '    Dim strNames() As String = strDisplayName.Split(",")
                    '    If strNames.Length > 1 Then
                    '        dtbGroupManagers.Rows(iRowIndex)("FirstName") = strNames(1).ToString.Trim
                    '        dtbGroupManagers.Rows(iRowIndex)("LastName") = strNames(0).ToString.Trim
                    '    End If
                    'End If
                Next
                dtbGroupManagers.AcceptChanges()
                Return dtbGroupManagers
            Catch Ex As Exception
                EventLog.WriteEntry("GroupManager", Ex.Message)
                Throw Ex
            End Try

        End Function
        Public Shared Function GetGroupManagers(ByVal ADSPath As String)
            Dim dbGME As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_SelectGroupManagers
            Dim dtbGrpMgrs As New DataTable(Constants.DTB_GROUP_MANAGERS)
            Try
                dbGME.Open()
                dbGME.Parameters.Add("@AdsPath", ADSPath, DbType.String)
                dbGME.ExecuteQuerySP(strProcRequest, dtbGrpMgrs)
                Return dtbGrpMgrs
            Catch Ex As Exception

            Finally
                dbGME.Close()
            End Try
        End Function

        'Used in RemoveAuthorizer page for email notifications - Added by Q07233 Gursewak Singh
        Public Function GetGroupManagersForEmail(ByVal ADSPath As String, ByVal UserName As String) As DataTable
            Dim dbGME As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_SelectGroupManagersForEmails
            Dim dtbGrpMgrs As New DataTable(Constants.DTB_GROUP_MANAGERS)
            Try
                dbGME.Open()
                dbGME.Parameters.Add("@AdsPath", ADSPath, DbType.String)
                dbGME.Parameters.Add("@UserName", UserName, DbType.String)
                dbGME.ExecuteQuerySP(strProcRequest, dtbGrpMgrs)
                Return dtbGrpMgrs
            Catch Ex As Exception

            Finally
                dbGME.Close()
            End Try
        End Function

        Public Shared Function GetGroupInfo(ByVal ADSPath As String)
            Dim dbGME As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = "[dbo].[pc_GMGroup_GetInfo]"
            Dim dtbGrpInfo As New DataTable("GroupInfo")
            Try
                dbGME.Open()
                dbGME.Parameters.Add("@AdsPath", ADSPath, DbType.String)
                dbGME.ExecuteQuerySP(strProcRequest, dtbGrpInfo)
                Return dtbGrpInfo
            Catch Ex As Exception

            Finally
                dbGME.Close()
            End Try
        End Function
#End Region

#Region "Function used for log"
        Public Shared Function LogAuthDelAdd(ByVal dtbLogDetails As DataTable, ByVal strUserId As String)
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_GMLogEntry
            Dim strGroupADsPath As String = ""
            Dim strMemberADsPath As String = ""
            Dim strAccessId As String = ""
            Dim strAction As String = ""
            Dim strCSVDetails As String = ""
            Dim strFullFileName As String = ""
            Dim strLogName As String = ""
            Dim swADLog As StreamWriter
            strLogName = Replace(Date.Now.Date(), "/", ".") & ".csv"

            If Not dtbLogDetails Is Nothing Then
                If dtbLogDetails.Rows.Count > 0 Then
                    For Each drLogDetails As DataRow In dtbLogDetails.Rows
                        strGroupADsPath = drLogDetails("ADSPath")
                        strMemberADsPath = "WinNT://KCUS/" & Convert.ToString(drLogDetails("UserName")).ToUpper()
                        If drLogDetails("AccessID") = 2 Then
                            strAction = "Added Authorizer"
                        End If
                        If drLogDetails("AccessID") = 3 Then
                            strAction = "Added Delegate"
                        End If
                        'strCSVDetails = Date.Now.ToString() & "," & strUserId & "," & strAction & "," & strGroupADsPath & "," & strMemberADsPath
                        'strFullFileName = Path.Combine(ConfigurationSettings.AppSettings("LogPath"), strLogName)
                        'swADLog = New StreamWriter(strFullFileName, True)
                        'swADLog.WriteLine(strCSVDetails)
                        Try
                            dbGM.Parameters.Add("@dt", Date.Now, DbType.DateTime)
                            dbGM.Parameters.Add("@Manager", strUserId, DbType.String)
                            dbGM.Parameters.Add("@act", strAction, DbType.String)
                            dbGM.Parameters.Add("@adspth", strGroupADsPath, DbType.String)
                            dbGM.Parameters.Add("@usr", strMemberADsPath, DbType.String)
                            dbGM.Open()
                            dbGM.CommandTimeout = 600
                            dbGM.ExecuteNonQuerySP(strProcRequest)
                        Catch e As Exception
                            'strCSVDetails = e.ToString()
                            'swADLog.WriteLine(strCSVDetails)
                        Finally
                            If Not dbGM Is Nothing Then
                                dbGM.Close()
                                dbGM.Dispose()
                            End If
                        End Try
                        ' swADLog.Close()
                        'swADLog = Nothing
                    Next
                End If
            End If
        End Function


        Public Shared Function LogAuthDelDel(ByVal dtbLogDetails As DataTable, ByVal strUserId As String)
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_GMLogEntry
            Dim strGroupADsPath As String = ""
            Dim strMemberADsPath As String = ""
            Dim strAccessId As String = ""
            Dim strAction As String = ""
            Dim strCSVDetails As String = ""
            Dim strFullFileName As String = ""
            Dim strLogName As String = ""
            Dim swADLog As StreamWriter
            strLogName = Replace(Date.Now.Date(), "/", ".") & ".csv"

            If Not dtbLogDetails Is Nothing Then
                If dtbLogDetails.Rows.Count > 0 Then
                    For Each drLogDetails As DataRow In dtbLogDetails.Rows
                        strGroupADsPath = drLogDetails("ADSPath")
                        strMemberADsPath = "WinNT://KCUS/" & Convert.ToString(drLogDetails("UserName")).ToUpper()
                        If drLogDetails("AccessID") = 2 Then
                            strAction = "Removed Authorizer"
                        End If
                        If drLogDetails("AccessID") = 3 Then
                            strAction = "Removed Delegate"
                        End If
                        'strCSVDetails = Date.Now.ToString() & "," & strUserId & "," & strAction & "," & strGroupADsPath & "," & strMemberADsPath
                        'strFullFileName = Path.Combine(ConfigurationSettings.AppSettings("LogPath"), strLogName)
                        'swADLog = New StreamWriter(strFullFileName, True)
                        'swADLog.WriteLine(strCSVDetails)
                        Try
                            dbGM.Parameters.Add("@dt", Date.Now, DbType.DateTime)
                            dbGM.Parameters.Add("@Manager", strUserId, DbType.String)
                            dbGM.Parameters.Add("@act", strAction, DbType.String)
                            dbGM.Parameters.Add("@adspth", strGroupADsPath, DbType.String)
                            dbGM.Parameters.Add("@usr", strMemberADsPath, DbType.String)
                            'dbGM.Parameters.Add("@usr", dtbLogDetails.Rows.Count.ToString, DbType.String)
                            dbGM.Open()
                            dbGM.CommandTimeout = 600
                            dbGM.ExecuteNonQuerySP(strProcRequest)
                        Catch e As Exception
                            'strCSVDetails = e.ToString()
                            'swADLog.WriteLine(strCSVDetails)
                        Finally
                            If Not dbGM Is Nothing Then
                                dbGM.Close()
                                dbGM.Dispose()
                                dbGM = DbFactory.CreateDatabase(Constants.SQLSERVER)
                            End If
                        End Try
                        'swADLog.Close()
                        'swADLog = Nothing
                    Next
                End If
            End If
        End Function
        Public Shared Function LogReassignOwner(ByVal strReassignedFrom As String, ByVal strGroupADsPath As String, ByVal strFlag As Char)
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_GMLogEntry
            Dim strAction As String = ""
            Dim strCSVDetails As String = ""
            Dim strFullFileName As String = ""
            Dim strLogName As String = ""
            Dim strReassignedTo As String = HttpContext.Current.Session("ManagerID")
            Dim swADLog As StreamWriter
            strLogName = Replace(Date.Now.Date(), "/", ".") & ".csv"
            strReassignedTo = "WinNT://KCUS/" & Convert.ToString(strReassignedTo).ToUpper()
            If strFlag = "A" Then
                strAction = "Approved Reassign Ownership"
            ElseIf strFlag = "R" Then
                strAction = "Rejected Reassign Ownership"
            ElseIf strFlag = "S" Then
                strAction = "Rejected Reassign Ownership - Requester and New Owner are same"
            ElseIf strFlag = "D" Then
                strAction = "Rejected Reassign Ownership - Requester and Current Owner are different"
            End If
            'strCSVDetails = Date.Now.ToString() & "," & strReassignedFrom & "," & strAction & "," & strGroupADsPath & "," & strReassignedTo
            'strFullFileName = Path.Combine(ConfigurationSettings.AppSettings("LogPath"), strLogName)
            'swADLog = New StreamWriter(strFullFileName, True)
            'swADLog.WriteLine(strCSVDetails)
            Try
                dbGM.Parameters.Add("@dt", Date.Now, DbType.DateTime)
                dbGM.Parameters.Add("@Manager", strReassignedFrom, DbType.String)
                dbGM.Parameters.Add("@act", strAction, DbType.String)
                dbGM.Parameters.Add("@adspth", strGroupADsPath, DbType.String)
                dbGM.Parameters.Add("@usr", strReassignedTo, DbType.String)
                dbGM.Open()
                dbGM.CommandTimeout = 600
                dbGM.ExecuteNonQuerySP(strProcRequest)
            Catch e As Exception
                'strCSVDetails = e.ToString()
                'swADLog.WriteLine(strCSVDetails)
            Finally
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try
            'swADLog.Close()
            'swADLog = Nothing

        End Function
        'insert the recertifyGroupClass logs in the database
        Public Sub InsertRecertifyGroupClass(ByVal strADSPath As String, ByVal iClassID As Integer, ByVal iOldClassID As Integer, ByVal strChangedUserID As String, ByVal bQ1 As Boolean, ByVal bQ2 As Boolean, ByVal bQ3 As Boolean, ByVal bQ4 As Boolean, ByVal bQ5 As Boolean, ByVal bQ6 As Boolean, ByVal bQ7 As Boolean, ByVal bQ8 As Boolean, ByVal classificationcomment As String) 'Prateek Rc
            Dim dbGM As Database = DbFactory.CreateDatabase(Constants.SQLSERVER)
            Dim strProcRequest As String = Constants.sp_InsertRecertify
            Try
                dbGM.Parameters.Add("@strAdsPath", strADSPath, DbType.String)
                dbGM.Parameters.Add("@strClassID", iClassID, DbType.Int32)
                dbGM.Parameters.Add("@OldClassID", iOldClassID, DbType.Int32)
                dbGM.Parameters.Add("@strBID", strChangedUserID, DbType.String)
                dbGM.Parameters.Add("@Q1", bQ1, DbType.Boolean)
                dbGM.Parameters.Add("@Q2", bQ2, DbType.Boolean)
                dbGM.Parameters.Add("@Q3", bQ3, DbType.Boolean)
                dbGM.Parameters.Add("@Q4", bQ4, DbType.Boolean)
                dbGM.Parameters.Add("@Q5", bQ5, DbType.Boolean)
                dbGM.Parameters.Add("@Q6", bQ6, DbType.Boolean)
                dbGM.Parameters.Add("@Q7", bQ7, DbType.Boolean)
                dbGM.Parameters.Add("@Q8", bQ8, DbType.Boolean)
                dbGM.Parameters.Add("@classificationcomment", classificationcomment, DbType.String) 'Prateek RC 
                dbGM.Open()
                dbGM.CommandTimeout = 600
                dbGM.ExecuteNonQuerySP(strProcRequest)
            Finally
                If Not m_dtbUserGroupDetails Is Nothing Then
                    m_dtbUserGroupDetails.Dispose()
                End If
                If Not dbGM Is Nothing Then
                    dbGM.Close()
                    dbGM.Dispose()
                End If
            End Try
        End Sub
        'BOC Prateek RC
        Function getownercommentdetails(ByVal userid As String, ByVal adspath As String) As DataTable
            Dim query As String
            query = "select A.adspath,A.ownerComments,A.classid,B.accessid from tbgmgroup A left outer join tbgmacl b on a.adspath=b.adspath where b.username=" & "'" & userid & "'" & " and a.adspath=" & "'" & adspath & "'"
            Dim con As SqlConnection
            Dim cmd As SqlCommand
            Dim adp As New SqlDataAdapter
            Dim dt As New DataTable
            con = New SqlConnection(gmConnectionString)
            con.Open()
            cmd = New SqlCommand(query, con)
            adp.SelectCommand = cmd
            adp.Fill(dt)
            cmd = New SqlCommand()
            con.Close()
            con.Dispose()
            Return dt
        End Function

        'EOC Prateek RC
#End Region

    End Class




End Namespace