﻿<%@ Register TagPrefix="uc1" TagName="Menu" Src="~/Library/UserControls/Menu.ascx" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Page Trace="false" MasterPageFile="~/MasterPage.master" Title="Accept Ownership" Language="vb"
    AutoEventWireup="false" Inherits="AccountManager.AcceptOwnership" CodeFile="AcceptOwnership.aspx.vb" %>

<%@ Register Assembly="DevExpress.Web.ASPxGridView.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table>
        <tr>
            <td>
                <asp:Label ID="lblWelcome" runat="server" CssClass="TableHeader">Ownership Change Requests</asp:Label><br />
                <br />
                <asp:Label ID="lblMessage" runat="server" CssClass="LabelRed"></asp:Label><br />
                <asp:Panel ID="pnlRequests" runat="server">
                <table>
                    <tr>
                        <td>
                            <table class="DisplayTables">
                                <tbody>
                                    <tr>
                                        <td>
                                            Ownership Change Request</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <b>Accept Change</b> Step 1: Highlight the accounts that you accept ownership for.<br />
                                            Step 2: Click the Accept button.<br />
                                            Note: Once the Accept button is clicked you are now the owner for all highlighted
                                            accounts. Click <a href="/AccountManager/MyUsers.aspx" target="_self">here</a> to
                                            view all your accounts.<br />
                                            <b>Reject Change</b> Step 1: Highlight the accounts that you do not want to own.<br />
                                            Step 2: Enter comments why you should not own the account(s).<br />
                                            Step 3: Click the Reject button.<br />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            Enter Comments:<span class="ValidationHeader">*</span> (required only for Reject)<asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtComments" Display="None" ErrorMessage="Please enter comments." SetFocusOnError="True" ValidationGroup="RejectOwner"></asp:RequiredFieldValidator><br />
                                            <asp:TextBox ID="txtComments" runat="server" TextMode="MultiLine" MaxLength="350" Rows="6" Columns="40"></asp:TextBox></td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <table>
                                                <tr>
                                                    <td style="width: 100px; height: 24px">
                                                        <dx:aspxbutton id="cmdUpdate" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                            csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                            text="Accept"></dx:aspxbutton>
                                                    </td>
                                                    <td style="width: 100px; height: 24px">
                                                        <dx:aspxbutton id="cmdDelete" runat="server" ValidationGroup="RejectOwner" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                            csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                            text="Reject"></dx:aspxbutton>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <asp:ValidationSummary ID="ValidationSummary1" ValidationGroup="RejectOwner" runat="server" />
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td>
                                            &nbsp;<dx:aspxgridview id="grdRequests" runat="server" autogeneratecolumns="False"
                                cssfilepath="~/App_Themes/PlasticBlue/{0}/styles.css" csspostfix="PlasticBlue"
                                keyfieldname="RequestID" previewfieldname="Comments" width="793px">
<Styles CssPostfix="PlasticBlue" CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css">
<Header SortingImageSpacing="10px" ImageSpacing="10px"></Header>
</Styles>

<SettingsPager ShowDefaultImages="False">
<AllButton Text="All"></AllButton>

<NextPageButton Text="Next &gt;"></NextPageButton>

<PrevPageButton Text="&lt; Prev"></PrevPageButton>
</SettingsPager>

<ImagesFilterControl>
<LoadingPanel Url="~/App_Themes/PlasticBlue/Editors/Loading.gif"></LoadingPanel>
</ImagesFilterControl>

<Images SpriteCssFilePath="~/App_Themes/PlasticBlue/{0}/sprite.css">
<LoadingPanelOnStatusBar Url="~/App_Themes/PlasticBlue/GridView/gvLoadingOnStatusBar.gif"></LoadingPanelOnStatusBar>

<LoadingPanel Url="~/App_Themes/PlasticBlue/GridView/Loading.gif"></LoadingPanel>
</Images>
<Columns>
<dx:GridViewCommandColumn ShowSelectCheckbox="True" VisibleIndex="0"></dx:GridViewCommandColumn>
<dx:GridViewDataTextColumn FieldName="AccountID" Caption="Account ID" VisibleIndex="1"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="Status" Caption="Status" VisibleIndex="2"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="RequestType" Caption="Request Type" VisibleIndex="3"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="DateSubmitted" Caption="Date Submitted" VisibleIndex="4"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="OldOwnerDN" Caption="Current Owner" VisibleIndex="5"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="NewOwnerDN" Caption="New Owner" VisibleIndex="6"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="UserSubmitted" Caption="Submitted By" VisibleIndex="7"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="RequestID" Visible="False" VisibleIndex="8"></dx:GridViewDataTextColumn>
    <dx:GridViewDataTextColumn FieldName="NewOwner" Visible="False" VisibleIndex="9"></dx:GridViewDataTextColumn>
    <dx:GridViewDataTextColumn FieldName="OldOwner" Visible="False" VisibleIndex="10"></dx:GridViewDataTextColumn>
</Columns>

<Settings ShowPreview="True"></Settings>

<StylesEditors>
<CalendarHeader Spacing="11px"></CalendarHeader>

<ProgressBar Height="25px"></ProgressBar>
</StylesEditors>
</dx:aspxgridview></td>
                    </tr>
                </table>
                    </asp:Panel>
            </td>
        </tr>
    </table>
</asp:Content>
