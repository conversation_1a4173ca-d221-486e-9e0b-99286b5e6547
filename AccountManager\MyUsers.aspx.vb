Imports DevExpress.Web.ASPxGridView
Imports DevExpress.Web.ASPxEditors
Imports System.Data
Imports System.DirectoryServices

Namespace AccountManager

    Partial Class MyUsers
        Inherits System.Web.UI.Page

#Region "Variables"

        Protected WithEvents cmdChangePwd As System.Web.UI.WebControls.Button
        Protected WithEvents cmdVerifyOwner As System.Web.UI.WebControls.Button
        Protected WithEvents lblChangePwd As System.Web.UI.WebControls.Label
        Protected WithEvents txtOldPwd As System.Web.UI.WebControls.TextBox
        Protected WithEvents txtNewPd As System.Web.UI.WebControls.TextBox
        Protected WithEvents txtVerifyNew As System.Web.UI.WebControls.TextBox
        Protected WithEvents txtNewPwdComments As System.Web.UI.WebControls.TextBox
        Protected WithEvents cmdUpdPwd As System.Web.UI.WebControls.But<PERSON>
        Protected WithEvents pChangePassword As System.Web.UI.WebControls.Panel
        Protected WithEvents txtNewPwd As System.Web.UI.WebControls.TextBox
        Protected WithEvents grdRequests As DevExpress.Web.ASPxGrid.ASPxGrid
        Protected WithEvents xAccount As DevExpress.Web.ASPxDataControls.ASPxLabel
        Protected WithEvents rdoVerifyOwner As System.Web.UI.WebControls.RadioButton
        Protected WithEvents lblVerifyOwner As System.Web.UI.WebControls.Label
        Protected WithEvents pVerifyOwner As System.Web.UI.WebControls.Panel
        Protected WithEvents Aspxlabel1 As DevExpress.Web.ASPxDataControls.ASPxLabel
        Protected WithEvents testtxbox As DevExpress.Web.ASPxDataControls.ASPxTextBox
        Protected WithEvents ASPxTextBox2 As DevExpress.Web.ASPxDataControls.ASPxTextBox
        Protected WithEvents txtDescription As DevExpress.Web.ASPxDataControls.ASPxTextBox
        Protected WithEvents lblApp As DevExpress.Web.ASPxDataControls.ASPxLabel
#End Region

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub




        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()

        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

            If Not Page.IsPostBack Then
                Dim hlpFill As New Helper
                hlpFill.FillAccessDropDown(Me.ddlFAccess, True)

                hlpFill.AddBlank(ddlFAccess)
                hlpFill.AddBlank(ddlFOwner)
                GetAccountsForUser(True)
		If [Global].GblIsMOSAIC Then
                    rdoPwdExempt.Visible = True
		else
		    rdoPwdExempt.Visible = False
                End If
                Try
                    gvPasswordPolicy.DataSource = DataAccess.GetPwdPolicyMessage
                    gvPasswordPolicy.DataBind()
                Catch ex As Exception
                    gvPasswordPolicy.Visible = False
                End Try


            Else
                GetAccountsForUser(False)

            End If


        End Sub
'change By jasmine --show User Display name from AD . this function makes AD connection and return display name :- CHG0983304
        Function getUserDisplayName(ByVal strUserID As String) As String
            Dim dEntry As DirectoryEntry = New DirectoryEntry("LDAP://DC=KCC,DC=COM")
            Dim dSearcher As DirectorySearcher = New DirectorySearcher(dEntry)
            Dim result As SearchResult

            dSearcher.ReferralChasing = &H40
            dSearcher.PageSize = 2000
            dSearcher.SearchScope = 2
            Dim retDisplayName As String = ""
            dSearcher.Filter = "(&(objectClass=user)(cn= " & strUserID & "))"
            dSearcher.PropertiesToLoad.Add("displayName")
            Try
                For Each result In dSearcher.FindAll
                    If Not result Is Nothing Then
                        With result.GetDirectoryEntry.Properties
                            Dim strObjLDAP As String = result.GetDirectoryEntry.Path
                            retDisplayName = .Item("displayName").Value
                        End With
                    End If
                Next
            Catch ex As Exception
                Console.WriteLine("Error occurred while getting the User details: " & ex.Message)
            End Try
            Return retDisplayName
        End Function
        'end change jasmine

        Function GetAccountsForUser(ByVal blnForce As Boolean, Optional ByVal strWhere As String = "") As ArrayList
	
	     Dim strUserID As String
    
	     strUserID = Left(System.Web.HttpContext.Current.Request.ServerVariables("LOGON_USER").Split("\")(1), 7).ToString
        
 

            Dim usrcur As UserInfo

            If Trim(strWhere) <> "" Or Me.ddlFOwner.SelectedValue <> "" Then

                usrcur = New UserInfo
                usrcur.GetAccountsForUser(usrcur.SQLGetAccountsForUser(strWhere, Me.ddlFOwner.SelectedValue))
            Else
                usrcur = UserInfo.GetSessionUserList(True)
            End If

            'lblWelcome.Text = "Welcome: " & usrcur.FirstName & " " & usrcur.LastName & " (" & usrcur.GetUserID & ")"
	    lblWelcome.Text = "Welcome: " & usrcur.FirstName & " " & usrcur.LastName & " (" & strUserID  & ")"

            LoadData(usrcur.AccountsArray, usrcur, strWhere)

            Return usrcur.AccountsArray
        End Function

        Sub LoadData(ByVal arList As ArrayList, ByVal usrCur As UserInfo, ByVal strWhere As String)

            Me.grdUsers.Visible = True
            Dim dbGet As New DataAccess

            If arList.Count < 1 Then
                If strWhere <> "" Then
                    lblMessage.Text = "No records found with your filters."
                    Me.grdUsers.Visible = False
                Else
                    lblMessage.Text = "You are not the owner, delegate or backup for any accounts."
                    pUsers.Visible = False

                End If

            Else

                LoadOwnerDropDown(arList)

                grdUsers.KeyFieldName = "AccountID"
                grdUsers.DataSource = usrCur.AccountsOwnedByUser
                grdUsers.DataBind()
                grdUsers.FocusedRowIndex = -1
                'grdUsers.ExpandAllRows()

                'If grdUsers < 40 Then
                'grdUsers.PageSize = grdUsers.Rows.Count
                'Else
                'grdUsers.PageSize = 40
                ' End If

            End If

        End Sub


#Region "Continue Functions"

        Function FindChecked() As String

            FindChecked = ""

            If Not Me.chkUpdAll.Checked Then

                If lstChecked.Items.Count = 1 Then
                    FindChecked = lstChecked.Items.Item(0).Text
                Else
                    Dim intIndex As Integer

                    For intIndex = 0 To (lstChecked.Items.Count) - 1
                        FindChecked = lstChecked.Items.Item(intIndex).Text & ";" & FindChecked
                    Next

                End If

            Else

                Dim alList As ArrayList
                Dim i As Integer = 0

                alList = ApplyFilter()

                While i < alList.Count
                    FindChecked = alList(i) & ";" & FindChecked
                    i = i + 1
                End While
            End If

            Return FindChecked

        End Function

        Sub SetSessionAccounts(ByVal strType As String)
            Session("NotOwnDel") = ""
            Session("Accounts") = ""

            Dim i As Integer
            Dim strValues As String()
            Dim hlpCheck As New Helper
            Dim usrCur As UserInfo = UserInfo.GetSessionUserList(False)
            Dim blnAccess As Boolean
            strValues = Split(FindChecked, ";")

            For i = 0 To strValues.Length - 1

                Select Case UCase(strType)

                    Case "OWNER"
                        blnAccess = hlpCheck.IsOwner(strValues(i), usrCur.GetUserID)
                    Case "OWNERDELEGATE"
                        blnAccess = hlpCheck.IsOwnerOrDelegate(strValues(i), usrCur.GetUserID)
                    Case Else
                        blnAccess = True

                End Select

                If blnAccess Then
                    'If Not Session("Accounts").ToString.IndexOf(strValues(i)) > -1 Then
                    If Trim(strValues(i)) <> "" Then
                        Session("Accounts") = strValues(i) & ";" & Session("Accounts")
                    End If
                    'End If
                Else
                    If Trim(strValues(i)) <> "" Then
                        Session("NotOwnDel") = strValues(i) & ", " & Session("NotOwnDel")
                    End If
                End If

            Next i
        End Sub

        Sub ChangeOwners()
            Dim strPage As String

            SetSessionAccounts("OWNER")
            strPage = "/AccountManager/Views/ChangeOwner.aspx"
            'Commented below conditions to run ownership change for mutliple accounts
            'If lstChecked.Items.Count > 1 Then
            '    lblMessage.Text = "You can only change owner for one account.  Please click on a single account, then click continue."
            'ElseIf Me.chkUpdAll.Checked Then
            '    lblMessage.Text = "You can only change the owner for one account.  Uncheck the select all option and click on a single account, then click continue."
            'Else
            If Trim(Session("Accounts")) <> "" Then
                Response.Redirect(strPage)
            Else
                If Trim(Session("NotOwnDel")) <> "" Then
                    Me.lblMessage.Text = "You need to select at least an account where you are listed as the owner."
                Else
                    Me.lblMessage.Text = "You need to select at least one account before clicking continue."
                End If

            End If
            'End If

        End Sub

        Sub ChangePwd()

            If lstChecked.Items.Count > 1 Then
                lblMessage.Text = "You can only change the password for one account.  Please click on a single account, then click continue."
            ElseIf Me.chkUpdAll.Checked Then
                lblMessage.Text = "You can only change the password for one account.  Uncheck the select all option and click on a single account, then click continue."
            Else

                Dim strValues As String
                strValues = FindChecked()

                If strValues <> "" Then
                    If strValues.ToUpper().StartsWith("MT") Then
                        UserMsgBox([Global].GblPasswordChangeNotAllowMessage)
                    Else
                        Response.Redirect("/AccountManager/Views/ChangePwd.aspx?AccountID=" & strValues)
                    End If
                Else
                    Me.lblMessage.Text = "You need to select an account before clicking continue."
                End If

            End If

        End Sub

        Sub ViewLocalGroups()

            SetSessionAccounts("ANYONE")

            If Trim(Session("Accounts")) <> "" Then
                Response.Redirect("/AccountManager/Views/ViewLocalGrps.aspx")
            Else
                Me.lblMessage.Text = "You need to select at least one account before clicking continue."
            End If

        End Sub

        Sub ViewServices()

            SetSessionAccounts("ANYONE")

            If Trim(Session("Accounts")) <> "" Then
                Response.Redirect("/AccountManager/Views/ViewServices.aspx")
            Else
                Me.lblMessage.Text = "You need to select at least one account before clicking continue."
            End If

        End Sub

        Sub UpdateBackup()

            SetSessionAccounts("OWNERDELEGATE")
            If lstChecked.Items.Count > 1 Then
                lblMessage.Text = "You can only update for one account.  Please click on a single account, then click continue."
            ElseIf Me.chkUpdAll.Checked Then
                lblMessage.Text = "You can only update for one account.  Uncheck the select all option and click on a single account, then click continue."
            Else
                If Trim(Session("Accounts")) <> "" Then
                    Response.Redirect("/AccountManager/Views/UpdateBackup.aspx")
                Else
                    If Session("NotOwnDel") <> "" Then
                        UserMsgBox("You selected accounts you are not listed as the owner or delegate.  You will not be able to manage these accounts. " & Session("NotOwnDel"))
                    End If
                    Me.lblMessage.Text = "You need to select accounts where you are listed as an owner or delegate to add backups."
                End If
            End If

        End Sub

        Sub DeleteAccount()

            'If lstChecked.Items.Count > 1 Then
            '    lblMessage.Text = "You can only delete one account at a time.  Please click on a single account, then click continue."
            'ElseIf Me.chkUpdAll.Checked Then
            '    lblMessage.Text = "You can only delete one account at a time.  Uncheck the select all option and click on a single account, then click continue."
            'Else

            Dim strValues As String
            strValues = FindChecked()

            If strValues <> "" Then
                Session("DeleteAccounts") = strValues
                'Response.Redirect("/AccountManager/Views/DeleteAccount.aspx?AccountID=" & strValues)
                Response.Redirect("/AccountManager/Views/DeleteAccount.aspx")
            Else
                Me.lblMessage.Text = "You need to select an account before clicking continue."
            End If
            'End If

        End Sub

        Sub ManageAccount()

            If lstChecked.Items.Count > 1 Then
                lblMessage.Text = "You can only manage one account at a time.  Please click on a single account, then click continue."
            ElseIf Me.chkUpdAll.Checked Then
                lblMessage.Text = "You can only manage one account at a time.  Uncheck the select all option and click on a single account, then click continue."
            Else
                Dim strValues As String
                strValues = FindChecked()

                If strValues <> "" Then
                    Response.Redirect("/AccountManager/Views/ManageAccountDetails.aspx?AccountID=" & strValues)
                Else
                    Me.lblMessage.Text = "You need to select an account before clicking continue."
                    'Me.lblMessage.Text = lstChecked.Items(0).ToString
                End If

            End If

        End Sub

        Sub ManageWorkstations()

            If lstChecked.Items.Count > 1 Then
                lblMessage.Text = "You can only manage one account at a time.  Please click on a single account, then click continue."
            ElseIf Me.chkUpdAll.Checked Then
                lblMessage.Text = "You can only manage one account at a time.  Uncheck the select all option and click on a single account, then click continue."
            Else

                Dim strValues As String
                strValues = FindChecked()

                If strValues <> "" Then
                    Response.Redirect("/AccountManager/Views/ManageWorkstations.aspx?AccountID=" & strValues)
                Else
                    Me.lblMessage.Text = "You need to select an account before clicking continue."
                End If

            End If

        End Sub
        Sub PasswordException()
            If lstChecked.Items.Count > 1 Then
                lblMessage.Text = "You can only manage one account at a time.  Please click on a single account, then click continue."
            ElseIf Me.chkUpdAll.Checked Then
                lblMessage.Text = "You can only manage one account at a time.  Uncheck the select all option and click on a single account, then click continue."
            Else

                Dim strValues As String
                strValues = FindChecked()

                If strValues <> "" Then
                    Response.Redirect("/AccountManager/Views/PwdException.aspx?AccountID=" & strValues)
                Else
                    Me.lblMessage.Text = "You need to select an account before clicking continue."
                End If

            End If
        End Sub
        Sub PasswordExempt()
            If lstChecked.Items.Count > 1 Then
                lblMessage.Text = "You can only manage one account at a time.  Please click on a single account, then click continue."
            ElseIf Me.chkUpdAll.Checked Then
                lblMessage.Text = "You can only manage one account at a time.  Uncheck the select all option and click on a single account, then click continue."
            Else
                Dim strValues As String
                strValues = FindChecked()
                If strValues <> "" Then
                    Response.Redirect("/AccountManager/Views/PwdExemptProcess.aspx?AccountID=" & strValues)
                Else
                    Me.lblMessage.Text = "You need to select an account before clicking continue."
                End If
            End If
        End Sub
        Sub UnlockAccount()

            If lstChecked.Items.Count > 1 Then
                lblMessage.Text = "You can only manage one account at a time.  Please click on a single account, then click continue."
            ElseIf Me.chkUpdAll.Checked Then
                lblMessage.Text = "You can only manage one account at a time.  Uncheck the select all option and click on a single account, then click continue."
            Else
                Dim strValues As String
                strValues = FindChecked()

                If strValues <> "" Then
                    Response.Redirect("/AccountManager/Views/UnlockAccount.aspx?AccountID=" & strValues)
                Else
                    Me.lblMessage.Text = "You need to select an account before clicking continue."
                End If


            End If

        End Sub
        Private Sub getSelectedRows()
            Dim List As Object

            List = grdUsers.GetSelectedFieldValues("AccountID")
            For Each Account As Object In List
                lstChecked.Items.Add(Account.ToString)
                'Me.lblMessage.Text = Account.ToString & "," & Me.lblMessage.Text
                ' lblAccountID.Text = Account.ToString
            Next


        End Sub

        Private Sub cmdContinue_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdContinue.Click
            getSelectedRows()
            If rdoNewOwner.Checked Then
                ChangeOwners()
            ElseIf rdoUnlock.Checked Then
                UnlockAccount()
            ElseIf rdoViewAdmins.Checked Then
                ViewLocalGroups()
            ElseIf rdoViewServices.Checked Then
                ViewServices()
            ElseIf rdoUpdBackup.Checked Then
                UpdateBackup()
            ElseIf rdoDelete.Checked Then
                DeleteAccount()
            ElseIf rdoMngDetails.Checked Then
                ManageAccount()
            ElseIf rdoMngWorkStations.Checked Then
                ManageWorkstations()
            ElseIf rdopwdException.Checked Then
                PasswordException()
            ElseIf rdoPwdExempt.Checked Then
                PasswordExempt()
            Else
                ChangePwd()
            End If
            lstChecked.Items.Clear()
        End Sub

#End Region

#Region "Filter Functions"

        Sub LoadOwnerDropDown(ByVal arList As ArrayList)

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader
            Dim arUsers As New ArrayList
            Dim intIndex As Integer = 0

            strSQL = "SELECT Distinct UserName,UserNameDN FROM vUsr WHERE ("

            If arList.Count < 1 Then
                'pOwners.Visible = False
                'grdOwners.Visible = False
            Else
                Do While intIndex < arList.Count
                    strSQL = strSQL & " AccountID = '" & arList.Item(intIndex) & "' OR"
                    intIndex = intIndex + 1
                Loop
            End If

            strSQL = strSQL.Remove(strSQL.Length - 2, 2)
            strSQL = strSQL & ") Order By UserNameDN asc "
            srRead = dbGet.GetDataReaderByStringId(strSQL)

            Do While srRead.Read

                Dim intItem As Integer
                Dim strFUserID, strFUser As String

                'strFUserID = srRead.Item("UserName")
                'strFUser = srRead.Item("UserNameDN")
 'change(CHG0983304) by jasmine --UserNameDN: this will call the function getuserdisplayname and pass the userid and function will return displayname from AD and store in variable newusernamedn
                strFUserID = srRead.Item("UserName")
                ' Dim oldusernamedn As String = Row("UserNameDn").ToString()
                Dim userido As String = strFUserID
                Dim newusernamedn As String = getUserDisplayName(userido.Trim())
                'Row("UserNameDN") = newusernamedn
                strFUser = newusernamedn
                'end change by jasmine --UserNameDN

                intItem = ddlFOwner.Items.IndexOf(ddlFOwner.Items.FindByValue(Helper.IsValueNull(strFUserID)))
                If intItem < 0 Then
                    Dim liNew As New ListItem
                    liNew.Text = strFUser & " (" & strFUserID & ")"
                    liNew.Value = strFUserID
                    Me.ddlFOwner.Items.Add(liNew)
                End If

            Loop

            srRead.Close()
            dbGet.CloseConnections()
            dbGet = Nothing
            srRead = Nothing

        End Sub


        Function ApplyFilter() As ArrayList

            Dim strWhere As String = ""
            Dim strNot As String = ""
            Dim usrCur As UserInfo = UserInfo.GetSessionUserList(False)

            'If Me.ddlOption.SelectedValue = 0 Then
            '    strNot = " NOT "
            'End If

            If Trim(Me.txtFAccount.Text) <> "" Then
                strWhere = " AND (" & strNot & " AccountID like '%" & Trim(Me.txtFAccount.Text) & "%') "
            End If
            If Me.ddlFAccess.SelectedValue <> "" Then
                strWhere = " AND (" & strNot & " Access = '" & Me.ddlFAccess.SelectedItem.Text & "') " & strWhere
            End If
            If Me.chkPastDue.Checked Then
                strWhere = " AND (ExpirationDate < GETDATE() )" & strWhere
            End If

            If Trim(Me.txtFilterDesc.Text) <> "" Then
                strWhere = " AND (" & strNot & " Description like '%" & Trim(Me.txtFilterDesc.Text) & "%') " & strWhere
            End If
            If Trim(Me.txtFilterPurpose.Text) <> "" Then
                strWhere = " AND (" & strNot & " Purpose like '%" & Trim(Me.txtFilterPurpose.Text) & "%') " & strWhere
            End If
            If Trim(Me.txtFilterAppName.Text) <> "" Then
                strWhere = " AND (" & strNot & " AppName like '%" & Trim(Me.txtFilterAppName.Text) & "%') " & strWhere
            End If

            Trace.Warn(strWhere)
            Return GetAccountsForUser(False, strWhere)

        End Function

        Protected Sub cmdApplyF_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmdApplyF.Click
            ApplyFilter()
        End Sub

        Protected Sub cmdClearF_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmdClearF.Click
            Me.txtFAccount.Text = ""
            Me.ddlFOwner.SelectedIndex = 0
            Me.ddlFAccess.SelectedIndex = 0
            Me.txtFilterAppName.Text = ""
            Me.txtFilterDesc.Text = ""
            Me.txtFilterPurpose.Text = ""

            GetAccountsForUser(True, "")
        End Sub

#End Region

        Public Sub UserMsgBox(ByVal sMsg As String)

            Dim sb As New StringBuilder()
            Dim oFormObject As System.Web.UI.Control

            sMsg = sMsg.Replace("'", "\'")
            sMsg = sMsg.Replace(Chr(34), "\" & Chr(34))
            sMsg = sMsg.Replace(vbCrLf, "\n")
            sMsg = "<script language=javascript>alert(""" & sMsg & """)</script>"

            sb = New StringBuilder()
            sb.Append(sMsg)

            For Each oFormObject In Me.Controls
                If TypeOf oFormObject Is HtmlForm Then
                    Exit For
                End If
            Next

            ' Add the javascript after the form object so that the 
            ' message doesn't appear on a blank screen.
            oFormObject.Controls.AddAt(oFormObject.Controls.Count, New LiteralControl(sb.ToString()))

        End Sub


        Protected Sub grdUsers_FocusedRowChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles grdUsers.FocusedRowChanged
            'lblAccountID.Text = Date.Now.ToString



        End Sub

        Protected Sub grdUsers_SelectionChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles grdUsers.SelectionChanged
            'lblAccountID.Text = Date.Now.ToString

        End Sub

    End Class

End Namespace


'Sub NewFilter()
'    Dim sbFilter As StringBuilder = New StringBuilder(1000)
'    Dim strNot As String = ""
'    Dim usrCur As UserInfo = UserInfo.GetSessionUserList(False)

'    'sbFilter.Append(usrCur.SQLGetAccountsForUser)            

'    If Trim(Me.txtFAccount.Text) <> "" Then
'        sbFilter.Append(" (" & strNot & " AccountID like '%" & Me.txtFAccount.Text & "%') ")
'    End If

'    If Me.ddlFAccess.SelectedValue <> "" Then
'        If sbFilter.ToString = "" Then
'            sbFilter.Append(" (" & strNot & " Access = '" & Me.ddlFAccess.SelectedItem.Text & "') ")
'        Else
'            sbFilter.Append(" AND (" & strNot & " Access = '" & Me.ddlFAccess.SelectedItem.Text & "') ")
'        End If

'    End If

'    If Me.ddlFOwner.SelectedValue <> "" Then
'        If sbFilter.ToString = "" Then
'            sbFilter.Append(" (" & strNot & " UserName = '" & Me.ddlFOwner.SelectedValue & "') ")
'        Else
'            sbFilter.Append(" AND (" & strNot & " UserName = '" & Me.ddlFOwner.SelectedValue & "') ")
'        End If

'    End If

'    If Me.chkPastDue.Checked Then
'        If sbFilter.ToString = "" Then
'            sbFilter.Append(" (ExpirationDate < GETDATE() )")
'        Else
'            sbFilter.Append(" AND (ExpirationDate < GETDATE() )")
'        End If

'    End If

'    If sbFilter.ToString <> "" Then
'        If sbFilter.ToString = "" Then
'            sbFilter.Append(" (UserName = '" & usrCur.GetUserID & "') ")
'        Else
'            sbFilter.Append(" AND (UserName = '" & usrCur.GetUserID & "') ")
'        End If

'    End If

'    If sbFilter.ToString = "" Then
'        Me.grdUsers.DataSource = usrCur.DistinctAccountsOwnedByUser
'        Me.grdUsers.DataBind()
'    Else
'        Dim dvFilteredView As DataView = usrCur.AccountsOwnedByUser.DefaultView
'        Trace.Warn(sbFilter.ToString)
'        dvFilteredView.RowFilter = sbFilter.ToString()
'        If (dvFilteredView.Count > 0) Then
'            Me.grdUsers.DataSource = dvFilteredView
'            Me.grdUsers.DataBind()
'        Else
'            Me.lblMessage.Text = "Your filter was to restrictive and no results were found."
'        End If
'    End If

'    usrCur = Nothing
'End Sub



