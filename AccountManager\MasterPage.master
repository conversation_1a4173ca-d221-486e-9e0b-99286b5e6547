﻿<%@ Master Language="VB" CodeFile="MasterPage.master.vb" Inherits="MasterPage" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 1.0 Transitional//EN">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta content="Microsoft Visual Studio.NET 7.0" name="GENERATOR" />
    <meta content="Visual Basic 7.0" name="CODE_LANGUAGE" />
    <meta content="JavaScript" name="vs_defaultClientScript" />    
    <meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema" />
    <meta content="http://schemas.microsoft.com/intellisense/nav4-0" name="vs_targetSchema" />
    <script type="text/javascript" src="JScript.js"></script>   
    <script type="text/javascript" src="Grid.js">
    function BackupOwnerExpandAll(sender) {
        ctl00_ContentPlaceHolder1_lgrdOwners.ExpandAllRows = sender.checked
    }
    
    </script>  
      <style type="text/css">
       BODY { background: url(/AccountManager/Library/images/esmbkg.jpg);
              background-position: left top; 
        }
</style>
    <link href="styles.css" type="text/css" rel="stylesheet" />

</head>
<body>
    <form id="fAM" method="post" runat="server">
    <div style="vertical-align: bottom"></div>
        <table>
            <tr>
                <td valign="top" align="left" style="width: 148px" >                
                
<div style="height: 100%; width: 100%; border: none 0;">
    <table cellspacing="0" cellpadding="0" border="0" =style="height: 100%; width: 100%; border: none 0; ">
        <tr>
            <td align="left">
                <asp:Image ID="imgInt" ImageUrl="/AccountManager/Library/Images/internal2.gif" runat="server" ImageAlign="TextTop" />
                <br />
                <br />
                <asp:Image ID="imgWST" ImageUrl="/AccountManager/Library/Images/CSAlogo1.png" Width="140px" runat="server"
                    ImageAlign="TextTop" />
                <br />
                <br />
            </td>
        </tr>
        <tr>
            <td class="FunctionStep">
                Account Manager</td>
        </tr>
        <tr>
            <td align="left">
                <asp:Menu ID="mAM" runat="server" Font-Size="1.0em" DynamicMenuStyle-Font-Bold="true" 
                    DynamicHoverStyle-BackColor="ActiveCaption" StaticMenuItemStyle-VerticalPadding="2"
                    StaticMenuItemStyle-HorizontalPadding="2" BackColor="Transparent" DynamicHorizontalOffset="2"
                    Font-Names="Verdana" ForeColor="white" StaticSubMenuIndent="10px">
                    <Items>
                        <asp:MenuItem Text="Home" NavigateUrl="/AccountManager/Default.aspx" Value="Home"></asp:MenuItem>
                        <asp:MenuItem Text="My Accounts" NavigateUrl="/AccountManager/MyUsers.aspx" Value="My Accounts"></asp:MenuItem>
                        <asp:MenuItem Text="Pending Ownership<br> Changes" NavigateUrl="/AccountManager/Views/AcceptOwnership.aspx"
                            Value="Pending"></asp:MenuItem>
                        <asp:MenuItem Text="Find Owners" NavigateUrl="/AccountManager/Views/FindOwners.aspx" Value="Find Owners">
                        </asp:MenuItem>
                        <asp:MenuItem Text="Questions/Feedback" NavigateUrl="mailto:_Computer Security, Global"
                            Value="Questions/Feedback"></asp:MenuItem>
                    </Items>
                    <StaticMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                    <DynamicHoverStyle BackColor="#284E98" ForeColor="White" />
                    <DynamicMenuStyle BackColor="#B5C7DE" />
                    <StaticSelectedStyle BackColor="#507CD1" />
                    <DynamicSelectedStyle BackColor="#507CD1" />
                    <DynamicMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                    <StaticHoverStyle BackColor="#284E98" ForeColor="White" />
                </asp:Menu>
            </td>
        </tr>
        <tr>
            <td class="FunctionStep">
                <br />
                Documentation</td>
        </tr>
        <tr>
            <td align="left">
                <asp:Menu ID="Menu1" runat="server" Font-Size="1.0em" DynamicMenuStyle-Font-Bold="true"
                    DynamicHoverStyle-BackColor="ActiveCaption" StaticMenuItemStyle-VerticalPadding="2"
                    StaticMenuItemStyle-HorizontalPadding="2" BackColor="Transparent" DynamicHorizontalOffset="2"
                    Font-Names="Verdana" ForeColor="white" StaticSubMenuIndent="10px">
                    <Items>
                        <asp:MenuItem Text="FAQ" NavigateUrl="/AccountManager/Tutorials/FAQ.aspx" Value="FAQ"></asp:MenuItem>
                        <asp:MenuItem Text="QRC" NavigateUrl="https://kcc.service-now.com/kc_sp?sys_kb_id=e1bbadf71ba9a8d021e155b62a4bcbca&id=kb_article_view&sysparm_rank=1&sysparm_tsqueryId=e70ead3f1ba9a8d021e155b62a4bcbf5" target="_blank"
                            Value="QRC"></asp:MenuItem>
                    </Items>
                    <StaticMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                    <DynamicHoverStyle BackColor="#284E98" ForeColor="White" />
                    <DynamicMenuStyle BackColor="#B5C7DE" />
                    <StaticSelectedStyle BackColor="#507CD1" />
                    <DynamicSelectedStyle BackColor="#507CD1" />
                    <DynamicMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                    <StaticHoverStyle BackColor="#284E98" ForeColor="White" />
                </asp:Menu>
            </td>
        </tr>
        <asp:Panel ID="pReports" runat="server" Visible="false">
            <tr>
                <td class="FunctionStep">
                    <br />
                    Reports</td>
            </tr>
            <tr>
                <td align="left">
                    <asp:Menu ID="Menu2" runat="server" Font-Size="1.0em" DynamicMenuStyle-Font-Bold="true"
                        DynamicHoverStyle-BackColor="ActiveCaption" StaticMenuItemStyle-VerticalPadding="2"
                        StaticMenuItemStyle-HorizontalPadding="2" BackColor="Transparent" DynamicHorizontalOffset="2"
                        Font-Names="Verdana" ForeColor="white" StaticSubMenuIndent="10px">
                        <Items>
                            <asp:MenuItem Text="Reports" NavigateUrl="/AccountManager/Reports/Reports.aspx" Value="Reports"></asp:MenuItem>
                            <%--<asp:MenuItem Text="User Stats" NavigateUrl="/AccountManager/Reports/Stats.aspx" Value="Stats"></asp:MenuItem>--%>
                        </Items>
                        <StaticMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                        <DynamicHoverStyle BackColor="#284E98" ForeColor="White" />
                        <DynamicMenuStyle BackColor="#B5C7DE" />
                        <StaticSelectedStyle BackColor="#507CD1" />
                        <DynamicSelectedStyle BackColor="#507CD1" />
                        <DynamicMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                        <StaticHoverStyle BackColor="#284E98" ForeColor="White" />
                    </asp:Menu>
                </td>
            </tr>
        </asp:Panel>
        <asp:Panel ID="pAdmin" runat="server" Visible="false" >
            <tr>
                <td class="FunctionStep">
                    <br />
                    Administration</td>
            </tr>
            <tr>
                <td align="left">
                    <asp:Menu ID="Menu3" runat="server" Font-Size="1.0em" DynamicMenuStyle-Font-Bold="true"
                        DynamicHoverStyle-BackColor="ActiveCaption" StaticMenuItemStyle-VerticalPadding="2"
                        StaticMenuItemStyle-HorizontalPadding="2" BackColor="Transparent" DynamicHorizontalOffset="2"
                        Font-Names="Verdana" ForeColor="white" StaticSubMenuIndent="10px">
                        <Items>
                            <asp:MenuItem Text="Manage Accounts" NavigateUrl="/AccountManager/CS/Manage.aspx" Value="MGACT"></asp:MenuItem>
                            <asp:MenuItem Text="Requests" NavigateUrl="/AccountManager/CS/Requests.aspx" Value="Req"></asp:MenuItem>
                            <asp:MenuItem Text="User Logs" NavigateUrl="/AccountManager/CS/ViewLog.aspx" Value="logs"></asp:MenuItem>
                            <%--<asp:MenuItem Text="Mass Owner Update" NavigateUrl="/AccountManager/CS/MassOwnerUpdate.aspx" Value="mass"></asp:MenuItem>--%>
                        </Items>
                        <StaticMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                        <DynamicHoverStyle BackColor="#284E98" ForeColor="White" />
                        <DynamicMenuStyle BackColor="#B5C7DE" />
                        <StaticSelectedStyle BackColor="#507CD1" />
                        <DynamicSelectedStyle BackColor="#507CD1" />
                        <DynamicMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                        <StaticHoverStyle BackColor="#284E98" ForeColor="White" />
                    </asp:Menu>
                </td>
            </tr>
        </asp:Panel>
    </table>
</div>
                </td>
                <td valign="top">
        <%--      <% Response.WriteFile("~/Library/Visual/Header/topHeader.htm")%>
                 <br />--%>
                    <div>
                        <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                        </asp:ContentPlaceHolder>
                    </div>
                </td>
            </tr>
        </table>
    </form>

</body>
</html>
