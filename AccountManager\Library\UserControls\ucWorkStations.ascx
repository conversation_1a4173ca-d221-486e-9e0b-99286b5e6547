﻿<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Control Language="vb" AutoEventWireup="false" Inherits="AccountManager.ucWorkStations"
    CodeFile="ucWorkStations.ascx.vb" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<table>
    <tr>
        <td>
            <asp:Label ID="lblWelcome" CssClass="TableHeader" runat="server">Add or remove computers this account can log on to</asp:Label><br/>
            <br />
            <asp:Label ID="lblMessage" CssClass="LabelRed" runat="server"></asp:Label><br/>
            <table>
                <tr>
                    <td>
                        <table class="DisplayTables">
                            <tr>
                                <td>
                                    <table>
                                        <tr>
                                            <td align="center" colspan="2">
                                               
                                                    <asp:Label ID="lblNewWorkstation" CssClass="LabelRed" runat="server" Font-Size="12pt"></asp:Label></td>
                                        </tr>
                                        
                                            <tr>
                                                <td colspan="2">
                                                    <asp:Panel ID="pInfo" runat="server">
                                                        <asp:Panel ID="pAdminInstr" runat="server">
                                                            <table>
                                                            <tr>
                                                                <td>
                                                                    <strong>Looking up a Non-User Account</strong></td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    Step 1: Enter valid Non-User ID.</td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    Step 3: Click the Get User button.</td>
                                                            </tr>
                                                                </table>
                                                        </asp:Panel>
                                                        <table>
                                                        <tr>
                                                            <td>
                                                                <strong>Adding a new computer</strong></td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                Step 1: Enter valid workstation/server name (USTCXXX).</td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                Step 2: Enter in comments detailing the change.</td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                Step 3: Click Add button.</td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <strong>Deleting a computer</strong></td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <font color="red">Note: Removing a computer from the non-user account means the non-user account will not have access to that computer anymore.</font></td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                Step 1: Click the computer that you want to delete.</td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                Step 2: Enter in comments detailing the change.</td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                Step 3: Click Delete button.</td>
                                                        </tr>
                                                    </table>
                                                    
                                                   <asp:Panel ID="pVerify" runat="server" CssClass="PanelMessageBox">
                                                    <table style="text-align:center;">
                                                        <tr>
                                                            <td>
                                                                <asp:Label ID="lblNoCompMessage" runat="server"></asp:Label></td>
                                                        </tr>
                                                        <tr>
                                                            <td align="center">
                                                                <asp:Button ID="cmdYes" runat="server" Text="Yes"></asp:Button>&nbsp;
                                                                <asp:Button ID="cmdNo" runat="server" Text="No"></asp:Button></td>
                                                        </tr>
                                                    </table>
                                                        </asp:Panel>
                                                        <asp:HiddenField ID="hfAccountNonExpiry" runat="server" />
                                                        <br/>
                                                        <asp:Panel ID="pAdmin" runat="server">
                                                        <table>
                                                            <colgroup>
                                                <col style="width:130px" />
                                                                <col style="width:auto" />
                                                <col style="width:auto" />
                                            </colgroup>
                                                            <tr>
                                                                <td>
                                                                    <asp:Label ID="lblAdmin" runat="server">Non-User ID</asp:Label>:<span class="ValidationHeader">*</span>
                                                                    </td><td>
                                                                    <asp:TextBox ID="txtID" runat="server"></asp:TextBox></td><td>
                                                                         <dx:aspxbutton ID="cmdGet" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                    csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css" runat="server" ValidationGroup="GetUser" Text="Get User"></dx:aspxbutton>
                                                                                                                              </td>
                                                                <td>
                                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtID" ErrorMessage="Please enter Non-User ID." SetFocusOnError="True" ValidationGroup="GetUser">Please enter Non-User ID.</asp:RequiredFieldValidator>
                                                                </td>
                                                            </tr>
                    
                                                        </table>
                                                            </asp:Panel>
                                                        <asp:Panel ID="pnlStationsTextboxes" runat="server"
>                                                 <table>
                                            <colgroup>
                                                <col style="width:130px" />
                                                <col style="width:auto" />
                                                <col style="width:auto" />
                                            </colgroup>
                                            <tr>
                                                <td>
                                                    Account ID:</td>
                                                <td>
                                                    <asp:Label ID="lblAccountID" runat="server"></asp:Label>
                                                </td>
                                                 <td>
                                                                    &nbsp;</td>
                                            </tr>
                                                                <tr>
                                                                    <td>New Workstation:<span class="ValidationHeader">*</span> </td>
                                                                    <td>
                                                                        <asp:TextBox ID="txtWorkstation" runat="server"></asp:TextBox>
                                                                        (*required only for Add button.)</td>
                                                                    <td>
                                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ControlToValidate="txtWorkstation" ErrorMessage="Please enter Work Station." SetFocusOnError="True" ValidationGroup="AddWork">Please enter Work Station.</asp:RequiredFieldValidator>
                                                                    </td>
                                                                </tr>
                                            <tr>
                                                <td>
                                                    Comments:<span class="ValidationHeader">*</span>
                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtComments" runat="server" Columns="42" Rows="4" TextMode="MultiLine" MaxLength="500"></asp:TextBox>

                                                </td>
                                                <td style="vertical-align:top">
                                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server"  ControlToValidate="txtComments" ErrorMessage="Please enter comments." SetFocusOnError="True" ValidationGroup="AddWork" Display="Dynamic">Please enter comments.</asp:RequiredFieldValidator>
                                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" ControlToValidate="txtComments" ErrorMessage="Please enter comments." SetFocusOnError="True" ValidationGroup="DelWork">Please enter comments.</asp:RequiredFieldValidator>
                                                                   </td>
                                            </tr>
                                            <tr>
                                                <td colspan="2">
                                                    <table>
                                                        <tr>
                                                            <td style="width: 100px">
                                                                <dx:aspxbutton id="cmdAdd" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                    csspostfix="Office2003Blue" ValidationGroup="AddWork" height="10px" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                                    text="Add" Width="100%"></dx:aspxbutton>
                                                            </td>
                                                            <td style="width: 100px">
                                                                <dx:aspxbutton id="cmdDelete" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                    csspostfix="Office2003Blue" ValidationGroup="DelWork" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                                    text="Delete" Width="100%"></dx:aspxbutton>
                                                            </td>
                                                            <td style="width: 223px">
                                                                <dx:aspxbutton id="cmdReturn" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                    csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                                    text="Return to My Accounts" width="223px"></dx:aspxbutton>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    
                                                </td>
                                            </tr>
                                                    </table>
                                                            </asp:Panel>
                                    </asp:Panel>
                                                    </td>
                                                </tr>
                                    </table>
                                </td>
                            </tr>
                            
                                <tr>
                                    <td colspan="2"><asp:Panel ID="pStations" runat="server">
                                        <table><tr><td colspan="2">
                                        <br/>
                                        <asp:Label ID="lblWrkSt" runat="server" Font-Bold="true">Users X can log on to these workstations</asp:Label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <asp:ListBox ID="lstWorkSt" runat="server"></asp:ListBox>
                                        </td>
                                    <td style="vertical-align:top">
                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator5" runat="server" ControlToValidate="lstWorkSt" InitialValue="" ErrorMessage="Please select workstation." SetFocusOnError="True" ValidationGroup="DelWork">Please select workstation.</asp:RequiredFieldValidator>
                                    </td>
                                </tr>
                                        </table>
                                        </asp:Panel>
                                    </td>
                                </tr>
                            
                            </table>
                    </td>
                </tr>
            </table>
</td>
</tr>
</table>
