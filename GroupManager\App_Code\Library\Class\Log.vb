Imports System
Imports System.Text
Imports System.IO


Namespace GroupManager



Public Class Log
    Public Function LogAuthDelAdd(ByVal dtbLogDetails As DataTable, ByVal strUserId As String)

        Dim strGroupADsPath As String = ""
        Dim strMemberADsPath As String = ""
        Dim strAccessId As String = ""
        Dim strAction As String = ""
        Dim strCSVDetails As String = ""
        Dim strFullFileName As String = ""
        Dim strLogName As String = ""
        Dim swADLog As StreamWriter
        strLogName = Replace(Date.Now.Date(), "/", ".") & ".csv"

        If Not dtbLogDetails Is Nothing Then
            If dtbLogDetails.Rows.Count > 0 Then
                strGroupADsPath = dtbLogDetails.Rows(0)("ADSPath")
                strMemberADsPath = "WinNT://KCUS/" & dtbLogDetails.Rows(0)("UserName")
                If dtbLogDetails.Rows(0)("AccessID") = 2 Then
                    strAction = "Added Authorizer"
                End If
                If dtbLogDetails.Rows(0)("AccessID") = 3 Then
                    strAction = "Added Delegate"
                End If
                strCSVDetails = Date.Now.ToString() & "," & strUserId & "," & strAction & "," & strGroupADsPath & "," & strMemberADsPath
                strFullFileName = Path.Combine("D:\Inetpub\wwwroot\ws.kcc.com\groupmanager\Library\Log\", strLogName)
                swADLog = New StreamWriter(strFullFileName, True)
                swADLog.WriteLine(strCSVDetails)
                swADLog.Close()
            End If
        End If
    End Function
End Class

End Namespace
