<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<configSections>
		<sectionGroup name="devExpress">
			<section name="settings" type="DevExpress.Web.ASPxClasses.SettingsConfigurationSection, DevExpress.Web.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
			<section name="compression" type="DevExpress.Web.ASPxClasses.CompressionConfigurationSection, DevExpress.Web.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
			<section name="themes" type="DevExpress.Web.ASPxClasses.ThemesConfigurationSection, DevExpress.Web.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
			<section name="errors" type="DevExpress.Web.ASPxClasses.ErrorsConfigurationSection, DevExpress.Web.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
		</sectionGroup>
	</configSections>
	<appSettings>
		<add key="SA_HOST" value="USTWA357" />
		<add key="TestMode" value="FALSE" />
		<add key="URL" value="http://ws.kcc.com/" />
	</appSettings>
	<system.web>
		<customErrors mode="Off" />
		<pages>
			<controls>
				<add tagPrefix="asp" namespace="System.Web.UI" assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
			</controls>
		</pages>
		<compilation debug="true">
			<assemblies>
				<add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
				<add assembly="System.DirectoryServices, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="System.Drawing.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="System.Web, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="System, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
				<add assembly="System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="System.Xml, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
				<add assembly="System.Data, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
				<add assembly="System.Web.Services, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="System.DirectoryServices.Protocols, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="System.EnterpriseServices, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="System.ServiceProcess, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="System.Web.RegularExpressions, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
				<add assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868B8147B5EAE4" />
				<add assembly="DevExpress.Web.ASPxEditors.v11.2, Version=*********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
				<add assembly="DevExpress.Web.ASPxPivotGrid.v11.2, Version=*********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
				<add assembly="DevExpress.Web.ASPxGridView.v11.2, Version=*********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
				<add assembly="DevExpress.Web.v11.2, Version=*********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
				<add assembly="DevExpress.Data.v11.2, Version=*********, Culture=neutral, PublicKeyToken=B88D1754D700E49A" />
			</assemblies>
		</compilation>
		<identity impersonate="false" />
	</system.web>
	<system.web.extensions>
	</system.web.extensions>
	<system.webServer>
		<validation validateIntegratedModeConfiguration="false" />
		<modules>
			<add name="ScriptModule" preCondition="integratedMode" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
			<add type="DevExpress.Web.ASPxClasses.ASPxHttpHandlerModule, DevExpress.Web.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule" />
		</modules>
		<handlers>
			<remove name="WebServiceHandlerFactory-Integrated" />
			<add name="ScriptHandlerFactory" verb="*" path="*.asmx" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
			<add name="ScriptHandlerFactoryAppServices" verb="*" path="*_AppService.axd" preCondition="integratedMode" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
			<add name="ScriptResource" preCondition="integratedMode" verb="GET,HEAD" path="ScriptResource.axd" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
		</handlers>
		<defaultDocument>
			<files>
				<clear />
				<add value="default.aspx" />
				<add value="Default.htm" />
				<add value="Default.asp" />
				<add value="index.htm" />
				<add value="index.html" />
				<add value="iisstart.htm" />
			</files>
		</defaultDocument>
	</system.webServer>
	<devExpress>
		<settings rightToLeft="false" />
		<compression enableHtmlCompression="false" enableCallbackCompression="true" enableResourceCompression="true" enableResourceMerging="false" />
		<themes enableThemesAssembly="true" />
		<errors callbackErrorRedirectUrl="" />
	</devExpress>
	<system.net>
		<mailSettings>
			<smtp deliveryMethod="Network">
				<specifiedPickupDirectory pickupDirectoryLocation="C:\inetpub\mailroot\Pickup" />
				<network host="" />
			</smtp>
		</mailSettings>
	</system.net>
</configuration>