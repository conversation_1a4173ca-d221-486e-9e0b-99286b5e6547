Imports System.Web
Imports System.Web.SessionState
Imports System.Data

Namespace AccountManager

    Public Class [Global]

        Inherits System.Web.HttpApplication

        Public Const GblAccountMANAGER_SESSION As String = "AccountManager"
        Public Const GblUserConn = "server=USTCAS29;UID=esh0neadm;PWD="
        Public Const GblGCAConn As String = "server=USTCAS29;DATABASE=gca0nedb;Integrated Security=True;Connect Timeout=200;"

        'Public Const GblUserConn = "server=USTCAS29;UID=esh0neadm;PWD=;DATABASE=esh0nedb;Connect Timeout=200" 'Prateek

        '  Public Shared GblSauConn As String = "Server=USTCAS16;Database=sau0nedb;UID=sau0neadm;PWD="
        'Public Const GblSauConn As String = "Server=USTCAS41;Database=sau0nedb;UID=sau0neadm;PWD="
        'New Prod Db.
        Public Const GblSauConn As String = "Server=USTCAS74;Database=sau2nedb;UID=sau2nepublic;PWD="

        Public Shared GblServerPath = "D:\Inetpub\wwwroot\ws.kcc.com\AccountManager\Library\Output\"
        'Public Shared GblServerPath = "D:\Inetpub\wwwroot\ws.dev.kcc.com\AccountManager\Library\Output\"
        Public Shared GblDownLoad = GblHttp & "AccountManager/Library/Output/"

        Public Shared GblHttp = System.Configuration.ConfigurationManager.AppSettings("URL")
        Public Shared GblSAServer = System.Configuration.ConfigurationManager.AppSettings("SA_Host")
        Public Shared GblTestMode = System.Configuration.ConfigurationManager.AppSettings("TestMode")
        Public Shared GblTestModeEmail As String = ""
        Public Shared GblInvalidOwnerMessage As String = "Owner for Non-User Account must be Permanenet KC Employee."
        Public Shared GblPasswordChangeNotAllowMessage As String = "Passwords for MT accounts are managed by _Global, Job Scheduling. Please contact them for assistance."
        ' Public Shared GblDCForPassword As String = "USTCNDC2"
	Public Shared GblDCForPassword As String = "USTCNDC0"
        Public Shared GblMsgLinkAccess As String = " (Link is accessible from KCUS Only) "
        Public Shared GblLDAP As String = "LDAP://DC=kcc,DC=com"
        Public Shared GblAccountsLDAP As String = "LDAP://OU=Accounts,DC=kcc,DC=com"
        Public Shared GblADSPath As String = "WinNT://KCUS/"
        Public Shared GblDomain As String = "KCUS"
	Public Shared GblIsMOSAIC As Boolean = False
        ' Public Shared GblLDAP As String = "LDAP://DC=kctest,DC=com"
        ' Public Shared GblAccountsLDAP As String = "LDAP://OU=Accounts,DC=kctest,DC=com"
        ' Public Shared GblADSPath As String = "WinNT://kctest/"
        Public Shared GblValidLDAPForWorkStations() As String = {"LDAP://DC=kcc,DC=com", "LDAP://DC=Internet,DC=Kimberly-Clark,DC=com", "LDAP://DC=kctest,DC=com"}

        Public Const GblTypeNewOwner = "Change Owner"
        Public Const GblReqStatusNew = "New"
        Public Const GblReqStatusDone = "Complete"
        Public Const GblReqStatusDelete = "Deleted"

        Public Const GblOwnerAccess = 1
        Public Const GblBackupAccess = 2

        Public Const GblActDel = "Account Deleted"
        Public Const GblActBackupAdded = "Backup Added"
        Public Const GblActOwnDel = "Owner Deleted"
        Public Const GblActOwnAdd = "Owner Updated"
        Public Const GblActOwnAccept = "Change Owner Accepted"
        Public Const GblActOwnDec = "Change Owner Rejected"
        Public Const GblActOwnChange = "Change Owner Competed"
        Public Const GblActOwnReq = "Change Owner Requested"
        Public Const GblActPwdChg = "Password Change"
        Public Const GblActPwdChgMes = "Password Change Message"
        Public Const GblActOwnVer = "Ownership Verified"
        Public Const GblActMassOwnUpdate = "Mass Ownership Update"
        Public Const GblActMassOwnVerifUpdate = "Mass Ownership Verification Update"


#Region " Component Designer Generated Code "

        Public Sub New()
            MyBase.New()

            'This call is required by the Component Designer.
            InitializeComponent()

            'Add any initialization after the InitializeComponent() call

        End Sub

        'Required by the Component Designer
        Private components As System.ComponentModel.IContainer

        'NOTE: The following procedure is required by the Component Designer
        'It can be modified using the Component Designer.
        'Do not modify it using the code editor.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()
            components = New System.ComponentModel.Container
        End Sub

#End Region



        Sub Application_Start(ByVal sender As Object, ByVal e As System.EventArgs)
            ' Fires when the application is started
        End Sub

        Sub Session_Start(ByVal sender As Object, ByVal e As System.EventArgs)
            ' Fires when the session is started
            'This code allows the project to be copied from one area of development to another without having to change all the paths.
            Dim strPath As String
            strPath = Trim(LCase(Request.ServerVariables("HTTP_HOST")))

            If strPath = "ws.dev.kcc.com" Then
                'GblUserConn = "server=USTCAS41;UID=esh0neadm;PWD="
                GblHttp = "http://ws.dev.kcc.com/"
                GblServerPath = "D:\Inetpub\wwwroot\ws.dev.kcc.com\AccountManager\Library\Output\"
                GblDownLoad = GblHttp & "AccountManager/Library/Output/"

            ElseIf strPath = "ws.pqa.kcc.com" Then
                GblHttp = "http://ws.pqa.kcc.com/"
                GblServerPath = "D:\Inetpub\wwwroot\ws.pqa.kcc.com\AccountManager\Library\Output\"
                GblDownLoad = GblHttp & "AccountManager/Library/Output/"

            ElseIf strPath = "ws.qa.kcc.com" Then
                GblHttp = "http://ws.qa.kcc.com/"
                GblServerPath = "D:\Inetpub\wwwroot\ws.qa.kcc.com\AccountManager\Library\Output\"
                GblDownLoad = GblHttp & "AccountManager/Library/Output/"

            ElseIf strPath = "ws.kcc.com" Then
                GblHttp = "https://ws.kcc.com/"
                GblServerPath = "D:\Inetpub\wwwroot\ws.kcc.com\AccountManager\Library\Output\"
                GblDownLoad = GblHttp & "AccountManager/Library/Output/"

            ElseIf strPath = "localhost" Then
                GblHttp = "http://localhost/"
                GblServerPath = "D:\Inetpub\wwwroot\ws.dev.kcc.com\AccountManager\Library\Output\"
                GblDownLoad = GblHttp & "AccountManager/Library/Output/"
            Else
                GblHttp = "https://ws.kcc.com/"
                GblServerPath = "D:\Inetpub\wwwroot\ws.kcc.com\AccountManager\Library\Output\"
                GblDownLoad = GblHttp & "AccountManager/Library/Output/"

            End If
        End Sub

        Sub Application_BeginRequest(ByVal sender As Object, ByVal e As System.EventArgs)
            ' Fires at the beginning of each request
        End Sub

        Sub Application_AuthenticateRequest(ByVal sender As Object, ByVal e As System.EventArgs)
            ' Fires upon attempting to authenticate the use
        End Sub

        Sub Application_Error(ByVal sender As Object, ByVal e As System.EventArgs)
            ' Fires when an error occurs
        End Sub

        Sub Session_End(ByVal sender As Object, ByVal e As System.EventArgs)
            ' Fires when the session ends
        End Sub

        Sub Application_End(ByVal sender As Object, ByVal e As System.EventArgs)
            ' Fires when the application ends
        End Sub

    End Class

End Namespace

