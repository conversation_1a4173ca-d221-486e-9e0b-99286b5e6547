Namespace AccountManager

Public Class Requests

    Dim strAccount As String
    Dim strType As String
    Dim strNewOwner, strNewOwnerDN As String
    Dim strComments As String
    Dim strStatus As String
    Dim strSubmittingUser As String
    Dim strOldOwner, strOldOwnerDN As String
    Dim strSQL As String
    Dim strDescription, strNoOwnerDescription As String
    Dim blnAcctException As Boolean
        Dim IsError As Boolean
        Dim ErrorMsg As String

#Region "Variables Defined"
    Property AcctException()
        Get
            Return blnAcctException
        End Get
        Set(ByVal Value)
            blnAcctException = Value
        End Set
    End Property
    Property DescriptionNoOwnerInfo()
        Get
            Return strNoOwnerDescription
        End Get
        Set(ByVal Value)
            strNoOwnerDescription = Value
        End Set
    End Property
    Property Description()
        Get
            Return strDescription
        End Get
        Set(ByVal Value)
            strDescription = Value
        End Set
    End Property

    Property NewOwnerDN()
        Get
            Return strNewOwnerDN
        End Get
        Set(ByVal Value)
            strNewOwnerDN = Value
        End Set
    End Property
    Property OldOwnerDN()
        Get
            Return strOldOwnerDN
        End Get
        Set(ByVal Value)
            strOldOwnerDN = Value
        End Set
    End Property
    Property SQL()
        Get
            Return strSQL
        End Get
        Set(ByVal Value)
            strSQL = Value
        End Set
    End Property
    Property AccountID()
        Get
            Return strAccount
        End Get
        Set(ByVal Value)
            strAccount = Value
        End Set
    End Property
    Property OldOwner()
        Get
            Return strOldOwner
        End Get
        Set(ByVal Value)
            strOldOwner = Value
        End Set
    End Property
    Property Type()
        Get
            Return strType
        End Get
        Set(ByVal Value)
            strType = Value
        End Set
    End Property
    Property NewOwner()
        Get
            Return strNewOwner
        End Get
        Set(ByVal Value)
            strNewOwner = Value
        End Set
    End Property
    Property Comments()
        Get
            Return strComments
        End Get
        Set(ByVal Value)
            strComments = Value
        End Set
    End Property
    Property Status()
        Get
            Return strStatus
        End Get
        Set(ByVal Value)
            strStatus = Value
        End Set
    End Property
    Property SubmittingUser()
        Get
            Return strSubmittingUser
        End Get
        Set(ByVal Value)
            strSubmittingUser = Value
        End Set
        End Property
        ReadOnly Property GetIsError() As String
            Get
                Return IsError
            End Get
        End Property
        ReadOnly Property GetErrorMsg() As String
            Get
                Return ErrorMsg
            End Get
        End Property
#End Region

        Function InsertRequest() As String
            IsError = False
            ErrorMsg = ""
            Dim dbUpd As New DataAccess
            Dim hlpClean As New Helper

            'strSQL = "sp_UMRequest_Add '" & strAccount & "','" & strType & "','" & _
            '            hlpClean.dbCleanUpString(strComments) & "','" & strNewOwner & "','" & hlpClean.dbCleanUpString(strNewOwnerDN) & "','" & _
            '            strStatus & "','" & Now & "','" & strSubmittingUser & "','" & strOldOwner & "','" & hlpClean.dbCleanUpString(strOldOwnerDN) & "','" & hlpClean.dbCleanUpString(strDescription) & "'"
            strSQL = "sp_UMRequest_Add_OwnerChange '" & strAccount & "','" & strType & "','" & _
                          hlpClean.dbCleanUpString(strComments) & "','" & strNewOwner & "','" & hlpClean.dbCleanUpString(strNewOwnerDN) & "','" & _
                          strStatus & "','" & strSubmittingUser & "','" & strOldOwner & "','" & hlpClean.dbCleanUpString(strOldOwnerDN) & "','" & hlpClean.dbCleanUpString(strDescription) & "'"

            Dim retval As String = ""
            If dbUpd.CallStoredProcedurewithScalar(strSQL, retval) = False Then
                IsError = True
                ErrorMsg = dbUpd.ErrorMsg
            End If

            Return retval
        End Function

    Function DeleteAccountID()

        Dim dbUpd As New DataAccess
        Dim hlpClean As New Helper

        strSQL = "sp_UMUser_Delete '" & strAccount & "'"

        dbUpd.UpdateDBByStringId(strSQL)

    End Function

    Function MassOwnerUpdate()
        Dim dbUpd As New DataAccess
        Dim hlpClean As New Helper

        strSQL = "sp_UMOwner_MASSUpdate '" & strOldOwner & "','" & strNewOwner & "','" & hlpClean.dbCleanUpString(strNewOwnerDN) & "'"

        dbUpd.UpdateDBByStringId(strSQL)
    End Function

    Function UpdateAutoRequest(ByVal strAccountID As String, ByVal strStatus As String)
        Dim dbUpd As New DataAccess

        strSQL = "sp_UMRequest_UpdateByID '" & strStatus & "','" & strAccountID & "','New Account'"

        dbUpd.UpdateDBByStringId(strSQL)
    End Function

    Function UpdateRequest(ByVal intReqID As Integer, ByVal strStatus As String)
        Dim dbUpd As New DataAccess
        Dim hlpClean As New Helper

        strSQL = "sp_UMRequest_Update " & intReqID & ",'" & strStatus & "','" & hlpClean.dbCleanUpString(strComments) & "'"
        dbUpd.UpdateDBByStringId(strSQL)

    End Function

    Function UpdateOwner()
        Dim dbUpd As New DataAccess
        Dim hlpClean As New Helper

        If Not hlpClean.IsOwner(strAccount, strNewOwner) Then
            strSQL = "sp_UMOwner_Delete '" & strAccount & "','" & strNewOwner & "'"
            dbUpd.UpdateDBByStringId(strSQL)

            strSQL = "sp_UMOwner_Update '" & strAccount & "','" & strOldOwner & "','" & strNewOwner & "','" & hlpClean.dbCleanUpString(strNewOwnerDN) & "'"
            'strSQL = "sp_UMOwner_Add '" & strAccount & "','" & Global.GblOwnerAccess & "','" & strNewOwner & "','" & hlpClean.dbCleanUpString(strNewOwnerDN) & "'"
            dbUpd.UpdateDBByStringId(strSQL)
        End If
    
    End Function

    Function SetDescription(Optional ByVal strNewDescription As String = "") As String

        Dim usrUpdDesc As New UserInfo(strAccount)
        Dim strNewDesc As String
        Dim hlpClean As New Helper

        If strNewDescription = "" Then

            Dim strTemp, strCurDesc As String
            Dim intIndex As Integer
            Dim strValues As String()

            strCurDesc = usrUpdDesc.Description

            If strCurDesc <> "" Then

                intIndex = strCurDesc.IndexOf("(")
                If intIndex < 1 Then
                    strTemp = "NONE FOUND"
                Else
                    strTemp = strCurDesc.Remove(0, intIndex)
                    strNewDesc = strCurDesc.Remove(intIndex, strCurDesc.Length - intIndex)

                    strValues = Split(strTemp, " ")
                    strTemp = Replace(strValues(0), "(", "")

                    Dim adOwnerCheck As New UserInfo(strNewOwner)
                    If adOwnerCheck.UserFound() Then
                        strNewDesc = strNewDesc & " " & adOwnerCheck.UserNameandID
                    Else
                        strNewDesc = strCurDesc
                    End If

                End If
            Else
                strNewDesc = strCurDesc
            End If

        Else
            strNewDescription = hlpClean.dbCleanUpString(strNewDescription)
            strNewDesc = strNewDescription
        End If

        usrUpdDesc.SetDescription(usrUpdDesc.LdapPath, strNewDesc)
        SetDescription = strNewDesc

    End Function

    Function FindOwnerInDescription() As String

        Dim usrUpdDesc As New UserInfo(strAccount)
        Dim strTemp, strDesc As String
        Dim intIndex As Integer
        Dim strValues As String()

        strDesc = usrUpdDesc.Description

        If strDesc <> "" Then
            intIndex = strDesc.IndexOf("(")
            If intIndex < 1 Then
                strTemp = strDesc
            Else
                strTemp = strDesc.Remove(0, intIndex)
                strNoOwnerDescription = strDesc.Remove(intIndex, strDesc.Length - intIndex)
                strValues = Split(strTemp, " ")
                strTemp = Replace(strValues(0), "(", "")
            End If

            FindOwnerInDescription = strTemp

        Else
            FindOwnerInDescription = strDesc
        End If
    End Function

End Class

End Namespace
