'Title: AD
'Created by: <PERSON> on 10/1/2002
'Purpose:  Retrieve and Change Active Directory Information.
'Assumptions:  Everyone is in the KCUS domiain (kcc.com)
'Effects:  Resets passwords, enables accounts, unlocks accounts
'Inputs: User ID
'Returns: 
Imports System.IO
Imports System.DirectoryServices
Imports System.Data.SqlClient
Imports GroupManager.[Global]
Imports GroupManager.UserInfo
Imports ActiveDs


Namespace GroupManager

Public Class AD

    Dim strLDAP, strPath, strFileName, strMessage As String
    Dim swADLog As StreamWriter
    Dim intRecordsAffected As Integer

    Function GetAffectedRecords()
        GetAffectedRecords = intRecordsAffected
    End Function

    Function GetMessage()
        GetMessage = strMessage
    End Function

    'Title: Constructor for AD.vb
    'Created by: <PERSON> on 10/1/2002 
    'Purpose:  Create a new isntance of AD.vb
    'Assumptions:  Assumes user is in the KCUS domain
    'Effects:  Sets initial values for variables, log file, filters
    'Inputs: user id (ex. e12344, b12345)
    'Returns: Nothing
    Sub New()
        intRecordsAffected = 0
        strLDAP = "LDAP://DC=kcc,DC=com"
        strPath = glbLogPath
        strFileName = "GM_AD_Compare_" & Now.ToLongDateString & ".csv"
        strPath = strPath & strFileName
    End Sub

    'Title: WriteLog
    'Created by: Shane Z Smith on 10/1/2002
    'Purpose:  Write to the log file, actions taken by the current user logged in
    'Assumptions:  None
    'Effects:  Writes to log file. New log file for each day.
    'Inputs: the line being written
    'Returns: none
    Private Function WriteLog(ByVal strLine As String)

        swADLog = New StreamWriter(strPath, True)
        swADLog.WriteLine(strLine)
        swADLog.Close()

    End Function

    'Title: GetUser
    'Created by: Shane Z Smith on 10/1/2002
    'Purpose:  Retrieve user information from active directory
    'Assumptions:  User exists within the KCUS domain
    'Effects:  Retrieves user and all the properties we need within GetAccountInfo
    'Inputs: none
    'Returns: if the function was successful or not, true or false.
    Public Function IsGroupInAD(ByVal strGroup As String) As Boolean

        Try
            Dim strLdap As String
            strLdap = "LDAP://kcc.com"

            Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLdap)
            Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
            Dim objSearchRes As System.DirectoryServices.SearchResult

            objSearcher.Filter = ("(objectClass=group)")
            objSearcher.Filter = ("(sAMAccountName=" & strGroup & ")")

            objSearchRes = objSearcher.FindOne

            If objSearchRes Is Nothing Then
                IsGroupInAD = False
            Else
                'path is the Active Directory path to that object
                'sPath = objSearchRes.GetDirectoryEntry.Path
                'CN is usually the BID of the person (unique identifier in AD)
                'strCN = objSearchRes.GetDirectoryEntry.Properties.Item("cn").Value
                IsGroupInAD = True
            End If

            Return IsGroupInAD

        Catch ex As Exception
            'strMessage = ex.Message
            'WriteLog(strMessage)
        End Try

    End Function
    'added newly when converting java applet to dotnet
    Public Function GetGroupName(ByVal strGroup As String) As String
        Dim strCN As String
        Try
            Dim strLdap As String
            strLdap = "LDAP://kcc.com"

            Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLdap)
            Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
            Dim objSearchRes As System.DirectoryServices.SearchResult

            'objSearcher.Filter = ("(objectClass=group)")
            'objSearcher.Filter = ("(sAMAccountName=" & strGroup & ")")
            objSearcher.Filter = "(&(objectClass=group) (sAMAccountName=" & strGroup & "))"


            objSearchRes = objSearcher.FindOne

            If objSearchRes Is Nothing Then
                GetGroupName = "Group Not Found"
            Else
                'path is the Active Directory path to that object
                'sPath = objSearchRes.GetDirectoryEntry.Path
                'CN is usually the BID of the person (unique identifier in AD)
                strCN = objSearchRes.GetDirectoryEntry.Properties.Item("cn").Value

            End If


        Catch ex As Exception
            'strMessage = ex.Message
            'WriteLog(strMessage)
        End Try
        Return strCN

    End Function
    'added newly when converting java applet to dotnet
    Public Function GetDisplayNameForApplet(ByVal strUserID As String)
        Dim strDisplayName As String
        Dim strLdap As String
        strLdap = "LDAP://kcc.com"
        Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLdap)
        Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
        Dim objSearchRes As System.DirectoryServices.SearchResult
        Try
            'objSearcher.Filter = ("(objectClass=user)")
            'objSearcher.Filter = ("(sAMAccountName=" & strUserID & ")")
            objSearcher.Filter = "(&(objectClass=user) (sAMAccountName=" & strUserID & "))"

            If objSearcher.FindAll.Count > 0 Then
                For Each objSearchRes In objSearcher.FindAll
                    strDisplayName = objSearchRes.GetDirectoryEntry.Properties.Item("Description").Value
                Next
            'Else : strDisplayName = strUserID
             Else : strDisplayName = "Owner does not exist"    
	End If

            Return strDisplayName

        Catch ex As Exception
            objSearcher.Dispose()
        Finally
            If Not objDirEnt Is Nothing Then
                objDirEnt.Dispose()
            End If
            If Not objSearcher Is Nothing Then
                objSearcher.Dispose()
            End If
        End Try


    End Function


    'Title: GetAccountInfo
    'Created by: Shane Z Smith on 10/1/2002
    'Purpose:  Retrieve specific fields from Active Directory, assign them to variables.
    'Assumptions:  none
    'Effects:  Values assigned
    'Inputs: user id, and his/her particular LDAP path
    'Returns: true if successfull or an error message.
    Public Function StartCompare() As Boolean

        Try
            Dim strID, strGroup As String
            Dim intResult As Integer

            Dim dbGetAllGroups As New GroupManager_DB
            Dim dr As SqlDataReader

            strID = Replace(System.Web.HttpContext.Current.Request.ServerVariables("LOGON_USER"), "KCUS\", "")
            'Dim curUser As New UserInfo(strID)

            dr = dbGetAllGroups.GetSQLDataREader("pc_GM_GetAllGroups")

            WriteLog("Ran by: " & strID & " at " & Now)
            WriteLog(("******************User Logged In********************"))
            WriteLog("Groups Not in AD")

            Do While dr.Read

                strGroup = dr.Item("GroupName")

                If Not IsGroupInAD(strGroup) Then

                    intResult = DeleteGroup(dr.Item("AdsPath"))

                    If intResult > 0 Then
                        WriteLog(strGroup & " DELETED FORM DATABASE --> Number of Records Deleted: " & intResult)
                    Else
                        WriteLog(strGroup & " was NOT deleted Message: " & intResult & " Adspath: " & dr.Item("AdsPath"))
                    End If

                    intRecordsAffected = intRecordsAffected + 1

                End If

            Loop

            WriteLog(("******************User Logged Out********************"))
            Return True

        Catch ex As Exception
            strMessage = ex.Message
            WriteLog(strMessage)
            Return False
        End Try

    End Function

    Function DeleteGroup(ByVal strGroup As String) As String

        Try
            Dim dbGetAllGroups As New GroupManager_DB
            Dim dr As SqlDataReader

            dr = dbGetAllGroups.GetSQLDataREader("pc_GMGroup_Remove", strGroup, "@ADSPath")
            Return dr.RecordsAffected

        Catch ex As Exception
            strMessage = ex.Message
            WriteLog(strMessage)
            Return 0
        End Try

    End Function

    ''''''''''Title: IsMember
    ''''''''''Created by: Shane Z Smith on 10/1/2002
    ''''''''''Purpose:  Determine if user is a member of a group
    ''''''''''Assumptions:  None
    ''''''''''Effects:  Queries active directory
    ''''''''''Inputs: User LDAP path and the group that we want to know if the user is a member of
    ''''''''''Returns: Returns true if user is a member of the group in question or false if they are 
    ''''''''''           a member of.
    '''''''''Function IsMember(ByVal LDAP As String, ByVal Group As String) As Boolean

    '''''''''    Dim dsUser As DirectoryEntry = New DirectoryEntry(LDAP)
    '''''''''    Dim objValue As Object
    '''''''''    Dim Key As String
    '''''''''    Dim strGroup As String
    '''''''''    IsMember = False

    '''''''''    'Read the memberOf property to see which groups the user is a member of
    '''''''''    'Loop through all the groups
    '''''''''    For Each objValue In dsUser.Properties("memberOf")

    '''''''''        strGroup = LCase(objValue.ToString())

    '''''''''        'If the group is found within the current property then the user
    '''''''''        'is a member of the group
    '''''''''        'IndexOf returns -1 when nothing is found
    '''''''''        If strGroup.IndexOf(LCase(Group)) <> -1 Then
    '''''''''            IsMember = True
    '''''''''        End If

    '''''''''    Next objValue

    '''''''''End Function
    Function IsMember(ByVal strUserID As String, ByVal strGroup As String) As Boolean
        'System.Threading.Thread.CurrentThread.Sleep(3000)
        Dim dtbUserGroups As DataTable = GetUserGroups(strUserID)
        Dim intCount As Integer
        strGroup = strGroup.ToUpper
        For intCount = 0 To dtbUserGroups.Rows.Count - 1
            If CStr(dtbUserGroups.Rows(intCount)(0)).ToUpper = strGroup Then
                IsMember = True
                Exit Function
            End If
        Next
        IsMember = False
    End Function


    Public Function GetDisplayName(ByVal strUserID As String)
            Dim strDisplayName As String = ""
        Dim strLdap As String
        strLdap = "LDAP://kcc.com"
        Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLdap)
        Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
        Dim objSearchRes As System.DirectoryServices.SearchResult
        Try
            objSearcher.Filter = ("(objectClass=user)")
            objSearcher.Filter = ("(sAMAccountName=" & strUserID & ")")

            If objSearcher.FindAll.Count > 0 Then
                For Each objSearchRes In objSearcher.FindAll
                        strDisplayName = objSearchRes.GetDirectoryEntry.Properties.Item("DisplayName").Value
                Next
            'Else : strDisplayName = strUserID
            Else : strDisplayName = "Owner does not exist"
		End If

            Return strDisplayName

        Catch ex As Exception
            objSearcher.Dispose()
        Finally
            If Not objDirEnt Is Nothing Then
                objDirEnt.Dispose()
            End If
            If Not objSearcher Is Nothing Then
                objSearcher.Dispose()
            End If
        End Try


    End Function


    Public Function GetUserName(ByVal strUserID As String)

        Dim objSearcher As New System.DirectoryServices.DirectorySearcher
        objSearcher.SearchRoot = New DirectoryEntry("LDAP://kcc.com")
        objSearcher.Filter = String.Format("(&(objectCategory=user)(objectClass=user)(sAMAccountName={0}))", strUserID)
        Dim objSearchRes As System.DirectoryServices.SearchResult
        Try
            objSearcher.PropertyNamesOnly = True
            objSearcher.PropertiesToLoad.Add("sn")
            objSearcher.PropertiesToLoad.Add("givenName")
            objSearchRes = objSearcher.FindOne()
            Dim dirEntry As DirectoryEntry = objSearchRes.GetDirectoryEntry()
            Dim strDisplayName As String = dirEntry.Properties("sn").Value & ", " & dirEntry.Properties("givenName").Value
            Return strDisplayName
        Catch
            objSearcher.Dispose()
            Return 0
        Finally
            objSearcher.Dispose()
        End Try

    End Function
    Public Function GetUserID(ByVal strFirstName As String, ByVal strLastName As String)

        Dim objSearcher As New System.DirectoryServices.DirectorySearcher
        objSearcher.SearchRoot = New DirectoryEntry("LDAP://kcc.com")
        objSearcher.Filter = String.Format("(&(objectCategory=user)(objectClass=user)(givenname={0})(sn={1}))", strFirstName, strLastName)
        Dim objSearchRes As System.DirectoryServices.SearchResult
        Try
            objSearcher.PropertyNamesOnly = True
            objSearcher.PropertiesToLoad.Add("cn")
            objSearchRes = objSearcher.FindOne()
            Dim dirEntry As DirectoryEntry = objSearchRes.GetDirectoryEntry()
            Dim strUserID As String = dirEntry.Properties("cn").Value
            Return strUserID
        Catch
            objSearcher.Dispose()
            Return 0
        Finally
            objSearcher.Dispose()
        End Try

    End Function
    Public Function GetUserGroups(ByVal strUserID As String) As DataTable
        Dim strLdap As String
        Dim dtbUserGroups As New DataTable
        dtbUserGroups.TableName = Constants.DTB_USER_GROUPS
        dtbUserGroups.Columns.Add("GroupName")
        ' Dim dsUserGroups As New DataSet
        strLdap = "LDAP://kcc.com"

        Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLdap)
        Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
        Dim objSearchResColl As System.DirectoryServices.SearchResultCollection

        objSearcher.Filter = ("(objectClass=user)")
        objSearcher.Filter = ("(sAMAccountName=" & strUserID & ")")
        Try
            objSearchResColl = objSearcher.FindAll()
            Dim objSearchRes As System.DirectoryServices.SearchResult

            For Each objSearchRes In objSearchResColl
                Dim PropVal As Object
                For Each PropVal In objSearchRes.Properties("memberof")
                    Dim dr As DataRow = dtbUserGroups.NewRow()
                    Dim astrResult() As String = PropVal.ToString.Split(",")
                    dr("GroupName") = CType(Replace(astrResult(0).ToString, "CN=", ""), String)
                    dtbUserGroups.Rows.Add(dr)
                Next
            Next

        Catch ex As Exception

        Finally
            If Not dtbUserGroups Is Nothing Then
                ' dtbUserGroups.Dispose()
            End If
        End Try
        Return dtbUserGroups
    End Function
    Public Function GetUserGroupsForMyMembership(ByVal strUserID As String) As DataTable
        Dim strLdap As String
        Dim dtbUserGroups As New DataTable
        dtbUserGroups.TableName = Constants.DTB_USER_GROUPS
        dtbUserGroups.Columns.Add("GroupName")
        dtbUserGroups.Columns.Add("Description")
        ' Dim dsUserGroups As New DataSet
        strLdap = "LDAP://kcc.com"

        Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLdap)
        Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
        Dim objSearchResColl As System.DirectoryServices.SearchResultCollection

        Dim objDirEnt1 As New DirectoryServices.DirectoryEntry(strLdap)
        Dim objSearcher1 As New System.DirectoryServices.DirectorySearcher(objDirEnt1)
        Dim objSearchRes1 As System.DirectoryServices.SearchResult

        objSearcher.Filter = ("(objectClass=user)")
        objSearcher.Filter = ("(sAMAccountName=" & strUserID & ")")
        Try
            objSearchResColl = objSearcher.FindAll()
            Dim objSearchRes As System.DirectoryServices.SearchResult

            For Each objSearchRes In objSearchResColl

                Dim PropVal As Object
                For Each PropVal In objSearchRes.Properties("memberof")
                    Dim dr As DataRow = dtbUserGroups.NewRow()
                    Dim astrResult() As String = PropVal.ToString.Split(",")
                    dr("GroupName") = CType(Replace(astrResult(0).ToString, "CN=", ""), String)
                    'dr("Description") = CType(Replace(astrResult(0).ToString, "CN=", ""), String)
                    objSearcher1.Filter = "(&(objectClass=group) (sAMAccountName=" & Convert.ToString(dr("GroupName")) & "))"
                    objSearchRes1 = objSearcher1.FindOne
                    If objSearchRes1 Is Nothing Then
                        dr("Description") = "Group Not Found"
                    Else
                        dr("Description") = objSearchRes1.GetDirectoryEntry.Properties.Item("description").Value
                    End If
                    dtbUserGroups.Rows.Add(dr)
                Next
            Next

        Catch ex As Exception

        Finally
            If Not dtbUserGroups Is Nothing Then
                ' dtbUserGroups.Dispose()
            End If
        End Try
        Return dtbUserGroups
    End Function
    Public Function ListMembersForGroup(ByVal strGroup As String) As DataTable
        Dim strLdap As String
        Dim dtbUserGroups As New DataTable
        dtbUserGroups.TableName = "GroupMembers"
        dtbUserGroups.Columns.Add("Member")
        ' Dim dsUserGroups As New DataSet
        strLdap = "LDAP://kcc.com"

        Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLdap)
        Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
        Dim objSearchResColl As System.DirectoryServices.SearchResultCollection

        objSearcher.Filter = ("(objectClass=group)")
        objSearcher.Filter = ("(sAMAccountName=" & strGroup & ")")
        Try
            objSearchResColl = objSearcher.FindAll()
            Dim objSearchRes As System.DirectoryServices.SearchResult

            For Each objSearchRes In objSearchResColl
                Dim PropVal As Object
                For Each PropVal In objSearchRes.Properties("member")
                    Dim dr As DataRow = dtbUserGroups.NewRow()
                    Dim astrResult() As String = PropVal.ToString.Split(",")
                    dr("Member") = CType(Replace(astrResult(0).ToString, "CN=", ""), String)
                    dtbUserGroups.Rows.Add(dr)
                Next
            Next

        Catch ex As Exception

        Finally
            If Not dtbUserGroups Is Nothing Then
                ' dtbUserGroups.Dispose()
            End If
        End Try
        Return dtbUserGroups
    End Function
    Public Function FindUsers(ByVal strUserDetails As String, ByVal bUserID As Boolean) As DataSet
        Dim dtbUsers As DataTable
        Dim dsUsers As DataSet
        Dim strLdap As String
        strLdap = "LDAP://kcc.com"
        Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLdap)
        Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
        Dim objSearchRes As System.DirectoryServices.SearchResult
        Try
            objSearcher.Filter = ("(objectClass=user)")
            If bUserID Then
                If Not strUserDetails = "" Then
                    objSearcher.Filter = ("(sAMAccountName=" & strUserDetails & ")")
                End If
            Else
                If Not strUserDetails = "" Then
                    Dim strUserNames As String()
                    strUserNames = strUserDetails.Split(",")
                    If strUserNames.Length > 1 Then
                        objSearcher.Filter = "(&(sn=" & strUserNames(0).ToString() & "*)(givenname=" & strUserNames(1).ToString() & "*))"
                    Else
                        objSearcher.Filter = ("(sn=" & strUserNames(0).ToString() & "*)")
                    End If
                End If
            End If


            dtbUsers = New DataTable("DataTableUsers")
            dtbUsers.Columns.Add("UserID")
            dtbUsers.Columns.Add("UserName")
            Dim dr As DataRow
            If objSearcher.FindAll.Count > 0 Then
                For Each objSearchRes In objSearcher.FindAll
                    dr = dtbUsers.NewRow()
                    dr("UserID") = objSearchRes.GetDirectoryEntry.Properties.Item("sAMAccountName").Value
                    dr("UserName") = objSearchRes.GetDirectoryEntry.Properties.Item("Description").Value
                    dtbUsers.Rows.Add(dr)
                Next
            End If
            dsUsers = New DataSet
            dsUsers.Tables.Add(dtbUsers)
            dsUsers.AcceptChanges()
            Return dsUsers
        Catch ex As Exception
            If Not dtbUsers Is Nothing Then
                dtbUsers.Dispose()
            End If
        Finally
            If Not objDirEnt Is Nothing Then
                objDirEnt.Dispose()
            End If
            If Not objSearcher Is Nothing Then
                objSearcher.Dispose()
            End If
        End Try
    End Function

    Public Function AddMember(ByVal strGroupADSPath As String, ByVal strMemberADSPath As String, ByRef lErr As Object) As Boolean
        Dim objGroup As IADsGroup


        Try
            objGroup = GetObject(strGroupADSPath & ",group")


            objGroup.Add(strMemberADSPath)
            objGroup.SetInfo()



            AddMember = True

            If Not (objGroup Is Nothing) Then objGroup = Nothing
            Exit Function

        Catch ex As Exception
            'HttpContext.Current.Response.Write(ex)
            If Not (objGroup Is Nothing) Then objGroup = Nothing
            AddMember = False
        End Try

    End Function
    Public Function RemoveMember(ByVal strGroupADSPath As String, ByVal strMemberADSPath As String, ByRef lErr As Object) As Boolean

        Dim objGroup As IADsGroup
        Try
            objGroup = GetObject(strGroupADSPath & ",group")

            objGroup.Remove(strMemberADSPath)

            objGroup.SetInfo()

            RemoveMember = True

            If Not (objGroup Is Nothing) Then objGroup = Nothing
            Exit Function

        Catch ex As Exception
            If Not (objGroup Is Nothing) Then objGroup = Nothing
            RemoveMember = False
        End Try

    End Function

'BOC Prateek RC CHG0301270
         Public Function GetEmployeeType(ByVal strUserID As String) As String

            If strUserID = "" Then
                GetEmployeeType = ""
            Else
                Dim objSearcher As New System.DirectoryServices.DirectorySearcher
                objSearcher.SearchRoot = New DirectoryEntry("LDAP://kcc.com")
                objSearcher.Filter = String.Format("(&(objectCategory=user)(objectClass=user)(sAMAccountName={0}))", strUserID)
                Dim objSearchRes As System.DirectoryServices.SearchResult
                Try
                    objSearcher.PropertyNamesOnly = True
                    objSearcher.PropertiesToLoad.Add("employeeType")
                    objSearchRes = objSearcher.FindOne()
                    Dim dirEntry As DirectoryEntry = objSearchRes.GetDirectoryEntry()
                    Dim strDisplayName As String = dirEntry.Properties("employeeType").Value
                    GetEmployeeType = strDisplayName
                    Return GetEmployeeType
                Catch ex As Exception
                    Return GetEmployeeType = ""
                End Try
            End If
        End Function
        'EOC Prateek RC CHG0301270

 Public Function GetEmailDetail(ByVal id As String) As Boolean
            Dim sPath As String = "LDAP://DC=KCC,DC=COM"
            Dim strUserEmail As String = ""
            Dim objDirEnt As New DirectoryServices.DirectoryEntry(sPath)
            Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
            Dim objSearchRes As System.DirectoryServices.SearchResult
            objSearcher.Filter = "(&(objectclass=person)(cn=" & id & "))"
            objSearchRes = objSearcher.FindOne
            Try
                With objSearchRes.GetDirectoryEntry.Properties
                    Dim strObjectName As String = .Item("CN").Value
                    If strObjectName.ToUpper.ToString() = id.ToUpper.ToString() Then 'CHG0339257 Check user id in both case 
                        Try
                            strUserEmail = .Item("Mail").Value
                        Catch ex As Exception
                            Return False
                        End Try
                    End If
                End With
                Try
                    objDirEnt.Dispose()
                    objDirEnt.Close()
                    objSearcher.Dispose()
                    objSearcher = Nothing
                    objSearchRes = Nothing
                    If strUserEmail = "" Then
                        Return False
                    Else
                        Return True
                    End If
                Catch ex As Exception
                    Return False
                End Try
            Catch ex As Exception
                Return False
            End Try
        End Function


 Public Function GetEmailDetailForGroup(ByVal stradspath As String) As Boolean
            Dim sPath As String = "LDAP://DC=KCC,DC=COM"
            Dim strgroupEmail As String = ""
            Dim objDirEnt As New DirectoryServices.DirectoryEntry(sPath)
            Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
            Dim objSearchRes As System.DirectoryServices.SearchResult
            objSearcher.Filter = "(&(objectClass=group) (sAMAccountName=" & stradspath & "))"
            objSearchRes = objSearcher.FindOne
            Try
                With objSearchRes.GetDirectoryEntry.Properties
                    Dim strObjectName As String = .Item("CN").Value
                    If strObjectName.ToUpper.ToString() = stradspath.ToUpper.ToString() Then  'CHG0339257 Check user id in both case 
                        Try
                            strgroupEmail = .Item("Mail").Value
                        Catch ex As Exception
                            Return False
                        End Try
                    End If
                End With
                Try
                    objDirEnt.Dispose()
                    objDirEnt.Close()
                    objSearcher.Dispose()
                    objSearcher = Nothing
                    objSearchRes = Nothing
                    If strgroupEmail = "" Then
                        Return False
                    Else
                        Return True
                    End If
                Catch ex As Exception
                    Return False
                End Try
            Catch ex As Exception
                Return False
            End Try
        End Function


End Class

End Namespace
