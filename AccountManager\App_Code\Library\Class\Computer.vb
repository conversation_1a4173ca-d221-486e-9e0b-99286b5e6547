Imports System.DirectoryServices


Namespace AccountManager

Public Class Computer

    Dim strMessage As String

    ReadOnly Property Message()
        Get
            Return strMessage
        End Get
    End Property

    Function GetSingleAsset(ByVal strFilter As String, ByVal strObjLDAP As String) As Boolean

        Dim objDirEnt As New DirectoryServices.DirectoryEntry(strObjLDAP)
        Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
        Dim objSearchRes As System.DirectoryServices.SearchResult

        Try
            Dim strResult As String
            If strFilter = "" Then
                objSearcher.Filter = ("(objectclass=computer)")
            Else
                objSearcher.Filter = "(&(objectclass=computer)(cn=" & strFilter & "))"
            End If

            objSearchRes = objSearcher.FindOne

            If objSearchRes Is Nothing Then
                strMessage = "Computer name " & strFilter & " was not found in Active Directory."
                Return False
            Else

                With objSearchRes.GetDirectoryEntry.Properties

                    strObjLDAP = objSearchRes.GetDirectoryEntry.Path 'path is the Active Directory path to that object

                End With
                objDirEnt.Close()
                Return True
            End If

        Catch ex As Exception
            Console.WriteLine("Error GetSingleAsset: " & ex.Message)
            If ex.Message.IndexOf("The server is not operational") > -1 Then
                Return False
            Else
                strMessage = ex.Message
            End If

        End Try

    End Function


End Class

End Namespace
