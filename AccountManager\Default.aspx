<%@ Page Language="vb" AutoEventWireup="false" Inherits="AccountManager._Default"
    CodeFile="Default.aspx.vb" MasterPageFile="~/MasterPage.master" %>

<%@ Register TagPrefix="uc1" TagName="Menu" Src="~/Library/UserControls/Menu.ascx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table>
        <tr>
            <td>
                <asp:Label ID="lblWelcome" runat="server" CssClass="TableHeader">
                <img src="./Library/images/AMHead.bmp"></asp:Label>
                <br>
                <br>
                <hr>
                <h3>
                    <%--<center>
                        <strong>Welcome to Account Manager v 1.7</strong></center>--%>
                </h3>
                <br>
                <h4>
                    <center>
                        This site is used to manage Kimberly-Clark's Non-User IDs.</center>
                </h4>
                <br />
                <%--<u>New Features:</u><br>--%>
                The following enhancements have been made effective 11-Dec-2020:<br>
                <ul>
                    <img src="./Library/images/blue_bullet.gif" width="8" border="0">
                   All non-user IDs that are managed by the Owner, Delegate and Backup will be added to Account Manager at creation.</ul>
                <ul>
                    <img src="./Library/images/blue_bullet.gif" width="8" border="0">
                    The ability to select multiple IDs when selecting <I>Delete Account</I> or <I>Change Ownership</I> is now available.</ul>
                <ul>
                    <img src="./Library/images/blue_bullet.gif" width="8" border="0">
                    The <I>My Accounts</I> page has been updated to include the two types of Passwords allowed and two new columns:
              <table>
                  <tr>
                      <td style="width:15px;"></td>
                      <td><img src="./Library/images/Tri_Bullet.png" width="8" border="0"> Password Change Due Date</td>
                  </tr>
                  <tr>
                      <td style="width:15px;"></td>
                      <td><img src="./Library/images/Tri_Bullet.png" width="8" border="0"> Password Type on the account</td>
                  </tr>
              </table>
                    </ul>
                <br>
                <hr>
            </td>
        </tr>
        <tr>
        </tr>
    </table>
</asp:Content>
