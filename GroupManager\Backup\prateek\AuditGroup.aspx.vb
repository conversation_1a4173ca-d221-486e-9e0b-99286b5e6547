Namespace GroupManager

Partial Class AuditGroup
    Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

    End Sub


    Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
        'CODEGEN: This method call is required by the Web Form Designer
        'Do not modify it using the code editor.
        InitializeComponent()
    End Sub

#End Region

    Dim strADSPath As String
    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'Put user code to initialize the page here
        'If Not Page.IsPostBack Then
        strADSPath = CStr(Request.QueryString("GP"))
        LoadDates(strADSPath)
        'End If
    End Sub

    Sub LoadDates(ByVal AdsPath As String)

        Dim dbGet As New GroupManager_DB
        Dim strSQL As String
        Dim srRead As SqlClient.SqlDataReader
        'Dim usrInfo As New UserInfo

        'strSQL = "pc_GMGroup_GetMgrAudit '" & AdsPath & "','" & usrInfo.GetUserID & "'"
        strSQL = "pc_GMGroup_GetMgrAudit '" & AdsPath & "','" & Session("ManagerID") & "'"

        srRead = dbGet.GetDataReaderByStringId(strSQL, [Global].gmConnectionString)

        dgAudit.DataSource = srRead
        dgAudit.DataBind()

        srRead.Close()
        srRead = Nothing

        lblGroupName.Text = "<Font color=blue>" & Replace(UCase(AdsPath), "WINNT://KCUS/", "") & "</font>"
    End Sub

    Sub UpdateDate()

        Dim strSQL As String
        'Dim usrInfo As New UserInfo
        Dim dbUPd As New GroupManager_DB

        If dgAudit.Items.Count < 5 Then
            strSQL = "pc_GMGroup_InsertAudit '" & strADSPath & "','" & Now & "','" & Session("ManagerID") & "'"
        Else
            strSQL = "pc_GMGroup_UpdateAudit '" & Now & "','" & Session("ManagerID") & "','" & dgAudit.Items(0).Cells(2).Text & "'"
        End If

        dbUPd.DBUpdByStringId(strSQL, [Global].gmConnectionString)

    End Sub

    Private Sub cmdUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdUpdate.Click
        UpdateDate()
        LoadDates(strADSPath)
    End Sub

    Private Sub cmdReturn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdReturn.Click
        Response.Redirect("MyGroups.aspx")
    End Sub
End Class

End Namespace
