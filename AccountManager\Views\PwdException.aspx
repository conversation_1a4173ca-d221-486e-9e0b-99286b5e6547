<%@ Register TagPrefix="uc1" TagName="Menu" Src="~/Library/UserControls/Menu.ascx" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Page MasterPageFile="~/MasterPage.master" Title="Password Not Required Acknowledgement" Language="VB" AutoEventWireup="false" CodeFile="PwdException.aspx.vb" Inherits="AccountManager.PwdException" %>

<%@ Register Assembly="DevExpress.Web.ASPxGridView.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>

<asp:Content ID="Content1" runat="server" ContentPlaceHolderID="ContentPlaceHolder1">
    <asp:Label ID="lblException" runat="server" Text="Password Not Required Acknowledgement"></asp:Label><br />
    <br />
    <asp:Label ID="lblMessage" runat="server" CssClass="LabelRed" Visible="False"></asp:Label><br />
    <table>
        <tbody>
            <tr>
                <td style="height: 483px; width: 599px;">
                    <asp:Panel ID="pDelete" runat="server">
                        <table class="DisplayTables">
                            <tbody>
                                <tr>
                                    <td style="width: 673px">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td colspan="2" style="width: 564px">
                                                        <table>
                                                            <tbody>
                                                                <tr>
                                                                    <td style="width: 590px">
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td style="height: 21px; width: 590px;">
                                                                        History</td>
                                                                </tr>
                                                                <tr>
                                                                    <td style="width: 590px">
                                                                        <dx:aspxgridview id="grdUsers" runat="server" autogeneratecolumns="False" cssfilepath="~/App_Themes/PlasticBlue/{0}/styles.css"
                                                                            csspostfix="PlasticBlue" width="561px">
<Styles CssPostfix="PlasticBlue" CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css">
<Header SortingImageSpacing="10px" ImageSpacing="10px"></Header>
</Styles>

<SettingsPager ShowDefaultImages="False">
<AllButton Text="All"></AllButton>

<NextPageButton Text="Next &gt;"></NextPageButton>

<PrevPageButton Text="&lt; Prev"></PrevPageButton>
</SettingsPager>

<ImagesFilterControl>
<LoadingPanel Url="~/App_Themes/PlasticBlue/Editors/Loading.gif"></LoadingPanel>
</ImagesFilterControl>

<Images SpriteCssFilePath="~/App_Themes/PlasticBlue/{0}/sprite.css">
<LoadingPanelOnStatusBar Url="~/App_Themes/PlasticBlue/GridView/gvLoadingOnStatusBar.gif"></LoadingPanelOnStatusBar>

<LoadingPanel Url="~/App_Themes/PlasticBlue/GridView/Loading.gif"></LoadingPanel>
</Images>
<Columns>
<dx:GridViewDataTextColumn FieldName="ID" Caption="ID" Visible="False" VisibleIndex="0"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="AccountID" Caption="Account ID" VisibleIndex="1"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="OwnerID" Caption="Owner" VisibleIndex="2"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="DateSigned" Caption="Date of Signing" VisibleIndex="3"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="ActDesc" Caption="Action" VisibleIndex="4"></dx:GridViewDataTextColumn>
</Columns>

<StylesEditors>
<CalendarHeader Spacing="11px"></CalendarHeader>

<ProgressBar Height="25px"></ProgressBar>
</StylesEditors>
</dx:aspxgridview>
                                                                        &nbsp;<dxwdc:ASPxLabel ID="lblNoException" runat="server" TabIndex="0" Text="No Password Exception History"
                                                                            Visible="False">
                                                                            <EditorProperties>
                                                                                <LookAndFeel>
                                                                                    <LabelStyle Font-Names="Arial Black" Font-Overline="False" ForeColor="Red" />
                                                                                </LookAndFeel>
                                                                            </EditorProperties>
                                                                        </dxwdc:ASPxLabel>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <br />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        Account ID: &nbsp;
                                                        <asp:Label ID="lblAccountID" runat="server"></asp:Label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        Short Description: &nbsp;
                                                        <asp:Label ID="lblAccountDesc" runat="server"></asp:Label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        Criteria:</td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <ul>
                                                            <li>This account is used by a process, not a person (Shared ID).</li><li>This account is not used for interactive logins.</li><li>This account has no dial up privileges.</li><li>This account is locked down to specific servers/workstations.</li><li>The password is a minimum of twelve (12) characters long.</li><li>The password contains a combination of alpha (a-z) and numeric (0-9) characters.</li><li>The purpose and password change process of the ID is documented in case the account
                                                                password
                                                                is compromised. The document also describes any dependencies that rely on this ID
                                                                to function.</li></ul>
                                                        <p>
                                                            When you click "I Agree", the Non-User account ID will be added to a security
                                                            group that will restrict the account from accessing site specific file and print server share
                                                            information (i.e., xxxxFN01\Share) and Corporate share information (i.e.,
                                                            KCFILES, EUROFILES, APFILES, SharePoint, MYSITES) and the ID will be monitored for
                                                            interactive logins.</p>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 673px">
                                        <asp:CheckBox ID="chkbxException" runat="server" />I, the Owner of the above Non-User
                                        ID, acknowledge that the account meets all requirements for the "no password change
                                        required" policy.&nbsp;<br />
                                        <br />
                                        <asp:Label ID="lblOwner" runat="server" Font-Bold="True" ForeColor="Red"></asp:Label>
                                        <asp:Label ID="lblDateSigned" runat="server" ForeColor="Navy"></asp:Label></td>
                                </tr>
                                <tr>
                                    <td style="width: 673px">
                                        <table>
                                            <tr>
                                                <td style="width: 100px; height: 30px">
                                                    <dx:aspxbutton id="cmdUpdate" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                        csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                        text="I Agree"></dx:aspxbutton>
                                                </td>
                                                <td style="width: 100px; height: 30px">
                                                    <dx:aspxbutton id="cmdDisable" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                        csspostfix="Office2003Blue" enabled="False" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                        text="I Disagree"></dx:aspxbutton>
                                                </td>
                                                <td style="width: 262px; height: 30px">
                                                    <dx:aspxbutton id="cmdReturn" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                        csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                        text="Return to My Accounts"></dx:aspxbutton>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </asp:Panel>
                </td>
            </tr>
        </tbody>
    </table>
</asp:Content>


