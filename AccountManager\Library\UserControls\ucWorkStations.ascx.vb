Namespace AccountManager

    Partial Class ucWorkStations
        Inherits System.Web.UI.UserControl

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

            'Dim strPath As String
            'strPath = LCase(Request.ServerVariables("PATH_INFO"))

            If Not Page.IsPostBack Then
                Dim hlpCheck As New Helper
                Dim usrcur As New UserInfo


                pVerify.Visible = False

                'If strPath.IndexOf("manage.aspx") > -1 Then
                If IsAdmin() Then
                    ChangeView(False, False, False, True)
                    pAdmin.Visible = True
                    pAdminInstr.Visible = True
                    cmdReturn.Visible = False
                Else
                    pAdmin.Visible = False
                    pAdminInstr.Visible = False
                    cmdReturn.Visible = True
                    Dim strUser As String

                    strUser = GetUserID()
                    If Not hlpCheck.IsOwnerOrBackup(UCase(strUser), usrcur.GetUserID) Then
                        pInfo.Visible = False
                        pStations.Visible = False
                        ChangeView(True, True, False, False)
                        lblMessage.Text = "You are not the owner of " & UCase(strUser) & "."
                        lblMessage.CssClass = "LabelRed"
                    Else
                        ChangeView(True, True, True, True)
                        LoadWorkStations(strUser)
                    End If
                End If

            End If
        End Sub

        Function GetUserID() As String

            Dim strUser As String

            If IsAdmin() Then
                strUser = txtID.Text
            Else
                strUser = Request.QueryString("AccountID")
            End If
            Return strUser

        End Function

        Function IsAdmin() As Boolean

            Dim strPath As String
            strPath = LCase(Request.ServerVariables("PATH_INFO"))

            If strPath.IndexOf("manage.aspx") > -1 Then
                IsAdmin = True
            Else
                IsAdmin = False
            End If

            Return IsAdmin

        End Function

        Sub ChangeView(ByVal blnAdd As Boolean, ByVal blnDel As Boolean, ByVal blnStations As Boolean, ByVal blnInfo As Boolean)
            pStations.Visible = blnStations
            pnlStationsTextboxes.Visible = blnAdd
            cmdAdd.Visible = blnAdd
            cmdDelete.Visible = blnDel
            pInfo.Visible = blnInfo
        End Sub

        Sub LoadWorkStations(ByVal strUserID As String)

            Dim strUser As String
            Dim strWrk As String()

            lblNewWorkstation.Text = ""
            lstWorkSt.Items.Clear()
            strUser = UCase(strUserID)
            Dim usrGet As New UserInfo(strUser)

            If usrGet.UserFound() Then
                hfAccountNonExpiry.Value = usrGet.IsAccountNeverExpiresByLDAP(usrGet.LdapPath)
                strWrk = Split(usrGet.WorkStations, ",")

                lstWorkSt.DataSource = strWrk
                lstWorkSt.DataBind()
                strUser = usrGet.GetUserID
                lblAccountID.Text = strUser
                lblWrkSt.Text = strUser & " can log on to the following computers"

                If lstWorkSt.Items(0).Value = "" Then
                    ChangeView(True, True, False, True)
                    cmdDelete.Enabled = False
                    pStations.Visible = False
                    If IsAdmin() Then
                        lblNewWorkstation.Text = "Currently, non-user account has access to all computers."
                    Else
                        lblNewWorkstation.Text = "Currently, non-user account has access to all computers. If you add a computer you will be restricting this ID to added computers." '  You will have to contact Computer Security in order to give this account access to all computers again."
                    End If
                Else
                    ChangeView(True, True, True, True)
                    cmdDelete.Enabled = True
                    pStations.Visible = True
                    Dim stationcount As Integer = strWrk.Length
                    If stationcount > 30 Then
                        stationcount = 30
                    End If
                    lstWorkSt.Rows = stationcount + 1
                End If
                If IsAdmin() Then
                    cmdGet.Text = "Reset"
                    txtID.Enabled = False
                End If
            Else
                lblNewWorkstation.Text = "The user " & strUserID & " was not found"
            End If

        End Sub

        Function WorkstationExist(ByVal strNewWrk As String) As Boolean

            Dim intIndex As Integer = 0
            WorkstationExist = False
            Do While intIndex < lstWorkSt.Items.Count

                If UCase(lstWorkSt.Items(intIndex).Value) = UCase(Trim(strNewWrk)) Then
                    WorkstationExist = True
                    Exit Do
                End If
                intIndex = intIndex + 1
            Loop

            Return WorkstationExist

        End Function

        Function WorkstationExistInAD(ByVal strNewWrk As String) As Boolean

            Dim intIndex As Integer = 0
            Dim adCheck As New Computer
            For Each strLDAP As String In [Global].GblValidLDAPForWorkStations
                WorkstationExistInAD = adCheck.GetSingleAsset(strNewWrk, strLDAP)
                If WorkstationExistInAD Then
                    Exit For
                End If
            Next
            'WorkstationExistInAD = adCheck.GetSingleAsset(strNewWrk, "LDAP://DC=kcc,DC=com")
            'If Not WorkstationExistInAD Then
            '    WorkstationExistInAD = adCheck.GetSingleAsset(strNewWrk, "LDAP://DC=Internet,DC=Kimberly-Clark,DC=com")
            'End If
            'If Not WorkstationExistInAD Then
            '    WorkstationExistInAD = adCheck.GetSingleAsset(strNewWrk, "LDAP://DC=kctest,DC=com")
            'End If

            Return WorkstationExistInAD
        End Function

        Sub AddWorksations()

            Dim strUser As String
            strUser = GetUserID()

            Dim usrUpd As New UserInfo(strUser)

            usrUpd.AddWorkstation(usrUpd.LdapPath, UCase(txtWorkstation.Text))
            lblNewWorkstation.Text = UCase(txtWorkstation.Text) & " added to user " & strUser
            LoadWorkStations(strUser)

            Dim hlpLog As New Helper
            hlpLog.InsertLog(strUser, "Workstation Added " & UCase(txtWorkstation.Text), Date.Now, txtComments.Text)
            txtWorkstation.Text = ""
            txtComments.Text = ""
        End Sub

        Sub DeleteWorkstation()

            'If lstWorkSt.Items.Count = 1 And Not IsAdmin() Then 'Also removed for CSA Team.
            Dim IsAccountNeverExpiry As Boolean = hfAccountNonExpiry.Value
            If lstWorkSt.Items.Count = 1 And IsAccountNeverExpiry Then
                lblNewWorkstation.Text = "You can not remove the last computer from this non-user account.  The non-user account must have access to at least one computer at all times."
            Else
                Dim strUser As String
                strUser = GetUserID()

                Dim strNewWorkstation, strDelStation As String
                Dim strWrk As String()
                Dim intIndex As Integer = 0
                Dim usrGet As New UserInfo(strUser)

                strDelStation = lstWorkSt.SelectedValue
                strWrk = Split(usrGet.WorkStations, ",")
                strNewWorkstation = ""

                Do While intIndex < strWrk.Length

                    If intIndex <> lstWorkSt.SelectedIndex Then
                        strNewWorkstation = UCase(strWrk(intIndex)) & "," & strNewWorkstation
                    End If

                    intIndex = intIndex + 1
                Loop

                If intIndex > 1 Then
                    If strNewWorkstation.Substring(strNewWorkstation.Length - 1, 1) = "," Then
                        strNewWorkstation = strNewWorkstation.Remove(strNewWorkstation.Length - 1, 1)
                    End If
                End If

                Dim usrUPD As New UserInfo(strUser)

                usrUPD.SetWorkstation(usrUPD.LdapPath, strNewWorkstation)

                lblNewWorkstation.Text = lstWorkSt.SelectedValue & " deleted from user " & strUser & "."
                LoadWorkStations(strUser)

                Dim hlpLog As New Helper
                hlpLog.InsertLog(strUser, "Workstation Deleted " & strDelStation, Date.Now, txtComments.Text)
                txtComments.Text = ""

            End If

        End Sub

        Private Sub cmdDelete_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmdDelete.Click

            Dim strUser As String
            strUser = GetUserID()

            If lstWorkSt.SelectedValue = "" Then
                lblNewWorkstation.Text = "You must select a workstation from the below list to delete before clicking the delete button."
            ElseIf Trim(txtComments.Text) = "" Then
                lblNewWorkstation.Text = "You must enter some comments why you are deleting this computer from user " & strUser
            Else
                DeleteWorkstation()
            End If

        End Sub

        Function VerifyWorksationFields() As Boolean

            Dim strUser As String
            strUser = GetUserID()

            VerifyWorksationFields = False
            If Trim(txtWorkstation.Text) = "" Then
                lblNewWorkstation.Text = "You must enter a workstation name first before clicking the add button"
            ElseIf Trim(txtComments.Text) = "" Then
                lblNewWorkstation.Text = "You must enter some comments why you are adding this computer for user " & strUser
            Else
                VerifyWorksationFields = True
                lblNewWorkstation.Text = ""
            End If
            Return VerifyWorksationFields
        End Function

        Private Sub cmdAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdAdd.Click

            Dim strUser As String
            strUser = GetUserID()

            If VerifyWorksationFields() Then
                If WorkstationExistInAD(txtWorkstation.Text) Then
                    If Not WorkstationExist(txtWorkstation.Text) Then
                        AddWorksations()
                    Else
                        lblNewWorkstation.Text = "User " & strUser & " already has access to " & txtWorkstation.Text & ". No action was taken."
                    End If
                Else
                    lblNoCompMessage.Text = "This computer does not exist in a supported domain currently. " & _
                                            "Do you still want to add " & txtWorkstation.Text & "?"
                    pVerify.Visible = True
                    lblNewWorkstation.Text = ""
                End If
            End If
        End Sub

        Private Sub cmdYes_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdYes.Click

            pVerify.Visible = False

            If VerifyWorksationFields() Then
                AddWorksations()
            End If

        End Sub

        Private Sub cmdNo_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdNo.Click
            pVerify.Visible = False
        End Sub

        Private Sub cmdGet_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdGet.Click
            If cmdGet.Text = "Reset" Then
                txtID.Text = ""
                txtID.Enabled = True
                cmdGet.Text = "Get User"
                pVerify.Visible = False
                ChangeView(False, False, False, True)
            Else
                Dim strUser As String
                strUser = GetUserID()

                strUser = Trim(txtID.Text)

                If strUser <> "" Then
                    LoadWorkStations(strUser)
                End If
            End If
           
        End Sub

        Private Sub txtID_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtID.TextChanged
            cmdDelete.Visible = False
        End Sub

        Private Sub cmdReturn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdReturn.Click
            Response.Redirect("/AccountManager/MyUsers.aspx")
        End Sub
    End Class

End Namespace
