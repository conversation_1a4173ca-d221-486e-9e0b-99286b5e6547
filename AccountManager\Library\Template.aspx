﻿<%@ Register TagPrefix="uc1" TagName="Menu" src="~/Library/UserControls/Menu.ascx" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Page Language="vb" AutoEventWireup="false" Inherits="AccountManager.Template" CodeFile="Template.aspx.vb" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD html 4.0 Transitional//EN">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head runat="server">
		<title>TEMPLATE</title>
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR"/>
		<meta content="Visual Basic .NET 7.1" name="CODE_LANGUAGE"/>
		<meta content="JavaScript" name="vs_defaultClientScript"/>
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema"/>
		<!-- REPLACED VIRTUAL PATH HERE - TESTING --><link href="/Styles.css" type="text/css" rel="stylesheet"/>
	</head>
	<body>
		<form id="frmRequests" method="post" runat="server">
			<table cellspacing="0" cellpadding="0" border="0">
				<tr>
					<td valign="top" width="150"><uc1:menu id="Menu1" runat="server"></uc1:menu></td>
					<td valign="top"><br/>
						<br/>
						<br/>
						<asp:label id="lblWelcome" CssClass="TableHeader" Runat="server">Accounts that are not in AccountManager</asp:label><br/>
						<br/>
						<asp:label id="lblMessage" CssClass="LabelRed" Runat="server"></asp:label><br/>
						<table>
							<tr>
								<td>
									<table class="DisplayTables">
										<tr>
											<td>Select Action:&nbsp;
												<asp:radiobutton id="rdoNewOwner" Runat="server" Text="Add New Owner" Checked="True" AutoPostBack="False"
													GroupName="rdoActions"></asp:radiobutton>&nbsp;
												<asp:radiobutton id="rdoUpdOwner" Runat="server" Text="Update Owners" Checked="True" AutoPostBack="False"
													GroupName="rdoActions"></asp:radiobutton>&nbsp;
												<asp:radiobutton id="rdoDelete" Runat="server" Text="Delete" AutoPostBack="False" GroupName="rdoActions"></asp:radiobutton>&nbsp;
												<asp:radiobutton id="rdoNewAccounts" Runat="server" Text="Show Orphaned Accounts" AutoPostBack="False"
													GroupName="rdoActions"></asp:radiobutton>&nbsp;
												<asp:radiobutton id="rdoAddNew" Runat="server" Text="Add New Account" AutoPostBack="False" GroupName="rdoActions"></asp:radiobutton></td>
										</tr>
									</table>
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
		</form>
	</body>
</html>
