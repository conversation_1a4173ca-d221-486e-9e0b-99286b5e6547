Imports DevExpress.Web.ASPxGrid
Imports System.Data

Namespace AccountManager

Partial Class ucOrphanAccounts
    Inherits System.Web.UI.UserControl

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

    End Sub
    Protected WithEvents lblOwnType As System.Web.UI.WebControls.Label
    Protected WithEvents ddlOwnType As System.Web.UI.WebControls.DropDownList
    Protected WithEvents chkExcept As System.Web.UI.WebControls.CheckBox
    Protected WithEvents cmdInsert As DevExpress.Web.ASPxDataControls.ASPxButton
    Protected WithEvents lblOwnerDN As DevExpress.Web.ASPxDataControls.ASPxLabel
    Protected WithEvents txtOwnerID As DevExpress.Web.ASPxDataControls.ASPxTextBox
    Protected WithEvents txtDesc As DevExpress.Web.ASPxDataControls.ASPxTextBox


    Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
        'CODEGEN: This method call is required by the Web Form Designer
        'Do not modify it using the code editor.
        InitializeComponent()
    End Sub

#End Region

    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        FindNewAccounts()
        LoadInstruction()
    End Sub

    Sub LoadInstruction()

            lblInstructions.Text = "" & _
                "Step 1: Select 'Add Account' and hit continue to add any of the below accounts.<br/>"

    End Sub

    Sub FindNewAccounts()

        Dim strSQL As String
        Dim dbGet As New DataAccess
        Dim srRead As SqlClient.SqlDataReader

        strSQL = "SELECT * FROM tbUMRequests WHERE Status = 'New Account' Order By AccountID"
        srRead = dbGet.GetDataReaderByStringId(strSQL)

        If srRead.HasRows Then
                grdUsers.KeyFieldName = "AccountID"
                grdUsers.DataSource = srRead
                grdUsers.DataBind()
                grdUsers.ExpandAll()
                grdUsers.Visible = True
            Else
                grdUsers.Visible = False
                lblMessage.Text = "No account found."
                '    EnableControls(False)
        End If

        srRead.Close()
        srRead = Nothing
        dbGet.CloseConnections()

    End Sub

    End Class

End Namespace
