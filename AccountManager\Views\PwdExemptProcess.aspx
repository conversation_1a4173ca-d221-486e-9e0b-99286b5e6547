﻿<%@ Page Title="" Language="VB" MasterPageFile="~/MasterPage.master" AutoEventWireup="false" CodeFile="PwdExemptProcess.aspx.vb" Inherits="AccountManager.PwdExemptProcess" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
    <style>
        .tablepadding td{
            padding:4px 0 4px 0;
        }
        
         .tablepaddingWithRight td{
            padding:4px 15px 4px 0;
        }
    </style>
    <table class="DisplayTables">
        <tr>
            <td><asp:Label ID="lblWelcome" runat="server" CssClass="TableHeader">Password Exempt Process</asp:Label><br /><asp:Label ID="lblheaderDescription" runat="server" ForeColor="Red" CssClass="TableHeader">This process only applies to accounts that are used in a Manufacturing Facility that meet the following criteria.</asp:Label></td>
        </tr>
        <tr>
            <td>
               
                <asp:MultiView ID="MVMain" runat="server">
                    <asp:View ID="View1" runat="server">
                       <table>
                           <tr><td>
                               <asp:Label ID="lblMainMessage" runat="server" CssClass="LabelRed"></asp:Label>
                               </td></tr>

                       </table>
                    </asp:View>
                    <asp:View ID="View2" runat="server">
                         <table class="tablepadding">
                             <colgroup>
                                <col style="width:500px;" />
                                 <col style="width:auto;" />
                             </colgroup>
                            <tr>
            <td colspan="2">Account ID: <asp:label  runat="server" ID="lblActID" CssClass="LabelNormal"></asp:label></td> 
        </tr>
        <tr>
            <td colspan="2">Description: <asp:label runat="server" ID="lblActDesc" CssClass="LabelNormal"></asp:label>
            </td>
        </tr>
        <%--<tr>
            <td colspan="2">Account Type: <asp:label runat="server" ID="lblActType"></asp:label>
            </td>
        </tr>--%>
                           
        <tr>
            <td>Q1 - Is this account used in a manufacturing application that is classified as Internal Use Only?</td> <td>
                <asp:radiobuttonlist runat="server" RepeatDirection="Horizontal" ID="rdoANS1">
                    <asp:ListItem Value="Y">YES</asp:ListItem>
                    <asp:ListItem Value="N" Selected="True">NO</asp:ListItem>
                </asp:radiobuttonlist> 
            </td>
              </tr>
        <tr>
            <td>Q2 – Is this account associated with a system that uses two factor authentication?</td> <td><asp:radiobuttonlist runat="server" RepeatDirection="Horizontal" ID="rdoANS2">
                <asp:ListItem Value="Y">YES</asp:ListItem>
                    <asp:ListItem Value="N" Selected="True">NO</asp:ListItem>
                            </asp:radiobuttonlist> </td>
        </tr>
        <tr>
            <td >Q3 - Is this account used by runtime software to ‘ONLY’ control the immediate functionality of the machine AND interactive access is limited to “basic functions” that do not expose restricted information or could cause harm to personal or Company information (i.e. Start/Stop/Monitor)?</td> <td ><asp:radiobuttonlist runat="server" RepeatDirection="Horizontal" ID="rdoANS3">
                <asp:ListItem Value="Y">YES</asp:ListItem>
                    <asp:ListItem Value="N" Selected="True">NO</asp:ListItem>
                            </asp:radiobuttonlist> </td>
        </tr>
        <tr>
            <td>Purpose: Include info about software used with this account (mfg/software name, where it is used, etc.)<asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtPurpose" Display="None" ErrorMessage="Please enter purpose." SetFocusOnError="True" ValidationGroup="a"> </asp:RequiredFieldValidator>
            </td><td>
                <asp:textbox runat="server" MaxLength="200" TextMode="MultiLine" ID="txtPurpose"></asp:textbox>
            </td>
        </tr>
        
        <tr>
            <td></td><td>
                <table>
                                       <tr>
                                           <td><dx:ASPxButton ID="btnSubmit" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                        CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                                        Text="Submit" ValidationGroup="a">
                                                                    </dx:ASPxButton></td>
                                           <td><dx:ASPxButton ID="btnCancel" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                        CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                                        Text="Cancel">
                                                                    </dx:ASPxButton></td>
                                       </tr>
                                   </table>
                 
                <%--<asp:button runat="server" text="Submit" ID="btnSubmit" ValidationGroup="a"  />--%>
                <%--<asp:button runat="server" text="Cancel" ID="btnCancel" />--%>
            </td>
        </tr>
        <tr><td colspan="2">
            <asp:label runat="server" ID="lblsubMsg"></asp:label>
            <asp:ValidationSummary ID="ValidationSummary1" runat="server" ValidationGroup="a" />
        </td></tr>
                        </table>
                    </asp:View>
                    <asp:View ID="View3" runat="server">
                       <table class="tablepaddingWithRight">
                          
                           <%--<tr><td colspan="2">
                               <asp:Label ID="lblfinishMessage" runat="server" CssClass="LabelRed"></asp:Label>
                               </td></tr>--%>
                           <tr><td colspan="2">
                               <asp:Label ID="lblPwdExempt" runat="server" ></asp:Label>
                               </td></tr>
                           <tr>
            <td>Account ID:</td> <td>
                <asp:label runat="server" ID="lblFActID"></asp:label>
                               <asp:HiddenField ID="hfPwdExemptID" runat="server" />
            </td>
        </tr>
        <tr>
            <td>Description:</td> <td>
                <asp:label runat="server" ID="lblFActDesc"></asp:label>
            </td>
        </tr>
       <%-- <tr>
            <td>Password Type:</td> <td>
                <asp:label runat="server" ID="lblFActType"></asp:label>
            </td>
        </tr>--%>
                           <%--<tr>
                               <td>Is Account Password Exempt?</td>
                               <td>
                                   <asp:Label ID="lblFIsPwdExempt" runat="server"></asp:Label>
                               </td>
                           </tr>--%>
                           <tr>
                               <td>Purpose:</td>
                               <td>
                                   <asp:Label ID="lblFPurpose" runat="server"></asp:Label>
                               </td>
                           </tr>
                           <tr>
                               <td>Team Review Status:</td>
                               <td>
                                   <asp:Label Visible="false" ID="lblFIsPwdExempt" runat="server"></asp:Label>
                                   <asp:Label ID="lblFStatus" runat="server"></asp:Label>
                               </td>
                           </tr>
                           <tr>
                               <td>&nbsp;</td>
                               <td>
                                   <dx:ASPxButton ID="btnEdit" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css" CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css" Text="Edit">
                                   </dx:ASPxButton>
                                   <dx:ASPxButton ID="btnRemove" runat="server"  CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css" CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css" Text="Remove Account from Password Exempt">
                                       <ClientSideEvents Click="function(s, e) {
    e.processOnServer = confirm('Are you sure you want to remove this Account from Password Exempt?');
  }" />
                                   </dx:ASPxButton>
                                   <%--<asp:Button ID="btnEdit" runat="server" text="Edit" />--%><%--<asp:Button ID="btnBack" runat="server" text="Back" />--%></td>
                           </tr>
                       </table>
                    </asp:View>
                </asp:MultiView>
            </td>
        </tr>
        <tr>
            <td>
                <dx:ASPxButton ID="btnBack" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                        CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                                        Text="Return to My Accounts" Width="173px">
                                                                    </dx:ASPxButton>
            </td>
        </tr>
        
    </table>
    <br />
</asp:Content>

