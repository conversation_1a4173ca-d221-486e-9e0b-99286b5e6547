<%@ Page Trace="False" Language="vb" AutoEventWireup="false" Inherits="GroupManager.AuditGroup" CodeFile="AuditGroup.aspx.vb" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<title>Audit Group</title>
		<meta content="Microsoft Visual Studio.NET 7.0" name="GENERATOR"/>
		<meta content="Visual Basic 7.0" name="CODE_LANGUAGE"/>
		<meta content="JavaScript" name="vs_defaultClientScript"/>
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema"/>
		<link href="Styles.css" type="text/css" rel="stylesheet"/>
	</head>
	<body><!--onload="SetFocus();"-->
		<form id="frmAuditGroup" runat="server">
			<script language="JavaScript" src="../Library/scripts/NoRightClick.js"></script>
			<!-- #include File="sideMenuNet.asp" -->
			<h4>Audit History for the Group
				<asp:label id="lblGroupName" Runat="server"></asp:label></h4>
			<hr/>
			<table>
				<tr>
					<td><asp:datagrid id="dgAudit" Runat="server" AllowPaging="False" AutoGenerateColumns="False" BorderColor="#CCCCCC"
							BorderStyle="Outset" BorderWidth="4" BackColor="White" CellPadding="5">
							<SelectedItemStyle Font-Bold="True" ForeColor="White" BackColor="#669999"></SelectedItemStyle>
							<ItemStyle Font-Size="68.75%" Font-Names="Arial" ForeColor="#000066" BorderWidth="1"></ItemStyle>
							<HeaderStyle Font-Size="68.75%" Font-Names="Arial" Font-Bold="True" ForeColor="White" BackColor="#006699"></HeaderStyle>
							<FooterStyle Font-Size="68.75%" Font-Names="Arial" ForeColor="#000066" BackColor="White"></FooterStyle>
							<Columns>
								<asp:BoundColumn DataField="DateReviewed" HeaderText="Date Reviewed"></asp:BoundColumn>
								<asp:BoundColumn DataField="BID" HeaderText="Reviewed By"></asp:BoundColumn>
								<asp:BoundColumn DataField="ID" Visible="false" Headertext="ID"></asp:BoundColumn>
							</Columns>
						</asp:datagrid></td>
				</tr>
				<tr>
					<td><asp:button id="cmdUpdate" Runat="server" Text="Update Review Date" CssClass="Buttons"></asp:button>
						&nbsp;
						<asp:button id="cmdReturn" CssClass="Buttons" Text="View all Groups" Runat="server"></asp:button></td>
				</tr>
			</table>
		</form>
		<!--#include virtual="~/Library/Visual/Footer/defaultfooter_new.htm"--> 
		
	</body>
</html>
