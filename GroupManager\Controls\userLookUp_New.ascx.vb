Namespace GroupManager

Partial Class userLookUp_New
    Inherits System.Web.UI.UserControl

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

    End Sub


    Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
        'CODEGEN: This method call is required by the Web Form Designer
        'Do not modify it using the code editor.
        InitializeComponent()
    End Sub

#End Region

    Private m_LeftPos As String = "250"
    Private m_TopPos As String = "200"
#Region "Properties"
    Public Property UserID() As String
        Get
            Return TextBoxUserID.Text.Trim()
        End Get
        Set(ByVal value As String)
            TextBoxUserID.Text = value
        End Set
    End Property

    Public ReadOnly Property UserName() As String
        Get
            Return LabelUserName.Text.Trim()
        End Get
    End Property

    Public WriteOnly Property ShowUserNameLabel() As Boolean
        Set(ByVal value As Boolean)
            LabelUserName.Visible = value
        End Set
    End Property

    Public WriteOnly Property EnableRequiredFieldValidatorUserID() As Boolean
        Set(ByVal Value As Boolean)
            RequiredFieldValidatorUserID.Enabled = Value
            RequiredFieldValidatorUserID.Visible = Value
        End Set
    End Property

    Public WriteOnly Property LeftPos() As String
        Set(ByVal value As String)
            m_LeftPos = value
        End Set
    End Property

    Public WriteOnly Property TopPos() As String
        Set(ByVal Value As String)
            m_TopPos = Value
        End Set
    End Property

    Public WriteOnly Property EnableRegExValidatorUserID() As Boolean
        Set(ByVal Value As Boolean)
            RegularExpressionValidatorUserID.Enabled = Value
            RegularExpressionValidatorUserID.Visible = Value
        End Set
    End Property
#End Region

    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'Put user code to initialize the page here
        LinkButtonUserID.Attributes.Add("onClick", "return openWindow('" & Me.ClientID & "', '" & m_LeftPos & "', '" & m_TopPos & "');")
    End Sub

    Public Sub ClearText_UserID()
        'Clears the UserID text box
        TextBoxUserID.Text = ""
    End Sub

End Class

End Namespace
