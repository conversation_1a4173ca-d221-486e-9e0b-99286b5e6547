Imports System.Data.SqlClient
Imports System.Data

Namespace AccountManager

    Public Class DataAccess
        Public Shared dtPwdPolicyMessage As DataTable

        Dim myConnection As SqlConnection
        Dim myCommand As SqlCommand
        Public IsError As Boolean
        Public ErrorMsg As String


        Public Shared Function GetPwdPolicyMessage(Optional ByVal IsRefresh As Boolean = False) As DataTable

            If dtPwdPolicyMessage Is Nothing Or IsRefresh Then
                Dim MyCommand As SqlDataAdapter
                MyCommand = New SqlDataAdapter("Select * from tbUMAccountType", [Global].GblUserConn)
                MyCommand.SelectCommand.CommandType = CommandType.Text
                MyCommand.SelectCommand.Connection.Open()
                MyCommand.SelectCommand.ExecuteNonQuery()
                dtPwdPolicyMessage = New DataTable("PwdPolicy")
                MyCommand.Fill(dtPwdPolicyMessage)
                MyCommand.SelectCommand.Connection.Close()
                MyCommand.Dispose()
            End If

            Return dtPwdPolicyMessage

        End Function

        Sub CloseConnections()
            Try
            Finally
                myConnection.Close()
                myCommand.Connection.Close()
                myConnection = Nothing
                myCommand = Nothing
            End Try
        End Sub

        'Title: GetDataSetByID
        'Created by: Shane Z Smith on 5/2/02
        'Purpose:  Get a dataset
        'Assumptions:  None
        'Effects:  Runs Stored Procedure
        'Inputs:  Stored Procedure and an ID
        'Returns:  DataSet
        Function GetDataSetByID(ByVal SQLText As String) As DataSet

            'Dim dr As SqlDataReader
            Dim DS As DataSet
            Dim MyCommand As SqlDataAdapter

            myConnection = New SqlConnection([Global].GblUserConn)
            MyCommand = New SqlDataAdapter(SQLText, myConnection)

            MyCommand.SelectCommand.Connection.Open()
            MyCommand.SelectCommand.ExecuteNonQuery()

            DS = New DataSet
            MyCommand.Fill(DS, "ServerList")

            MyCommand.SelectCommand.Connection.Close()

            Return DS

        End Function

        Function UpdateDBByStringId(ByVal SQLText As String)

            myConnection = New SqlConnection([Global].GblUserConn)
            myCommand = New SqlCommand(SQLText, myConnection)
            myCommand.CommandType = CommandType.Text

            myConnection.Open()
            myCommand.ExecuteReader()
            myConnection.Close()
            myCommand.Connection.Close()
            myConnection.Close()



        End Function

        Function CallStoredProcedurewithScalar(ByVal SQLText As String, ByRef strRet As String) As Boolean
            Dim myConn As New SqlConnection([Global].GblUserConn)
            Dim mycommand As New SqlCommand()
            Dim transaction As SqlTransaction
            Dim rowAffected As Integer = 0
            Dim retvalue As Boolean = False
            IsError = False
            ErrorMsg = ""
            strRet = ""
            Try
                myConn.Open()
                transaction = myConn.BeginTransaction
                mycommand.Transaction = transaction
                mycommand.CommandType = CommandType.Text
                mycommand.Connection = myConn
                mycommand.CommandText = SQLText
                strRet = mycommand.ExecuteScalar()
                transaction.Commit()
                retvalue = True
            Catch ex As Exception
                transaction.Rollback()
                IsError = True
                ErrorMsg = ex.Message.ToString
            Finally
                mycommand.Dispose()
                myConn.Close()
            End Try
            Return retvalue
        End Function

        Function GetDataReaderByStringId(ByVal SQLText As String, Optional ByVal StrConn As String = [Global].GblUserConn) As SqlDataReader

            Dim dr As SqlDataReader

            myConnection = New SqlConnection(StrConn)
            myCommand = New SqlCommand(SQLText, myConnection)
            myCommand.CommandType = CommandType.Text

            myConnection.Open()
            dr = myCommand.ExecuteReader(CommandBehavior.CloseConnection)

            Return dr

        End Function
        Function GetDataReaderByStringId1(ByVal SQLText As String, Optional ByVal StrConn As String = [Global].GblSauConn) As SqlDataReader

            Dim sqlr As SqlDataReader

            myConnection = New SqlConnection(StrConn)
            myCommand = New SqlCommand(SQLText, myConnection)
            myCommand.CommandType = CommandType.Text

            myConnection.Open()
            sqlr = myCommand.ExecuteReader(CommandBehavior.CloseConnection)


            Return sqlr

        End Function

        Function GetDataTable(ByVal SQLText As String, Optional ByVal StrConn As String = [Global].GblUserConn) As DataTable

            Dim MyCommand As SqlDataAdapter
            Dim DS As DataTable

            myConnection = New SqlConnection(StrConn)
            MyCommand = New SqlDataAdapter(SQLText, myConnection)

            MyCommand.SelectCommand.Connection.Open()
            MyCommand.SelectCommand.ExecuteNonQuery()

            DS = New DataTable("UserList")
            MyCommand.Fill(DS)
            MyCommand.SelectCommand.Connection.Close()

            DS.Columns.Add("CHKLFLAG")
            DS.AcceptChanges()

            MyCommand.Dispose()
            myConnection.Close()
            myConnection.Dispose()

            Return DS

        End Function

        Function GetExceptionDataTable(ByVal SQLText As String, Optional ByVal StrConn As String = [Global].GblUserConn) As DataTable

            Dim MyCommand As SqlDataAdapter
            Dim DS As DataTable

            myConnection = New SqlConnection(StrConn)
            MyCommand = New SqlDataAdapter(SQLText, myConnection)

            MyCommand.SelectCommand.Connection.Open()
            MyCommand.SelectCommand.ExecuteNonQuery()

            DS = New DataTable("ExceptionList")
            MyCommand.Fill(DS)
            MyCommand.SelectCommand.Connection.Close()

            MyCommand.Dispose()
            myConnection.Close()
            myConnection.Dispose()

            Return DS

        End Function

        Function GetBackupByAccountID(ByVal SQLText As String, Optional ByVal StrConn As String = [Global].GblUserConn) As DataTable

            Dim MyCommand As SqlDataAdapter
            Dim DS As DataTable

            myConnection = New SqlConnection(StrConn)
            MyCommand = New SqlDataAdapter(SQLText, myConnection)

            MyCommand.SelectCommand.Connection.Open()
            MyCommand.SelectCommand.ExecuteNonQuery()

            DS = New DataTable("UserBackup")
            MyCommand.Fill(DS)
            MyCommand.SelectCommand.Connection.Close()

            MyCommand.Dispose()
            myConnection.Close()
            myConnection.Dispose()

            Return DS

        End Function

        Public Shared Function getManager() As String
            Dim strLogonUser As String = System.Web.HttpContext.Current.Request.ServerVariables("LOGON_USER")
            Dim aryLogonUser() As String = strLogonUser.Split("\")
            Return aryLogonUser(1)
        End Function

        Function CheckTypeforLoginID_UserID(ByVal strAccountID As String, ByVal strUserID As String, ByVal strLoginID As String) As DataTable

            Dim MyCommand As SqlDataAdapter
            Dim DS As DataTable

            MyCommand = New SqlDataAdapter("SP_UMOwners_Check_ODB", [Global].GblUserConn)
            MyCommand.SelectCommand.CommandType = CommandType.StoredProcedure
            MyCommand.SelectCommand.Parameters.AddWithValue("@AccountID", strAccountID)
            MyCommand.SelectCommand.Parameters.AddWithValue("@UserID", strUserID)
            MyCommand.SelectCommand.Parameters.AddWithValue("@LoginID", strLoginID)

            MyCommand.SelectCommand.Connection.Open()
            MyCommand.SelectCommand.ExecuteNonQuery()
            'select ISNULL(@UserType,0) as UserType, ISNULL(@LoginType,0) as LoginType,@CountsRow as CountsRow
            DS = New DataTable("AccessDetails")
            MyCommand.Fill(DS)
            MyCommand.SelectCommand.Connection.Close()

            MyCommand.Dispose()

            Return DS

        End Function

        Function GetOwnershipChangeRequests(ByVal strAccountIDs As String) As DataTable

            Dim MyCommand As SqlDataAdapter
            Dim DS As DataTable
            Dim strQuery As String = "Select *,NewOwner + ' ('+NewOwnerDN+')' As NewOwnerDet,OldOwner + ' (' + OldOwnerDN + ')' as RequestedBy,'Pending' as StatusShow from tbUMRequests where RequestType='Change Owner' AND Status='New' AND AccountID IN ({0})"
            strQuery = String.Format(strQuery, strAccountIDs)
            MyCommand = New SqlDataAdapter(strQuery, [Global].GblUserConn)
            MyCommand.SelectCommand.CommandType = CommandType.Text
            'MyCommand.SelectCommand.Parameters.AddWithValue("@AccountIDs", strAccountIDs)

            MyCommand.SelectCommand.Connection.Open()
            MyCommand.SelectCommand.ExecuteNonQuery()

            DS = New DataTable("OwnershipRequests")
            MyCommand.Fill(DS)
            MyCommand.SelectCommand.Connection.Close()

            MyCommand.Dispose()

            Return DS

        End Function

        
        Function InsertOrUpdatePwdExempt(ByVal AccountID As String, ByVal IsPwdExempt As Boolean, ByVal strAns As String, ByVal strPurpose As String, ByVal intTicketEntryID As Integer, ByVal strManager As String) As Boolean
            Dim myConn As New SqlConnection([Global].GblUserConn)
            Dim mycommand As New SqlCommand()
            Dim transaction As SqlTransaction
            Dim rowAffected As Integer = 0
            Dim retvalue As Boolean = False
            IsError = False
            ErrorMsg = ""
            Try
                myConn.Open()
                transaction = myConn.BeginTransaction
                mycommand.Transaction = transaction
                mycommand.CommandType = CommandType.Text
                mycommand.Connection = myConn

                mycommand.CommandText = "Update tbUMPwdExempt Set PwdExemptRowStatus='Deleted' where PwdExemptAccountID=@AccountID; insert into tbUMPwdExempt (PwdExemptAccountID,PwdExemptIsPwdExempt,PwdExemptAnswers,PwdExemptPurpose,PwdExemptBy,PwdExemptOn,PwdExemptStatus,PwdExemptTicketID) values (@AccountID,@IsPwdExempt,@PwdExemptAnswers,@PwdExemptPurpose,@PwdExemptBy,GETDATE(),@PwdExemptStatus,@PwdExemptTicketID);IF (@IsPwdExempt=0) BEGIN Update tbUMUsers Set IsPwdExempt=0 where AccountID=@AccountID; END; Insert into tbUMLog values (@PwdExemptBy,@AccountID,'Password Exempt Process: ' + Cast(@IsPwdExempt as varchar),GETDATE(),'Policy Response: '+ @PwdExemptAnswers + ' Purpose: ' + @PwdExemptPurpose);"
                mycommand.Parameters.AddWithValue("@AccountID", AccountID)
                mycommand.Parameters.AddWithValue("@IsPwdExempt", IsPwdExempt)
                mycommand.Parameters.AddWithValue("@PwdExemptAnswers", strAns)
                mycommand.Parameters.AddWithValue("@PwdExemptPurpose", strPurpose)
                mycommand.Parameters.AddWithValue("@PwdExemptBy", strManager)
                If IsPwdExempt Then
                    mycommand.Parameters.AddWithValue("@PwdExemptStatus", "Pending")
                Else
                    mycommand.Parameters.AddWithValue("@PwdExemptStatus", "Not Qualified")
                End If
                mycommand.Parameters.AddWithValue("@PwdExemptTicketID", intTicketEntryID)

                rowAffected = mycommand.ExecuteNonQuery()
                transaction.Commit()
                retvalue = True
            Catch ex As Exception
                transaction.Rollback()
                IsError = True
                ErrorMsg = ex.Message.ToString
            Finally
                mycommand.Dispose()
                myConn.Close()
            End Try
            Return retvalue
        End Function

        Function RemovePwdExempt(ByVal AccountID As String, ByVal PwdExemptID As Integer, ByVal strManager As String) As Boolean
            Dim myConn As New SqlConnection([Global].GblUserConn)
            Dim mycommand As New SqlCommand()
            Dim transaction As SqlTransaction
            Dim rowAffected As Integer = 0
            Dim retvalue As Boolean = False
            IsError = False
            ErrorMsg = ""
            Try
                myConn.Open()
                transaction = myConn.BeginTransaction
                mycommand.Transaction = transaction
                mycommand.CommandType = CommandType.Text
                mycommand.Connection = myConn

                mycommand.CommandText = "Update tbumPwdexempt Set PwdExemptStatus='Removed',PwdExemptBy=PwdExemptBy + ',' + @PwdExemptBy where PwdExemptID=@PwdExemptID;Update tbUMUsers Set IsPwdExempt=0 where AccountID=@AccountID;Insert into tbUMLog values (@PwdExemptBy,@AccountID,'Removed Password Exempt',GETDATE(),'Removed Password Exempt');"
                mycommand.Parameters.AddWithValue("@AccountID", AccountID)
                mycommand.Parameters.AddWithValue("@PwdExemptID", PwdExemptID)
                mycommand.Parameters.AddWithValue("@PwdExemptBy", strManager)
                rowAffected = mycommand.ExecuteNonQuery()
                transaction.Commit()
                retvalue = True
            Catch ex As Exception
                transaction.Rollback()
                IsError = True
                ErrorMsg = ex.Message.ToString
            Finally
                mycommand.Dispose()
                myConn.Close()
            End Try
            Return retvalue
        End Function

        
        Function ReviewPwdExempt(ByVal AccountID As String, ByVal PwdExemptID As Integer, ByVal PwdExemptStatus As String, ByVal PwdExemptRITM As String, ByVal PwdExemptComments As String, ByVal strManager As String) As Boolean
            Dim myConn As New SqlConnection([Global].GblUserConn)
            Dim mycommand As New SqlCommand()
            Dim transaction As SqlTransaction
            Dim rowAffected As Integer = 0
            Dim retvalue As Boolean = False
            IsError = False
            ErrorMsg = ""
            Try
                myConn.Open()
                transaction = myConn.BeginTransaction
                mycommand.Transaction = transaction
                mycommand.CommandType = CommandType.Text
                mycommand.Connection = myConn

                mycommand.CommandText = "Update tbumPwdexempt Set PwdExemptStatus=@PwdExemptStatus,PwdExemptRITM=@PwdExemptRITM,PwdExemptComments=@PwdExemptComments,PwdExemptTeamReviewedOn=GetDate(),PwdExemptTeamReviewedBy=@PwdExemptTeamReviewedBy where PwdExemptID=@PwdExemptID; Update tbUMUsers Set IsPwdExempt=Case When @PwdExemptStatus='Approved' Then 1 else 0 End where AccountID=@AccountID;"
                mycommand.Parameters.AddWithValue("@AccountID", AccountID)
                mycommand.Parameters.AddWithValue("@PwdExemptID", PwdExemptID)
                mycommand.Parameters.AddWithValue("@PwdExemptStatus", PwdExemptStatus)
                mycommand.Parameters.AddWithValue("@PwdExemptRITM", PwdExemptRITM)
                mycommand.Parameters.AddWithValue("@PwdExemptComments", PwdExemptComments)
                mycommand.Parameters.AddWithValue("@PwdExemptTeamReviewedBy", strManager)
                rowAffected = mycommand.ExecuteNonQuery()
                transaction.Commit()
                retvalue = True
            Catch ex As Exception
                transaction.Rollback()
                IsError = True
                ErrorMsg = ex.Message.ToString
            Finally
                mycommand.Dispose()
                myConn.Close()
            End Try
            Return retvalue
        End Function

        Public Function insertTicketRequest(strShortDescription As String, strDetails As String, strReqID As String, strDynamicShortDescription As String) As Integer
            Dim intID As Integer = 0
            Dim myConn As New SqlConnection([Global].GblGCAConn)
            Dim mycommand As New SqlCommand
            Try
                strReqID = strReqID.ToUpper.TrimEnd("M", "A")
                myConn.Open()
                mycommand.CommandText = "[dbo].[InsertTicketDetails]"
                mycommand.CommandType = CommandType.StoredProcedure
                mycommand.Connection = myConn
                mycommand.Parameters.AddWithValue("@Details", strDetails)
                mycommand.Parameters.AddWithValue("@ShortDescription", strShortDescription)
                mycommand.Parameters.AddWithValue("@RequestorID", strReqID)
                mycommand.Parameters.AddWithValue("@DynamicShortDescription", strDynamicShortDescription)
                mycommand.Parameters.Add("@ID", SqlDbType.Int)
                mycommand.Parameters("@ID").Direction = ParameterDirection.Output
                mycommand.ExecuteNonQuery()
                Dim Id As String = mycommand.Parameters("@ID").Value.ToString
                intID = CType(Id, Integer)
            Catch ex As Exception
                intID = -1
                Throw ex
            Finally
                mycommand.Dispose()
                myConn.Close()
            End Try
            Return intID
        End Function

        Function AddUMUser(ByVal AccountID As String, ByVal strDescription As String, ByVal strAppName As String, ByVal strPurpose As String, ByVal dtExpires As DateTime, ByVal IsExpirationDate As Boolean, ByVal blnDisabled As Boolean, ByVal IsExpired As Boolean, ByVal strManager As String, ByVal OwnerId As String, ByVal OwnerName As String, ByVal DelegateId As String, ByVal DelegateName As String) As Boolean
            Dim myConn As New SqlConnection([Global].GblUserConn)
            Dim mycommand As New SqlCommand()
            Dim transaction As SqlTransaction
            Dim rowAffected As Integer = 0
            Dim retvalue As Boolean = False
            IsError = False
            ErrorMsg = ""
            Try
                myConn.Open()
                transaction = myConn.BeginTransaction
                mycommand.CommandType = CommandType.StoredProcedure
                mycommand.Connection = myConn
                mycommand.Transaction = transaction
                mycommand.CommandText = "sp_UMUser_AddWithLog"
                mycommand.Parameters.AddWithValue("@AccountID", AccountID)
                mycommand.Parameters.AddWithValue("@Description", strDescription)
                mycommand.Parameters.AddWithValue("@AppName", strAppName)
                mycommand.Parameters.AddWithValue("@Purpose", strPurpose)
                mycommand.Parameters.AddWithValue("@UsrLocation", "KCUS")
                mycommand.Parameters.AddWithValue("@DataClassification", "LOW")
                mycommand.Parameters.AddWithValue("@ExpirationDate", dtExpires)
                mycommand.Parameters.AddWithValue("@IsExpirationDate", IsExpirationDate)
                mycommand.Parameters.AddWithValue("@ActDisabled", blnDisabled)
                mycommand.Parameters.AddWithValue("@IsExpired", IsExpired)
                mycommand.Parameters.AddWithValue("@OwnerId", OwnerId)
                mycommand.Parameters.AddWithValue("@OwnerName", OwnerName)
                mycommand.Parameters.AddWithValue("@DelegateId", DelegateId)
                mycommand.Parameters.AddWithValue("@DelegateName", DelegateName)
                mycommand.Parameters.AddWithValue("@Manager", strManager)
                rowAffected = mycommand.ExecuteNonQuery()
                transaction.Commit()
                retvalue = True
            Catch ex As Exception
                transaction.Rollback()
                IsError = True
                ErrorMsg = ex.Message.ToString
            Finally
                mycommand.Dispose()
                myConn.Close()
            End Try
            Return retvalue
        End Function



        Function AddUMOwnerDelegateBackup(ByVal AccountID As String, ByVal UserID As String, ByVal UserDisplayName As String, ByVal Type As Integer, ByVal strManager As String, Optional ByVal strComments As String = "") As Boolean
            Dim myConn As New SqlConnection([Global].GblUserConn)
            Dim mycommand As New SqlCommand()
            Dim trans As SqlTransaction
            Dim rowAffected As Integer = 0
            Dim retvalue As Boolean = False
            IsError = False
            ErrorMsg = ""
            Try
                myConn.Open()
                trans = myConn.BeginTransaction
                mycommand.CommandType = CommandType.StoredProcedure
                mycommand.Connection = myConn
                mycommand.Transaction = trans
                mycommand.CommandText = "sp_UMOwner_AddWithLog"
                mycommand.Parameters.AddWithValue("@AccountID", AccountID)
                mycommand.Parameters.AddWithValue("@Type", Type)
                mycommand.Parameters.AddWithValue("@UserID", UserID)
                mycommand.Parameters.AddWithValue("@UserDisplayName", UserDisplayName)
                mycommand.Parameters.AddWithValue("@Manager", strManager)
                mycommand.Parameters.AddWithValue("@Comments", strComments)
                rowAffected = mycommand.ExecuteNonQuery()
                trans.Commit()
                retvalue = True
            Catch ex As Exception
                trans.Rollback()
                IsError = True
                ErrorMsg = ex.Message.ToString
            Finally
                mycommand.Dispose()
                myConn.Close()
            End Try
            Return retvalue
        End Function

        Function DeleteUMBackupWithLog(ByVal AccountID As String, ByVal UserID As String, ByVal strManager As String, ByVal strComments As String) As Boolean
            Dim myConn As New SqlConnection([Global].GblUserConn)
            Dim mycommand As New SqlCommand()
            Dim trans As SqlTransaction
            Dim rowAffected As Integer = 0
            Dim retvalue As Boolean = False
            IsError = False
            ErrorMsg = ""
            Try
                myConn.Open()
                trans = myConn.BeginTransaction
                mycommand.CommandType = CommandType.StoredProcedure
                mycommand.Connection = myConn
                mycommand.Transaction = trans
                mycommand.CommandText = "sp_UMOwner_DeleteBackupWithLog"
                mycommand.Parameters.AddWithValue("@AccountID", AccountID)
                mycommand.Parameters.AddWithValue("@UserName", UserID)
                mycommand.Parameters.AddWithValue("@Manager", strManager)
                mycommand.Parameters.AddWithValue("@Comments", CleanUpStringForDB(strComments))
                rowAffected = mycommand.ExecuteNonQuery()
                trans.Commit()
                retvalue = True
            Catch ex As Exception
                trans.Rollback()
                IsError = True
                ErrorMsg = ex.Message.ToString
            Finally
                mycommand.Dispose()
                myConn.Close()
            End Try
            Return retvalue
        End Function

        Function AccountUpdateAfterPasswordChange(ByVal AccountID As String, ByVal PasswordLastSet As DateTime) As Boolean
            Dim myConn As New SqlConnection([Global].GblUserConn)
            Dim mycommand As New SqlCommand()

            Dim rowAffected As Integer = 0
            Dim retvalue As Boolean = False
            IsError = False
            ErrorMsg = ""
            Try
                myConn.Open()

                mycommand.CommandType = CommandType.StoredProcedure
                mycommand.Connection = myConn

                mycommand.CommandText = "sp_UM_PasswordChange_ByID"
                mycommand.Parameters.AddWithValue("@AccountID", AccountID)
                mycommand.Parameters.AddWithValue("@PasswordLastSet", PasswordLastSet)
                rowAffected = mycommand.ExecuteNonQuery()

                retvalue = True
            Catch ex As Exception
                IsError = True
                ErrorMsg = ex.Message.ToString
                Throw ex
            Finally
                mycommand.Dispose()
                myConn.Close()
            End Try
            Return retvalue
        End Function

        Public Shared Function CleanUpStringForDB(ByVal Value As String) As String

            Value = Replace(Value, "'", "`")
            Value = Replace(Value, Chr(34), "`")

            Return Value

        End Function

    End Class

End Namespace
