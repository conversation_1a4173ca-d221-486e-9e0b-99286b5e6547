Imports System.Data
Imports System.Data.SqlClient
Imports AccountManager.Global
Namespace AccountManager

    Partial Class ucUpdAcct
        Inherits System.Web.UI.UserControl

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
            'Put user code to initialize the page here

            If Not Page.IsPostBack Then
                LoadInstruction()
            End If
        End Sub

        Sub LoadInstruction()

            lblInstructions.Text = "" & _
                "Step 1: Enter Account ID you want to update. Click Lookup.<br/>" & _
                "Step 2: Update Description and other fields as needed:<br/>" & _
                "Step 3: Click Update button.<br/>"
        End Sub

        Sub EnableDisableButton(ByVal IsVisible As Boolean)
            txtAccount.Enabled = IsVisible
            cmdLookup.Enabled = IsVisible
        End Sub


        Sub FindAccount()
           
            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            strSQL = String.Format("SELECT tbUMUsers.*,tbUMPwdExempt.* FROM tbUMUsers Left JOIN tbUMPwdExempt On AccountID=PwdExemptAccountID AND PwdExemptStatus='Pending' AND PwdExemptRowStatus<>'Deleted'  WHERE AccountID = '{0}'", txtAccount.Text)
            srRead = dbGet.GetDataReaderByStringId(strSQL)
            pnlPwdExempt.Visible = False
            If srRead.Read Then
                Dim ActDesc As String = srRead.Item("Description")
                HFDesc.Value = ActDesc
                Dim intindex As Integer = ActDesc.IndexOf(")")
                txtDescStartpart.Text = (ActDesc.Substring(0, intindex + 1)).Trim()
                txtDesc.Text = (ActDesc.Substring(intindex + 1)).Trim()
                Dim blIsExpired As Boolean = srRead.Item("IsExpired")
                hfActType.Value = blIsExpired
                If Not blIsExpired Then
                    chkException1.Checked = srRead.Item("ActException")
                    HFchkException.Value = srRead.Item("ActException")
                    If srRead.Item("ActExceptionSONCNumber") Is DBNull.Value Then
                        hfSONCNumber.Value = ""
                    Else
                        hfSONCNumber.Value = srRead.Item("ActExceptionSONCNumber")
                    End If
                    If srRead.Item("ActExceptionReason") Is DBNull.Value Then
                        hfReason.Value = ""
                    Else
                        hfReason.Value = srRead.Item("ActExceptionReason")
                    End If
                    If srRead.Item("PwdExemptID") Is DBNull.Value Then
                        pnlPwdExempt.Visible = False
                        hfPwdExemptID.Value = ""
                    Else
                        hfPwdExemptID.Value = srRead.Item("PwdExemptID")
                        pnlPwdExempt.Visible = True
                    End If
                    ddlResponse.SelectedIndex = 0
                    setValuesForPwdExemptControls()

                    chkException1.Enabled = True
                    setValuesForExceptionControls(chkException1.Checked)
                Else
                    HFchkException.Value = "False"
                    hfSONCNumber.Value = ""
                    hfReason.Value = ""
                    chkException1.Enabled = False
                    setValuesForExceptionControls(HFchkException.Value)
                    pnlPwdExempt.Visible = False
                    setValuesForPwdExemptControls()

                End If
                EnableDisableButton(False)
                pUpdate.Visible = True
            Else
                EnableDisableButton(True)
                pUpdate.Visible = False
                lblMessage.Text = "No account found."
            End If

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()

        End Sub

        Private Sub cmdLookup_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdLookup.Click
            FindAccount()
        End Sub

        Private Sub cmdUpdReset_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdUpdReset.Click
            lblMessage.Text = ""
            cmdUpdate.Enabled = True
            HFchkException.Value = "False"
            hfSONCNumber.Value = ""
            hfReason.Value = ""
            chkException1.Enabled = False
            hfActType.Value = "False"
            setValuesForExceptionControls(HFchkException.Value)
            ddlResponse.SelectedIndex = 0
            setValuesForPwdExemptControls()
            pUpdate.Visible = False
            EnableDisableButton(True)
        End Sub
	 
        Function UpdateException(ByVal ActException As Integer, ByVal ActExeptionSONCNumber As String, ByVal ActExeptionReason As String, ByVal strAccountID As String)
            ' Dim dbUpd As New DataAccess
            Dim strSQL As String
            
            strSQL = String.Format("Update tbUMUsers set ActException ='{0}',ActExceptionSONCNumber = '{1}',ActExceptionReason = '{2}' where AccountId = '{3}'", ActException, ActExeptionSONCNumber, ActExeptionReason, strAccountID)
            Try

                UpdateDBByStringId(strSQL)
            Catch ex As Exception


            End Try

        End Function

           Function updateDescription(ByVal strActDescription As String, ByVal strAccountID As String)
            Dim dbUpd As New DataAccess
            Dim strSQL As String
            strSQL = "Update tbUMUsers set description =" & "'" & strActDescription & "'" & " where AccountId = '" & strAccountID & "'"
            Try
                
                UpdateDBByStringId(strSQL)
                Dim usrAccount As New UserInfo(strAccountID)

                usrAccount.SetDescriptionInAD(usrAccount.LdapPath, strActDescription, txtDesc.Text.Trim)
            Catch ex As Exception
               
                
            End Try

        End Function
        
        Function UpdateDBByStringId(ByVal SQLText As String)
            
            Try
                Dim connUpd As SqlConnection
                Dim cmdUpd As SqlCommand

                connUpd = New SqlConnection([Global].GblUserConn)
                cmdUpd = New SqlCommand(SQLText, connUpd)
                cmdUpd.CommandType = CommandType.Text

                connUpd.Open()
                cmdUpd.ExecuteReader()

                connUpd.Close()
                cmdUpd.Connection.Close()
                connUpd.Close()


                connUpd = Nothing
                cmdUpd = Nothing
            Catch e As Exception
                Console.WriteLine("Exception Caught")
            End Try

        End Function

        Private Sub setValuesForExceptionControls(ByVal IsChecked As Boolean)
            If IsChecked Then
                txtSONCNumber.Text = hfSONCNumber.Value
                txtReason.Text = hfReason.Value
            Else
                txtSONCNumber.Text = ""
                txtReason.Text = ""
            End If
            lblrfvSONCNumber.Visible = IsChecked
            rfvSONCNumber.Enabled = IsChecked
            txtSONCNumber.Enabled = IsChecked
            lblrfvReason.Visible = IsChecked
            rfvReason.Enabled = IsChecked
            txtReason.Enabled = IsChecked
        End Sub

        Private Sub setValuesForPwdExemptControls()
            Dim IsSelected As Boolean = True
            If ddlResponse.SelectedValue = "0" Then
                IsSelected = False
                txtTicketNumber.Text = ""
                txtPwdExemptComments.Text = ""
            End If
            lblrfvTicketNumber.Visible = IsSelected
            rfvTicketNumber.Enabled = IsSelected
            txtTicketNumber.Enabled = IsSelected
            lblrfvComments.Visible = IsSelected
            rfvComments.Enabled = IsSelected
            txtPwdExemptComments.Enabled = IsSelected
        End Sub

        Private Sub chkException1_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkException1.CheckedChanged
            setValuesForExceptionControls(chkException1.Checked)
        End Sub

        Private Sub ddlResponse_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ddlResponse.SelectedIndexChanged
            setValuesForPwdExemptControls()
        End Sub

        Private Sub cmdUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdUpdate.Click

            Dim usrUpd As New UserInfo(txtAccount.Text)
            Dim hlpLog As New Helper
            Dim usrcur As New UserInfo
            Dim updatedDesc As String 'Simar
            Dim description As String = txtDesc.Text
            Dim IsDescriptionChange As Boolean = False
            Dim IsExceptionChange As Boolean = False
            Dim IsPwdExemptChange As Boolean = False
            Dim strMessage As String = ""
            Dim strError As String = ""
            updatedDesc = String.Format("{0} {1}", txtDescStartpart.Text, txtDesc.Text)
            If updatedDesc <> HFDesc.Value Then
                If description.IndexOf("(") = -1 And description.IndexOf(")") = -1 Then
                    updateDescription(updatedDesc, txtAccount.Text)
                    hlpLog.InsertLog(txtAccount.Text, "Description Updated", Now, "Old Description: " & HFDesc.Value)
                    IsDescriptionChange = True
                    HFDesc.Value = updatedDesc
                    strMessage = "Account Updated : " & updatedDesc
                Else
                    IsDescriptionChange = True
                    strError = "Description should not have multiple '(' and ')'."
                End If
                ' usrUpd.UpdateAccount()
                ' usrUpd.UpdateAccountExeption(chkException.Checked)
                'If chkException1.Checked = True Then
                '    UpdateException(1, txtAccount.Text)
                '    updateDescription(updatedDesc, txtAccount.Text)
                '    lblMessage.Text = "Account Updated : " & updatedDesc & " and Account Set As Exception"
                '    ' usrUpd.SetDescription(usrUpd.LdapPath, txtDesc.Text)
                '    ' usrUpd.SetDescription(usrUpd.LdapPath, updatedDesc)
                'Else
                '    UpdateException(1, txtAccount.Text)
                '    ' lblMessage.Text=chkException.Checked
                '    UpdateException(0, txtAccount.Text)
                '    updateDescription(updatedDesc, txtAccount.Text)
                '    'usrUpd.SetDescription(usrUpd.LdapPath, txtDesc.Text)
                '    ' usrUpd.SetDescription(usrUpd.LdapPath, updatedDesc)
                '    hlpLog.InsertLog(txtAccount.Text, [Global].GblActDel, Now, "Account Updated By: " & usrcur.UserNameandID)
                '    lblMessage.Text = "Account Updated : " & updatedDesc
                '    ' End If
                'End If
            End If

            Dim CurrentException As Boolean = Convert.ToBoolean(HFchkException.Value)
            Dim blIsExpired As Boolean = Convert.ToBoolean(hfActType.Value)
            Dim NewException As Boolean = chkException1.Checked
            If (Not blIsExpired) And (CurrentException <> NewException Or hfSONCNumber.Value <> txtSONCNumber.Text Or hfReason.Value <> txtReason.Text) Then
                UpdateException(NewException, txtSONCNumber.Text, txtReason.Text, txtAccount.Text)
                hlpLog.InsertLog(txtAccount.Text, "Exception Details Updated", Now, String.Format("Old Details: Exception: {0}, SONC Number: {1}, Reason: {2}", HFchkException.Value, hfSONCNumber.Value, hfReason.Value))
                IsExceptionChange = True
                HFchkException.Value = NewException
                hfSONCNumber.Value = txtSONCNumber.Text
                hfReason.Value = txtReason.Text
                If IsDescriptionChange Then
                    strMessage = strMessage & " and "
                End If
                strMessage = strMessage & "Account's exception is set as " & chkException1.Checked
            End If

            If hfPwdExemptID.Value <> "" Then
                If ddlResponse.SelectedValue <> "0" Then
                    Dim intID As Integer = Convert.ToInt32(hfPwdExemptID.Value)
                    Dim dataAccessObj As New DataAccess
                    If dataAccessObj.ReviewPwdExempt(txtAccount.Text, intID, ddlResponse.SelectedValue, txtTicketNumber.Text, txtPwdExemptComments.Text, DataAccess.getManager()) Then
                        pnlPwdExempt.Visible = False
                        hfPwdExemptID.Value = ""
                        If IsDescriptionChange Or IsExceptionChange Then
                            strMessage = strMessage & "."
                        End If
                        strMessage = strMessage & "Your response have been recorded for Password Exempt."
                    Else
                        strError = strError & "Error occurred while recording your response for Password Exempt."
                    End If
                    IsPwdExemptChange = True
                End If
            End If

            If IsDescriptionChange Or IsExceptionChange Or IsPwdExemptChange Then
                lblMessage.Text = strError & strMessage
            Else
                lblMessage.Text = "No Change in Description / Exception / Password Exempt"
            End If



        End Sub

    End Class
End Namespace

