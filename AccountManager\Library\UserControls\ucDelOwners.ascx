﻿<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Control Language="vb" AutoEventWireup="false" Inherits="AccountManager.ucDelOwners" CodeFile="ucDelOwners.ascx.vb" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>

 <script type="text/javascript">
     function OnEndCallBack(s, e) {
         if (s.cpIsUpdated != '') {
             document.getElementById('<%= lblMessage.ClientID%>').innerHTML = "Backup has been deleted successfully."
             //clientLabel.SetText('Backup has been deleted successfully.');
             //clientLabel.GetMainElement().style.backgroundColor = 'green';
             //clientLabel.GetMainElement().style.color = 'white';
         }
         else {
             document.getElementById('<%= lblMessage.ClientID%>').innerHTML = "";
         }
     }
    </script>

<table class="DisplayTables" style="width:100%">
	<tr>
		<td class="FunctionStep">Delete Backups</td>
	</tr>
	<tr>
		<td><asp:label id="lblMessage" CssClass="LabelRed" Runat="server"></asp:label><dx:ASPxLabel ID="lblMessageDev" runat="server" ClientInstanceName="clientLabel" ClientVisible="True" Font-Size="10pt">
                                                </dx:ASPxLabel></td>
	</tr>
	<tr>
		<td>Enter ID:
			<asp:textbox id="txtDelAccountID" Runat="Server"></asp:textbox></td>
	</tr>
	<tr>
		<td>
            <LOOKANDFEEL Kind="Office2003"><EDITORSTYLE Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black" BackColor="White"></EDITORSTYLE>
				<LABELSTYLE Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LABELSTYLE>
				<SCROLLBARBUTTONSTYLE BackColor="#84ABE3">
					<FILTERS>
						<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER EndColor="132, 171, 227" GradientMode="Horizontal" StartColor="221, 236, 254"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
					</FILTERS>
				</SCROLLBARBUTTONSTYLE>
				<ELEMENTSSETTINGS ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
					ScrollBarMargin="1"></ELEMENTSSETTINGS>
				<POPUPSTYLE Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"></POPUPSTYLE>
				<BUTTONSTYLE Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black" BackColor="#84ABE3"
					UseHotTrackStyle="True" UsePressedStyle="True" Wrap="False" Margin="1">
					<HOTTRACKSTYLE BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
						<FILTERS>
							<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER EndColor="255, 213, 153" StartColor="255, 243, 202"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
						</FILTERS>
					</HOTTRACKSTYLE>
					<PRESSEDSTYLE BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
						<FILTERS>
							<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER EndColor="255, 202, 134" StartColor="254, 148, 80"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
						</FILTERS>
					</PRESSEDSTYLE>
					<FILTERS>
						<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER EndColor="132, 171, 227" StartColor="221, 236, 254"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
					</FILTERS>
				</BUTTONSTYLE>
			</LOOKANDFEEL>
            <editorstyle backcolor="White" bordercolor="#6787B8" font-names="Verdana" font-size="8pt"
                forecolor="Black">
</editorstyle>
            <dx:aspxbutton id="cmdDelSearch" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                csspostfix="Office2003Blue" font-names="Verdana" height="12px" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                text="Search"></dx:aspxbutton>
        </td>
	</tr>
	<%--<tr>
		<td>Enter User ID to Delete:
			<asp:textbox id="txtDeleteID" Runat="Server"></asp:textbox></td>
	</tr>--%>
	<tr>
		<td><asp:label id="lblInstructions" Runat="server"></asp:label><br/>
		</td>
	</tr>
	<%--<tr>
		<td>
            <dx:aspxbutton id="cmdDelete" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                csspostfix="Office2003Blue" font-names="Verdana" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                text="Delete"></dx:aspxbutton>
        </td>
	</tr>--%>
    <tr><td>
        <table>
	<tr>
		<td style="width: 870px; ">
            <dx:aspxgridview id="grdDelUsers" runat="server" autogeneratecolumns="False" KeyFieldName="ID" OnRowDeleting="grdUsers_RowDeleting" cssfilepath="~/App_Themes/PlasticBlue/{0}/styles.css"
                csspostfix="PlasticBlue" font-names="Verdana" width="833px">
                 <ClientSideEvents EndCallback="OnEndCallBack" />
<Styles CssPostfix="PlasticBlue" CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css">
<Header SortingImageSpacing="10px" ImageSpacing="10px"></Header>

<AlternatingRow BackColor="silver"></AlternatingRow>
</Styles>

<SettingsPager ShowDefaultImages="False">
<AllButton Text="All"></AllButton>

<NextPageButton Text="Next &gt;"></NextPageButton>

<PrevPageButton Text="&lt; Prev"></PrevPageButton>
</SettingsPager>

<ImagesFilterControl>
<LoadingPanel Url="~/App_Themes/PlasticBlue/Editors/Loading.gif"></LoadingPanel>
</ImagesFilterControl>

<Images SpriteCssFilePath="~/App_Themes/PlasticBlue/{0}/sprite.css">
<LoadingPanelOnStatusBar Url="~/App_Themes/PlasticBlue/GridView/gvLoadingOnStatusBar.gif"></LoadingPanelOnStatusBar>

<LoadingPanel Url="~/App_Themes/PlasticBlue/GridView/Loading.gif"></LoadingPanel>
</Images>
                <SettingsBehavior AllowSort="false" />
                <SettingsBehavior ConfirmDelete="true" />
                <SettingsText ConfirmDelete="Are you sure to delete this Backup?" />
<Columns>
    <%--GroupIndex="0"--%>
   <%-- <dx:GridViewDataTextColumn FieldName="ID" Caption="FFID" VisibleIndex="6"></dx:GridViewDataTextColumn>--%>
<dx:GridViewDataTextColumn FieldName="AccountID" VisibleIndex="0"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="UserName" Caption="Backup ID" VisibleIndex="1"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="UserNameDN" Caption="Backup Name" VisibleIndex="2"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="Access" Caption="Access" VisibleIndex="3"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="Description" Caption="Description" VisibleIndex="4"></dx:GridViewDataTextColumn>
    <dx:GridViewCommandColumn ButtonType="Button" DeleteButton-Visible="true" VisibleIndex="5"></dx:GridViewCommandColumn>
    <dx:GridViewDataTextColumn FieldName="Type" Caption="Type" VisibleIndex="6" Visible="false"></dx:GridViewDataTextColumn>
       <%-- <HeaderTemplate></HeaderTemplate>
         <DeleteButton  Visible="true" Text="Delete">
            </DeleteButton>
    </dx:GridViewCommandColumn>--%>
 </Columns>

<Settings ShowGroupPanel="False"></Settings>

<StylesEditors>
<CalendarHeader Spacing="11px"></CalendarHeader>

<ProgressBar Height="25px"></ProgressBar>
</StylesEditors>
</dx:aspxgridview>
        </td>
	</tr>
</table>
        </td></tr>
</table>


