<%@ Register TagPrefix="uc1" TagName="Menu" src="~/Library/UserControls/Menu.ascx" %>
<%@ Register TagPrefix="dgwc" Namespace="Dundas.Gauges.WebControl" Assembly="DundasWebGauge" %>
<%@ Register TagPrefix="dcwc" Namespace="Dundas.Charting.WebControl" Assembly="DundasWebChart" %>
<%@ Page MasterPageFile="~/MasterPage.master" Title="Stats" Language="vb" AutoEventWireup="false" Inherits="AccountManager.Stats" CodeFile="Stats.aspx.vb" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table>
        <tr>
            <td>
						<asp:label id="lblWelcome" CssClass="TableHeader" Runat="server">Account Manager Statistics</asp:label><br/>
						<br/>
						<asp:label id="lblMessage" CssClass="LabelRed" Runat="server"></asp:label><br/>
						<table class="DisplayTables">
							<tr>
								<td align="center" width="100%" colspan="3">
									<dgwc:gaugecontainer id="gcStatsGauge" runat="server" AutoLayout="false" width="800px" height="400px"
										BackColor="White" ImageUrl="\Library\Images\Stats">
										<Labels>
											<dgwc:GaugeLabel Name="PercentPD" TextColor="Red" ResizeMode="None" Font="Microsoft Sans Serif, 8.25pt, style=Bold"
												BorderColor="Red" BackGradientEndColor="" Parent="CircularGauges.PastDue" BackColor="" Text="Past Due">
												<Size Height="18" Width="30"></Size>
												<Location Y="98" X="89"></Location>
											</dgwc:GaugeLabel>
											<dgwc:GaugeLabel Name="PercentES" TextColor="Yellow" ResizeMode="None" Font="Microsoft Sans Serif, 8.25pt, style=Bold"
												BorderColor="Red" BackGradientEndColor="" Parent="CircularGauges.WillExpire" BackColor="" Text="Past Due">
												<Size Height="18" Width="30"></Size>
												<Location Y="98" X="89"></Location>
											</dgwc:GaugeLabel>
											<dgwc:GaugeLabel Name="PercentOK" TextColor="LimeGreen" ResizeMode="None" Font="Microsoft Sans Serif, 8.25pt, style=Bold"
												BorderColor="Red" BackGradientEndColor="" Parent="CircularGauges.OK" BackColor="" Text="Past Due">
												<Size Height="18" Width="30"></Size>
												<Location Y="98" X="89"></Location>
											</dgwc:GaugeLabel>
											<dgwc:GaugeLabel Name="TitlePD" TextColor="White" ResizeMode="None" Font="Microsoft Sans Serif, 8.25pt, style=Bold"
												BackGradientEndColor="" TextShadowOffset="1" Parent="CircularGauges.PastDue" BackColor="" Text="Past Due & Not Disabled">
												<Size Height="28" Width="100"></Size>
												<Location Y="-32.6867561" X="7.22890949"></Location>
											</dgwc:GaugeLabel>
											<dgwc:GaugeLabel Name="TitleDisExp" TextColor="White" ResizeMode="None" Font="Microsoft Sans Serif, 7pt, style=Bold"
												BackGradientEndColor="" TextShadowOffset="1" Parent="LinearGauges.ExpireAndDis" BackColor=""
												Text="Total" TextAlignment="BottomRight">
												<Size Height="24" Width="67"></Size>
												<Location Y="93" X="8"></Location>
											</dgwc:GaugeLabel>
											<dgwc:GaugeLabel Name="TitleRequests" TextColor="White" ResizeMode="None" Font="Microsoft Sans Serif, 7pt, style=Bold"
												BackGradientEndColor="" TextShadowOffset="1" Parent="LinearGauges.PendingRequests" BackColor=""
												Text="Total" TextAlignment="BottomRight">
												<Size Height="24" Width="65"></Size>
												<Location Y="93" X="8"></Location>
											</dgwc:GaugeLabel>
											<dgwc:GaugeLabel Name="Times" TextColor="Yellow" ResizeMode="None" Font="Microsoft Sans Serif, 8.25pt, style=Bold"
												BorderColor="Brown" BackGradientEndColor="" Parent="LinearGauges.Total" BackColor="" Text="x 100"
												TextAlignment="BottomCenter">
												<Size Height="30" Width="97"></Size>
												<Location Y="69.2967453" X="-0.000261010078"></Location>
											</dgwc:GaugeLabel>
											<dgwc:GaugeLabel Name="TimesExDis" TextColor="Yellow" ResizeMode="None" Font="Microsoft Sans Serif, 8.25pt, style=Bold"
												BorderColor="Brown" BackGradientEndColor="" Parent="LinearGauges.ExpireAndDis" BackColor="" Text="x 100"
												TextAlignment="BottomCenter">
												<Size Height="30" Width="97"></Size>
												<Location Y="69.2967453" X="-0.000261010078"></Location>
											</dgwc:GaugeLabel>
											<dgwc:GaugeLabel Name="TitleES" TextColor="White" ResizeMode="None" Font="Microsoft Sans Serif, 8.25pt, style=Bold"
												BackGradientEndColor="" TextShadowOffset="1" Parent="CircularGauges.WillExpire" BackColor=""
												Text="Past Due in 45 days">
												<Size Height="40" Width="140"></Size>
												<Location Y="-34.2605858" X="2.99997759"></Location>
											</dgwc:GaugeLabel>
											<dgwc:GaugeLabel Name="TitleOK" TextColor="White" ResizeMode="None" Font="Microsoft Sans Serif, 8.25pt, style=Bold"
												BackGradientEndColor="" TextShadowOffset="1" Parent="CircularGauges.OK" BackColor="" Text="Compliant">
												<Size Height="40" Width="140"></Size>
												<Location Y="-34.2605858" X="15.9999781"></Location>
											</dgwc:GaugeLabel>
											<dgwc:GaugeLabel Name="TitleTotal" TextColor="White" ResizeMode="None" Font="Microsoft Sans Serif, 7pt, style=Bold"
												BackGradientEndColor="" TextShadowOffset="1" Parent="CircularGauges.PastDue" BackColor="" Text="Total"
												TextAlignment="BottomRight">
												<Size Height="24" Width="31"></Size>
												<Location Y="130.116364" X="345"></Location>
											</dgwc:GaugeLabel>
											<dgwc:GaugeLabel Name="BottomTitle" TextColor="White" ResizeMode="None" Font="Microsoft Sans Serif, 9pt"
												BorderColor="Brown" BackGradientEndColor="" Parent="" BackColor="" Text="Account Manager Current Statistics"
												TextAlignment="MiddleCenter">
												<Size Height="25" Width="100"></Size>
												<Location Y="80.685936" X="-5.********"></Location>
											</dgwc:GaugeLabel>
											<dgwc:GaugeLabel Name="TotalPD" TextColor="White" ResizeMode="None" Font="Microsoft Sans Serif, 9pt"
												BackGradientEndColor="" Parent="CircularGauges.PastDue" BackColor="" Text="TOTAL" TextAlignment="MiddleLeft">
												<Size Height="100" Width="75"></Size>
												<Location Y="68.66954" X="87.98038"></Location>
											</dgwc:GaugeLabel>
											<dgwc:GaugeLabel Name="TotalES" TextColor="White" ResizeMode="None" Font="Microsoft Sans Serif, 9pt"
												BackGradientEndColor="" Parent="CircularGauges.WillExpire" BackColor="" Text="TOTAL" TextAlignment="MiddleLeft">
												<Size Height="100" Width="75"></Size>
												<Location Y="68.66954" X="87.98038"></Location>
											</dgwc:GaugeLabel>
											<dgwc:GaugeLabel Name="TotalOK" TextColor="White" ResizeMode="None" Font="Microsoft Sans Serif, 9pt"
												BackGradientEndColor="" Parent="CircularGauges.OK" BackColor="" Text="TOTAL" TextAlignment="MiddleLeft">
												<Size Height="100" Width="75"></Size>
												<Location Y="70.7103043" X="87.69459"></Location>
											</dgwc:GaugeLabel>
										</Labels>
										<Values>
											<dgwc:InputValue Name="Default"></dgwc:InputValue>
										</Values>
										<BackFrame FrameWidth="1" Style="Simple" BackGradientEndColor="Black" ShadowOffset="1" Shape="Rectangular"
											FrameColor="Snow" BackColor="CornflowerBlue" BackGradientType="TopBottom"></BackFrame>
										<CircularGauges>
											<dgwc:CircularGauge Name="PastDue">
												<Scales>
													<dgwc:CircularScale TickMarksOnTop="True" FillColor="White" StartAngle="0" BorderWidth="1" SweepAngle="180"
														Radius="52" BorderColor="White" Name="Default">
														<LabelStyle TextColor="White"></LabelStyle>
														<MajorTickMark Width="4" FillColor="White" Shape="Rectangle"></MajorTickMark>
														<MinorTickMark Width="2" FillColor="White"></MinorTickMark>
													</dgwc:CircularScale>
												</Scales>
												<BackFrame FrameWidth="1" Style="Simple" BackGradientEndColor="0, 0, 0, 139" ShadowOffset="2"
													Shape="AutoShape" FrameColor="Tomato" BackColor="0, 100, 149, 237"></BackFrame>
												<Size Height="98.36066" Width="19.21875"></Size>
												<PivotPoint Y="52" X="76"></PivotPoint>
												<Location Y="0" X="0"></Location>
												<Pointers>
													<dgwc:CircularPointer NeedleStyle="NeedleStyle4" FillGradientType="None" FillGradientEndColor="26, 59, 105"
														CapWidth="30" Width="5" Name="Default"></dgwc:CircularPointer>
												</Pointers>
												<Ranges>
													<dgwc:CircularRange Name="MaxLimit" BorderWidth="0" StartValue="0" FillColor="ForestGreen"></dgwc:CircularRange>
												</Ranges>
											</dgwc:CircularGauge>
											<dgwc:CircularGauge Name="WillExpire">
												<Scales>
													<dgwc:CircularScale FillColor="White" StartAngle="0" BorderWidth="1" SweepAngle="180" Radius="52" BorderColor="White"
														Name="Default">
														<LabelStyle TextColor="White"></LabelStyle>
														<MajorTickMark Width="4" FillColor="White" Shape="Rectangle"></MajorTickMark>
														<MinorTickMark Width="2" FillColor="White"></MinorTickMark>
													</dgwc:CircularScale>
												</Scales>
												<BackFrame FrameWidth="1" Style="Simple" BackGradientEndColor="0, 0, 0, 139" ShadowOffset="2"
													Shape="AutoShape" BackColor="0, 100, 149, 237"></BackFrame>
												<Size Height="98.36066" Width="19.21875"></Size>
												<PivotPoint Y="50" X="76"></PivotPoint>
												<Location Y="0" X="20"></Location>
												<Pointers>
													<dgwc:CircularPointer NeedleStyle="NeedleStyle4" FillGradientType="None" FillGradientEndColor="26, 59, 105"
														CapWidth="30" Width="5" Name="Default"></dgwc:CircularPointer>
												</Pointers>
												<Ranges>
													<dgwc:CircularRange Name="MaxLimit" BorderWidth="0" StartValue="0" FillColor="ForestGreen"></dgwc:CircularRange>
												</Ranges>
											</dgwc:CircularGauge>
											<dgwc:CircularGauge Name="OK">
												<Scales>
													<dgwc:CircularScale FillColor="White" StartAngle="0" BorderWidth="1" SweepAngle="180" Radius="52" BorderColor="White"
														Name="Default">
														<LabelStyle TextColor="White"></LabelStyle>
														<MajorTickMark Width="4" FillColor="White" Shape="Rectangle"></MajorTickMark>
														<MinorTickMark Width="2" FillColor="White"></MinorTickMark>
													</dgwc:CircularScale>
												</Scales>
												<BackFrame FrameWidth="1" Style="Simple" BackGradientEndColor="0, 0, 0, 139" ShadowOffset="2"
													Shape="AutoShape" BackColor="0, 100, 149, 237"></BackFrame>
												<Size Height="98.36066" Width="19.21875"></Size>
												<PivotPoint Y="50" X="76"></PivotPoint>
												<Location Y="0" X="40"></Location>
												<Pointers>
													<dgwc:CircularPointer NeedleStyle="NeedleStyle4" FillGradientType="None" FillGradientEndColor="26, 59, 105"
														CapWidth="30" Width="5" Name="Default"></dgwc:CircularPointer>
												</Pointers>
												<Ranges>
													<dgwc:CircularRange Name="MaxLimit" BorderWidth="0" StartValue="0" FillGradientEndColor="ForestGreen"
														FillColor="Red"></dgwc:CircularRange>
												</Ranges>
											</dgwc:CircularGauge>
										</CircularGauges>
										<LinearGauges>
											<dgwc:LinearGauge Name="Total">
												<Scales>
													<dgwc:LinearScale StartMargin="10" FillGradientEndColor="Black" EndMargin="10" Width="15" FillColor="White"
														BorderWidth="1" Name="Default" Position="57">
														<LabelStyle FormatString="N0" TextColor="White"></LabelStyle>
														<MajorTickMark Width="3" FillColor="White"></MajorTickMark>
														<MinorTickMark Width="2" FillColor="White"></MinorTickMark>
													</dgwc:LinearScale>
												</Scales>
												<BackFrame FrameWidth="1" Style="Simple" BackGradientEndColor="26, 59, 105" ShadowOffset="1"
													Shape="RoundedRectangular" BackColor=""></BackFrame>
												<Size Height="70.18033" Width="10.609375"></Size>
												<Location Y="7" X="64"></Location>
												<Pointers>
													<dgwc:LinearPointer DistanceFromScale="-14" ThermometerBackGradientEndColor="" FillGradientType="VerticalCenter"
														ThermometerBackColor="" Placement="Inside" Width="12" Type="Bar" Name="Default"></dgwc:LinearPointer>
												</Pointers>
											</dgwc:LinearGauge>
											<dgwc:LinearGauge Name="ExpireAndDis">
												<Scales>
													<dgwc:LinearScale StartMargin="10" FillGradientEndColor="Black" EndMargin="10" Width="15" FillColor="White"
														BorderWidth="1" Name="Default" Position="57">
														<LabelStyle FormatString="N0" TextColor="White"></LabelStyle>
														<MajorTickMark Width="3" FillColor="White"></MajorTickMark>
														<MinorTickMark Width="2" FillColor="White"></MinorTickMark>
													</dgwc:LinearScale>
												</Scales>
												<BackFrame FrameWidth="1" Style="Simple" BackGradientEndColor="26, 59, 105" ShadowOffset="1"
													Shape="RoundedRectangular" BackColor=""></BackFrame>
												<Size Height="70.18033" Width="10.609375"></Size>
												<Location Y="7" X="76"></Location>
												<Pointers>
													<dgwc:LinearPointer DistanceFromScale="-14" ThermometerBackGradientEndColor="" FillGradientType="VerticalCenter"
														ThermometerBackColor="" Placement="Inside" Width="12" Type="Bar" Name="Default"></dgwc:LinearPointer>
												</Pointers>
											</dgwc:LinearGauge>
											<dgwc:LinearGauge Name="PendingRequests">
												<Scales>
													<dgwc:LinearScale StartMargin="10" FillGradientEndColor="Black" EndMargin="10" Width="15" FillColor="White"
														BorderWidth="1" Name="Default" Position="57">
														<LabelStyle FormatString="N0" TextColor="White"></LabelStyle>
														<MajorTickMark Width="3" FillColor="White"></MajorTickMark>
														<MinorTickMark Width="2" FillColor="White"></MinorTickMark>
													</dgwc:LinearScale>
												</Scales>
												<BackFrame FrameWidth="1" Style="Simple" BackGradientEndColor="26, 59, 105" ShadowOffset="1"
													Shape="RoundedRectangular" BackColor=""></BackFrame>
												<Size Height="70.18033" Width="10.609375"></Size>
												<Location Y="7" X="88"></Location>
												<Pointers>
													<dgwc:LinearPointer DistanceFromScale="-14" ThermometerBackGradientEndColor="" FillGradientType="VerticalCenter"
														ThermometerBackColor="" Placement="Inside" Width="12" Type="Bar" Name="Default"></dgwc:LinearPointer>
												</Pointers>
											</dgwc:LinearGauge>
										</LinearGauges>
									</dgwc:gaugecontainer></td>
							</tr>
							<tr>
								<td colSpan="3"><DCWC:CHART id="Chart1" runat="server" BackColor="WhiteSmoke" ImageUrl="\Library\Images\Chart1.png"
										Width="700px" BackGradientEndColor="White" BorderLineStyle="Solid" BorderLineColor="26, 59, 105" Palette="Dundas"
										BackGradientType="DiagonalLeft">
										<BorderSkin FrameBackColor="CornflowerBlue" FrameBackGradientEndColor="CornflowerBlue" SkinStyle="Emboss"
											PageColor="AliceBlue"></BorderSkin>
										<Legends>
											<dcwc:Legend BorderColor="26, 59, 105" Name="Default" BackColor="White" ShadowOffset="2"></dcwc:Legend>
										</Legends>
										<Series>
											<dcwc:Series BorderWidth="2" XValueType="DateTime" Name="Total Accounts" ChartType="Line" BorderColor="26, 59, 105"
												ShadowOffset="2" YValueType="Double"></dcwc:Series>
											<dcwc:Series BorderWidth="2" XValueType="DateTime" Name="OK Accounts" ChartType="Line" BorderColor="26, 59, 105"
												ShadowOffset="2" YValueType="Double"></dcwc:Series>
											<dcwc:Series BorderWidth="2" XValueType="DateTime" Name="Past Due" ChartType="Line" BorderColor="Red"
												ShadowOffset="2" YValueType="Double"></dcwc:Series>
											<dcwc:Series BorderWidth="2" XValueType="DateTime" Name="Past Due &amp; Disabled" ChartType="Line"
												BorderColor="26, 59, 105" ShadowOffset="2" YValueType="Double"></dcwc:Series>
											<dcwc:Series BorderWidth="2" XValueType="DateTime" Name="Disabled" ChartType="Line" BorderColor="26, 59, 105"
												ShadowOffset="2" YValueType="Double"></dcwc:Series>
											<dcwc:Series BorderWidth="2" XValueType="DateTime" Name="Past Due in 45 Days" ChartType="Line"
												BorderColor="26, 59, 105" ShadowOffset="2" YValueType="Double"></dcwc:Series>
										</Series>
										<Titles>
											<dcwc:Title Text="Account Manager Historical Data"></dcwc:Title>
										</Titles>
										<ChartAreas>
											<dcwc:ChartArea Name="Default" BorderColor="26, 59, 105" BorderStyle="Solid" ShadowOffset="2" BackColor="White">
												<AxisY2>
													<MajorGrid LineColor="Silver"></MajorGrid>
													<MinorGrid LineColor="Silver"></MinorGrid>
												</AxisY2>
												<AxisX2>
													<MajorGrid LineColor="Silver"></MajorGrid>
													<MinorGrid LineColor="Silver"></MinorGrid>
												</AxisX2>
												<Area3DStyle Light="Realistic"></Area3DStyle>
												<AxisY>
													<MajorGrid LineColor="Silver"></MajorGrid>
													<MinorGrid LineColor="Silver"></MinorGrid>
												</AxisY>
												<AxisX Title="Dates">
													<MajorGrid LineColor="Silver"></MajorGrid>
													<MinorGrid LineColor="Silver"></MinorGrid>
													<MajorTickMark Style="Inside"></MajorTickMark>
												</AxisX>
											</dcwc:ChartArea>
										</ChartAreas>
									</DCWC:CHART></td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
</asp:Content>