Imports DevExpress.Web.ASPxGrid
Imports System.Data

Namespace AccountManager

    Partial Class ChangeOwner
        Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub
        Protected WithEvents rfOwner As System.Web.UI.WebControls.RequiredFieldValidator


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
            If Not Page.IsPostBack Then
                Dim IsGoBack As Boolean = False
                If Session("Accounts") = "" Then
                    If Request.ServerVariables("HTTP_REFERER") Is Nothing Then
                        If Request.QueryString("AccountId") <> "" Then
                            Session("Accounts") = Request.QueryString("AccountId")
                        Else
                            IsGoBack = True
                        End If
                    Else
                        IsGoBack = True
                    End If
                End If
                If IsGoBack Then
                    Response.Redirect("/AccountManager/MyUsers.aspx")
                Else
                    LoadUsers()
                    If Trim(Session("NotOwnDel")) <> "" Then
                        UserMsgBox("You selected accounts you are not listed as the owner.  You will not be able to manage these accounts. " & Session("NotOwnDel"))
                        Session("NotOwnDel") = ""
                    End If
                End If
               
            End If

        End Sub

        Sub LoadUsers()
            Dim strUsers As String()
            Dim i As Integer = 0
            strUsers = Split(Session("Accounts"), ";")
            lstAccounts.Items.Clear()
            lblMessage.Text = ""
            Dim usrcur As New UserInfo
            Dim hlpCheck As New Helper
            Dim strAccounts As String = ""
            Do While i < strUsers.Length
                If Trim(strUsers(i)) <> "" Then
                    If Not hlpCheck.IsOwner(strUsers(i), usrcur.GetUserID) Then
                        lblMessage.Text = lblMessage.Text & "You are not the owner of " & strUsers(i) & ".<br/>"
                        lblMessage.CssClass = "LabelRed"
                    Else
                        lstAccounts.Items.Add(strUsers(i))
                        strAccounts = strAccounts & ",'" & strUsers(i) & "'"
                    End If
                End If
                i = i + 1
            Loop

            If lstAccounts.Items.Count < 1 Then
                pChange.Visible = False
            Else
                strAccounts = strAccounts.Trim(",")
                Dim daObj As New DataAccess
                Dim dt As DataTable = daObj.GetOwnershipChangeRequests(strAccounts)
                grdRequests.DataSource = dt
                grdRequests.DataBind()

                If dt.Rows.Count > 0 Then
                    lblNewOwner.Text = "There is a pending ownership request.  If you want to submit a new request, the pending request will be removed."
                    pnlRequests.Visible = True
                Else
                    pnlRequests.Visible = False
                End If

            End If

        End Sub

        Private Sub cmdUpdateOwner_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdUpdateOwner.Click
            lblNewOwner.Text = ""
            lblMessage.Text = ""
            If ValidFields() Then

                Dim reqNew As New Requests
                Dim usrcur As New UserInfo
                Dim strUsers As String()
                Dim hlpIns As New Helper
                Dim usrNewOwner As New UserInfo(txtNewOwner.Text, True)
                Dim emSend As New Email(usrNewOwner.GetUserID)

                strUsers = Split(Session("Accounts"), ";")

                If usrNewOwner.UserFound Then
                    If usrNewOwner.UserEmployeeType <> "E" Then
                        lblNewOwner.Text = "Owner of a Non-User Account can only be a permanent KC Employee.  Please enter a valid user ID."
                    ElseIf usrcur.GetUserID = usrNewOwner.GetUserID Then
                        lblNewOwner.Text = "You can not submit ownership change request for yourself.  Please enter a valid user ID."
                    Else
                        Dim strAlready As String = ""
                        Dim strNew As String = ""
                        Dim strError As String = ""
                        Dim i As Integer = 0
                        Dim blnLoad As Boolean = True

                        Do While i < lstAccounts.Items.Count

                            Dim strAccount As String = lstAccounts.Items(i).Value

                            If strAccount <> "" Then
                                txtNewOwnerComments.Text = hlpIns.dbCleanUpString(txtNewOwnerComments.Text)
                                reqNew.AccountID = strAccount
                                reqNew.Comments = txtNewOwnerComments.Text
                                reqNew.NewOwner = txtNewOwner.Text
                                reqNew.NewOwnerDN = usrNewOwner.DisplayName
                                reqNew.Type = [Global].GblTypeNewOwner
                                reqNew.Status = [Global].GblReqStatusNew
                                reqNew.SubmittingUser = usrcur.GetUserID
                                reqNew.OldOwner = usrcur.GetUserID
                                reqNew.OldOwnerDN = usrcur.DisplayName
                                reqNew.Description = usrNewOwner.Description
                                Dim strretval As String = reqNew.InsertRequest()
                                If reqNew.GetIsError Then
                                    strError = strError & strAccount & ";"
                                    'lblMessage.Text = reqNew.GetErrorMsg
                                Else
                                    'Label1.Text = strretval
                                    If strretval = "Added" Then
                                        strNew = strNew & strAccount & ";"
                                    ElseIf strretval = "Already" Then
                                        strAlready = strAlready & strAccount & ";"
                                    Else
                                        strError = strError & strAccount & ";"
                                    End If
                                End If
                                'hlpIns.InsertLog(strAccount, [Global].GblActOwnReq, Now, txtNewOwnerComments.Text & " (Old Owner: " & reqNew.OldOwner & " - New Owner: " & txtNewOwner.Text & ")")
                            End If
                            i = i + 1
                        Loop
                        strNew = strNew.Trim(";")
                        strAlready = strAlready.Trim(";")
                        strError = strError.Trim(";")
                        Dim strMsg As String = ""
                        If strAlready <> "" Then
                            strMsg = "Request is already pending with same user for accounts (" & strAlready & ")."
                        End If
                        If strNew <> "" Then
                            emSend.CreateAndSend("Account Ownership Change Request", strNew.Split(";"))
                            lblMessage.Text = "Request has been submitted for accounts (" & strNew & ").  New owner must accept changes."
                            lblMessage.CssClass = "LabelGreen"
                            'strMsg = strMsg & "<br>Request has been submitted for accounts (" & strNew & ").  New owner must accept changes."
                        End If
                        If strError <> "" Then
                            If strMsg <> "" Then
                                strMsg = strMsg & "<br>"
                            End If
                            strMsg = strMsg & "Error is occured while processing the request for accounts (" & strError & ")."
                        End If
                        ' strMsg = strMsg.Trim("<br>")
                        If strMsg <> "" Then
                            lblNewOwner.Text = strMsg
                        End If
                        ClearFields()
                    End If
                    
                Else
                    lblNewOwner.Text = "User ID " & txtNewOwner.Text & " does not exist.  Please enter a valid user ID."
                End If

            End If

        End Sub

        Sub ClearFields()
            txtNewOwner.Enabled = False
            txtNewOwnerComments.Enabled = False
            'lstAccounts.Items.Clear()
            cmdUpdateOwner.Enabled = False
            pnlRequests.Visible = False
        End Sub

        Function ValidFields() As Boolean
            If Trim(txtNewOwner.Text) <> "" And txtNewOwnerComments.Text <> "" Then
                ValidFields = True
                'Dim usrcur As New UserInfo
                'Dim strNewOwner As String = txtNewOwner.Text.Trim
                'If LCase(usrcur.GetUserID) = LCase(strNewOwner) Then
                '    lblNewOwner.Text = "You are already listed as the owner.  This page is used to change the ownership to new owner."
                '    ValidFields = False
                'Else
                '    Dim newOwner As New UserInfo(strNewOwner, True)
                '    'If hlpOwner.IsValidOwnerID(txtNewOwner.Text) Then
                '    If newOwner.UserEmployeeType.ToString.ToUpper = "C" Then
                '        ValidFields = True
                '    Else
                '        lblNewOwner.Text = [Global].GblInvalidOwnerMessage
                '        ValidFields = False
                '    End If

                'End If
            Else
                If Trim(txtNewOwner.Text) = "" Then
                    lblNewOwner.Text = "You must enter a owner ID in the New Owner field."
                Else
                    lblNewOwner.Text = "You must enter comments describing why the change is happening."
                End If
                ValidFields = False
            End If
            'Dim hlpOwner As New Helper
            'Dim usrcur As New UserInfo
            'Dim strNewOwner As String = txtNewOwner.Text.Trim
            'If LCase(usrcur.GetUserID) = LCase(strNewOwner) Then
            '    lblNewOwner.Text = "You are already listed as the owner.  This page is used to change ownership to a new owner."
            '    ValidFields = False
            'Else
            '    Dim newOwner As New UserInfo(strNewOwner, True)
            '    'If hlpOwner.IsValidOwnerID(txtNewOwner.Text) Then
            '    If newOwner.UserEmployeeType.ToString.ToUpper = "E" Then
            '        If Trim(txtNewOwner.Text) <> "" And txtNewOwnerComments.Text <> "" Then
            '            ValidFields = True
            '        Else
            '            If Trim(txtNewOwner.Text) = "" Then
            '                lblNewOwner.Text = "You must enter a owner ID in the New Owner/Backup field."
            '            Else
            '                lblNewOwner.Text = "You must enter comments describing why the change is happening."
            '            End If

            '            ValidFields = False
            '        End If
            '    Else
            '        lblNewOwner.Text = [Global].GblInvalidOwnerMessage
            '        ValidFields = False
            '    End If
            'End If

            Return ValidFields
        End Function

        Private Sub cmdReturn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdReturn.Click
            Response.Redirect("/AccountManager/MyUsers.aspx")
        End Sub

        Public Sub UserMsgBox(ByVal sMsg As String)

            Dim sb As New StringBuilder()
            Dim oFormObject As System.Web.UI.Control

            sMsg = sMsg.Replace("'", "\'")
            sMsg = sMsg.Replace(Chr(34), "\" & Chr(34))
            sMsg = sMsg.Replace(vbCrLf, "\n")
            sMsg = "<script language=javascript>alert(""" & sMsg & """)</script>"

            sb = New StringBuilder()
            sb.Append(sMsg)

            For Each oFormObject In Me.Controls
                If TypeOf oFormObject Is HtmlForm Then
                    Exit For
                End If
            Next

            ' Add the javascript after the form object so that the 
            ' message doesn't appear on a blank screen.
            oFormObject.Controls.AddAt(oFormObject.Controls.Count, New LiteralControl(sb.ToString()))

        End Sub

    End Class

End Namespace
