Imports System.Data
Namespace AccountManager

    Partial Class ViewLocalGrps
        Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Dim strSQL As String

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
            LoadRequests()
        End Sub

        Sub LoadRequests()

            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            'SA_Common_LocalUsers_DEFAULT
            'strSQL = "SELECT ID,Host,Name,AsMemberOf,Type,FullName, JobRunTimeKEy " & _
            '"FROM SA_Common_LocalUsers_DEFAULT WHERE SA_HOST = '" & [Global].GblSAServer & "' AND ("

            strSQL = "SELECT rowGUID,Host,Name,AsMemberOf,Type,FullName, JobRunTimeKEy " & _
            "FROM SA_Local_Users_Groups_Membership WHERE SA_HOST = '" & [Global].GblSAServer & "'AND ("

            'strSQL = strSQL & "FROM SA_Common_LocalUsers_DEFAULT WHERE SA_HOST = '" & [Global].GblSAServer & "' AND ("
            Dim strUsers As String()
            Dim i As Integer = 0

            strUsers = Split(Session("Accounts"), ";")

            Do While i < strUsers.Length
                If Trim(strUsers(i)) <> "" Then
                    strSQL = strSQL & " Name like '%" & Trim(strUsers(i)) & "%' OR"
                End If
                i = i + 1
            Loop
            strSQL = strSQL.Remove(strSQL.Length - 2, 2)
            strSQL = strSQL & ") Order by Name, Type "
            Trace.Warn(strSQL)

            srRead = dbGet.GetDataReaderByStringId(strSQL, [Global].GblSauConn)

            If srRead.HasRows Then
                grdLocalGrps.DataKeyField = "ID"
                grdLocalGrps.DataSource = srRead
                grdLocalGrps.DataBind()
                'grdRequests.ExpandAllRows()
                lblLastUpd.Visible = True
                cmdExport.Visible = True
            Else
                lblLastUpd.Visible = False
                lblMessage.Text = "<font color=red>No servers were found where these accounts were members of local groups.</font>"
                cmdExport.Visible = False
            End If

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()

        End Sub

        Private Sub cmdReturn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdReturn.Click
            Response.Redirect("/AccountManager/MyUsers.aspx")
        End Sub

        Private Sub cmdExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdExport.Click
            Dim prtReport As New Print([Global].GblSauConn)

            hlDownload.NavigateUrl = prtReport.CreateCSV(strSQL)
            hlDownload.Text = "Download File"
        End Sub
    End Class

End Namespace
