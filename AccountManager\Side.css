﻿BODY
{
    MARGIN-TOP: 0px;
    MARGIN-LEFT: 0px;
    MARGIN-RIGHT: 0px;
    FONT-FAMILY: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, 'MS Sans Serif';
    background-image: url(\Library\images\esmbkg.jpg);
    
}
.LeftNavUp
{
    BORDER-RIGHT: #2f72b0 2px solid;
    BORDER-TOP: #2f72b0 2px solid;
    FONT-WEIGHT: bold;
    FONT-SIZE: 10px;
    MARGIN: 1px;
    BORDER-LEFT: #2f72b0 2px solid;
    WIDTH: 160px;
    CURSOR: hand;
    COLOR: #2f72b0;
    LINE-HEIGHT: 10pt;
    PADDING-TOP: 1px;
    BORDER-BOTTOM: #2f72b0 2px solid;
    FONT-FAMILY: Verdana,Arial,Helvetica;
    LETTER-SPACING: normal;
    HEIGHT: 20px;
    BACKGROUND-COLOR: #2f72b0;
    TEXT-DECORATION: none
}
.LeftNavDown
{
    BORDER-RIGHT: #78a3d2 2px solid;
    BORDER-TOP: #78a3d2 2px solid;
    FONT-WEIGHT: bold;
    FONT-SIZE: 10px;
    MARGIN: 1px;
    BORDER-LEFT: #78a3d2 2px solid;
    WIDTH: 160px;
    CURSOR: hand;
    COLOR: #78a3d2;
    LINE-HEIGHT: 10pt;
    PADDING-TOP: 1px;
    BORDER-BOTTOM: #78a3d2 2px solid;
    FONT-FAMILY: Verdana,Arial,Helvetica;
    LETTER-SPACING: normal;
    HEIGHT: 20px;
    TEXT-DECORATION: none
}
.LeftNavOn
{
    BORDER-RIGHT: #dcdcdc 2px solid;
    BORDER-TOP: #808080 2px solid;
    FONT-WEIGHT: bold;
    FONT-SIZE: 10px;
    MARGIN: 1px;
    BORDER-LEFT: #808080 2px solid;
    WIDTH: 160px;
    CURSOR: default;
    COLOR: #666666;
    LINE-HEIGHT: 10pt;
    PADDING-TOP: 1px;
    BORDER-BOTTOM: #dcdcdc 2px solid;
    FONT-FAMILY: Verdana,Arial,Helvetica;
    LETTER-SPACING: normal;
    HEIGHT: 20px;
    BACKGROUND-COLOR: #78a3d2;
    TEXT-DECORATION: none
}
.LeftNavOff
{
    BORDER-RIGHT: #2f72b0 2px solid;
    BORDER-TOP: #2f72b0 2px solid;
    FONT-WEIGHT: bold;
    FONT-SIZE: 10px;
    MARGIN: 1px;
    BORDER-LEFT: #2f72b0 2px solid;
    WIDTH: 160px;
    CURSOR: hand;
    COLOR: #2f72b0;
    LINE-HEIGHT: 10pt;
    PADDING-TOP: 1px;
    BORDER-BOTTOM: #2f72b0 2px solid;
    FONT-FAMILY: Verdana,Arial,Helvetica;
    LETTER-SPACING: normal;
    HEIGHT: 20px;
    BACKGROUND-COLOR: #2f72b0;
    TEXT-DECORATION: none
}
.LeftNavChosen
{
    BORDER-RIGHT: #78a3d2 2px solid;
    BORDER-TOP: #78a3d2 2px solid;
    FONT-WEIGHT: bold;
    FONT-SIZE: 10px;
    MARGIN: 1px;
    BORDER-LEFT: #78a3d2 2px solid;
    WIDTH: 160px;
    COLOR: black;
    LINE-HEIGHT: 10pt;
    PADDING-TOP: 1px;
    BORDER-BOTTOM: #78a3d2 2px solid;
    FONT-FAMILY: Verdana,Arial,Helvetica;
    LETTER-SPACING: normal;
    HEIGHT: 20px;
    BACKGROUND-COLOR: #78a3d2;
    TEXT-DECORATION: none
}
.LeftNavLink
{
    COLOR: white;
    TEXT-DECORATION: none
}
