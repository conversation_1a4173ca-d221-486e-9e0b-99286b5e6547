A
{
	color: DarkBlue;
}
LeftMenu
{
     height: 100%; 
     width: 100%; 
     background-image: url(/Library/images/esmbkg.jpg); 
     background-repeat: repeat;
     vertical-align: top;
     background-position: left top;
     
    
}
A:hover
{
	text-decoration: underline;
}
.LabelRed
{
	font-weight: bolder;
	color: Red
}
.LabelGreen
{
	font-weight: bolder;
	color: green;
}
.LabelOrange
{
	font-weight: bolder;
	color: DarkOrange
}
.LabelNormal {
    font-weight:400 !important;
}
.DataGrid
{
	background-color: WhiteSmoke;
	font-weight: lighter
}
.DisplayTables
{
	border-right: 7px outset;
	border-top: 7px outset;
	font-weight: bold;
	font-size: 20pt;
	border-left: 7px outset;
	border-bottom: 7px outset;
	background-color: whitesmoke;
}
.DisplayTablesNoFont
{
	border-right: 7px outset;
	border-top: 7px outset;
	border-left: 7px outset;
	border-bottom: 7px outset;
	background-color: whitesmoke;
}
.TableHeader
{	
	text-align: left;
	font-weight: bolder
}
.RowHeader
{	
	text-align: left;
	font-weight: bolder;
}
.LabelGauge
{
    FONT-SIZE: 120%;
    FONT-WEIGHT: bold;
    background-color: whitesmoke
}
.FunctionStep
{
    FONT-SIZE: 68.75%;
    FONT-WEIGHT: bold
}
DIV
{
    FONT-SIZE: 10pt;
    FONT-FAMILY: Arial, Verdana
}
TD
{
    FONT-SIZE: 10pt;
    FONT-FAMILY: Arial, Verdana
}
H1
{
    PADDING-LEFT: 0px;
    FONT-WEIGHT: bold;
    FONT-SIZE: 50px;
    BACKGROUND: #2984ff;
    MARGIN: 0px;
    BEHAVIOR: url(gradient.sct);
    COLOR: black;
    FONT-STYLE: italic;
    FONT-FAMILY: arial black;
    LETTER-SPACING: -3px
}
H2
{
    FONT-SIZE: 20px;
    MARGIN-LEFT: 10px;
    MARGIN-RIGHT: 10px
}
H3
{
    FONT-SIZE: 15px;
    MARGIN-LEFT: 10px;
    MARGIN-RIGHT: 10px
}
H4
{
    FONT-SIZE: 12px;
    FONT-WEIGHT: bolder;
    MARGIN: 5px 0px 10px 5px
}
.Buttons
{
    border-style: outset;
    cursor: hand;
    font-size: 9pt;
    height: 25px;
    font-weight: normal;
    width: 110px
}
/* Accordion */
.accordionHeader
{
    border: 1px solid #2F4F4F;
    color: white;
    background-color: #2E4d7B;
	font-family: Arial, Sans-Serif;
	font-size: 12px;
	font-weight: bold;
    padding: 5px;
    margin-top: 5px;
    cursor: pointer;
}

#master_content .accordionHeader a
{
	color: #FFFFFF;
	background: none;
	text-decoration: none;
}

#master_content .accordionHeader a:hover
{
	background: none;
	text-decoration: underline;
}

.accordionHeaderSelected
{
    border: 1px solid #2F4F4F;
    color: white;
    background-color: #5078B3;
	font-family: Arial, Sans-Serif;
	font-size: 12px;
	font-weight: bold;
    padding: 5px;
    margin-top: 5px;
    cursor: pointer;
}

#master_content .accordionHeaderSelected a
{
	color: #FFFFFF;
	background: none;
	text-decoration: none;
}

#master_content .accordionHeaderSelected a:hover
{
	background: none;
	text-decoration: underline;
}

.accordionContent
{
    background-color: #D3DEEF;
    border: 1px dashed #2F4F4F;
    border-top: none;
    padding: 5px;
    padding-top: 10px;
}
.TreeView			{color:#000000; font-size:8pt; font-family: arial} 
A.TreeView:LINK		{Text-Decoration: none; color:#4682b4; font-size:8pt; font-family: arial} 
A.TreeView:VISITED	{Text-Decoration: none; color:#4682b4; font-size:8pt; font-family: arial} 
A.TreeView:HOVER	{Text-Decoration: underline; color:#4682b4; font-size:8pt; font-family: arial}


.ValidationHeader
{
    Color: Red;
    font-weight:bold;
    font-size:14pt;
}

.PanelMessageBox {
    border: 1px solid blue;
    background: white;
}