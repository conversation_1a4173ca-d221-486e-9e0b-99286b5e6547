﻿<%@ Register TagPrefix="uc1" TagName="Menu" Src="~/Library/UserControls/Menu.ascx" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>

<%@ Page MasterPageFile="~/MasterPage.master" Title="Manage Account Details" Language="vb"
    AutoEventWireup="false" Inherits="AccountManager.ManageAccountDetails" CodeFile="ManageAccountDetails.aspx.vb" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table>
        <tbody>
            <tr>
                <td>
                    <asp:Label ID="lblWelcome" runat="server" CssClass="TableHeader">Manage Account Details</asp:Label><br />
                    <br />
                    <asp:Label ID="lblMessage" runat="server" CssClass="LabelRed"></asp:Label><br />
                    <table>
                        <tbody>
                            <tr>
                                <td>
                                    <asp:Panel ID="pDelete" runat="server">
                                        <table class="DisplayTables">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <table>
                                                            <tbody>
                                                                <tr>
                                                                    <td colspan="2">
                                                                        <table>
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td>Step 1: Update the application name and purpose of the account.</td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td>Step 2: Click the Update button to save changes.</td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <br />
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Account ID:</td><td>
                                                                        <asp:Label ID="lblAccountID" runat="server"></asp:Label></td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Description:</td><td>
                                                                        <asp:Label ID="lblDescription" runat="server"></asp:Label>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td>Application Name:</td><td>
                                                                        <asp:TextBox ID="txtAppName" runat="server" MaxLength="80" Width="400px"></asp:TextBox></td>
                                                                </tr>
                                                                <tr>
                                                                    <td colspan="2">Purpose:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtPurpose" Display="None" ErrorMessage="Please enter purpose." SetFocusOnError="True" ValidationGroup="a"> </asp:RequiredFieldValidator><asp:HiddenField ID="hfAppName" runat="server" />
                                                                        <asp:HiddenField ID="hfPurpose" runat="server" />
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td colspan="2">
                                                                        <asp:TextBox ID="txtPurpose" runat="server" MaxLength="400" TextMode="MultiLine" Rows="9" Columns="70"></asp:TextBox></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <table style="width: 372px">
                                                            <tr>
                                                                <td style="width: 100px; height: 28px">
                                                                    <dx:ASPxButton ID="cmdUpdate" ValidationGroup="a" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                        CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                                        Text="Update">
                                                                    </dx:ASPxButton>
                                                                </td>
                                                                <td style="width: 100px; height: 28px">
                                                                    <dx:ASPxButton ID="cmdReturn" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                        CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                                        Text="Return to My Accounts" Width="158px">
                                                                    </dx:ASPxButton>
                                                                </td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <asp:ValidationSummary ID="ValidationSummary1" ValidationGroup="a" runat="server" />
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </asp:Panel>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
</asp:Content>
