﻿<%@ Register TagPrefix="uc1" TagName="Menu" src="~/Library/UserControls/Menu.ascx" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Page Language="vb" AutoEventWireup="false" Inherits="AccountManager.DelOwners" CodeFile="DelOwners.aspx.vb" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD html 4.0 Transitional//EN">
<html>
	<head runat="server">
		<title>TEST My Users</title>
		<meta content="Microsoft Visual Studio.NET 7.0" name="GENERATOR" />
		<meta content="Visual Basic 7.0" name="CODE_LANGUAGE"/>
		<meta content="JavaScript" name="vs_defaultClientScript"/>
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema"/>
		<meta content="http://schemas.microsoft.com/intellisense/nav4-0" name="vs_targetSchema">
	</head>
	<body>
		<table height="514" cellspacing="0" cellpadding="0" width="690" border="0" >
			<tr valign="top">
				<td width="690" height="514">
					<form id="frmRequests" method="post" runat="server">
						<table height="673" cellspacing="0" cellpadding="0" width="502" border="0" >
							<tr valign="top">
								<td width="10" height="15"></td>
								<td width="492"></td>
							</tr>
							<tr valign="top">
								<td height="658"></td>
								<td>
									<table cellspacing="0" cellpadding="0" border="0" height="657" width="491">
										<tr>
											<td valign="top" width="150"></td>
											<td valign="top"><br/>
												<br/>
												<br/>
												<asp:label id="lblWelcome" CssClass="TableHeader" Runat="server">Delete Owners and Backups</asp:label><br/>
												<br/>
												<asp:label id="lblMessage" CssClass="LabelRed" Runat="server"></asp:label><br/>
												<table>
													<tr>
														<td>
															<table class="DisplayTables">
																<tr>
																	<td>Enter ID:
																		<asp:TextBox id="txtAccount" Runat="Server"></asp:TextBox></td>
																</tr>
																<tr>
																	<td>
																		<dxwdc:aspxbutton id="cmdSearch" tabIndex="0" runat="server" Text="Search">
																			<LookAndFeel Kind="Office2003">
																				<EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black" BackColor="White"></EditorStyle>
																				<LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
																				<ScrollBarButtonStyle BackColor="#84ABE3">
																					<Filters>
																						<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																					</Filters>
																				</ScrollBarButtonStyle>
																				<ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
																					ScrollBarMargin="1"></ElementsSettings>
																				<PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"></PopupStyle>
																				<ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
																					UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
																					<HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
																						<Filters>
																							<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202"></dxwdc:LookAndFeelStyleGradientFilter>
																						</Filters>
																					</HotTrackStyle>
																					<PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
																						<Filters>
																							<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80"></dxwdc:LookAndFeelStyleGradientFilter>
																						</Filters>
																					</PressedStyle>
																					<Filters>
																						<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																					</Filters>
																				</ButtonStyle>
																			</LookAndFeel>
																		</dxwdc:aspxbutton><LOOKANDFEEL Kind="Office2003"><EDITORSTYLE BackColor="White" ForeColor="Black" BorderColor="#6787B8" Font-Names="Verdana" Font-Size="8pt"></EDITORSTYLE>
																			<LABELSTYLE ForeColor="Black" Font-Names="Verdana" Font-Size="8pt"></LABELSTYLE>
																			<SCROLLBARBUTTONSTYLE BackColor="#84ABE3">
																				<FILTERS>
																					<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="221, 236, 254" GradientMode="Horizontal" EndColor="132, 171, 227"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
																				</FILTERS>
																			</SCROLLBARBUTTONSTYLE>
																			<ELEMENTSSETTINGS ScrollBarMargin="1" ScrollBarBackColor="247, 245, 241" DropDownButtonWidth="17px"
																				ScrollBarSize="17px"></ELEMENTSSETTINGS>
																			<POPUPSTYLE BorderColor="#6787B8" Font-Names="Verdana" Font-Size="8pt"></POPUPSTYLE>
																			<BUTTONSTYLE BackColor="#84ABE3" ForeColor="Black" BorderColor="#6787B8" Font-Names="Verdana"
																				Font-Size="8pt" Margin="1" Wrap="False" UsePressedStyle="True" UseHotTrackStyle="True">
																				<HOTTRACKSTYLE BackColor="#FFD599" ForeColor="Black" BorderColor="Navy">
																					<FILTERS>
																						<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="255, 243, 202" EndColor="255, 213, 153"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
																					</FILTERS>
																				</HOTTRACKSTYLE>
																				<PRESSEDSTYLE BackColor="#FFCA86" ForeColor="Black" BorderColor="Navy">
																					<FILTERS>
																						<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="254, 148, 80" EndColor="255, 202, 134"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
																					</FILTERS>
																				</PRESSEDSTYLE>
																				<FILTERS>
																					<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="221, 236, 254" EndColor="132, 171, 227"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
																				</FILTERS>
																			</BUTTONSTYLE>
																		</LOOKANDFEEL></td>
																</tr>
																<tr>
																	<td>
																		<asp:label id="lblInstructions" Runat="server"></asp:label><br/>
																	</td>
																</tr>
																<tr>
																	<td><dxwdc:aspxbutton id="cmdDelete" tabIndex="0" runat="server" Text="Delete" visible="False">
																			<LookAndFeel Kind="Office2003">
																				<EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black" BackColor="White"></EditorStyle>
																				<LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
																				<ScrollBarButtonStyle BackColor="#84ABE3">
																					<Filters>
																						<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																					</Filters>
																				</ScrollBarButtonStyle>
																				<ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
																					ScrollBarMargin="1"></ElementsSettings>
																				<PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"></PopupStyle>
																				<ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
																					UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
																					<HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
																						<Filters>
																							<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202"></dxwdc:LookAndFeelStyleGradientFilter>
																						</Filters>
																					</HotTrackStyle>
																					<PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
																						<Filters>
																							<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80"></dxwdc:LookAndFeelStyleGradientFilter>
																						</Filters>
																					</PressedStyle>
																					<Filters>
																						<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																					</Filters>
																				</ButtonStyle>
																			</LookAndFeel>
																		</dxwdc:aspxbutton>&nbsp;
																		<dxwdc:aspxbutton id="grdReturn" tabIndex="0" runat="server" Text="Return" visible="True">
																			<LookAndFeel Kind="Office2003">
																				<EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black" BackColor="White"></EditorStyle>
																				<LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
																				<ScrollBarButtonStyle BackColor="#84ABE3">
																					<Filters>
																						<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																					</Filters>
																				</ScrollBarButtonStyle>
																				<ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
																					ScrollBarMargin="1"></ElementsSettings>
																				<PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"></PopupStyle>
																				<ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
																					UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
																					<HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
																						<Filters>
																							<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202"></dxwdc:LookAndFeelStyleGradientFilter>
																						</Filters>
																					</HotTrackStyle>
																					<PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
																						<Filters>
																							<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80"></dxwdc:LookAndFeelStyleGradientFilter>
																						</Filters>
																					</PressedStyle>
																					<Filters>
																						<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																					</Filters>
																				</ButtonStyle>
																			</LookAndFeel>
																		</dxwdc:aspxbutton>
																	</td>
																</tr>
															</table>
															<table>
																<TBODY>
																	<tr>
																		<td>
																		</td>
																	</tr>
																</TBODY>
															</table>
															<dxwg:ASPxGrid id="grdUsers" tabIndex="0" runat="server" AutoGenerateColumns="False" BorderColor="#6787B8" BorderStyle="Solid" ExpandBtnHeight="11px" ExpandBtnWidth="11px" HeaderHeight="25px" RowBtnWidth="18px" SearchBtnWidth="17px" SelectedBackColor="49, 106, 197" StatusBarItemSpacing="0">
																<SearchBtnStyle FixedWidth="True"></SearchBtnStyle>
																<GroupItemStyle Wrap="True" BackColor="#C1D8F7" BorderColor="#6787B8"></GroupItemStyle>
																<ButtonBars>
																	<dxwg:ButtonBar ButtonBarType="Navigator">
																		<BarItems>
																			<dxwdc:BarButton ButtonType="MoveFirst"></dxwdc:BarButton>
																			<dxwdc:BarButton ButtonType="MovePrevPage"></dxwdc:BarButton>
																			<dxwdc:BarButton ButtonType="MovePrev"></dxwdc:BarButton>
																			<dxwdc:BarTwoStateEditorButton ButtonType="ChangePageSize"></dxwdc:BarTwoStateEditorButton>
																			<dxwdc:BarButton ButtonType="MoveNext"></dxwdc:BarButton>
																			<dxwdc:BarButton ButtonType="MoveNextPage"></dxwdc:BarButton>
																			<dxwdc:BarButton ButtonType="MoveLast"></dxwdc:BarButton>
																			<dxwdc:BarButton ButtonType="InsertRow"></dxwdc:BarButton>
																			<dxwdc:BarButton ButtonType="EditRow"></dxwdc:BarButton>
																			<dxwdc:BarButton ButtonType="DeleteRow"></dxwdc:BarButton>
																			<dxwdc:BarButton ButtonType="Refresh"></dxwdc:BarButton>
																			<dxwdc:BarEditModeButton ButtonType="Post"></dxwdc:BarEditModeButton>
																			<dxwdc:BarEditModeButton ButtonType="Cancel"></dxwdc:BarEditModeButton>
																		</BarItems>
																	</dxwg:ButtonBar>
																</ButtonBars>
																<HeaderStyle FixedWidth="True" FixedHeight="True" Wrap="False" BorderStyle="None" Font-Bold="True"></HeaderStyle>
																<StatusBars>
																	<dxwg:StatusBar Height="20px" StatusBarType="Regular">
																		<BarItems>
																			<dxwdc:BarStatusSection StatusSectionType="Status"></dxwdc:BarStatusSection>
																			<dxwdc:BarStatusSection StatusSectionType="VisibleInterval"></dxwdc:BarStatusSection>
																			<dxwdc:BarStatusSection StatusSectionType="TotalVisible"></dxwdc:BarStatusSection>
																			<dxwdc:BarStatusSection StatusSectionType="TotalRows"></dxwdc:BarStatusSection>
																		</BarItems>
																	</dxwg:StatusBar>
																</StatusBars>
																<ItemStyle FixedWidth="True" Wrap="False" BackColor="White" Font-Size="7.5pt" VerticalAlign="Middle"></ItemStyle>
																<FooterStyle FixedWidth="True" FixedHeight="True" BackColor="#B0CBF1" Font-Bold="True" Wrap="False"></FooterStyle>
																<BehaviorOptions CellSelection="True" EnableMultiSelection="True"></BehaviorOptions>
																<Columns>
																	<dxwg:BoundColumn VisibleIndex="0" DataField="AccountID"></dxwg:BoundColumn>
																</Columns>
                                                                <ExpandBtnStyle BackColor="#F9F9F9" BorderColor="#6787B8" BorderStyle="Solid" BorderWidth="1px">
                                                                    <PressedStyle BackColor="#D0D0D0" BorderColor="Navy" ForeColor="Black">
                                                                    </PressedStyle>
                                                                    <HotTrackStyle BackColor="White" BorderColor="Navy">
                                                                    </HotTrackStyle>
                                                                </ExpandBtnStyle>
                                                                <RowBtnStyle BorderStyle="None">
                                                                </RowBtnStyle>
                                                                <GroupedHeaderStyle BorderColor="#6787B8" BorderStyle="Solid" BorderWidth="1px">
                                                                    <PressedStyle BorderColor="Navy">
                                                                    </PressedStyle>
                                                                    <HotTrackStyle BorderColor="Navy">
                                                                    </HotTrackStyle>
                                                                </GroupedHeaderStyle>
                                                                <SearchEditorStyle BorderColor="White">
                                                                </SearchEditorStyle>
                                                                <BarBtnEditorStyle BorderStyle="None">
                                                                </BarBtnEditorStyle>
                                                                <TitleStyle BackColor="#6787B8" ForeColor="White" />
                                                                <LookAndFeel Kind="Office2003">
                                                                    <ElementsSettings DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241" ScrollBarMargin="1"
                                                                        ScrollBarSize="17px" />
                                                                    <PopupStyle BorderColor="#6787B8" Font-Names="Verdana" Font-Size="8pt">
                                                                    </PopupStyle>
                                                                    <ButtonStyle BackColor="#84ABE3" BorderColor="#6787B8" Font-Names="Verdana" Font-Size="8pt"
                                                                        ForeColor="Black" Margin="1" UseHotTrackStyle="True" UsePressedStyle="True" Wrap="False">
                                                                        <PressedStyle BackColor="#FFCA86" BorderColor="Navy" ForeColor="Black">
                                                                            <Filters>
                                                                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80">
                                                                                </dxwdc:LookAndFeelStyleGradientFilter>
                                                                            </Filters>
                                                                        </PressedStyle>
                                                                        <Filters>
                                                                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254">
                                                                            </dxwdc:LookAndFeelStyleGradientFilter>
                                                                        </Filters>
                                                                        <HotTrackStyle BackColor="#FFD599" BorderColor="Navy" ForeColor="Black">
                                                                            <Filters>
                                                                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202">
                                                                                </dxwdc:LookAndFeelStyleGradientFilter>
                                                                            </Filters>
                                                                        </HotTrackStyle>
                                                                    </ButtonStyle>
                                                                    <LabelStyle Font-Names="Verdana" Font-Size="8pt" ForeColor="Black" />
                                                                    <EditorStyle BackColor="White" BorderColor="#6787B8" Font-Names="Verdana" Font-Size="8pt"
                                                                        ForeColor="Black">
                                                                    </EditorStyle>
                                                                    <ScrollBarButtonStyle BackColor="#84ABE3">
                                                                        <Filters>
                                                                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal"
                                                                                StartColor="221, 236, 254">
                                                                            </dxwdc:LookAndFeelStyleGradientFilter>
                                                                        </Filters>
                                                                    </ScrollBarButtonStyle>
                                                                </LookAndFeel>
                                                                <ButtonBarStyle BorderColor="#6787B8" BorderStyle="Solid" BorderWidth="1px">
                                                                </ButtonBarStyle>
                                                                <HeaderDraggedStyle BorderColor="LightGray" BorderStyle="Solid" BorderWidth="1px">
                                                                    <Filters>
                                                                        <dxwdc:LookAndFeelStyleAlphaFilter FinishOpacity="50" FinishX="50">
                                                                        </dxwdc:LookAndFeelStyleAlphaFilter>
                                                                    </Filters>
                                                                </HeaderDraggedStyle>
                                                                <StatusBarStyle BackColor="#DDECFE" BorderStyle="None">
                                                                    <Filters>
                                                                        <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254">
                                                                        </dxwdc:LookAndFeelStyleGradientFilter>
                                                                    </Filters>
                                                                </StatusBarStyle>
                                                                <FooterItemStyle BackColor="#B0CBF1">
                                                                </FooterItemStyle>
                                                                <BarBtnStyle BorderStyle="None">
                                                                    <PressedStyle BorderStyle="Solid">
                                                                    </PressedStyle>
                                                                    <HotTrackStyle BorderStyle="Solid">
                                                                    </HotTrackStyle>
                                                                </BarBtnStyle>
                                                                <PreviewStyle BackColor="#F9FCFF" ForeColor="#5881B9">
                                                                </PreviewStyle>
                                                                <SearchItemStyle BackColor="#C1D8F7">
                                                                </SearchItemStyle>
                                                                <GroupIndentStyle BackColor="#C1D8F7">
                                                                </GroupIndentStyle>
                                                                <GroupPanelStyle BackColor="#3E6DB9" Font-Bold="True" ForeColor="#DDECFE">
                                                                </GroupPanelStyle>
                                                                <AlternatingItemStyle BackColor="#E7F2FE" />
															</dxwg:ASPxGrid>
														</td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</td>
							</tr>
						</table>
					</form>
				</td>
			</tr>
		</table>
	</body>
</html>
