Imports System
Imports System.Data
Imports System.Text
Imports System.Collections.Specialized
Imports KCC.Common


Namespace GroupManager


Partial Class UserPopUp_New
    Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

    End Sub


    Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
        'CODEGEN: This method call is required by the Web Form Designer
        'Do not modify it using the code editor.
        InitializeComponent()
    End Sub

#End Region

    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'Put user code to initialize the page here
        If Not IsPostBack Then
            If Not Request.QueryString("UserID") = "" Then
                TextBoxUserName.Text = Request.QueryString("UserID")
            End If
        End If
    End Sub
    Private Sub ButtonSubmit_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles ButtonSubmit.Click
        ErrorValidationSummaryUC.ClearErrorMessage = True
        DataGridUserSearchResults.Visible = True
        Dim oAD As New AD
        Dim strUserID, strGetUserName, strUserName, strFirstName, strLastName, strGetUserID As String
        Dim dtbUser As New DataTable
        Dim drUser As DataRow
        Dim dsTemp As DataSet
        Dim dcUserID As DataColumn = New DataColumn("UserID")
        dcUserID.DataType = System.Type.GetType("System.String")
        Dim dcUserName As DataColumn = New DataColumn("UserName")
        dcUserName.DataType = System.Type.GetType("System.String")
        dtbUser.Columns.Add(dcUserID)
        dtbUser.Columns.Add(dcUserName)
        drUser = dtbUser.NewRow()
        If RadioButtonSearch.SelectedValue = "I" Then
            strUserID = TextBoxUserID.Text
            If strUserID = "" Then
                DataGridUserSearchResults.Visible = False
                ErrorValidationSummaryUC.ShowErrorMessage(Constants.MSG_ENTER_USERID)
                Return
            Else
                'strGetUserName = oAD.GetUserName(strUserID)
                'If strGetUserName <> "0" Then
                '    drUser.Item("UserID") = strUserID
                '    drUser.Item("UserName") = strGetUserName
                '    dtbUser.Rows.Add(drUser)
                'End If
                dsTemp = oAD.FindUsers(strUserID, True)
                If Not dsTemp Is Nothing Then
                    dtbUser = dsTemp.Tables(0)
                End If
            End If
        Else
            strUserName = TextBoxUserName.Text
            If (strUserName = "") Then
                DataGridUserSearchResults.Visible = False
                ErrorValidationSummaryUC.ShowErrorMessage(Constants.MSG_ENTER_USERNAME)
                Return
            Else
                'Dim astrUserName() As String = strUserName.ToString.Split(",")
                'If astrUserName.Length = 2 Then
                '    strFirstName = astrUserName(1)
                '    strLastName = astrUserName(0)
                '    strGetUserID = oAD.GetUserID(strFirstName, strLastName)
                '    If strGetUserID <> "0" Then
                '        drUser.Item("UserID") = strGetUserID
                '        drUser.Item("UserName") = strUserName
                '        dtbUser.Rows.Add(drUser)
                '    End If
                'End If
                dsTemp = oAD.FindUsers(strUserName, False)
                If Not dsTemp Is Nothing Then
                    dtbUser = dsTemp.Tables(0)
                End If
            End If
        End If
        Dim bFoundUsers As Boolean = False
        If Not dtbUser Is Nothing Then
            If dtbUser.Rows.Count > 0 Then
                Dim oGroupManager As GroupManager = GroupManager.GetCurrentSingleton()
                oGroupManager.SearchUserResult = dtbUser
                DataGridUserSearchResults.DataSource = dtbUser
                DataGridUserSearchResults.DataBind()
                bFoundUsers = True
            End If
        End If
        If bFoundUsers = False Then
            DataGridUserSearchResults.Visible = False
            ErrorValidationSummaryUC.ShowErrorMessage(Constants.MSG_NO_USERS)
        End If
    End Sub

    Private Sub DataGridUserSearchResults_ItemDataBound(ByVal sender As Object, ByVal e As System.Web.UI.WebControls.DataGridItemEventArgs) Handles DataGridUserSearchResults.ItemDataBound
        If e.Item.ItemType = ListItemType.Item Or e.Item.ItemType = ListItemType.AlternatingItem Then
            Dim drvUserRow As DataRowView = CType(e.Item.DataItem, DataRowView)

            'On selecting the user the pop should close taking the user ID
            Dim oHtmlInputRadioButton As HtmlInputRadioButton = CType(e.Item.FindControl("RadioGroupUser"), HtmlInputRadioButton)
            oHtmlInputRadioButton.Attributes.Add("onclick", "SetValue('" & Convert.ToString(drvUserRow("UserID")).Trim() & "', '" & Convert.ToString(drvUserRow("UserName")).Trim() & "', '" & Request.QueryString("UserControlID") & "_TextBoxUserID', '" & Request.QueryString("UserControlID") & "_LabelUserName')")
        End If
    End Sub

    Private Sub DataGridUserSearchResults_PageIndexChanged(ByVal source As Object, ByVal e As System.Web.UI.WebControls.DataGridPageChangedEventArgs) Handles DataGridUserSearchResults.PageIndexChanged
        Dim oGroupManager As GroupManager = GroupManager.GetCurrentSingleton()
        Dim dtbTemp As DataTable = oGroupManager.SearchUserResult
        DataGridUserSearchResults.CurrentPageIndex = e.NewPageIndex
        DataGridUserSearchResults.DataSource = dtbTemp
        DataGridUserSearchResults.DataBind()
    End Sub
End Class

End Namespace
