'Title: Active Directory Class, UserInfo
'Created by: <PERSON> on 
'Purpose:  Retrieve User Information via their BID
'Assumptions:  Everyone logged on has a BID
'Effects:  You are able to retrive different information about the current
'           user.
'Inputs:  Request.ServerVariables("LOGON_USER")
'Returns:  UserID, Name, location, etc.

Namespace GroupManager

Public Class UserInfo
    'This page allows other web pages to pull the user information from KC Active
    'directory based on the user id that is logged on.

    Dim strUserName, objNS
    Dim objUser
    Dim strUserEmail, strUserLocation, strUserPhone, strUserOffice, sPath

    Dim sUser = System.Web.HttpContext.Current.Request.ServerVariables("LOGON_USER")

    

    Function GetPath()
        GetPath = sPath
    End Function

    Function GetUserName()
        GetUserName = strUserName
    End Function

    Function GetUserID()
        GetUserID = sUser
    End Function

    Function GetUserEmail()
        GetUserEmail = strUserEmail
    End Function

    Function GetUserLocation()
        GetUserLocation = strUserLocation
    End Function

    Function GetUserOffice()
        GetUserOffice = strUserOffice
    End Function

    Function GetUserPhone()
        GetUserPhone = strUserPhone
    End Function
    'Function GMGroupAccess()
    '    Call GetID()
    '    'Return sUser
    '    Call IsmemberGM()

    '    End If
    'End Function

    'Title: New Constructor for UserInfo
    'Created by: Shane Z Smith on 5/13/02
    'Purpose:  To create a new instance of UserInfo with the BID of the current user logged in
    'Assumptions:  None
    'Effects:  Creates user info (name, id, email) based on the current user logged in
    'Inputs:  None
    'Returns:  None
    Public Sub New()

        Dim strCN As String
        Dim strLdap As String

        ' strLdap = "LDAP://OU=Accounts,DC=kcc,DC=com"
        strLdap = "LDAP://kcc.com"

            Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLdap)
        Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
        Dim objSearchRes As System.DirectoryServices.SearchResult

        Call GetID()

        objSearcher.Filter = ("(objectClass=user)")
        objSearcher.Filter = ("(sAMAccountName=" & sUser & ")")

        If objSearcher.FindAll.Count > 0 Then
            For Each objSearchRes In objSearcher.FindAll

                'path is the Active Directory path to that object
                sPath = objSearchRes.GetDirectoryEntry.Path
                'CN is usually the BID of the person (unique identifier in AD)

                sUser = objSearchRes.GetDirectoryEntry.Properties.Item("cn").Value
                strUserName = objSearchRes.GetDirectoryEntry.Properties.Item("Description").Value
                strUserEmail = objSearchRes.GetDirectoryEntry.Properties.Item("Email").Value

            Next
        End If

        If strUserEmail = "" Then
            strUserEmail = sUser & "@USTCAX00.kcc.com" & ";"
        End If


    End Sub

    'Parses the KCUS ID, by removeing the "KCUS\" and returning only the ID
    'This will not work for ID's larger then 6 characters (B12345A).  This is
    'primarly used to retrieve a user's general information (Email, Name, Phone, etc.).
    Private Sub GetID()

        Dim iLen = Len(sUser)
        Dim iFind = InStr(sUser, "\")

        If iFind > 0 Then
            sUser = Right(sUser, (iLen - iFind))
        End If

        If sUser.Length > 6 Then
            sUser = sUser.Remove(6, 1)
        End If

    End Sub


End Class
End Namespace
