Imports System.Data
Imports System.Data.SqlClient
Namespace AccountManager
    Partial Class PwdException
        Inherits System.Web.UI.Page

        Protected Sub Page_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
            Dim CurrUser = System.Web.HttpContext.Current.Request.ServerVariables("LOGON_USER")
            LoadData()
            If CheckAccess(CurrUser.ToString, Request.QueryString("AccountID").ToString) Then
                If LoadWorkStations(Request.QueryString("AccountID").ToString) Then
                    'do nothing as owner can sign the aggrement
                    lblMessage.Text = ""
                    lblMessage.Visible = True
                    LoadExceptionHistory()
                Else
                    'disbale this feature
                    lblMessage.Text = "This non user id is not restricted to any workstations. The id needs to be restricted first before password not required feature can be enabled for this id."
                    lblMessage.ForeColor = Drawing.Color.Red
                    cmdUpdate.Enabled = False
                    lblMessage.Enabled = True
                    lblMessage.Visible = True
                    LoadExceptionHistory()
                End If
            Else
                lblMessage.Text = "You do not have the access to use this feature. This feature can only be used by Owner of the non user id."
                lblMessage.ForeColor = Drawing.Color.Red
                cmdUpdate.Enabled = False
                lblMessage.Enabled = True
                lblMessage.Visible = True
            End If

        End Sub
        Sub LoadData()
            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader
            Dim arUsers As New ArrayList
            Dim hlpClean As New Helper

            strSQL = "SELECT * FROM vUsr " & _
                     "WHERE AccountID = '" & Request.QueryString("AccountId") & "'"
            srRead = dbGet.GetDataReaderByStringId(strSQL)

            If srRead.Read Then
                lblAccountDesc.Text = hlpClean.IsValueNull(srRead.Item("Description"))
                lblAccountID.Text = hlpClean.IsValueNull(srRead.Item("AccountID"))
                chkbxException.Checked = srRead.Item("ActException")
                If CBool(srRead.Item("ActException")) = True Then
                    cmdUpdate.Enabled = False
                    cmdDisable.Enabled = True
                End If
            End If
        End Sub
        Sub LoadExceptionHistory()
            Dim usrcur As UserInfo
            Dim dtbExceptionForAccount As DataTable
            Dim StrSQL As String
            Dim dbGet As New DataAccess

            StrSQL = "Select * from dbo.tbAccExcpHistory where rtrim(AccountID) Like '" & Request.QueryString("AccountId") & "' order by DateSigned desc"
            Try
                dtbExceptionForAccount = dbGet.GetExceptionDataTable(StrSQL)
                If dtbExceptionForAccount.Rows.Count > 0 Then
                    grdUsers.DataSource = dtbExceptionForAccount
                    grdUsers.DataBind()
                    grdUsers.ExpandAll()

                    'If grdUsers.Rows.Count < 40 Then
                    'grdUsers.PageSize = grdUsers.Rows.Count
                    'Else
                    'grdUsers.PageSize = 40
                    ' End If
                Else
                grdUsers.Enabled = False
                lblNoException.Visible = True
                End If
            Catch ex As Exception
                ' lblMessage.Text = ex.ToString
            End Try
        End Sub
        Function CheckAccess(ByVal strowner As String, ByVal straccount As String) As Boolean
            'FUNCTION : Returns True only if currevt user is the owner for the account.
            Dim dbget As New DataAccess
            Dim dReader As SqlDataReader
            Dim strsql As String
            strsql = "Select * from dbo.tbUMOwners where UserName Like '" & strowner.Substring(5) & "' and AccountID like '" & straccount & "'"
            Try
                dReader = dbget.GetDataReaderByStringId(strsql)
                If dReader.HasRows Then
                    While dReader.Read
                        If dReader.Item(2).ToString Like "1" Then
                            Return True
                            Exit Function
                        Else
                            Return False
                        End If
                    End While
                Else
                    Return False
                End If
            Catch ex As Exception
                lblMessage.Text = ex.ToString
                Return False
            End Try
        End Function
        Function LoadWorkStations(ByVal strUserID As String) As Boolean
            Dim strUser As String
            Dim strWrk As String()
            strUser = UCase(strUserID)
            Dim usrGet As New UserInfo(strUser)
            Try
                If usrGet.UserFound() Then
                    strWrk = Split(usrGet.WorkStations, ",")
                    If usrGet.WorkStations.ToString.Length > 4 Then
                        Return True ' WORKSTATION FOUND
                    Else
                        Return False 'NO WORKSTATION FOUND
                    End If
                Else
                    Return False 'NO USER FOUND HENCE NO WORKSTATION FOUND
                End If
            Catch ex As Exception
                Return False
            End Try
        End Function

        Protected Sub cmdUpdate_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmdUpdate.Click
            Dim dbUpd As New DataAccess
            Dim email As New Email
            Dim strSQL As String
            Dim usrGet As New UserInfo(Request.QueryString("AccountId").ToString)
            strSQL = "Update tbUMUsers Set ActException = 1 where AccountID Like '" & Request.QueryString("AccountId").ToString & "'"
            dbUpd.GetDataReaderByStringId(strSQL)
            UpdateHistory("I Agree")
            usrGet.ADDUserToDenyGroup(usrGet.LdapPath.ToString.Remove(0, 7))
            cmdUpdate.Enabled = False
            lblMessage.Visible = True
            lblMessage.Text = "Exception Added to the Database"
            lblMessage.ForeColor = Drawing.Color.Red
            LoadExceptionHistory()
            email.CopyAllBackups(Request.QueryString("AccountId").ToString)
            email.SendNoPasswordNetworkNotice("<EMAIL>", "ACTION REQUIRED:  Add ID to Envision", Request.QueryString("AccountId").ToString, "enable")

        End Sub

        Protected Sub cmdReturn_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmdReturn.Click
            Response.Redirect("/AccountManager/MyUsers.aspx")
        End Sub

        Protected Sub cmdDisable_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmdDisable.Click
            Dim dbUpd As New DataAccess
            Dim email As New Email
            Dim strSQL As String
            Dim usrGet As New UserInfo(Request.QueryString("AccountId").ToString)
            strSQL = "Update tbUMUsers Set ActException = 0 where AccountID Like '" & Request.QueryString("AccountId").ToString & "'"
            dbUpd.GetDataReaderByStringId(strSQL)
            UpdateHistory("I Disagree")
            usrGet.RemoveUserFromDenyGroup(usrGet.LdapPath.ToString.Remove(0, 7))
            cmdUpdate.Enabled = False
            lblMessage.Visible = True
            lblMessage.Text = "Exception removed from the Database"
            lblMessage.ForeColor = Drawing.Color.Red
            LoadExceptionHistory()
            email.CopyAllBackups(Request.QueryString("AccountId").ToString)
            email.SendNoPasswordNetworkNotice("<EMAIL>", "ACTION REQUIRED:  Remove ID from Envision", Request.QueryString("AccountId").ToString, "disable")
        End Sub
        Public Sub UpdateHistory(ByVal action As String)
            Dim CurrUser = System.Web.HttpContext.Current.Request.ServerVariables("LOGON_USER")
            Dim dbUpd As New DataAccess
            Dim strSQL As String
            strSQL = "Insert into dbo.tbAccExcpHistory values('" & Request.QueryString("AccountId").ToString & "','" & CurrUser.Substring(5) & "','" & Date.Now.ToString & "','" & action & "')"
            dbUpd.GetDataReaderByStringId(strSQL)
        End Sub
    End Class
End Namespace

