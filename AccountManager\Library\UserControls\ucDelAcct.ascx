﻿<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Control Language="vb" AutoEventWireup="false" Inherits="AccountManager.ucDelAcct" CodeFile="ucDelAcct.ascx.vb" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<link href="../../Styles.css" type="text/css" rel="stylesheet" />

<table class="DisplayTables" style="width:743px;">
	<tr>
		<td class="FunctionStep">Delete Account</td>
	</tr>
	<tr>
		<td><asp:label id="lblMessage" CssClass="LabelRed" Runat="server"></asp:label></td>
	</tr>
	<tr>
		<td >
            <table>
                <tr>
                    <td>Account ID:<span class="ValidationHeader">*</span></td>
                    <td><asp:TextBox id="txtAccount" Runat="server"></asp:TextBox></td>
                    <td><dx:aspxbutton id="cmdLookup"
                runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"  csspostfix="Office2003Blue"
                spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css" ValidationGroup="GetDelUser" text="Lookup ID"></dx:aspxbutton></td>
                    <td><asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtAccount" ErrorMessage="Please enter Non-User ID." SetFocusOnError="True" ValidationGroup="GetDelUser">Please enter Non-User ID.</asp:RequiredFieldValidator></td>
                </tr>
                <tr>
                    <td>
                        Description:
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lblDescription" runat="server"></asp:Label>
                    </td>
                </tr>
            </table>
            &nbsp;<asp:HiddenField ID="hfOwnerDelBackup" runat="server" />
        </td>
	</tr>
	<tr>
		<td>
            <table>
                <tr>
                    <td>
                        <dx:aspxbutton id="cmdDelete" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                text="Delete"></dx:aspxbutton>
                    </td>
                    <td>
                        <dx:aspxbutton id="cmdReset"
                runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css" csspostfix="Office2003Blue"
                spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css" text="Reset"></dx:aspxbutton>
                    </td>
                </tr>

            </table>
            
                        
        </td>
	</tr>
	<tr>
		<td>
			<asp:label id="lblInstructions" Runat="server"></asp:label><br/>
		</td>
	</tr>
    <tr>
        <td>
            <dx:aspxgridview id="grdDelUsers" runat="server" autogeneratecolumns="False" clientinstancename="grid"
                cssfilepath="~/App_Themes/PlasticBlue/{0}/styles.css" csspostfix="PlasticBlue" Width="743px">
<SettingsBehavior AllowFocusedRow="True"></SettingsBehavior>

<Styles CssPostfix="PlasticBlue" CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css">
<Header SortingImageSpacing="10px" ImageSpacing="10px"></Header>

<AlternatingRow BackColor="Silver"></AlternatingRow>
</Styles>

<SettingsPager ShowDefaultImages="False">
<AllButton Text="All"></AllButton>

<NextPageButton Text="Next &gt;"></NextPageButton>

<PrevPageButton Text="&lt; Prev"></PrevPageButton>
</SettingsPager>

<ImagesFilterControl>
<LoadingPanel Url="~/App_Themes/PlasticBlue/Editors/Loading.gif"></LoadingPanel>
</ImagesFilterControl>

<Images SpriteCssFilePath="~/App_Themes/PlasticBlue/{0}/sprite.css">
<LoadingPanelOnStatusBar Url="~/App_Themes/PlasticBlue/GridView/gvLoadingOnStatusBar.gif"></LoadingPanelOnStatusBar>
    
<LoadingPanel Url="~/App_Themes/PlasticBlue/GridView/Loading.gif"></LoadingPanel>
</Images>
                <SettingsBehavior AllowSort="false" AllowGroup="false" />
<Columns>
    <%--GroupIndex="0" SortIndex="0" SortOrder="Ascending"--%>
<dx:GridViewDataTextColumn FieldName="AccountID"  VisibleIndex="0"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="UserNameDN" Caption="User Name" VisibleIndex="1"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="UserName" Caption="User ID" VisibleIndex="2"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="Access" Caption="Access" VisibleIndex="3"></dx:GridViewDataTextColumn>
</Columns>

<StylesEditors>
<CalendarHeader Spacing="11px"></CalendarHeader>

<ProgressBar Height="25px"></ProgressBar>
</StylesEditors>
</dx:aspxgridview>
        </td>
    </tr>
</table>
