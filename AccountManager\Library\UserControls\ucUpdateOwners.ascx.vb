Imports DevExpress.Web.ASPxGrid
Imports System.Data
Namespace AccountManager

    Partial Class ucUpdateOwners
        Inherits System.Web.UI.UserControl

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub
        Protected WithEvents lblNewOwner As System.Web.UI.WebControls.Label


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
            LoadInstruction()
            If Not Page.IsPostBack Then
                Dim hlpLoad As New Helper
                hlpLoad.FillAccessDropDown(ddlOwnType, True)
            End If
        End Sub

        Function UpdateOwners_OLD()

            Dim strOldUser As String
            Dim i As Integer
            Dim blnLoad As Boolean = True
            Dim reqUpd As New Requests
            Dim usrcur As New UserInfo
            Dim usrNew As New UserInfo(Trim(txtNewOwner.Text))
            Dim hlpIns As New Helper

            If usrNew.UserFound Then
                'add new owner/backup only if employee
                Dim strSQL As String
                Dim dbInsert As New DataAccess
                Dim hlpClean As New Helper
                Dim usrFnd As New UserInfo(txtAccount.Text)
                If (ddlOwnType.SelectedItem.ToString.Contains("Owner") And IsEmployee(Trim(txtNewOwner.Text))) Or (Not ddlOwnType.SelectedItem.ToString.Contains("Owner")) Then
		  'If (ddlOwnType.SelectedItem.ToString.Contains("Owner") And IsEmployee(Trim(txtNewOwner.Text))) Then
                   ' usrFnd.SetDescription(usrFnd.LdapPath, txtDescription.Text) 'SIMAR
                    strSQL = "sp_UMOwner_Add '" & txtAccount.Text & "'," & ddlOwnType.SelectedValue & ",'" & usrNew.GetUserID & "','" & hlpClean.dbCleanUpString(usrNew.DisplayName) & "'"
                    dbInsert.UpdateDBByStringId(strSQL)
                    hlpClean.InsertLog(txtAccount.Text, [Global].GblActOwnAdd, Now, "Added " & ddlOwnType.SelectedItem.Text & " " & Trim(txtNewOwner.Text))
                    FindAccount()
                Else
                    lblMessage.Text = "Owner of a Non-User Account can only be a permanent KC Employee."
                    lblMessage.ForeColor = Drawing.Color.Red
                End If
            Else
                lblMessage.Text = "New owner " & txtNewOwner.Text & " does not exist in the domain."

            End If


        End Function


        Function UpdateOwners()

            'Dim strOldUser As String
            Dim i As Integer
            Dim blnLoad As Boolean = True
            Dim reqUpd As New Requests
            'Dim usrcur As New UserInfo
            Dim usrNew As New UserInfo(Trim(txtNewOwner.Text), True)
            Dim hlpIns As New Helper

            If usrNew.UserFound Then
                'add new owner/backup only if employee
                'Dim strSQL As String
                Dim dbInsert As New DataAccess
                Dim hlpClean As New Helper
                Dim OwnerType As String
                OwnerType = usrNew.UserEmployeeType
                Dim usrFnd As New UserInfo(txtAccount.Text)
                ' If (ddlOwnType.SelectedItem.ToString.Contains("Owner") And IsEmployee(Trim(txtNewOwner.Text))) Or (Not ddlOwnType.SelectedItem.ToString.Contains("Owner")) Then
                'If Owner
                Dim strUserID As String = (usrNew.GetUserID.ToString).ToUpper
                If strUserID = hfCurrentOwner.Value Then
                    lblMessage.Text = "New User ID should different from Current Owner."
                    lblMessage.ForeColor = Drawing.Color.Red
                ElseIf strUserID = hfCurrentDelegate.Value Then
                    lblMessage.Text = "New User ID should different from current Delegate."
                    If ddlOwnType.SelectedValue = 1 Then
                        lblMessage.Text = lblMessage.Text & "If you want to make current delegate as owner then you should change delegate first."
                    End If
                    lblMessage.ForeColor = Drawing.Color.Red
                Else
                    Dim IsChange As Boolean = False
                    Dim strNewDescription As String = ""
                    If ddlOwnType.SelectedValue = 1 Then
                        If OwnerType = "E" Then
                            IsChange = True
                            strNewDescription = String.Format("({0} {1} {2}) {3}", usrNew.GetUserID, usrNew.FirstName, usrNew.LastName, txtDisplayName.Text.Trim)
                            ' usrFnd.SetDescription(usrFnd.LdapPath, txtDescription.Text) 'SIMAR
                            'strSQL = "sp_UMOwner_Add '" & txtAccount.Text & "'," & ddlOwnType.SelectedValue & ",'" & usrNew.GetUserID & "','" & hlpClean.dbCleanUpString(usrNew.DisplayName) & "'"
                            'dbInsert.UpdateDBByStringId(strSQL)
                            'hlpClean.InsertLog(txtAccount.Text, [Global].GblActOwnAdd, Now, "Added " & ddlOwnType.SelectedItem.Text & " " & Trim(txtNewOwner.Text))
                            'FindAccount()
                        Else
                            lblMessage.Text = "Owner of a Non-User Account can only be a permanent KC Employee."
                            lblMessage.ForeColor = Drawing.Color.Red
                        End If
                        'If Backup/Delegate
                    Else
                        If OwnerType = "E" Or OwnerType = "C" Then
                            If ddlOwnType.SelectedValue = 2 Then
                                Dim strTempBackup As String = "," & strUserID & ","
                                If hfCurrentBackup.Value.Contains(strTempBackup) Then
                                    lblMessage.Text = "User ID is already backup of this account."
                                    lblMessage.ForeColor = Drawing.Color.Red
                                Else
                                    IsChange = True
                                    ' usrFnd.SetDescription(usrFnd.LdapPath, txtDescription.Text) 'SIMAR
                                    'strSQL = "sp_UMOwner_Add '" & txtAccount.Text & "'," & ddlOwnType.SelectedValue & ",'" & usrNew.GetUserID & "','" & hlpClean.dbCleanUpString(usrNew.DisplayName) & "'"
                                    'dbInsert.UpdateDBByStringId(strSQL)
                                    'hlpClean.InsertLog(txtAccount.Text, [Global].GblActOwnAdd, Now, "Added " & ddlOwnType.SelectedItem.Text & " " & Trim(txtNewOwner.Text))
                                    'FindAccount()
                                End If
                            ElseIf ddlOwnType.SelectedValue = 3 Then
                                IsChange = True
                            End If
                        Else
                            lblMessage.Text = "Please enter valid " & ddlOwnType.SelectedItem.Text & "."
                            lblMessage.ForeColor = Drawing.Color.Red
                        End If
                    End If
                    If IsChange Then
                        If dbInsert.AddUMOwnerDelegateBackup(usrFnd.GetUserID, strUserID, usrNew.DisplayName, ddlOwnType.SelectedValue, DataAccess.getManager()) Then
                            Dim IsError As Boolean = False
                            If strNewDescription <> "" Then
                                Dim strSQL As String = "Update tbUMUsers set description =" & "'" & strNewDescription & "'" & " where AccountId = '" & usrFnd.GetUserID & "'"
                                Try
                                    dbInsert.UpdateDBByStringId(strSQL)
                                    usrFnd.SetDescriptionInAD(usrFnd.LdapPath, strNewDescription, "")
                                Catch ex As Exception
                                    lblMessage.Text = "Error Occurred: " & ex.Message.ToString
                                    IsError = True
                                End Try
                            End If
                            If Not IsError Then
                                FindAccount()
                                lblMessage.Text = String.Format("User ({0}) has been added to account ({1}) as {2}", strUserID, usrFnd.GetUserID, ddlOwnType.SelectedItem.Text)
                            End If

                        Else
                            If dbInsert.IsError Then
                                lblMessage.Text = "Error Occurred: " & dbInsert.ErrorMsg
                            End If
                        End If
                    End If
                End If
                
                
            Else
                lblMessage.Text = "New owner " & txtNewOwner.Text & " does not exist in the domain."
            End If

        End Function





        Function DeleteAccounts()

            'Dim strUserId, strComments, strOwner As String
            'Dim intReqId As Integer
            'Dim i As Integer
            'Dim reqUpd As New Requests
            'Dim usrcur As New UserInfo
            'Dim hlpDel As New Helper

            'Trace.Warn("DeleteAccounts " & grdUsers.GetSelectedRowCount())

            'For i = 0 To (grdUsers.GetSelectedRowCount()) - 1
            '        Dim selectedRow As row = grdUsers.GetSelectedRow(i)

            '    If selectedRow.Level = grdUsers.GetGroupCount() Then

            '        strUserId = selectedRow.DataControllerRow("AccountID").ToString()
            '        strOwner = selectedRow.DataControllerRow("UserName").ToString()
            '        reqUpd.AccountID = strUserId
            '        reqUpd.OldOwner = strOwner
            '        Trace.Warn("Delete: " & strUserId)
            '        'reqUpd.DeleteAccountID()
            '        hlpDel.InsertLog(txtAccount.Text, [Global].GblActOwnDel, Now, usrcur.GetUserID & " Deleted")

            '    End If
            'Next i
            'FindAccount()
        End Function


        Sub FindAccount()
            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim dtOwners As DataTable
            
            hfCurrentOwner.Value = ""
            hfCurrentBackup.Value = ""
            hfCurrentDelegate.Value = ""

            strSQL = "SELECT * FROM vUsr WHERE AccountID = '" & txtAccount.Text & "' order by Access desc "
            dtOwners = dbGet.GetBackupByAccountID(strSQL)
            grdUsers.KeyFieldName = "ID"
            grdUsers.DataSource = dtOwners
            grdUsers.DataBind()
            grdUsers.ExpandAll()

            If dtOwners.Rows.Count > 0 Then
                Dim usrFnd As New UserInfo(txtAccount.Text)
                If usrFnd.UserFound Then
                    txtAccount.Text = usrFnd.GetUserID
                    txtDisplayName.Text = usrFnd.DisplayName
                    txtDescription.Text = usrFnd.Description
                    ' EnableControls(True)

                    cmdUpdate.Visible = True
                    '   cmdDelete.Visible = True
                    pUpdateOwner.Visible = True
                    lblMessage.Text = ""

                    
                    For Each dtrow As DataRow In dtOwners.Rows
                        Dim intType As Integer = dtrow("Type")
                        Dim strUserName As String = dtrow("UserName")
                        If intType = 1 Then
                            hfCurrentOwner.Value = strUserName.ToUpper
                        ElseIf intType = 2 Then
                            hfCurrentBackup.Value = hfCurrentBackup.Value & "," & strUserName.ToUpper
                        ElseIf intType = 3 Then
                            hfCurrentDelegate.Value = strUserName.ToUpper
                        End If
                    Next
                    hfCurrentBackup.Value = hfCurrentBackup.Value & ","
                    txtAccount.Enabled = False
                    cmdSearch.Enabled = False
                Else
                    cmdUpdate.Visible = False
                    ' cmdDelete.Visible = False
                    pUpdateOwner.Visible = False
                    lblMessage.Text = "No account found in AD."
                End If
                
            Else
                cmdUpdate.Visible = False
                ' cmdDelete.Visible = False
                pUpdateOwner.Visible = False
                lblMessage.Text = "No account found."
            End If

        End Sub

        Sub LoadInstruction()

            lblInstructions.Text = "" & _
                "Step 1: Enter Account ID you want to udpate. Click Search.<br/>" & _
                "Step 2: Enter the new owner ID:<br/>" & _
                "Step 3: Select if the person is the owner or backup owner.<br/>" & _
                "Step 4: Update Account Description<br/>" & _
                "Step 5: Click the Update/Delete button.<br/>" & _
                "Note: Once the update button is clicked the owner/backup will be added to this account and description will be updated."
        End Sub

        Private Sub cmdUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdUpdate.Click
            UpdateOwners()
        End Sub

        Private Sub cmdReset_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdReset.Click
            txtAccount.Enabled = True
            cmdSearch.Enabled = True
            txtDescription.Text = ""
            txtNewOwner.Text = ""
            ddlOwnType.SelectedIndex = 0
            pUpdateOwner.Visible = False
            lblMessage.Text = ""
        End Sub


        Private Sub cmdSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdSearch.Click
            FindAccount()
        End Sub

        'Private Sub cmdDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdDelete.Click
        '    DeleteAccounts()
        'End Sub
        Public Function IsEmployee(ByVal id As String) As Boolean

            ' Set to false to begin.
            IsEmployee = False

            If Len(id) = 6 Then
                If (IsEmplChar(Left(id, 1)) = True) And (IsNumeric(Mid(id, 2, 5)) = True) Then
                    IsEmployee = True
                End If
            End If
        End Function
        Public Function IsEmplChar(ByVal FirstPos As String) As Boolean

            'Check for first character of ID - if contractor, set to true

            If UCase(Left(FirstPos, 1)) = "B" Or UCase(Left(FirstPos, 1)) = "E" Or UCase(Left(FirstPos, 1)) = "J" Or _
              UCase(Left(FirstPos, 1)) = "K" Or UCase(Left(FirstPos, 1)) = "L" Or UCase(Left(FirstPos, 1)) = "M" Or _
              UCase(Left(FirstPos, 1)) = "S" Or UCase(Left(FirstPos, 1)) = "W" Or _
              UCase(Left(FirstPos, 1)) = "Y" Or UCase(Left(FirstPos, 1)) = "Z" Or UCase(Left(FirstPos, 1)) = "G" Or _
              UCase(Left(FirstPos, 1)) = "U" Then

                IsEmplChar = True

            Else
                IsEmplChar = False
            End If
        End Function
    End Class
End Namespace
