Imports System.Data
Namespace AccountManager

    Partial Class ucPastDue
        Inherits System.Web.UI.UserControl

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
            If Not Page.IsPostBack Then
                Me.txtDate.Text = Now.ToShortDateString
            End If
            LoadPastDueReport()
        End Sub

        Sub LoadPastDueReport()
            If IsDate(Me.txtDate.Text) Then
                GetPastDueByDate(CDate(Me.txtDate.Text))
                Me.lblMessage.Text = ""
            Else
                Me.lblMessage.Text = "You must enter a valid date in the text area.  MM/DD/YYYY"
            End If
            'Dim usrcur As New UserInfo
            'Dim strSQL As String
            'Dim dbGet As New DataAccess
            'Dim srRead As SqlClient.SqlDataReader
            'Dim arUsers As New ArrayList

            'strSQL = "sp_UMHistory_List 3,0"
            'srRead = dbGet.GetDataReaderByStringId(strSQL)

            'If srRead.HasRows Then
            '    grdReport.DataSource = srRead
            '    grdReport.DataBind()
            'End If

            'srRead.Close()
            'srRead = Nothing
            'dbGet.CloseConnections()

        End Sub

        Function GetPastDueByDate(ByVal dtDate As DateTime) As String

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            strSQL = "SELECT * FROM vUsr WHERE Access='Owner' AND actDisabled=0 AND ExpirationDate < '" & dtDate & "' AND ACTException = 0"
            srRead = dbGet.GetDataReaderByStringId(strSQL)
            Trace.Warn(strSQL)
            If srRead.HasRows Then
                grdReport.DataSource = srRead
                grdReport.DataBind()
            End If
            grdReport.EnableViewState = True

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()

            Return strSQL
        End Function

        Private Sub cmdExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdExport.Click

            Dim prtReport As New Print
            Dim strSQL As String

            If IsDate(Me.txtDate.Text) Then
                strSQL = "SELECT AccounTID as [Account ID],UserName as [User ID],UserNameDN as [User Name],Access,Description,ExpirationDate as [Past Due Date] FROM vUsr WHERE Access='Owner' AND actDisabled=0 AND ExpirationDate < '" & Me.txtDate.Text & "' AND ACTException = 0"
                Me.lblMessage.Text = ""

                hlDownload.NavigateUrl = prtReport.CreateCSV(strSQL)
                hlDownload.Text = "Download File"
            Else
                Me.lblMessage.Text = "You must enter a valid date in the text area.  MM/DD/YYYY"
            End If

        End Sub

        Private Sub cmdGet_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdGet.Click

        End Sub
    End Class

End Namespace
