Imports System.Data
Namespace AccountManager

    Public Class History


        Dim intDisabled, intExpiredNotDis, intTotal, intExpireSoon, intPendOwnReq, intGreenAccounts, intExpireAndDis As Integer

        ReadOnly Property Disabled()
            Get
                Return intDisabled
            End Get
        End Property
        ReadOnly Property ExpiredNotDis()
            Get
                Return intExpiredNotDis
            End Get
        End Property
        ReadOnly Property Total()
            Get
                Return intTotal
            End Get
        End Property
        ReadOnly Property ExpireSoon()
            Get
                Return intExpireSoon
            End Get
        End Property
        ReadOnly Property PendOwnReq()
            Get
                Return intPendOwnReq
            End Get
        End Property
        ReadOnly Property GreenAccounts()
            Get
                Return intGreenAccounts
            End Get
        End Property
        ReadOnly Property ExpireAndDis()
            Get
                Return intExpireAndDis
            End Get
        End Property

        Sub CollectHistory(ByVal intGetCount As Integer)

            Dim strSQL As String

            strSQL = "sp_UMHistory_List 2," & intGetCount
            intDisabled = GetData(strSQL)

            strSQL = "sp_UMHistory_List 3," & intGetCount
            intExpiredNotDis = GetData(strSQL)

            strSQL = "sp_UMHistory_List 4," & intGetCount
            intExpireAndDis = GetData(strSQL)

            strSQL = "sp_UMHistory_List 1," & intGetCount
            intExpireSoon = GetData(strSQL)

            strSQL = "sp_UMHistory_List 5," & intGetCount
            intGreenAccounts = GetData(strSQL)

            strSQL = "sp_UMHistory_List 6," & intGetCount
            intTotal = GetData(strSQL)

            strSQL = "sp_UMHistory_List 7," & intGetCount
            intPendOwnReq = GetData(strSQL)


        End Sub

        Private Function GetData(ByVal SQL As String) As Integer

            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader
            Dim strSQL As String

            srRead = dbGet.GetDataReaderByStringId(SQL)

            If srRead.Read Then
                GetData = srRead.Item(0)
            End If

            srRead.Close()
            dbGet.CloseConnections()

            Return GetData

        End Function

    End Class

End Namespace
