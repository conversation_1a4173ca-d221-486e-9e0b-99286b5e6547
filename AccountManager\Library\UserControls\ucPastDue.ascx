﻿<%@ Control Language="vb" AutoEventWireup="false" Inherits="AccountManager.ucPastDue"
    CodeFile="ucPastDue.ascx.vb" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<table class="DisplayTables">
    <tr>
        <td class="FunctionStep" colspan="2">
            Past Due Accounts</td>
    </tr>
    <tr>
        <td colspan="2">
            <asp:Label ID="lblMessage" CssClass="LabelRed" runat="server"></asp:Label></td>
    </tr>
    <tr>
        <td colspan="2">
            Enter Date (MM/DD/YYY):&nbsp;&nbsp;<asp:TextBox ID="txtDate" runat="server"></asp:TextBox></td>
    </tr>
    <tr>
        <td colspan="2">
            <dxwdc:ASPxButton ID="cmdGet" TabIndex="0" Text="Get Report" runat="server">
                <LookAndFeel Kind="Office2003">
                    <EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black"
                        BackColor="White">
                    </EditorStyle>
                    <LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
                    <ScrollBarButtonStyle BackColor="#84ABE3">
                        <Filters>
                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal"
                                StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
                        </Filters>
                    </ScrollBarButtonStyle>
                    <ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
                        ScrollBarMargin="1"></ElementsSettings>
                    <PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8">
                    </PopupStyle>
                    <ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
                        UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
                        <HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
                            <Filters>
                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202">
                                </dxwdc:LookAndFeelStyleGradientFilter>
                            </Filters>
                        </HotTrackStyle>
                        <PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
                            <Filters>
                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80">
                                </dxwdc:LookAndFeelStyleGradientFilter>
                            </Filters>
                        </PressedStyle>
                        <Filters>
                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254">
                            </dxwdc:LookAndFeelStyleGradientFilter>
                        </Filters>
                    </ButtonStyle>
                </LookAndFeel>
            </dxwdc:ASPxButton>
            &nbsp;
            <dxwdc:ASPxButton ID="cmdExport" TabIndex="0" Text="Export" runat="server">
                <LookAndFeel Kind="Office2003">
                    <EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black"
                        BackColor="White">
                    </EditorStyle>
                    <LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
                    <ScrollBarButtonStyle BackColor="#84ABE3">
                        <Filters>
                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal"
                                StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
                        </Filters>
                    </ScrollBarButtonStyle>
                    <ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
                        ScrollBarMargin="1"></ElementsSettings>
                    <PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8">
                    </PopupStyle>
                    <ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
                        UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
                        <HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
                            <Filters>
                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202">
                                </dxwdc:LookAndFeelStyleGradientFilter>
                            </Filters>
                        </HotTrackStyle>
                        <PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
                            <Filters>
                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80">
                                </dxwdc:LookAndFeelStyleGradientFilter>
                            </Filters>
                        </PressedStyle>
                        <Filters>
                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254">
                            </dxwdc:LookAndFeelStyleGradientFilter>
                        </Filters>
                    </ButtonStyle>
                </LookAndFeel>
            </dxwdc:ASPxButton>

        </td>
    </tr>
    <tr>
        <td colspan="2">
            <asp:HyperLink ID="hlDownload" runat="server" Font-Size="14" ForeColor="#000099"
                Font-Bold="True" Target="_blank"></asp:HyperLink></td>
    </tr>
    <tr>
        <td colspan="2">
            <dxwg:ASPxGrid ID="grdReport" TabIndex="0" runat="server" BorderColor="#6787B8" PageIndexButtonCount="4" StatusBarItemSpacing="0"
                SearchBtnWidth="17px" ExpandBtnHeight="11px" SelectedBackColor="49, 106, 197"
                RowBtnWidth="18px" HeaderHeight="25px" ExpandBtnWidth="11px" AutoGenerateColumns="False"
                BorderStyle="Solid" DataKeyField="ID">
                <AlternatingItemStyle BackColor="#E7F2FE"></AlternatingItemStyle>
                <ExpandBtnStyle BorderWidth="1px" BorderColor="#6787B8" BorderStyle="Solid" BackColor="#F9F9F9">
                    <HotTrackStyle BorderColor="Navy" BackColor="White">
                    </HotTrackStyle>
                    <PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#D0D0D0">
                    </PressedStyle>
                </ExpandBtnStyle>
                <ButtonBarStyle BorderWidth="1px" BorderColor="#6787B8" BorderStyle="Solid">
                </ButtonBarStyle>
                <GroupPanelStyle Font-Bold="True" ForeColor="#DDECFE" BackColor="#3E6DB9">
                </GroupPanelStyle>
                <SearchBtnStyle FixedWidth="True">
                </SearchBtnStyle>
                <TitleStyle ForeColor="White" BackColor="#6787B8"></TitleStyle>
                <GroupItemStyle BorderColor="#6787B8" BackColor="#C1D8F7" Wrap="True">
                </GroupItemStyle>
                <RowBtnStyle BorderStyle="None">
                </RowBtnStyle>
                <ButtonBars>
                    <dxwg:ButtonBar ButtonBarType="Navigator">
                        <BarItems>
                            <dxwdc:BarButton ButtonType="MoveFirst">
                            </dxwdc:BarButton>
                            <dxwdc:BarButton ButtonType="MovePrevPage">
                            </dxwdc:BarButton>
                            <dxwdc:BarButton ButtonType="MovePrev">
                            </dxwdc:BarButton>
                            <dxwdc:BarTwoStateEditorButton ButtonType="ChangePageSize">
                            </dxwdc:BarTwoStateEditorButton>
                            <dxwdc:BarButton ButtonType="MoveNext">
                            </dxwdc:BarButton>
                            <dxwdc:BarButton ButtonType="MoveNextPage">
                            </dxwdc:BarButton>
                            <dxwdc:BarButton ButtonType="MoveLast">
                            </dxwdc:BarButton>
                            <dxwdc:BarButton ButtonType="Refresh">
                            </dxwdc:BarButton>
                            <dxwdc:BarEditModeButton ButtonType="Post">
                            </dxwdc:BarEditModeButton>
                        </BarItems>
                    </dxwg:ButtonBar>
                </ButtonBars>
                <HeaderStyle FixedWidth="True" Font-Bold="True" BorderStyle="None" FixedHeight="True"
                    Wrap="False"></HeaderStyle>
                <StatusBars>
                    <dxwg:StatusBar Height="20px" StatusBarType="Regular">
                        <BarItems>
                            <dxwdc:BarStatusSection StatusSectionType="Status">
                            </dxwdc:BarStatusSection>
                            <dxwdc:BarStatusSection StatusSectionType="VisibleInterval">
                            </dxwdc:BarStatusSection>
                            <dxwdc:BarStatusSection StatusSectionType="TotalVisible">
                            </dxwdc:BarStatusSection>
                            <dxwdc:BarStatusSection StatusSectionType="TotalRows">
                            </dxwdc:BarStatusSection>
                        </BarItems>
                    </dxwg:StatusBar>
                </StatusBars>
                <ItemStyle FixedWidth="True" VerticalAlign="Middle" Font-Size="7.5pt" BackColor="White"
                    Wrap="False"></ItemStyle>
                <HeaderDraggedStyle BorderWidth="1px" BorderColor="LightGray" BorderStyle="Solid">
                    <Filters>
                        <dxwdc:LookAndFeelStyleAlphaFilter FinishOpacity="50" FinishX="50">
                        </dxwdc:LookAndFeelStyleAlphaFilter>
                    </Filters>
                </HeaderDraggedStyle>
                <LayoutOptions ShowFocusedBorder="True"></LayoutOptions>
                <LookAndFeel Kind="Office2003">
                    <EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black"
                        BackColor="White">
                    </EditorStyle>
                    <LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
                    <ScrollBarButtonStyle BackColor="#84ABE3">
                        <Filters>
                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal"
                                StartColor="221, 236, 254">
                            </dxwdc:LookAndFeelStyleGradientFilter>
                        </Filters>
                    </ScrollBarButtonStyle>
                    <ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
                        ScrollBarMargin="1"></ElementsSettings>
                    <PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8">
                    </PopupStyle>
                    <ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
                        UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
                        <HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
                            <Filters>
                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202">
                                </dxwdc:LookAndFeelStyleGradientFilter>
                            </Filters>
                        </HotTrackStyle>
                        <PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
                            <Filters>
                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80">
                                </dxwdc:LookAndFeelStyleGradientFilter>
                            </Filters>
                        </PressedStyle>
                        <Filters>
                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254">
                            </dxwdc:LookAndFeelStyleGradientFilter>
                        </Filters>
                    </ButtonStyle>
                </LookAndFeel>
                <GroupedHeaderStyle BorderWidth="1px" BorderColor="#6787B8" BorderStyle="Solid">
                    <HotTrackStyle BorderColor="Navy">
                    </HotTrackStyle>
                    <PressedStyle BorderColor="Navy">
                    </PressedStyle>
                </GroupedHeaderStyle>
                <BarBtnStyle BorderStyle="None">
                    <HotTrackStyle BorderStyle="Solid">
                    </HotTrackStyle>
                    <PressedStyle BorderStyle="Solid">
                    </PressedStyle>
                </BarBtnStyle>
                <FooterStyle FixedWidth="True" Font-Bold="True" FixedHeight="True" BackColor="#B0CBF1"
                    Wrap="False"></FooterStyle>
                <PreviewStyle ForeColor="#5881B9" BackColor="#F9FCFF">
                </PreviewStyle>
                <BarBtnEditorStyle BorderStyle="None">
                </BarBtnEditorStyle>
                <SearchEditorStyle BorderColor="White">
                </SearchEditorStyle>
                <BehaviorOptions AllowInsert="False" EnableMultiSelection="True" AllowDelete="False"
                    AutoEdit="False"></BehaviorOptions>
                <SearchItemStyle BackColor="#C1D8F7">
                </SearchItemStyle>
                <Columns>
                    <dxwg:BoundColumn HeaderText="Account ID" VisibleIndex="0" DataField="AccountID"
                        Width="115px">
                    </dxwg:BoundColumn>
                    <dxwg:BoundColumn Visible="False" HeaderText="Owner/Backup ID" DataField="UserName"
                        Width="140px">
                    </dxwg:BoundColumn>
                    <dxwg:BoundColumn HeaderText="Owner/Backup" VisibleIndex="1" DataField="UserNameDN"
                        Width="115px">
                    </dxwg:BoundColumn>
                    <dxwg:BoundColumn VisibleIndex="3" DataField="Description" Width="382px">
                    </dxwg:BoundColumn>
                    <dxwg:BoundColumn HeaderText="Past Due" VisibleIndex="2" DataField="ExpirationDate">
                    </dxwg:BoundColumn>
                    <dxwg:BoundColumn HeaderText="Access" VisibleIndex="4" DataField="Access" Width="75px">
                    </dxwg:BoundColumn>
                </Columns>
                <NavigatorButtons InsertRow="False" DeleteRow="False" Cancel="False" EditRow="False">
                </NavigatorButtons>
                <FooterItemStyle BackColor="#B0CBF1">
                </FooterItemStyle>
                <StatusBarStyle BorderStyle="None" BackColor="#DDECFE">
                    <Filters>
                        <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254">
                        </dxwdc:LookAndFeelStyleGradientFilter>
                    </Filters>
                </StatusBarStyle>
                <GroupIndentStyle BackColor="#C1D8F7">
                </GroupIndentStyle>
            </dxwg:ASPxGrid>
        </td>
    </tr>
</table>
