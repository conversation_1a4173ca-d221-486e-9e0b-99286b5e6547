﻿<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Control Language="vb" AutoEventWireup="false" Inherits="AccountManager.ucOrphanAccounts" CodeFile="ucOrphanAccounts.ascx.vb" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<script>
    function OnGetRowValues(values) {
alblAccountID.SetText(values[0]);
alblDescription.SetText(values[1]);
}
    </script>
<table class="DisplayTables">
	<tr>
		<td class="FunctionStep" colspan="2">Accounts not in Account Manager</td>
	</tr>
	<tr>
		<td><asp:label id="lblMessage" Runat="server" CssClass="LabelRed"></asp:label></td>
	</tr>
	<tr>
		<td>Account ID:</td>
		<td>
            <dx:ASPxLabel ID="lblID" runat="server" ClientInstanceName="alblAccountID">
            </dx:ASPxLabel>
        </td>
	</tr>
	<tr>
		<td style="height: 21px">Description:</td>
		<td style="height: 21px">
            <dx:ASPxLabel ID="lblDesc" runat="server" ClientInstanceName="alblDescription">
            </dx:ASPxLabel>
        </td>
	</tr>
	<tr>
		<td colspan="2"><asp:label id="lblInstructions" Runat="server"></asp:label><br/>
		</td>
	</tr>
</table>
<table>
	<tr>
		<td style="width: 630px">
            <dx:aspxgridview id="grdUsers" runat="server" autogeneratecolumns="False" cssfilepath="~/App_Themes/PlasticBlue/{0}/styles.css"
                csspostfix="PlasticBlue" keyfieldname="AccountID" width="644px" ClientInstanceName="grid">
<Styles CssPostfix="PlasticBlue" CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css">
<Header SortingImageSpacing="10px" ImageSpacing="10px"></Header>

<AlternatingRow BackColor="InactiveCaptionText"></AlternatingRow>
</Styles>

<SettingsPager ShowDefaultImages="False">
<AllButton Text="All"></AllButton>

<NextPageButton Text="Next &gt;"></NextPageButton>

<PrevPageButton Text="&lt; Prev"></PrevPageButton>
</SettingsPager>

<ImagesFilterControl>
<LoadingPanel Url="~/App_Themes/PlasticBlue/Editors/Loading.gif"></LoadingPanel>
</ImagesFilterControl>

<Images SpriteCssFilePath="~/App_Themes/PlasticBlue/{0}/sprite.css">
<LoadingPanelOnStatusBar Url="~/App_Themes/PlasticBlue/GridView/gvLoadingOnStatusBar.gif"></LoadingPanelOnStatusBar>

<LoadingPanel Url="~/App_Themes/PlasticBlue/GridView/Loading.gif"></LoadingPanel>
</Images>
<Columns>
<dx:GridViewDataTextColumn FieldName="AccountID" Caption="AccountID" VisibleIndex="0"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="UserNameDN" Caption="Owner/Backup" VisibleIndex="1"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="UserName" Caption="Owner/Backup ID" VisibleIndex="2"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="DateSubmitted" Caption="Date Added" VisibleIndex="3"></dx:GridViewDataTextColumn>
</Columns>

<StylesEditors>
<CalendarHeader Spacing="11px"></CalendarHeader>

<ProgressBar Height="25px"></ProgressBar>
</StylesEditors>
                <SettingsBehavior AllowFocusedRow="True" />
                <ClientSideEvents FocusedRowChanged="function(s, e) {
	grid.GetRowValues(grid.GetFocusedRowIndex(), 'AccountID;Description', OnGetRowValues);
}" />
</dx:aspxgridview>
        </td>
	</tr>
	<tr>
		<td style="width: 630px">
            &nbsp;<dx:aspxgridview id="grdNewAccount" runat="server" autogeneratecolumns="False"
                cssfilepath="~/App_Themes/PlasticBlue/{0}/styles.css" csspostfix="PlasticBlue"
                keyfieldname="AccountID" width="639px">
<Styles CssPostfix="PlasticBlue" CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css">
<Header SortingImageSpacing="10px" ImageSpacing="10px"></Header>

<AlternatingRow BackColor="InactiveCaptionText"></AlternatingRow>
</Styles>

<SettingsPager ShowDefaultImages="False">
<AllButton Text="All"></AllButton>

<NextPageButton Text="Next &gt;"></NextPageButton>

<PrevPageButton Text="&lt; Prev"></PrevPageButton>
</SettingsPager>

<ImagesFilterControl>
<LoadingPanel Url="~/App_Themes/PlasticBlue/Editors/Loading.gif"></LoadingPanel>
</ImagesFilterControl>

<Images SpriteCssFilePath="~/App_Themes/PlasticBlue/{0}/sprite.css">
<LoadingPanelOnStatusBar Url="~/App_Themes/PlasticBlue/GridView/gvLoadingOnStatusBar.gif"></LoadingPanelOnStatusBar>

<LoadingPanel Url="~/App_Themes/PlasticBlue/GridView/Loading.gif"></LoadingPanel>
</Images>
<Columns>
<dx:GridViewDataTextColumn FieldName="AccountID" GroupIndex="0" SortIndex="0" SortOrder="Ascending" VisibleIndex="0"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="UserNameDN" Caption="Owner/Backup" VisibleIndex="1"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="UserName" Caption="Owner/Backup ID" VisibleIndex="2"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="Access" Caption="Access" VisibleIndex="3"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="Description" Caption="Description" Visible="False" VisibleIndex="5"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="ExpirationDate" Caption="EXpirationDate" Visible="False" VisibleIndex="4"></dx:GridViewDataTextColumn>
</Columns>

<Settings ShowGroupPanel="True"></Settings>

<StylesEditors>
<CalendarHeader Spacing="11px"></CalendarHeader>

<ProgressBar Height="25px"></ProgressBar>
</StylesEditors>
</dx:aspxgridview></td>
	</tr>
</table>
