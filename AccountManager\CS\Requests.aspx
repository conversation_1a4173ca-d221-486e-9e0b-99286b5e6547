﻿<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="uc1" TagName="Menu" Src="~/Library/UserControls/Menu.ascx" %>

<%@ Page MasterPageFile="~/MasterPage.master" Title="Requests" Language="vb" AutoEventWireup="false" Inherits="AccountManager.Requests1"
    CodeFile="Requests.aspx.vb" %>

<asp:content id="Content1" contentplaceholderid="ContentPlaceHolder1" runat="Server">
    <table>
        <tr>
            <td>
						<asp:label id="lblWelcome" Runat="server" CssClass="TableHeader">User Requests</asp:label><br/>
						<br/>
						<asp:label id="lblMessage" Runat="server" CssClass="LabelRed"></asp:label><br/>
						<table>
							<tr>
								<td>
									<table class="DisplayTables">
		
											<tr>
												<td>Current Requests older then 14 days</td>
											</tr>
											<tr>
												<td>Step 1: Highlight the requests to update/delete.<br/>
													Step 2: Click the Update/Delete button.<br/>
													Note: Once the update button is clicked the suggested owner will be made the 
													new owner.<br/>
												</td>
											</tr>
											<tr>
												<td><dxwdc:aspxbutton id="cmdUpdate" tabIndex="0" runat="server" Text="Update">
														<LookAndFeel Kind="Office2003">
															<EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black" BackColor="White"></EditorStyle>
															<LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
															<ScrollBarButtonStyle BackColor="#84ABE3">
																<Filters>
																	<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																</Filters>
															</ScrollBarButtonStyle>
															<ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
																ScrollBarMargin="1"></ElementsSettings>
															<PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"></PopupStyle>
															<ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
																UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
																<HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
																	<Filters>
																		<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202"></dxwdc:LookAndFeelStyleGradientFilter>
																	</Filters>
																</HotTrackStyle>
																<PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
																	<Filters>
																		<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80"></dxwdc:LookAndFeelStyleGradientFilter>
																	</Filters>
																</PressedStyle>
																<Filters>
																	<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																</Filters>
															</ButtonStyle>
														</LookAndFeel>
													</dxwdc:aspxbutton>&nbsp;
													<dxwdc:aspxbutton id="cmdDelete" tabIndex="0" runat="server" Text="Delete">
														<LookAndFeel Kind="Office2003">
															<EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black" BackColor="White"></EditorStyle>
															<LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
															<ScrollBarButtonStyle BackColor="#84ABE3">
																<Filters>
																	<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																</Filters>
															</ScrollBarButtonStyle>
															<ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
																ScrollBarMargin="1"></ElementsSettings>
															<PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"></PopupStyle>
															<ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
																UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
																<HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
																	<Filters>
																		<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202"></dxwdc:LookAndFeelStyleGradientFilter>
																	</Filters>
																</HotTrackStyle>
																<PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
																	<Filters>
																		<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80"></dxwdc:LookAndFeelStyleGradientFilter>
																	</Filters>
																</PressedStyle>
																<Filters>
																	<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																</Filters>
															</ButtonStyle>
														</LookAndFeel>
													</dxwdc:aspxbutton></td>
							</tr></table>	</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td><dxwg:aspxgrid id="grdRequests" tabIndex="0" runat="server" PageIndexButtonCount="5" PreviewField="Comments"
							Width="995px" StatusBarItemSpacing="0" SearchBtnWidth="17px" ExpandBtnHeight="11px" SelectedBackColor="49, 106, 197"
							RowBtnWidth="18px" HeaderHeight="25px" ExpandBtnWidth="11px" AutoGenerateColumns="False" BorderStyle="Solid"
							BorderColor="#6787B8">
							<AlternatingItemStyle BackColor="#E7F2FE"></AlternatingItemStyle>
							<ExpandBtnStyle BorderWidth="1px" BorderColor="#6787B8" BorderStyle="Solid" BackColor="#F9F9F9">
								<HotTrackStyle BorderColor="Navy" BackColor="White"></HotTrackStyle>
								<PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#D0D0D0"></PressedStyle>
							</ExpandBtnStyle>
							<ButtonBarStyle BorderWidth="1px" BorderColor="#6787B8" BorderStyle="Solid"></ButtonBarStyle>
							<GroupPanelStyle Font-Bold="True" ForeColor="#DDECFE" BackColor="#3E6DB9"></GroupPanelStyle>
							<SearchBtnStyle FixedWidth="True"></SearchBtnStyle>
							<TitleStyle ForeColor="White" BackColor="#6787B8"></TitleStyle>
							<GroupItemStyle FixedWidth="True" BorderColor="#6787B8" BackColor="#C1D8F7" Wrap="False"></GroupItemStyle>
							<RowBtnStyle BorderStyle="None"></RowBtnStyle>
							<HeaderStyle FixedWidth="True" Font-Bold="True" BorderStyle="None" FixedHeight="True" Wrap="False"></HeaderStyle>
							<StatusBars>
								<dxwg:StatusBar Height="20px" StatusBarType="Regular">
									<BarItems>
										<dxwdc:BarStatusSection StatusSectionType="Status"></dxwdc:BarStatusSection>
										<dxwdc:BarStatusSection StatusSectionType="VisibleInterval"></dxwdc:BarStatusSection>
										<dxwdc:BarStatusSection StatusSectionType="TotalVisible"></dxwdc:BarStatusSection>
										<dxwdc:BarStatusSection StatusSectionType="TotalRows"></dxwdc:BarStatusSection>
									</BarItems>
								</dxwg:StatusBar>
							</StatusBars>
							<ItemStyle FixedWidth="True" VerticalAlign="Middle" Font-Size="7.5pt" BackColor="White" Wrap="False"></ItemStyle>
							<HeaderDraggedStyle BorderWidth="1px" BorderColor="LightGray" BorderStyle="Solid">
								<Filters>
									<dxwdc:LookAndFeelStyleAlphaFilter FinishOpacity="50" FinishX="50"></dxwdc:LookAndFeelStyleAlphaFilter>
								</Filters>
							</HeaderDraggedStyle>
							<LookAndFeel Kind="Office2003">
								<EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black" BackColor="White"></EditorStyle>
								<LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
								<ScrollBarButtonStyle BackColor="#84ABE3">
									<Filters>
										<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
									</Filters>
								</ScrollBarButtonStyle>
								<ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
									ScrollBarMargin="1"></ElementsSettings>
								<PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"></PopupStyle>
								<ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
									UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
									<HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
										<Filters>
											<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202"></dxwdc:LookAndFeelStyleGradientFilter>
										</Filters>
									</HotTrackStyle>
									<PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
										<Filters>
											<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80"></dxwdc:LookAndFeelStyleGradientFilter>
										</Filters>
									</PressedStyle>
									<Filters>
										<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
									</Filters>
								</ButtonStyle>
							</LookAndFeel>
							<GroupedHeaderStyle BorderWidth="1px" BorderColor="#6787B8" BorderStyle="Solid">
								<HotTrackStyle BorderColor="Navy"></HotTrackStyle>
								<PressedStyle BorderColor="Navy"></PressedStyle>
							</GroupedHeaderStyle>
							<BarBtnStyle BorderStyle="None">
								<HotTrackStyle BorderStyle="Solid"></HotTrackStyle>
								<PressedStyle BorderStyle="Solid"></PressedStyle>
							</BarBtnStyle>
							<FooterStyle FixedWidth="True" Font-Bold="True" FixedHeight="True" BackColor="#B0CBF1" Wrap="False"></FooterStyle>
							<PreviewStyle ForeColor="#5881B9" BackColor="#F9FCFF"></PreviewStyle>
							<BarBtnEditorStyle BorderStyle="None"></BarBtnEditorStyle>
							<AppearanceOptions ShowPreview="True" ShowNavigator="False" ShowGroupPanel="False"></AppearanceOptions>
							<SearchEditorStyle BorderColor="White"></SearchEditorStyle>
							<BehaviorOptions AllowInsert="False" EnableMultiSelection="True" AllowDelete="False" AutoEdit="False"
								EnableRowDblClick="False" PostBackOnKeyPress="False"></BehaviorOptions>
							<SearchItemStyle BackColor="#C1D8F7"></SearchItemStyle>
							<Columns>
								<dxwg:BoundColumn Visible="False" DataField="RequestID" Width="178px"></dxwg:BoundColumn>
								<dxwg:BoundColumn HeaderText="Account ID" VisibleIndex="0" DataField="AccountID" Width="126px"></dxwg:BoundColumn>
								<dxwg:BoundColumn HeaderText="Request Type" VisibleIndex="2" DataField="RequestType" Width="153px"></dxwg:BoundColumn>
								<dxwg:BoundColumn HeaderText="Status" VisibleIndex="1" DataField="Status" Width="153px"></dxwg:BoundColumn>
								<dxwg:BoundColumn Visible="False" HeaderText="Comments" DataField="Comments" Width="382px"></dxwg:BoundColumn>
								<dxwg:BoundColumn HeaderText="Current Owner" VisibleIndex="4" DataField="OldOwner" Width="126px"></dxwg:BoundColumn>
								<dxwg:BoundColumn HeaderText="New Owner" VisibleIndex="3" DataField="NewOwner" Width="126px"></dxwg:BoundColumn>
								<dxwg:BoundColumn HeaderText="Date Submitted" VisibleIndex="5" DataField="DateSubmitted" Width="126px"></dxwg:BoundColumn>
								<dxwg:BoundColumn HeaderText="Submitted By" VisibleIndex="6" DataField="UserSubmitted" Width="153px"></dxwg:BoundColumn>
							</Columns>
							<NavigatorButtons Ok="False" InsertRow="False" DeleteRow="False" Cancel="False" Refresh="False" EditRow="False"></NavigatorButtons>
							<FooterItemStyle BackColor="#B0CBF1"></FooterItemStyle>
							<StatusBarStyle BorderStyle="None" BackColor="#DDECFE">
								<Filters>
									<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
								</Filters>
							</StatusBarStyle>
							<GroupIndentStyle BackColor="#C1D8F7"></GroupIndentStyle>
						</dxwg:aspxgrid></td>
				</tr>
			</table>
</asp:content>
