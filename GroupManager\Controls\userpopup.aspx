<%@ Reference Control="~/controls/errorvalidationsummary.ascx" %>
<%@ Register TagPrefix="GME" TagName="errorValidationSummary" Src="errorValidationSummary.ascx" %>
<%@ Page Language="vb" AutoEventWireup="false" Inherits="GroupManager.UserPopUp" CodeFile="UserPopUp.aspx.vb" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<title>User LookUp Screen</title>
		<meta name="GENERATOR" content="Microsoft Visual Studio .NET 7.1"/>
		<meta name="CODE_LANGUAGE" content="Visual Basic .NET 7.1"/>
		<meta name="vs_defaultClientScript" content="JavaScript"/>
		<meta name="vs_targetSchema" content="http://schemas.microsoft.com/intellisense/ie5"/>
		<script language="javascript"> 
			function SetValue(strUser, strUserName, strUserTextBoxID, strUserNameID, hfUserName) 
			{
				if(strUserTextBoxID!='') 
				{
					if (window.opener.document.getElementById(strUserTextBoxID) == null) 
					{	
						TextBoxID = strUserTextBoxID;					  
						TextBoxIDindex = TextBoxID.indexOf('_') ;
						TextBoxIDNew = TextBoxID.substring(0,TextBoxIDindex); 
						window.opener.document.getElementById(TextBoxIDNew).value = strUser; 
						window.opener.document.getElementById(TextBoxIDNew).focus();
					} 
					else 
					{ 
						window.opener.document.getElementById(strUserTextBoxID).value = strUser; 
						window.opener.document.getElementById(strUserTextBoxID).focus();
					}
					if (window.opener.document.getElementById(strUserNameID) != null) {
					    window.opener.document.getElementById(strUserNameID).innerHTML = strUserName;
					}
					if (window.opener.document.getElementById(hfUserName) != null) {
					    window.opener.document.getElementById(hfUserName).innerText = strUserName;
					}
					/*else {
					    LabelID = strUserNameID;
					    alert(strUserNameID);
					    LabelIDindex = LabelID.indexOf('_');
					    LabelIDNew = LabelID.substring(0, LabelIDindex);
					    alert(window.opener.document.getElementById(LabelIDNew));
					    window.opener.document.getElementById(LabelIDNew).value = strUser;
					}*/
					//eval("window.opener." + strFormName + "." + strUserTextBoxID + ".focus()");
					window.close();
                   
				} 
			}
		</script>
		<link href="/GroupManager/Styles.css" rel="stylesheet"/>
	</head>
	<body>
		<form id="Form1" method="post" runat="server">
			<div id="divInput">
				<table cellpadding="0" cellspacing="0" border="0" width="100%">
					<tr>
						<td colspan="4" class="header" align="center">
							<h4>User Look Up Screen</h4>
						</td>
					</tr>
					<tr>
						<td colspan="4" align="center">
							&nbsp;
						</td>
					</tr>
					<tr>
						<td width="22%" align="right" class="small">
							User ID :
						</td>
						<td width="78%" colspan="2">
							<asp:TextBox ID="TextBoxUserID" runat="server" Columns="10" MaxLength="10" CssClass="textbox"></asp:TextBox>
						</td>
					</tr>
					<tr>
						<td width="22%" align="right" class="small">
							User Name :
						</td>
						<td width="38%">
							<asp:TextBox ID="TextBoxUserName" runat="server" Columns="20" MaxLength="25" CssClass="textbox"></asp:TextBox>
						</td>
						<td width="40%" class="small">
							(Last Name, First Name)
						</td>
					</tr>
					<tr>
						<td width="10%" class="small" align="right">Search by :
						</td>
						<td class="small">
							<asp:RadioButtonList ID="RadioButtonSearch" runat="server" RepeatDirection="Horizontal" CssClass="radiobutton">
								<asp:ListItem Selected="true" Value="I">User ID</asp:ListItem>
								<asp:ListItem Value="N">User Name</asp:ListItem>
							</asp:RadioButtonList>
						</td>
						<td align="right">
							<asp:Button ID="ButtonSubmit" Runat="server" Text="Search" CssClass="button"></asp:Button>&nbsp;
						</td>
					</tr>
					<tr>
						<td colspan="3">
							<GME:errorValidationSummary id="ErrorValidationSummaryUC" runat="server"></GME:errorValidationSummary>
						</td>
					</tr>
					<tr>
						<td colspan="3">
							&nbsp;
						</td>
					</tr>
				</table>
			</div>
			<div id="UserSearchResults">
				<asp:DataGrid Id="DataGridUserSearchResults" Runat="server" AutoGenerateColumns="False" AllowSorting="True"
					AllowPaging="True" PageSize="10" PagerStyle-Mode="NumericPages" PagerStyle-HorizontalAlign="Center"
					PagerStyle-CssClass="gridPageStyle" Width="100%">
					<AlternatingItemStyle CssClass="griAltItemStyle"></AlternatingItemStyle>
					<ItemStyle CssClass="gridItemStyle"></ItemStyle>
					<HeaderStyle HorizontalAlign="Center" CssClass="gridHeaderStyle"></HeaderStyle>
					<Columns>
						<asp:TemplateColumn HeaderText="Select">
							<HeaderStyle Width="15%"></HeaderStyle>
							<ItemStyle HorizontalAlign="Center" Width="15%"></ItemStyle>
							<ItemTemplate>
								<input type="radio" id="RadioGroupUser" name="RadioGroupUser" runat="server" />
							</ItemTemplate>
						</asp:TemplateColumn>
						<asp:BoundColumn DataField="UserID" SortExpression="UserID" HeaderText="User ID">
							<HeaderStyle Width="30%"  ForeColor="white"></HeaderStyle>
							<ItemStyle HorizontalAlign="Center" Width="30%"></ItemStyle>
						</asp:BoundColumn>
						<asp:BoundColumn DataField="UserName" SortExpression="UserName" HeaderText="User Name">
							<HeaderStyle Width="55%" ForeColor="white"></HeaderStyle>
							<ItemStyle HorizontalAlign="Center" Width="55%"></ItemStyle>
						</asp:BoundColumn>
					</Columns>
					<PagerStyle HorizontalAlign="Center" CssClass="gridPageStyle" Mode="NumericPages"></PagerStyle>
				</asp:DataGrid>
			</div>
			<br/>
		</form>
	</body>
</html>
