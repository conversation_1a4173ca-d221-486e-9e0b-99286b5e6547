﻿<%@ Page MasterPageFile="~/MasterPage.master" Title="Computer Security Administration"
    Language="vb" AutoEventWireup="false" Inherits="AccountManager.Orphan" CodeFile="Manage.aspx.vb" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<%@ Register TagPrefix="UM" TagName="WrkSt" Src="~/Library/UserControls/ucWorkStations.ascx" %>
<%@ Register TagPrefix="UM" TagName="OrphanAct" Src="~/Library/UserControls/ucOrphanAccounts.ascx" %>
<%@ Register TagPrefix="UM" TagName="DelAcct" Src="~/Library/UserControls/ucDelAcct.ascx" %>
<%@ Register TagPrefix="UM" TagName="DelOwners" Src="~/Library/UserControls/ucDelOwners.ascx" %>
<%@ Register TagPrefix="UM" TagName="Owners" Src="~/Library/UserControls/ucUpdateOwners.ascx" %>
<%@ Register TagPrefix="UM" TagName="AddNew" Src="~/Library/UserControls/ucAddNew.ascx" %>
<%@ Register TagPrefix="UM" TagName="UpdAcct" Src="~/Library/UserControls/ucUpdAcct.ascx" %>
<%@ Register TagPrefix="uc1" TagName="Menu" Src="~/Library/UserControls/Menu.ascx" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:ScriptManager ID="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <table>
        <tr>
            <td>
                <asp:Label ID="lblWelcome" runat="server" CssClass="TableHeader">User Manager Administration</asp:Label><br />
                <br />
                <asp:Label ID="lblMessage" runat="server" CssClass="LabelRed"></asp:Label><br />
                <table>
                    <tr>
                        <td>
                            <cc1:TabContainer ID="TabContainer1" runat="server"  ActiveTabIndex="0">
                                 <cc1:TabPanel ID="TabPanel4" runat="server" HeaderText="Orphaned Accounts">
                                    <ContentTemplate>
                                        <UM:OrphanAct ID="ucOrphan" runat="server"></UM:OrphanAct>
                                    </ContentTemplate>
                                </cc1:TabPanel>
                                <cc1:TabPanel ID="TabPanel6" runat="server" HeaderText="Add New Account">
                                    <ContentTemplate>
                                       <UM:AddNew id="AddNew1" runat="server"></UM:AddNew>
                                    </ContentTemplate>
                                </cc1:TabPanel>
                                 <cc1:TabPanel ID="TabPanel3" runat="server" HeaderText="Update Account">
                                    <ContentTemplate>
                                        <UM:UpdAcct ID="ucUpdAcct" runat="server"></UM:UpdAcct>
                                    </ContentTemplate>
                                </cc1:TabPanel>
                                <cc1:TabPanel ID="TabPanel5" runat="server" HeaderText="Add New Owner/Delegate/Backup">
                                    <ContentTemplate>
                                        <UM:Owners ID="ucOwners" runat="server"></UM:Owners>
                                    </ContentTemplate>
                                </cc1:TabPanel>
                                <cc1:TabPanel ID="TabPanel7" HeaderText="Delete Backups" runat="server">
                                    <ContentTemplate>
                                        <UM:DelOwners ID="ucDelOwners" runat="server"></UM:DelOwners>
                                    </ContentTemplate>
                                </cc1:TabPanel>
                                <cc1:TabPanel ID="TabPanel1" runat="server" HeaderText="Manage Computers">
                                    <ContentTemplate>
                                        <UM:WrkSt ID="ucWrkSt" runat="server"></UM:WrkSt>
                                    </ContentTemplate>
                                </cc1:TabPanel>
                                
                                <cc1:TabPanel ID="TabPanel2" runat="server" HeaderText="Delete Account">
                                    <ContentTemplate>
                                        <UM:DelAcct ID="ucDelAcct" runat="server"></UM:DelAcct>
                                    </ContentTemplate>
                                </cc1:TabPanel>
                               
                            </cc1:TabContainer>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</asp:Content>
