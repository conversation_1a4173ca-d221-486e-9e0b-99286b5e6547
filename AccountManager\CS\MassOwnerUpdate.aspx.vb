Namespace AccountManager

    Partial Class MassOwnerUpdate
        Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
            'Put user code to initialize the page here
        End Sub


        Sub UpdateOwners()

            Dim reqNew As New Requests
            Dim usrcur As New UserInfo
            Dim usrNewOwner As New UserInfo(txtNewuser.Text)
            Dim hlpIns As New Helper

            If usrNewOwner.UserFound Then
                reqNew.OldOwner = txtOldUser.Text
                reqNew.NewOwner = txtNewuser.Text
                reqNew.NewOwnerDN = usrNewOwner.DisplayName
                reqNew.MassOwnerUpdate()
                lblMessage.Text = "Owners and backup owners updated."
                hlpIns.InsertLog("Multipe", [Global].GblActMassOwnUpdate, Now, "Old Owner: " & txtOldUser.Text & " - New Owner: " & txtNewuser.Text)
            Else
                lblMessage.Text = "New User does not exist in active directory. "
            End If

        End Sub

        Private Sub cmdUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdUpdate.Click
            UpdateOwners()
        End Sub
    End Class

End Namespace
