Imports System
Imports System.Data
Imports System.Text
Imports System.Collections.Specialized
Imports KCC.Common


Namespace GroupManager


Partial Class UserPopUp
    Inherits System.Web.UI.Page
    Dim dtbUser As New DataTable

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

    End Sub


    Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
        'CODEGEN: This method call is required by the Web Form Designer
        'Do not modify it using the code editor.
        InitializeComponent()
    End Sub

#End Region

    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        If Not IsPostBack Then
            Session("SortOrder") = ""
            If Not Request.QueryString("UserID") = "" Then
                TextBoxUserID.Text = Request.QueryString("UserID")
            End If
        End If
    End Sub
    Private Sub ButtonSubmit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSubmit.Click
        ErrorValidationSummaryUC.ClearErrorMessage = True
        DataGridUserSearchResults.Visible = True
        Dim oAD As New AD
        Dim strUserID, strGetUserName, strUserName, strFirstName, strLastName, strGetUserID As String
        'Dim dtbUser As New DataTable
        Dim drUser As DataRow
        Dim dsTemp As DataSet
        Dim dcUserID As DataColumn = New DataColumn("UserID")
        dcUserID.DataType = System.Type.GetType("System.String")
        Dim dcUserName As DataColumn = New DataColumn("UserName")
        dcUserName.DataType = System.Type.GetType("System.String")
        dtbUser.Columns.Add(dcUserID)
        dtbUser.Columns.Add(dcUserName)
        drUser = dtbUser.NewRow()
        If RadioButtonSearch.SelectedValue = "I" Then
            strUserID = TextBoxUserID.Text.Trim()
            If strUserID = "" Then
                DataGridUserSearchResults.Visible = False
                ErrorValidationSummaryUC.ShowErrorMessage(Constants.MSG_ENTER_USERID)
                Return
            Else
                dsTemp = oAD.FindUsers(strUserID, True)
                If Not dsTemp Is Nothing Then
                    dtbUser = dsTemp.Tables(0)
                End If
            End If
        Else
            strUserName = TextBoxUserName.Text.Trim()
            If (strUserName = "") Then
                DataGridUserSearchResults.Visible = False
                ErrorValidationSummaryUC.ShowErrorMessage(Constants.MSG_ENTER_USERNAME)
                Return
            Else
             
                dsTemp = oAD.FindUsers(strUserName, False)
                If Not dsTemp Is Nothing Then
                    dtbUser = dsTemp.Tables(0)
                End If
            End If
        End If
        Dim bFoundUsers As Boolean = False
        If Not dtbUser Is Nothing Then
            If dtbUser.Rows.Count > 0 Then
                Dim oGroupManager As GroupManager = GroupManager.GetCurrentSingleton()
                oGroupManager.SearchUserResult = dtbUser

                dtbUser.DefaultView.Sort = "UserName ASC"

                DataGridUserSearchResults.DataSource = dtbUser
                DataGridUserSearchResults.DataBind()
                bFoundUsers = True
            End If
        End If
        If bFoundUsers = False Then
            DataGridUserSearchResults.Visible = False
            ErrorValidationSummaryUC.ShowErrorMessage(Constants.MSG_NO_USERS)
        End If
    End Sub

    Protected Sub DataGridUserSearchResults_ItemDataBound(ByVal sender As System.Object, ByVal e As DataGridItemEventArgs) Handles DataGridUserSearchResults.ItemDataBound
        If e.Item.ItemType = ListItemType.Item Or e.Item.ItemType = ListItemType.AlternatingItem Then
            Dim drvUserRow As DataRowView = CType(e.Item.DataItem, DataRowView)

            'On selecting the user the pop should close taking the user ID
            Dim oHtmlInputRadioButton As HtmlInputRadioButton = CType(e.Item.FindControl("RadioGroupUser"), HtmlInputRadioButton)
                oHtmlInputRadioButton.Attributes.Add("onclick", "SetValue('" & Convert.ToString(drvUserRow("UserID")).Trim() & "', '" & Convert.ToString(drvUserRow("UserName")).Trim() & "', '" & Request.QueryString("UserControlID") & "_TextBoxUserID', '" & Request.QueryString("UserControlID") & "_LabelUserName', '" & Request.QueryString("UserControlID") & "_hfLabelUserName')")

            End If

        End Sub

    Private Sub DataGridUserSearchResults_PageIndexChanged(ByVal source As Object, ByVal e As System.Web.UI.WebControls.DataGridPageChangedEventArgs) Handles DataGridUserSearchResults.PageIndexChanged
        Dim oGroupManager As GroupManager = GroupManager.GetCurrentSingleton()
        Dim dtbTemp As DataTable = oGroupManager.SearchUserResult
        DataGridUserSearchResults.CurrentPageIndex = e.NewPageIndex
        DataGridUserSearchResults.DataSource = dtbTemp
        DataGridUserSearchResults.DataBind()
    End Sub

    Private Sub DataGridUserSearchResults_SortCommand(ByVal source As Object, ByVal e As System.Web.UI.WebControls.DataGridSortCommandEventArgs) Handles DataGridUserSearchResults.SortCommand

        Dim dvSearch As New DataView

        Dim oGroupManager As GroupManager = GroupManager.GetCurrentSingleton()
        dvSearch = oGroupManager.SearchUserResult.DefaultView

        If Session("SortOrder") = "" Then
            dvSearch.Sort = e.SortExpression + " ASC"
            Session("SortOrder") = "ASC"
        ElseIf Session("SortOrder") = "ASC" Then
            dvSearch.Sort = e.SortExpression + " DESC"
            Session("SortOrder") = "DESC"
        Else
            dvSearch.Sort = e.SortExpression + " ASC"
            Session("SortOrder") = "ASC"
        End If

        DataGridUserSearchResults.DataSource = dvSearch
        DataGridUserSearchResults.DataBind()

    End Sub
End Class

End Namespace
