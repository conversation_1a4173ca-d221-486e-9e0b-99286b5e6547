Imports System
Imports System.Security.Principal
Imports System.Web.UI.Page


Namespace AccountManager

Partial Class Menu
    Inherits System.Web.UI.UserControl

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

    End Sub


    Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
        'CODEGEN: This method call is required by the Web Form Designer
        'Do not modify it using the code editor.
        InitializeComponent()
    End Sub

#End Region

        'add after LINK
        '<!-- #INCLUDE Virtual="/Library/Visual/Header/LeftHeader.htm" -->

    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

            CheckAccess()

    End Sub

    Sub CheckAccess()

        Dim pUser As New System.Web.UI.Page

        If pUser.User.IsInRole("KCUS\ESM DEVELOPMENT") Then
            pAdmin.Visible = True
            pReports.Visible = True
        ElseIf pUser.User.IsInRole("KCUS\zgl-096-556") Then
            pAdmin.Visible = False
            pReports.Visible = True
        ElseIf pUser.User.IsInRole("KCUS\ESM_AssetMgmt_Audit") Then
            pAdmin.Visible = False
            pReports.Visible = True
        ElseIf pUser.User.IsInRole("KCUS\KCFILES_CorpMISProgOffICSRICReviewer_C") Then
            pAdmin.Visible = False
            pReports.Visible = True
        ElseIf pUser.User.IsInRole("KCUS\ESM Comp Sec") Then
            pAdmin.Visible = True
            pReports.Visible = True
        Else
            pAdmin.Visible = False
            pReports.Visible = False
        End If
    
    End Sub

End Class

End Namespace
