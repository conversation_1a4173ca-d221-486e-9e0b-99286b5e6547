Imports System
Imports System.Collections
Imports System.ComponentModel
Imports System.Data
Imports System.Drawing
Imports System.Web
Imports System.Web.SessionState
Imports System.Web.UI
Imports System.Web.UI.WebControls
Imports System.Web.UI.htmlControls
Imports DevExpress.Web.ASPxDataControls
Imports DevExpress.Web.ASPxGrid
Imports AccountManager.[Global]
Imports System.Collections.Generic


Namespace AccountManager

Partial Class AcceptOwnership
    Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

    End Sub


    Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
        'CODEGEN: This method call is required by the Web Form Designer
        'Do not modify it using the code editor.
        InitializeComponent()
    End Sub

#End Region

    Dim strAnswer As String

    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

            If Not Page.IsPostBack Then
                LoadRequests()
            End If

        End Sub

    Sub LoadRequests()

        Dim strSQL As String
        Dim dbGet As New DataAccess
        Dim srRead As SqlClient.SqlDataReader
            Dim usrcur As New UserInfo()

            strSQL = "SELECT * FROM tbUMRequests WHERE Status = 'New' AND NewOwner = '" & usrcur.GetUserID & "'"
            srRead = dbGet.GetDataReaderByStringId(strSQL)
            Trace.Warn(strSQL)
            If srRead.HasRows Then
                grdRequests.KeyFieldName = "RequestID"
                grdRequests.DataSource = srRead
                grdRequests.DataBind()
                grdRequests.ExpandAll()
                pnlRequests.Visible = True
            Else
                If Not lblMessage.Text.IndexOf("for Account(s)") > -1 Then
                    lblMessage.Text = "No ownership change request found."
                End If
                pnlRequests.Visible = False
                'grdRequests.Visible = False
                'cmdUpdate.Visible = False
                'cmdDelete.Visible = False
            End If

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()

        End Sub

        Sub UpdateOwners()
            lblMessage.Text = ""
            lblMessage.CssClass = "LabelRed"
            Dim strUserId, strComments, strNewOwner, strOldOwner, strRequestID As String
            Dim hlpIns As New Helper
            Dim intReqId As Integer
            Dim i As Integer
            Dim blnLoad As Boolean = True
            'Dim reqUpd As New Requests
            Dim usrcur As New UserInfo
            Dim fieldNames As List(Of String) = New List(Of String)()
            fieldNames.Add("NewOwner")
            fieldNames.Add("AccountID")
            fieldNames.Add("Comments")
            fieldNames.Add("OldOwner")
            fieldNames.Add("RequestID")
            Dim strRequestAcceptedAccounts As String = ""
            Dim strErrorAccounts As String = ""
            Dim dbInsert As New DataAccess
            Dim List As Object
            List = grdRequests.GetSelectedFieldValues(fieldNames.ToArray)
            Dim Icount As Int64 = 0
            For Each item As Object() In List
                Icount = Icount + 1
                strNewOwner = item.GetValue(0).ToString
                strUserId = item.GetValue(1).ToString
                strComments = item.GetValue(2).ToString
                strOldOwner = item.GetValue(3).ToString
                strRequestID = item.GetValue(4).ToString
                Dim usrOwn As New UserInfo(strNewOwner, True)
                strComments = strComments & " (Owner Update by " & usrcur.GetUserID & ")"

                intReqId = strRequestID.ToString()
                Dim usrFnd As New UserInfo(strUserId)
                If usrFnd.UserFound Then
                    Dim strNewDescription As String = String.Format("({0} {1} {2}) {3}", usrOwn.GetUserID, usrOwn.FirstName, usrOwn.LastName, usrFnd.DisplayName)
                    If dbInsert.AddUMOwnerDelegateBackup(usrFnd.GetUserID, usrOwn.GetUserID, usrOwn.DisplayName, 1, DataAccess.getManager(), String.Format("{0},Page Comments:{1};Old Owner: {2}; intReqId={3}", GblActOwnAccept, txtComments.Text, usrOwn.GetUserID, intReqId)) Then

                        If strNewDescription <> "" Then
                            Dim strSQL As String = "Update tbUMUsers set description =" & "'" & strNewDescription & "'" & " where AccountId = '" & usrFnd.GetUserID & "'"
                            strSQL = strSQL & String.Format(";Update tbUMRequests	Set Status = '{0}',Comments = '{1}'	WHERE RequestID = {2}", [Global].GblReqStatusDone, strComments, intReqId)
                            Try
                                dbInsert.UpdateDBByStringId(strSQL)
                                'reqUpd.UpdateRequest(intReqId, [Global].GblReqStatusDone)
                                usrFnd.SetDescriptionInAD(usrFnd.LdapPath, strNewDescription, "")

                                'hlpIns.InsertLog(strUserId, GblActOwnAccept, Now, String.Format("{0},Comments:{1};Old Owner: {2}; intReqId={3}", GblActOwnAccept, txtComments.Text, usrOwn.GetUserID, intReqId))
                                strRequestAcceptedAccounts = strRequestAcceptedAccounts & "," & usrFnd.GetUserID
                            Catch ex As Exception
                                strErrorAccounts = strErrorAccounts & "," & usrFnd.GetUserID
                            End Try
                        End If
                    Else
                        If dbInsert.IsError Then
                            strErrorAccounts = strErrorAccounts & "," & usrFnd.GetUserID
                        End If
                    End If
                End If
            Next
            If Icount = 0 Then
                lblMessage.Text = "Please select at least one account"
            Else
                If strRequestAcceptedAccounts <> "" Then
                    lblMessage.Text = "Ownership has been accepted for Account(s) " & strRequestAcceptedAccounts.Trim(",") & ".<br/>"
                End If

                If strErrorAccounts <> "" Then
                    lblMessage.Text = lblMessage.Text & "Error occurred while accepting Ownership for Account(s) " & strErrorAccounts.Trim(",") & "."
                Else
                    lblMessage.CssClass = "LabelGreen"
                End If
                LoadRequests()
            End If


        End Sub

        Sub DeleteRequest()
            lblMessage.Text = ""
            lblMessage.CssClass = "LabelRed"
            Dim strUserId, strComments, strOldOwner, strNewOwner, strRequestID As String
            Dim strUsers As String()
            Dim strList As String
            Dim intReqId As Integer
            Dim i As Integer
            Dim blnLoad As Boolean = True
            Dim reqUpd As New Requests
            Dim usrcur As New UserInfo
            Dim hlpIns As New Helper

            Dim fieldNames As List(Of String) = New List(Of String)()
            fieldNames.Add("NewOwner")
            fieldNames.Add("AccountID")
            fieldNames.Add("Comments")
            fieldNames.Add("OldOwner")
            fieldNames.Add("RequestID")

            Dim strAccounts As String = ""
            Dim List As Object
            List = grdRequests.GetSelectedFieldValues(fieldNames.ToArray)
            Dim Icount As Int32 = 0
            For Each item As Object() In List
                Icount = Icount + 1
                strNewOwner = item.GetValue(0).ToString
                strUserId = item.GetValue(1).ToString
                strComments = item.GetValue(2).ToString
                strOldOwner = item.GetValue(3).ToString
                strRequestID = item.GetValue(4).ToString

                ' For i = 0 To (grdRequests.GetSelectedRowCount()) - 1
                'Dim selectedRow As Row = grdRequests.GetSelectedRow(i)

                'If selectedRow.Level = grdRequests.GetGroupCount() Then

                'strUserId = selectedRow.DataControllerRow("AccountID").ToString()
                'strComments = selectedRow.DataControllerRow("Comments").ToString()
                strComments = strComments & " (Request Deleted: Ownership Rejected by " & usrcur.GetUserID & ")"
                'strOldOwner = selectedRow.DataControllerRow("OldOwner").ToString()

                reqUpd.Comments = strComments
                reqUpd.AccountID = strUserId
                strAccounts = strAccounts & strUserId & ","
                intReqId = strRequestID.ToString()

                reqUpd.UpdateRequest(intReqId, [Global].GblReqStatusDelete)
                hlpIns.InsertLog(strUserId, GblActOwnDec, Now, txtComments.Text)

                Dim usrOld As New UserInfo(strOldOwner)
                Dim emSend As New Email(usrOld.GetUserID)

                emSend.CreateAndSendReject("REJECTED Account Ownership Change Request", strUserId, txtComments.Text)
                ' End If
            Next
            If Icount = 0 Then
                lblMessage.Text = "Please select at least one account"
            Else
                If strAccounts <> "" Then
                    lblMessage.Text = "Ownership has been rejected for Account(s) " & strAccounts.Trim(",") & ".<br/>"
                End If

                LoadRequests()
            End If
        End Sub

    Private Sub cmdUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdUpdate.Click
        UpdateOwners()
    End Sub

    Private Sub cmdDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdDelete.Click
        If txtComments.Text = "" Then
            lblMessage.Text = "You must enter a reason why you are rejecting the ownership change request."
        Else
            DeleteRequest()
        End If

    End Sub
End Class

End Namespace
