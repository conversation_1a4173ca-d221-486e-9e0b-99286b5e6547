﻿<%@ Control Language="vb" AutoEventWireup="false" Inherits="AccountManager.ucUpdAcct"
    CodeFile="ucUpdAcct.ascx.vb" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<asp:UpdatePanel ID="UpdatePanel1" runat="server">
    <ContentTemplate>
        <table class="DisplayTables" style="width:100%">
            <colgroup>
                <col style="width:205px;" />
                <col style="width:auto;" />
            </colgroup>
            <tr>
                <td colspan="2" class="FunctionStep">
                    Update Account Description / Exception / Password Exempt</td>
            </tr>
            <tr>
                <td colspan="2">
                   &nbsp;<asp:Label ID="lblMessage" CssClass="LabelRed" runat="server"></asp:Label></td>
            </tr>
            <tr>
                <td>
                    Enter ID:</td>
                <td>
                    <table><tr><td><asp:TextBox ID="txtAccount" runat="server"></asp:TextBox></td><td> <dx:ASPxButton ID="cmdLookup" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                        CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                        Text="Lookup ID">
                    </dx:ASPxButton></td></tr></table>
                </td>
            </tr>
            <asp:Panel ID="pUpdate" runat="server" Visible="False" >
                <tr>
                    <td>
                        Account Description:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtDesc" Display="None" ErrorMessage="Please enter Description." SetFocusOnError="True" ValidationGroup="UpdAcct"> </asp:RequiredFieldValidator>

                    </td>
                    <td>
                        <asp:TextBox ID="txtDescStartpart" runat="server" ReadOnly="True" Enabled="False"></asp:TextBox><asp:TextBox ID="txtDesc" runat="server" Width="500"></asp:TextBox><asp:CheckBox ID="chkException" runat="server" Visible="False"></asp:CheckBox>
                    <asp:HiddenField ID="HFDesc" runat="server" > </asp:HiddenField>
                    </td>

                </tr>

                <tr>
                   <td style="height:10px;" colspan="2">
                       
                   </td>
                </tr> 
              
               <tr>
                   <td>
                        Exception Account:</td>
                    <td>
                        <asp:CheckBox ID="chkException1" runat="server" Visible="true" AutoPostBack="True"></asp:CheckBox>
                        <asp:HiddenField ID="HFchkException" runat="server" > </asp:HiddenField>
                        <asp:HiddenField ID="hfActType" runat="server" > </asp:HiddenField>
                    </td>
                </tr> 
                <tr>
                   <td>
                        Exception SONC Number:<asp:Label ID="lblrfvSONCNumber" runat="server" class="ValidationHeader">*</asp:Label><asp:RequiredFieldValidator ID="rfvSONCNumber" runat="server" ControlToValidate="txtSONCNumber" Display="None" ErrorMessage="Please enter Exception SONC Number." SetFocusOnError="True" ValidationGroup="UpdAcct"> </asp:RequiredFieldValidator></td>
                    <td>
                        <asp:TextBox ID="txtSONCNumber" runat="server"  Width="500" MaxLength="99"></asp:TextBox>
                        <asp:HiddenField ID="hfSONCNumber" runat="server" > </asp:HiddenField>
                    </td>
                </tr> 
                <tr>
                   <td>
                        Exception Reason:<asp:Label ID="lblrfvReason" runat="server"  class="ValidationHeader">*</asp:Label><asp:RequiredFieldValidator ID="rfvReason" runat="server" ControlToValidate="txtReason" Display="None" ErrorMessage="Please enter Exception Reason." SetFocusOnError="True" ValidationGroup="UpdAcct"> </asp:RequiredFieldValidator></td>
                    <td>
                        <asp:TextBox ID="txtReason" runat="server"  Width="500" MaxLength="249"></asp:TextBox>
                        <asp:HiddenField ID="hfReason" runat="server" > </asp:HiddenField>
                    </td>
                </tr> 
                <asp:Panel ID="pnlPwdExempt" runat="server">
                <tr>
                   <td style="height:10px;" colspan="2">
                       
                   </td>
                </tr> 
                <tr>
                   <td colspan="2">
                        Password Exempt Process Request is Pending as below:
                       <asp:HiddenField ID="hfPwdExemptID" runat="server" > </asp:HiddenField>
                   </td>
                </tr> 
                
                <tr>
                   <td>
                       Response:
                        </td>
                    <td>
                        <asp:DropDownList ID="ddlResponse" AutoPostBack="true" runat="server">
                            <asp:ListItem Value="0">-- Select --</asp:ListItem>
                            <asp:ListItem>Approved</asp:ListItem>
                            <asp:ListItem>Rejected</asp:ListItem>
                        </asp:DropDownList>
                    </td>
                </tr> 
                <tr>
                   <td>
                        Service-now Ticket Number:<asp:Label ID="lblrfvTicketNumber" runat="server"  class="ValidationHeader">*</asp:Label><asp:RequiredFieldValidator ID="rfvTicketNumber" runat="server" ControlToValidate="txtTicketNumber" Display="None" ErrorMessage="Please enter Service-now Ticket Number." SetFocusOnError="True" ValidationGroup="UpdAcct"> </asp:RequiredFieldValidator></td>
                    <td>
                        <asp:TextBox ID="txtTicketNumber" runat="server"  Width="500" MaxLength="29"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                   <td>
                        Password Exempt Comments:<asp:Label ID="lblrfvComments" runat="server"  class="ValidationHeader">*</asp:Label><asp:RequiredFieldValidator ID="rfvComments" runat="server" ControlToValidate="txtPwdExemptComments" Display="None" ErrorMessage="Please enter Password Exempt Comments." SetFocusOnError="True" ValidationGroup="UpdAcct"> </asp:RequiredFieldValidator></td>
                    <td>
                        <asp:TextBox ID="txtPwdExemptComments" runat="server"  Width="500" MaxLength="499"></asp:TextBox>
                        
                    </td>
                </tr>
                    </asp:Panel>
                <tr>
                    <td colspan="2">
                        <table><tr><td>
                        <dx:ASPxButton ID="cmdUpdate" runat="server" ValidationGroup="UpdAcct" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                        CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                        Text="Update">
                    </dx:ASPxButton></td><td> <dx:ASPxButton ID="cmdUpdReset" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                        CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                        Text="Reset">
                    </dx:ASPxButton>
                            </td></tr></table>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <asp:ValidationSummary ID="ValidationSummary1" runat="server" ValidationGroup="UpdAcct" />
                    </td>
                </tr>
            </asp:Panel>
            <tr>
                <td colspan="2">
                    <asp:Label ID="lblInstructions" runat="server"></asp:Label><br />
                </td>
            </tr>
        </table>
    </ContentTemplate>
</asp:UpdatePanel>
