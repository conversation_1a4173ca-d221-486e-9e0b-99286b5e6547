﻿<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="uc1" TagName="Menu" src="~/Library/UserControls/Menu.ascx" %>
<%@ Page Language="vb" AutoEventWireup="false" Inherits="AccountManager.VerifyOwnership" CodeFile="VerifyOwnership.aspx.vb" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD html 4.0 Transitional//EN">
<html>
	<head runat="server">
		<title>Verify Ownership</title>
		<meta content="Microsoft Visual Studio.NET 7.0" name="GENERATOR" />
		<meta content="Visual Basic 7.0" name="CODE_LANGUAGE"/>
		<meta content="JavaScript" name="vs_defaultClientScript"/>
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema"/>
		<meta content="http://schemas.microsoft.com/intellisense/nav4-0" name="vs_targetSchema">
		<!-- REPLACED VIRTUAL PATH HERE - TESTING --><link href="/Styles.css" type="text/css" rel="stylesheet"/>
	</head>
	<body>
		<table height="1150" cellspacing="0" cellpadding="0" width="1315" border="0">
			<tr valign="top">
				<td width="1315" height="1150">
					<form id="frmRequestQuery" method="post" runat="server">
						<table height="1298" cellspacing="0" cellpadding="0" width="1138" border="0">
							<tr valign="top">
								<td width="1" height="30"></td>
								<td width="1137"></td>
							</tr>
							<tr valign="top">
								<td height="1268"></td>
								<td>
									<table height="1267" cellspacing="0" cellpadding="0" width="1136" border="0">
										<tr>
											<td valign="top" width="150"><uc1:menu id="Menu1" runat="server"></uc1:menu></td>
											<td valign="top"><br/>
												<br/>
												<br>
												<asp:label id="lblWelcome" Runat="server"></asp:label>
												<br/>
												<br>
												<asp:label id="lblMessage" Runat="server"></asp:label>
												<br/>
												<table>
													<tr>
														<td>
															<asp:panel id="pVerifyOwner" Runat="server">
																<table class="DisplayTables">
																	<tr>
																		<td>
																			<asp:Label id="lblVerifyOwner" Runat="server" CssClass="LabelRed"></asp:Label></td>
																	</tr>
																	<tr>
																		<td>Select the accounts you are an owner for. To select multiple account use 
																			Ctr+Click.<br/>
																			Click the Owner Verified button below.</td>
																	<tr>
																		<td>
																			<dxwdc:aspxbutton id="cmdOnwerVerified" tabIndex="0" runat="server" Text="Continue">
																				<LookAndFeel Kind="Office2003">
																					<EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black" BackColor="White"></EditorStyle>
																					<LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
																					<ScrollBarButtonStyle BackColor="#84ABE3">
																						<Filters>
																							<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																						</Filters>
																					</ScrollBarButtonStyle>
																					<ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
																						ScrollBarMargin="1"></ElementsSettings>
																					<PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"></PopupStyle>
																					<ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
																						UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
																						<HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
																							<Filters>
																								<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202"></dxwdc:LookAndFeelStyleGradientFilter>
																							</Filters>
																						</HotTrackStyle>
																						<PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
																							<Filters>
																								<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80"></dxwdc:LookAndFeelStyleGradientFilter>
																							</Filters>
																						</PressedStyle>
																						<Filters>
																							<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																						</Filters>
																					</ButtonStyle>
																				</LookAndFeel>
																			</dxwdc:aspxbutton></td>
																	</tr>
																</table>
															</asp:panel></td>
													</tr>
													<tr>
														<td><dxwg:aspxgrid id="grdUsers" tabIndex="0" runat="server" DataKeyField="AccountID" BorderColor="#6787B8"
																BorderStyle="Solid" AutoGenerateColumns="False" ExpandBtnWidth="11px" HeaderHeight="25px" RowBtnWidth="18px"
																SelectedBackColor="49, 106, 197" ExpandBtnHeight="11px" SearchBtnWidth="17px" StatusBarItemSpacing="0"
																PageIndexButtonCount="4" FocusedBorderColor="ControlDarkDark" FocusedBorderStyle="Double"><ALTERNATINGITEMSTYLE BackColor="#E7F2FE"></ALTERNATINGITEMSTYLE>
																<EXPANDBTNSTYLE BorderStyle="Solid" BorderColor="#6787B8" BackColor="#F9F9F9" BorderWidth="1px">
																	<HOTTRACKSTYLE BorderColor="Navy" BackColor="White"></HOTTRACKSTYLE>
																	<PRESSEDSTYLE BorderColor="Navy" BackColor="#D0D0D0" ForeColor="Black"></PRESSEDSTYLE>
																</EXPANDBTNSTYLE>
																<BUTTONBARSTYLE BorderStyle="Solid" BorderColor="#6787B8" BorderWidth="1px"></BUTTONBARSTYLE>
																<GROUPPANELSTYLE BackColor="#3E6DB9" ForeColor="#DDECFE" Font-Bold="True"></GROUPPANELSTYLE>
																<SEARCHBTNSTYLE FixedWidth="True"></SEARCHBTNSTYLE>
																<TITLESTYLE BackColor="#6787B8" ForeColor="White"></TITLESTYLE>
																<GROUPITEMSTYLE BorderColor="#6787B8" BackColor="#C1D8F7" FixedWidth="True" Wrap="False"></GROUPITEMSTYLE>
																<ROWBTNSTYLE BorderStyle="None"></ROWBTNSTYLE>
																<BUTTONBARS>
																	<dxwg:ButtonBar ButtonBarType="Navigator">
																		<BARITEMS>
																			<DXWDC:BARBUTTON ButtonType="MoveFirst"></DXWDC:BARBUTTON>
																			<DXWDC:BARBUTTON ButtonType="MovePrevPage"></DXWDC:BARBUTTON>
																			<DXWDC:BARBUTTON ButtonType="MovePrev"></DXWDC:BARBUTTON>
																			<DXWDC:BARTWOSTATEEDITORBUTTON ButtonType="ChangePageSize"></DXWDC:BARTWOSTATEEDITORBUTTON>
																			<DXWDC:BARBUTTON ButtonType="MoveNext"></DXWDC:BARBUTTON>
																			<DXWDC:BARBUTTON ButtonType="MoveNextPage"></DXWDC:BARBUTTON>
																			<DXWDC:BARBUTTON ButtonType="MoveLast"></DXWDC:BARBUTTON>
																			<DXWDC:BARBUTTON ButtonType="Refresh"></DXWDC:BARBUTTON>
																			<DXWDC:BAREDITMODEBUTTON ButtonType="Post"></DXWDC:BAREDITMODEBUTTON>
																		</BARITEMS>
																	</dxwg:ButtonBar>
																</BUTTONBARS>
																<HEADERSTYLE BorderStyle="None" Font-Bold="True" FixedWidth="True" Wrap="False" FixedHeight="True"></HEADERSTYLE>
																<STATUSBARS>
																	<dxwg:StatusBar StatusBarType="Regular" Height="20px">
																		<BARITEMS>
																			<DXWDC:BARSTATUSSECTION StatusSectionType="Status"></DXWDC:BARSTATUSSECTION>
																			<DXWDC:BARSTATUSSECTION StatusSectionType="VisibleInterval"></DXWDC:BARSTATUSSECTION>
																			<DXWDC:BARSTATUSSECTION StatusSectionType="TotalVisible"></DXWDC:BARSTATUSSECTION>
																			<DXWDC:BARSTATUSSECTION StatusSectionType="TotalRows"></DXWDC:BARSTATUSSECTION>
																		</BARITEMS>
																	</dxwg:StatusBar>
																</STATUSBARS>
																<ITEMSTYLE BackColor="White" FixedWidth="True" Wrap="False" Font-Size="7.5pt" VerticalAlign="Middle"></ITEMSTYLE>
																<HEADERDRAGGEDSTYLE BorderStyle="Solid" BorderColor="LightGray" BorderWidth="1px">
																	<FILTERS>
																		<DXWDC:LOOKANDFEELSTYLEALPHAFILTER FinishX="50" FinishOpacity="50"></DXWDC:LOOKANDFEELSTYLEALPHAFILTER>
																	</FILTERS>
																</HEADERDRAGGEDSTYLE>
																<LAYOUTOPTIONS ShowFocusedBorder="True"></LAYOUTOPTIONS>
																<LOOKANDFEEL Kind="Office2003">
																	<EDITORSTYLE BorderColor="#6787B8" BackColor="White" ForeColor="Black" Font-Size="8pt" Font-Names="Verdana"></EDITORSTYLE>
																	<LABELSTYLE ForeColor="Black" Font-Size="8pt" Font-Names="Verdana"></LABELSTYLE>
																	<SCROLLBARBUTTONSTYLE BackColor="#84ABE3">
																		<FILTERS>
																			<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="221, 236, 254" GradientMode="Horizontal" EndColor="132, 171, 227"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
																		</FILTERS>
																	</SCROLLBARBUTTONSTYLE>
																	<ELEMENTSSETTINGS ScrollBarMargin="1" ScrollBarBackColor="247, 245, 241" DropDownButtonWidth="17px"
																		ScrollBarSize="17px"></ELEMENTSSETTINGS>
																	<POPUPSTYLE BorderColor="#6787B8" Font-Size="8pt" Font-Names="Verdana"></POPUPSTYLE>
																	<BUTTONSTYLE BorderColor="#6787B8" BackColor="#84ABE3" ForeColor="Black" Wrap="False" Font-Size="8pt"
																		Font-Names="Verdana" Margin="1" UsePressedStyle="True" UseHotTrackStyle="True">
																		<HOTTRACKSTYLE BorderColor="Navy" BackColor="#FFD599" ForeColor="Black">
																			<FILTERS>
																				<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="255, 243, 202" EndColor="255, 213, 153"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
																			</FILTERS>
																		</HOTTRACKSTYLE>
																		<PRESSEDSTYLE BorderColor="Navy" BackColor="#FFCA86" ForeColor="Black">
																			<FILTERS>
																				<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="254, 148, 80" EndColor="255, 202, 134"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
																			</FILTERS>
																		</PRESSEDSTYLE>
																		<FILTERS>
																			<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="221, 236, 254" EndColor="132, 171, 227"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
																		</FILTERS>
																	</BUTTONSTYLE>
																</LOOKANDFEEL>
																<GROUPEDHEADERSTYLE BorderStyle="Solid" BorderColor="#6787B8" BorderWidth="1px">
																	<HOTTRACKSTYLE BorderColor="Navy"></HOTTRACKSTYLE>
																	<PRESSEDSTYLE BorderColor="Navy"></PRESSEDSTYLE>
																</GROUPEDHEADERSTYLE>
																<BARBTNSTYLE BorderStyle="None">
																	<HOTTRACKSTYLE BorderStyle="Solid"></HOTTRACKSTYLE>
																	<PRESSEDSTYLE BorderStyle="Solid"></PRESSEDSTYLE>
																</BARBTNSTYLE>
																<FOOTERSTYLE BackColor="#B0CBF1" Font-Bold="True" FixedWidth="True" Wrap="False" FixedHeight="True"></FOOTERSTYLE>
																<PREVIEWSTYLE BackColor="#F9FCFF" ForeColor="#5881B9"></PREVIEWSTYLE>
																<BARBTNEDITORSTYLE BorderStyle="None"></BARBTNEDITORSTYLE>
																<APPEARANCEOPTIONS ShowGroupPanel="False"></APPEARANCEOPTIONS>
																<SEARCHEDITORSTYLE BorderColor="White"></SEARCHEDITORSTYLE>
																<BEHAVIOROPTIONS PostBackOnKeyPress="False" AutoEdit="False" EnableGrouping="False" AllowDelete="False"
																	EnableMultiSelection="True" AllowInsert="False"></BEHAVIOROPTIONS>
																<SEARCHITEMSTYLE BackColor="#C1D8F7"></SEARCHITEMSTYLE>
																<COLUMNS>
																	<dxwg:BoundColumn Width="100px" DataField="AccountID" VisibleIndex="0" HeaderText="Account ID"></dxwg:BoundColumn>
																	<dxwg:BoundColumn Width="140px" DataField="UserName" HeaderText="Owner/Backup" Visible="False"></dxwg:BoundColumn>
																	<dxwg:BoundColumn Width="110px" DataField="UserNameDN" VisibleIndex="1" HeaderText="Owner/Backup"></dxwg:BoundColumn>
																	<dxwg:BoundColumn Width="382px" DataField="Description" VisibleIndex="3"></dxwg:BoundColumn>
																	<dxwg:BoundColumn Width="100px" DataField="ExpirationDate" VisibleIndex="2" HeaderText="Past Due"></dxwg:BoundColumn>
																	<dxwg:BoundColumn Width="110px" DataField="Access" VisibleIndex="4" HeaderText="Access"></dxwg:BoundColumn>
																	<dxwg:BoundColumn Width="150px" DataField="OwnerVerified" VisibleIndex="6" HeaderText="Owner Verified Date"
																		Visible="True"></dxwg:BoundColumn>
																</COLUMNS>
																<NAVIGATORBUTTONS EditRow="False" Cancel="False" DeleteRow="False" InsertRow="False"></NAVIGATORBUTTONS>
																<CLIENTSIDEEVENTS></CLIENTSIDEEVENTS>
																<FOOTERITEMSTYLE BackColor="#B0CBF1"></FOOTERITEMSTYLE>
																<STATUSBARSTYLE BorderStyle="None" BackColor="#DDECFE">
																	<FILTERS>
																		<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="221, 236, 254" EndColor="132, 171, 227"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
																	</FILTERS>
																</STATUSBARSTYLE>
																<GROUPINDENTSTYLE BackColor="#C1D8F7"></GROUPINDENTSTYLE>
															</dxwg:aspxgrid></td>
													</tr>
												</table>
												<br/>
											</td>
										</tr>
									</table>
								</td>
							</tr>
						</table>
					</FORM>
				</td>
			</tr>
		</table>
	</body>
</html>
