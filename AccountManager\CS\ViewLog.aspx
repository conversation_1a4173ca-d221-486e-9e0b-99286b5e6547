﻿<%@ Page MasterPageFile="~/MasterPage.master" Title="View Logs" Language="vb" AutoEventWireup="false"
    Inherits="AccountManager.ViewLog" CodeFile="ViewLog.aspx.vb" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>

<%@ Register TagPrefix="uc1" TagName="Menu" Src="~/Library/UserControls/Menu.ascx" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table>
        <tr>
            <td>
                <asp:Label ID="lblWelcome" runat="server" CssClass="TableHeader">User Requests</asp:Label><br />
                <br />
                <asp:Label ID="lblMessage" runat="server" CssClass="LabelRed"></asp:Label><br />
                <table>
                    <tr>
                        <td>
                            <table class="DisplayTables">
                                <tr>
                                    <td>
                                        View Log</td>
                                </tr>
                                <tr>
                                    <td>
                                        Step 1: Enter Account ID<br />
                                        Step 2: Click the Submit button to retreive all logs pertaining to that account.<br />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Account ID: &nbsp;
                                        <asp:TextBox ID="txtAccount" runat="server"></asp:TextBox>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <dx:aspxbutton id="cmdGet" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                            csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                            text="Submit"></dx:aspxbutton>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            &nbsp;<dx:aspxgridview id="grdRequests" runat="server" autogeneratecolumns="False"
                                cssfilepath="~/App_Themes/PlasticBlue/{0}/styles.css" csspostfix="PlasticBlue"
                                keyfieldname="ID" previewfieldname="Comments" width="464px">
<Styles CssPostfix="PlasticBlue" CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css">
<Header SortingImageSpacing="10px" ImageSpacing="10px"></Header>

<PreviewRow BackColor="ControlLight"></PreviewRow>
</Styles>

<SettingsPager ShowDefaultImages="False">
<AllButton Text="All"></AllButton>

<NextPageButton Text="Next &gt;"></NextPageButton>

<PrevPageButton Text="&lt; Prev"></PrevPageButton>
</SettingsPager>

<ImagesFilterControl>
<LoadingPanel Url="~/App_Themes/PlasticBlue/Editors/Loading.gif"></LoadingPanel>
</ImagesFilterControl>

<Images SpriteCssFilePath="~/App_Themes/PlasticBlue/{0}/sprite.css">
<LoadingPanelOnStatusBar Url="~/App_Themes/PlasticBlue/GridView/gvLoadingOnStatusBar.gif"></LoadingPanelOnStatusBar>

<LoadingPanel Url="~/App_Themes/PlasticBlue/GridView/Loading.gif"></LoadingPanel>
</Images>
<Columns>
<dx:GridViewDataTextColumn FieldName="UsrAction" Caption="ActionTaken" VisibleIndex="0"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="DtAction" Caption="Date" VisibleIndex="1"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="AccountID" Caption="Account ID" VisibleIndex="2"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="Usr" Caption="User" VisibleIndex="3"></dx:GridViewDataTextColumn>
</Columns>

<Settings ShowPreview="True"></Settings>

<StylesEditors>
<CalendarHeader Spacing="11px"></CalendarHeader>

<ProgressBar Height="25px"></ProgressBar>
</StylesEditors>
</dx:aspxgridview></td>
                    </tr>
                </table></td></tr></table>
</asp:Content>
