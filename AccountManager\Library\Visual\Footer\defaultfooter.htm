<P>
	<hr color="#98accc" width="100%" align="left">
</P>
<table cellpadding="1" cellspacing="1">
	<tr>
		<td><STRONG><FONT size="1" face="Verdana">Windows Services 2004-2005</FONT></STRONG></td>
	</tr>
	<tr>
		<td><FONT size="1" face="Verdana"><A href="mailto:<EMAIL>">Send Comments</A></FONT></td>
	</tr>
	<tr>
		<td><script language="JavaScript">


function date_ExpireFix(date)
{
  var d = date.getDate();
  var m = date.getMonth() + 1;
  var y = date.getYear() + 1;

//month = today.getMonth() + 1;
//date = today.getDate();
//year = today.getFullYear();

  // handle different year values 
  // returned by IE and NS in 
  // the year 2000.
  if(y >= 2000)
  {
    y -= 2000;
  }
  if(y >= 100)
  {
    y -= 100;
  }

  // could use splitString() here 
  // but the following method is 
  // more compatible
  var mmm = 
    ( 1==m)?'Jan':( 2==m)?'Feb':(3==m)?'Mar':
    ( 4==m)?'Apr':( 5==m)?'May':(6==m)?'Jun':
    ( 7==m)?'Jul':( 8==m)?'Aug':(9==m)?'Sep':
    (10==m)?'Oct':(11==m)?'Nov':'Dec';

  return "" +
    (d<10?"0"+d:d) + "-" +
    mmm + "-" +
    (y<10?"0"+y:y);
}

function date_ddmmmyy(date)
{
  var d = date.getDate();
  var m = date.getMonth() + 1;
  var y = date.getYear();

  // handle different year values 
  // returned by IE and NS in 
  // the year 2000.
  if(y >= 2000)
  {
    y -= 2000;
  }
  if(y >= 100)
  {
    y -= 100;
  }

  // could use splitString() here 
  // but the following method is 
  // more compatible
  var mmm = 
    ( 1==m)?'Jan':( 2==m)?'Feb':(3==m)?'Mar':
    ( 4==m)?'Apr':( 5==m)?'May':(6==m)?'Jun':
    ( 7==m)?'Jul':( 8==m)?'Aug':(9==m)?'Sep':
    (10==m)?'Oct':(11==m)?'Nov':'Dec';

  return "" +
    (d<10?"0"+d:d) + "-" +
    mmm + "-" +
    (y<10?"0"+y:y);
}


//
// get last modified date of the 
// current document.
//
function date_lastmodified()
{
  var lmd = document.lastModified;
  var s   = "Unknown";
  var d1;

  // check if we have a valid date
  // before proceeding
  if(0 != (d1=Date.parse(lmd)))
  {
    s = "" + date_ddmmmyy(new Date(d1));
  }

  return s;
}


function date_Expires()
{
  var lmd = document.lastModified;
  var s   = "Unknown";
  var d1;

  // check if we have a valid date
  // before proceeding
  if(0 != (d1=Date.parse(lmd)))
  {

    s = "" + date_ExpireFix(new Date(d1));
  }
  return s;
}

//
// finally display the last modified date
// as DD-MMM-YY
//
//document.write( 
 // "This page was updated on " + 
  //date_lastmodified() );


		
<!-- Hide JavaScript from older browsers 

// date for last modified
//today = new Date();
//month = today.getMonth() + 1;
//date = today.getDate();
//year = today.getFullYear();

document.write("<b><font size=1>Last Updated: </b>")
//document.write ( month + "/" + date + "/" + year ) ;
document.write (date_lastmodified()) ;
document.write("&nbsp;&nbsp;&nbsp;&nbsp;</font>")


// date for expiration one year from last modified
//today = new Date();
//today = Date(date_Expires());
//month = today.getMonth() + 1;
//date = today.getDate();
//year = today.getFullYear() + 1;

document.write("<b><font size=1>Expires: </b>")
document.write (date_Expires()) ;
document.write("</font>")



// End hiding JavaScript from older browsers -->
			</script></td>
	</tr>
</table>
