Imports System.Web
Imports System.Web.SessionState


Namespace GroupManager


    Public Class [Global]
        Inherits System.Web.HttpApplication

        'Public Const gmConnectionString = "server=USTCAS24;UID=esh0nedbo;PWD=;DATABASE=esh0nedb"
        'Public Const gmConnectionString = "server=USTCAS24;UID=esh0neadm;PWD=;DATABASE=esh0nedb"
        'Public Const gmConnectionString	= "Data Source=ustcas24;Initial Catalog=esh0nedb;Integrated Security=true;"
        Public Shared gmConnectionString = "server=USTCAS29;UID=esh0neadm;PWD=;DATABASE=esh0nedb"
        'Public Shared gmConnectionString = "server=USTCAS24;UID=esh0nedbo;PWD=;DATABASE=esh0nedb"
        'Public Shared gmConnectionString = "server=10.236.52.84;UID=kcuser;PWD=;DATABASE=esh0nedb"
        'Public Const glbLogPath = "D:\Inetpub\wwwroot\esm.qa.kcc.com\GroupManager\Library\Log\Compare\"
        'Public Const glbLogPath = "D:\Inetpub\wwwroot\esm.kcc.com\GroupManager\Library\Log\Compare\"
        'Public Shared glbLogPath = "D:\Inetpub\wwwroot\esm.dev.kcc.com\GroupManager\Library\Log\Compare
        Public Shared glbLogPath = "D:\Inetpub\wwwroot\ws.kcc.com\GroupManager\Library\Log\Compare\"

        Public Shared GblWebPath = "ws.kcc.com"
        Public Shared GblDownloadPath = "https:\\" & GblWebPath & "\GmADmin\Library\Downloads\"
        Public Shared GblServer = "ustcaw342"
        Public Shared GblServerPath = "\\ustcaw342\" & GblWebPath & "\GmADmin\Library\Downloads\"

#Region " Component Designer Generated Code "

        Public Sub New()
            MyBase.New()

            'This call is required by the Component Designer.
            InitializeComponent()

            'Add any initialization after the InitializeComponent() call

        End Sub

        'Required by the Component Designer
        Private components As System.ComponentModel.IContainer

        'NOTE: The following procedure is required by the Component Designer
        'It can be modified using the Component Designer.
        'Do not modify it using the code editor.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()
            components = New System.ComponentModel.Container()
        End Sub

#End Region

        Sub Application_Start(ByVal sender As Object, ByVal e As EventArgs)
            ' Fires when the application is started
        End Sub

        Sub Session_Start(ByVal sender As Object, ByVal e As EventArgs)

            Dim strPath As String

            strPath = Trim(LCase(Request.ServerVariables("HTTP_HOST")))
            GblServer = Server.MachineName
            GblWebPath = strPath

            GblDownloadPath = "https:\\" & GblWebPath & "\GroupManager\Library\Downloads\"
            GblServerPath = "\\" & GblServer & "\" & GblWebPath & "\GroupManager\Library\Downloads\"
            glbLogPath = "D:\Inetpub\wwwroot\" & GblWebPath & "\GroupManager\Library\Log\Compare\"

            If strPath = "wstc.kcc.com" Or strPath = "wstw.kcc.com" Then
                GblWebPath = "ws.kcc.com"
            End If

            GblDownloadPath = "https:\\" & GblWebPath & "\GroupManager\Library\Downloads\"
            GblServerPath = "\\" & GblServer & "\" & GblWebPath & "\GroupManager\Library\Downloads\"
            glbLogPath = "D:\Inetpub\wwwroot\" & GblWebPath & "\GroupManager\Library\Log\Compare\"


            '''''''''''''''''''''''Added from Global.asa file
            ' Get the users ID and set the Session("ManagerID")
            ' ManagerID is used to query for which group the manager is an Owner or Authorizer of.

            'Get AUTH_USER from IIS Server Variables
            Dim strAuth_User As String
            Dim vntAuth_User As String()
            Dim strUserDomain As String
            Dim strUserName As String
            strAuth_User = UCase(Trim(Request.ServerVariables("AUTH_USER")))

            'Get User Domain and User Name
            If InStr(strAuth_User, "\") Then
                vntAuth_User = Split(strAuth_User, "\")
                strUserDomain = vntAuth_User(0)
                strUserName = vntAuth_User(1)
            ElseIf InStr(strAuth_User, "/") Then
                vntAuth_User = Split(strAuth_User, "/")
                strUserDomain = vntAuth_User(0)
                strUserName = vntAuth_User(1)
            Else
                strUserDomain = ""
                strUserName = strAuth_User
            End If
            Session("ManagerID") = strUserName
            ''''''''''''''''''''''''Added from Global.asa file
        End Sub

        Sub Application_BeginRequest(ByVal sender As Object, ByVal e As EventArgs)
            ' Fires at the beginning of each request
        End Sub

        Sub Application_AuthenticateRequest(ByVal sender As Object, ByVal e As EventArgs)
            ' Fires upon attempting to authenticate the use
        End Sub

        Sub Application_Error(ByVal sender As Object, ByVal e As EventArgs)
            ' Fires when an error occurs
        End Sub

        Sub Session_End(ByVal sender As Object, ByVal e As EventArgs)
            ' Fires when the session ends
        End Sub

        Sub Application_End(ByVal sender As Object, ByVal e As EventArgs)
            ' Fires when the application ends
        End Sub

    End Class

End Namespace
