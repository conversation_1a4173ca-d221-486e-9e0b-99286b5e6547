﻿<%@ Page Language="vb" MasterPageFile="~/MasterPage.master" Title="Change Owner"
    AutoEventWireup="false" Inherits="AccountManager.ChangeOwner" CodeFile="ChangeOwner.aspx.vb" %>

<%@ Register Assembly="DevExpress.Web.ASPxGridView.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridLookup" TagPrefix="dx1" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx1" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>

<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table cellspacing="0" cellpadding="0" border="0">
        <tr>
            <td>
                <asp:Label ID="lblWelcome" runat="server" CssClass="TableHeader">Submit request to Change Ownership for the selected accounts.</asp:Label><br />
                <br />
                <asp:Label ID="lblMessage" runat="server"></asp:Label><br />
                                                        <asp:Label ID="lblNewOwner" CssClass="LabelRed" runat="server"></asp:Label><br />
                <asp:Panel ID="pChange" runat="server">
                <table  class="DisplayTables">
                    <tr>
                        <td>
                            
                                <table>
                                    <tr>
                                        <td>
                                            <table>
                                                <tr>
                                                    <td colspan="2">
                                                        <table>
                                                            <tr>
                                                                <td>
                                                                    Step 1: Verify account(s) listed.</td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    Step 2: Enter a valid user id (eg: B12345).</td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    Step 3: Enter in comments detailing the change.</td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    Step 4: Click submit button.</td>
                                                            </tr>
                                                        </table>
                                                        <br />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        New Owner ID:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtNewOwner" Display="None" ErrorMessage="Please enter new Owner ID." SetFocusOnError="True" ValidationGroup="ChgOwner"></asp:RequiredFieldValidator>
                                                    </td>
                                                    <td>
                                                        <asp:TextBox ID="txtNewOwner" runat="server"></asp:TextBox></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        Comments:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="txtNewOwnerComments" Display="None" ErrorMessage="Please enter comments." SetFocusOnError="True" ValidationGroup="ChgOwner"></asp:RequiredFieldValidator>
                                                    </td>
                                                    <td>
                                                        <asp:TextBox ID="txtNewOwnerComments" MaxLength="500" runat="server" TextMode="MultiLine" Rows="4"
                                                            Columns="40"></asp:TextBox></td>
                                                </tr>
                                            </table>
                                        </td>
                                        <td valign="top" rowspan="6">
                                            <asp:ListBox ID="lstAccounts" runat="server" Rows="8"></asp:ListBox></td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <table>
                                                <tr>
                                                    <td style="width: 100px; height: 34px">
                                            <dx:aspxbutton id="cmdUpdateOwner" ValidationGroup="ChgOwner" runat="server" text="Submit" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css" CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"></dx:aspxbutton>
                                                    </td>
                                                    <td style="width: 266px; height: 34px">
                                                        <dx:aspxbutton id="cmdReturn" runat="server" text="Return to My Accounts" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css" CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css" Width="177px"></dx:aspxbutton>
                                                    </td>
                                                </tr>
                                            </table>
                                            
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <asp:ValidationSummary ID="ValidationSummary1" ValidationGroup="ChgOwner" runat="server" />
                                        </td>
                                    </tr>
                                    
                                </table>
                            <asp:Panel ID="pnlRequests" runat="server">

                                <table style="width:100%;">
                                    <tr>
                                        <td>
                                            <b>Pending Ownership Requests:</b>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <dx1:ASPxGridView ID="grdRequests" runat="server" AutoGenerateColumns="False" ClientInstanceName="grid" CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css" CssPostfix="PlasticBlue" Font-Size="Small" KeyFieldName="RequestID" Width="100%">
                                    <Columns>
                                        <dx1:gridviewdatatextcolumn Caption="AccountID" FieldName="AccountID" VisibleIndex="1">
                                        </dx1:gridviewdatatextcolumn>
                                        <dx1:gridviewdatatextcolumn Caption="New Owner" FieldName="NewOwnerDet" VisibleIndex="2">
                                        </dx1:gridviewdatatextcolumn>
                                        <dx1:gridviewdatatextcolumn Caption="Requested By" FieldName="OldOwner" VisibleIndex="3">
                                        </dx1:gridviewdatatextcolumn>
                                        <dx1:gridviewdatatextcolumn Caption="Status" FieldName="StatusShow" VisibleIndex="4">
                                        </dx1:gridviewdatatextcolumn>
                                      </Columns>
                                        
                                    <settings showfilterrow="false" showfooter="True" showgrouppanel="false"  />
                                                <SettingsBehavior AllowSort="false" />
                                    <styles cssfilepath="~/App_Themes/PlasticBlue/{0}/styles.css" csspostfix="PlasticBlue">
                                        <header imagespacing="10px" sortingimagespacing="10px">
                                        </header>
                                        <alternatingrow backcolor="silver">
                                        </alternatingrow>
                                    </styles>
                                    <settingspager showdefaultimages="False">
                                        <allbutton text="All">
                                        </allbutton>
                                        <nextpagebutton text="Next &gt;">
                                        </nextpagebutton>
                                        <prevpagebutton text="&lt; Prev">
                                        </prevpagebutton>
                                    </settingspager>
                                    <imagesfiltercontrol>
                                        <loadingpanel url="~/App_Themes/PlasticBlue/Editors/Loading.gif">
                                        </loadingpanel>
                                    </imagesfiltercontrol>
                                    <images spritecssfilepath="~/App_Themes/PlasticBlue/{0}/sprite.css">
                                        <loadingpanelonstatusbar url="~/App_Themes/PlasticBlue/GridView/gvLoadingOnStatusBar.gif">
                                        </loadingpanelonstatusbar>
                                        <loadingpanel url="~/App_Themes/PlasticBlue/GridView/Loading.gif">
                                        </loadingpanel>
                                    </images>
                                    <styleseditors>
                                        <calendarheader spacing="11px">
                                        </calendarheader>
                                        <progressbar height="25px">
                                        </progressbar>
                                    </styleseditors>
                                    <settingsloadingpanel text="Loading......" />
                                </dx1:ASPxGridView>
                                        </td>
                                    </tr>
                                </table>
                           </asp:Panel>
                        </td>
                    </tr>
                </table>
                         </asp:Panel>
            </td>
        </tr>
    </table>
</asp:Content>
