Imports Microsoft.VisualBasic
Imports System.Data

Public Class Account

    Dim strAccountID As String
    Dim strAccess As String

    ReadOnly Property Access() As String
        Get
            Return strAccess
        End Get
    End Property


    Sub New(ByVal UserId As String)
        strAccountID = UserId
    End Sub

    Function GetDetails(ByVal UserName As String) As Boolean

        Dim strSQL As String
        Dim dbGet As New AccountManager.DataAccess
        Dim srGet As Data.SqlClient.SqlDataReader


        strSQL = "SELECT * FROM vUsr WHERE AccountID = '" & strAccountID & "' AND UserName = '" & UserName & "' "

        srGet = dbGet.GetDataReaderByStringId(strSQL)

        If srGet.Read Then
            GetDetails = True
            strAccess = srGet.Item("Access")
        End If

        srGet.Close()
        srGet = Nothing
        dbGet.CloseConnections()
        dbGet = Nothing

        Return GetDetails

    End Function
End Class
