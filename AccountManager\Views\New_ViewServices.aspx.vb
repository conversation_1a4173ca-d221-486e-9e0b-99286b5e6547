Imports System.Data
Namespace AccountManager

    Partial Class ViewServices
        Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Dim strSQL As String

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
            LoadRequests()
        End Sub

        Sub LoadRequests()

            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            'strSQL = "SELECT ID,Host,DisplayName,AccountName,JobRunTimeKEy FROM SA_Common_Services_DEFAULT WHERE SA_HOST = '" & [Global].GblSAServer & "' AND ("

            strSQL = "SELECT rowGUID,Host,DisplayName,AccountName,JobRunTimeKEy FROM SA_Services_DEFAULT WHERE SA_HOST = '" & [Global].GblSAServer & "' AND ("

            Dim strUsers As String()
            Dim i As Integer = 0

            strUsers = Split(Session("Accounts"), ";")

            Do While i < strUsers.Length
                If Trim(strUsers(i)) <> "" Then
                    strSQL = strSQL & " AccountName like '%" & Trim(strUsers(i)) & "%' OR"
                End If
                i = i + 1
            Loop
            strSQL = strSQL.Remove(strSQL.Length - 2, 2)
            strSQL = strSQL & ") Order by AccountName,Host"

            srRead = dbGet.GetDataReaderByStringId(strSQL, [Global].GblSauConn)
            Trace.Warn("FOO: " & srRead.HasRows)
            If srRead.HasRows Then
                grdServices.DataKeyField = "ID"
                grdServices.DataSource = srRead
                grdServices.DataBind()
                'grdRequests.ExpandAllRows()
                cmdExport.Visible = True
                grdServices.Visible = True
            Else
                grdServices.Visible = False
                lblMessage.CssClass = "LabelRed"
                lblMessage.Text = "No services found running as the selected accounts"
                cmdExport.Visible = False
            End If

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()

        End Sub

        Private Sub cmdReturn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdReturn.Click
            Response.Redirect("/AccountManager/MyUsers.aspx")
        End Sub

        Private Sub cmdExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdExport.Click
            Dim prtReport As New Print([Global].GblSauConn)

            hlDownload.NavigateUrl = prtReport.CreateCSV(strSQL)
            hlDownload.Text = "Download File"
        End Sub
    End Class

End Namespace
