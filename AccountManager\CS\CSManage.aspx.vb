Imports System.DirectoryServices
Public Class CSManage
    Inherits System.Web.UI.Page

    Protected WithEvents cmdVirtualDone As System.Web.UI.WebControls.Button
    Protected WithEvents cmdVirtualEdit As System.Web.UI.WebControls.Button
    Protected WithEvents litDate As System.Web.UI.WebControls.Literal
    Protected WithEvents rdoGroup As System.Web.UI.WebControls.RadioButton
    Protected WithEvents rdoOwner As System.Web.UI.WebControls.RadioButton
    Protected WithEvents rdoBackup As System.Web.UI.WebControls.RadioButton
    Protected WithEvents rdoUser As System.Web.UI.WebControls.RadioButton
    Protected WithEvents pGroup As System.Web.UI.WebControls.Panel
    Protected WithEvents cmdUpdate As System.Web.UI.WebControls.Button
    Protected WithEvents lblTip As System.Web.UI.WebControls.Label

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

    End Sub
    Protected WithEvents lblTitle As System.Web.UI.WebControls.Label
    Protected WithEvents txtSearch As System.Web.UI.WebControls.TextBox
    Protected WithEvents cmdSearch As System.Web.UI.WebControls.Button
    Protected WithEvents cmdFinish As System.Web.UI.WebControls.Button
    Protected WithEvents lblMessage As System.Web.UI.WebControls.Label
    Protected WithEvents txtInProg As System.Web.UI.WebControls.TextBox
    Protected WithEvents dgLookup As System.Web.UI.WebControls.DataGrid
    Protected WithEvents txtSearchTitle As System.Web.UI.WebControls.TextBox

    'NOTE: The following placeholder declaration is required by the Web Form Designer.
    'Do not delete or move it.
    Private designerPlaceholderDeclaration As System.Object

    Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
        'CODEGEN: This method call is required by the Web Form Designer
        'Do not modify it using the code editor.
        InitializeComponent()
    End Sub

#End Region

    Const StrTipSrv = "Tip: Entering 'USTC' will find all assets with the letters USTC within the name."
    Const StrTipSoft = "Tip: Entering 'Windows' will find all Windows versions."
    Const StrTipUser = "Tip: Enter the user ID to find users."
    Const StrTipUserGrp = "" 'Tip: Select either user or group to search by and enter a user ID or the name of the group."
    Const StrTipEmail = "Tip: Enter the name of the email address. Example _Enteprise will find all email address' that starts with '_Enterprise'."

    Dim strLookup As String
    Dim strSoftNames As String
    Dim strSoftIDs As String

    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        'cmdSearch.Attributes.Add("onclick", "return confirm('Are you sure you want to delete this company?');")
        cmdSearch.Attributes.Add("onclick", "frmLookUp.txtInProg.value='Search in progress';")

        If Not Page.IsPostBack Then
            txtSearchTitle.Text = Request.QueryString("Lookup")
            strLookup = txtSearchTitle.Text
        End If

        'RegisterStartupScript("Focus", Helper.SetControlFocus("txtSearch"))

        strLookup = txtSearchTitle.Text
        GetTitle()

    End Sub

    Function GetValues(ByVal DgFields As DataGrid, ByVal ChkName As String)

        Dim strSelectedVal As String
        Dim cb As CheckBox
        Dim dgi As DataGridItem
        Dim strField As String

        For Each dgi In DgFields.Items
            cb = dgi.FindControl(ChkName)

            If cb.Checked Then
                strSoftNames = strSoftNames & CType(dgi.Cells(3).Text, String) & ";"
                strSoftIDs = strSoftIDs & CType(dgi.Cells(2).Text, String) & ";"
            End If

        Next

    End Function

    Sub GetTitle()

        Select Case LCase(strLookup)
            Case "server"
                lblTitle.Text = "Lookup Hardware"
                lblTip.Text = StrTipSrv

            Case "software"
                lblTitle.Text = "Lookup Software"
                dgLookup.Columns(0).Visible = False
                dgLookup.Columns(1).Visible = True
                cmdFinish.Visible = True
                lblTip.Text = StrTipSrv

            Case "region"
                lblTitle.Text = "Lookup Region"

            Case "subregion"
                lblTitle.Text = "Lookup Sub Region"

            Case "country"
                lblTitle.Text = "Lookup Country"

            Case "city"
                lblTitle.Text = "Lookup City"

            Case "site"
                lblTitle.Text = "Lookup Country"

            Case "owner", "PersonOnlyName", "person"
                lblTitle.Text = "Lookup Person by ID"
                lblTip.Text = StrTipUser

            Case "userorgroup"
                lblTitle.Text = "Lookup user by ID or group account by group name"
                pGroup.Visible = True
                lblTip.Text = StrTipUserGrp

            Case "email"
                lblTitle.Text = "Lookup Email Account by name (ex: _Enterprise Server, Mgmt)."
                lblTip.Text = StrTipEmail

        End Select
    End Sub

    Function GetSQL(ByVal strLike As String) As String

        Dim strSQL As String

        Select Case strLookup
            Case "server"
                strSQL = "SELECT [Host Name], [Asset Type], Asset_ID as [Asset ID]  FROM " & _
                        " dbo.vTblAsset " & strLike & _
                        " ORDER BY [Host Name]"
            Case "software"
                strSQL = "SELECT Reg_ID as [Software ID],UniqueName as [Software Title] FROM vRegSoftware WHERE RegType = 'Software' " & _
                        strLike & _
                        "ORDER BY UniqueName"
            Case "region"
                strSQL = "SELECT dd_ID as [Region ID], dd_Value as Region from vDropDowns WHERE dd_Name = 'ddlRegion' " & strLike & _
                         " Order By Region"

            Case "subregion"
                strSQL = "SELECT dd_ID as [Region ID], dd_Value as SubRegion from vDropDowns WHERE dd_Name = 'ddlSubRegion' " & strLike & _
                         " Order By SubRegion"

            Case "country"
                strSQL = "SELECT dd_ID as [Country ID], dd_Value as Country from vDropDowns WHERE dd_Name = 'ddlCountry'" & strLike & _
                         " Order By Country"

            Case "city"
                strSQL = "SELECT dd_ID as [City ID], dd_Value as City from vDropDowns WHERE dd_Name = 'ddlCity'" & strLike & _
                         " Order By City"

            Case "site"
                strSQL = "SELECT Site_ID as [Site ID], Site from vSites " & strLike & _
                         " Order By Site"

            Case "person", "PersonOnlyName", "owner"
                strSQL = "person"

            Case "email"
                strSQL = "email"

            Case "UserOrGroup"
                strSQL = "UserOrGroup"

            Case Else
                lblTitle.Text = "We can't lookup the field " & strLookup
        End Select

        Return strSQL

    End Function

    Sub LoadLookupData(ByVal SQL As String)

        'Dim adUsr As New AD
        'Try

        '    If SQL = "person" Or SQL = "email" Or SQL = "UserOrGroup" Then

        '        If Trim(txtSearch.Text) <> "" Then

        '            Dim srOwners As SearchResultCollection

        '            adUsr.ObjectClass = "User"
        '            adUsr.ObjectCategory = "Person"

        '            If SQL = "email" Then
        '                adUsr.Filter = "(&(objectClass=User)(objectCategory=person)(|(DisplayName=" & txtSearch.Text & "*)(cn=" & txtSearch.Text & "*)))"

        '            ElseIf SQL = "UserOrGroup" And rdoGroup.Checked Then
        '                adUsr.Filter = "(&(objectClass=group)(cn=" & txtSearch.Text & "*))"

        '            ElseIf (SQL = "UserOrGroup" And rdoUser.Checked) Or SQL = "person" Then
        '                adUsr.ChangeLDAP("LDAP://OU=Accounts,DC=kcc,DC=com")

        '            End If

        '            srOwners = adUsr.FindObjects(txtSearch.Text)

        '            AddUsersToDG(srOwners)
        '            lblTitle.Text = adUsr.Message
        '            srOwners = Nothing

        '        End If

        '    Else
        '        Dim dbGetLook As New Database
        '        Dim drGet As SqlClient.SqlDataReader

        '        Try
        '            drGet = dbGetLook.GetDataReaderByStringId(SQL, Global.GblAssetConn)

        '            dgLookup.DataSource = drGet
        '            dgLookup.DataBind()
        '        Finally
        '            drGet.Close()
        '            drGet = Nothing
        '            dbGetLook.CloseConnections()
        '        End Try

        '    End If

        '    If dgLookup.Items.Count < 1 Then
        '        lblMessage.Text = "No records found matching the text you entered."
        '    End If

        'Catch e As Exception
        '    If e.Message.IndexOf("Invalid attempt to Read when reader is closed") > -1 Then
        '        lblMessage.Text = "No records found matching the text you entered."
        '    Else
        '        lblMessage.Text = adUsr.Message & "  System Error: " & e.Message
        '    End If
        'End Try

    End Sub

    Sub CloseWindow(ByVal Index As Integer)
        Dim strjscript As String = "<script language=""javascript"">"

        Select Case strLookup

            Case "server"
                strjscript = strjscript & "window.opener." & HttpContext.Current.Request.QueryString("formname") & ".value = '" & dgLookup.Items(Index).Cells(2).Text & "';"
                strjscript = strjscript & "window.opener." & HttpContext.Current.Request.QueryString("formname2") & ".value = '" & dgLookup.Items(Index).Cells(4).Text & "';"
                strjscript = strjscript & "window.opener." & HttpContext.Current.Request.QueryString("formname1") & ".value = '" & dgLookup.Items(Index).Cells(3).Text & "';window.close();"

            Case "PersonOnlyName"
                Dim strName As String
                strName = dgLookup.Items(Index).Cells(2).Text & " (" & dgLookup.Items(Index).Cells(3).Text & ")"
                strjscript = strjscript & "window.opener." & HttpContext.Current.Request.QueryString("formname") & ".value = '" & strName & "';window.close();"

            Case "software"

                If dgLookup.Items.Count = 1 Then
                    strSoftNames = strSoftNames & CType(dgLookup.Items(Index).Cells(3).Text, String) & ";"
                    strSoftIDs = strSoftIDs & CType(dgLookup.Items(Index).Cells(2).Text, String) & ";"
                Else
                    GetValues(dgLookup, "chkValues")
                End If

                strjscript = strjscript & "window.opener." & HttpContext.Current.Request.QueryString("formname") & ".value = '" & strSoftNames & "';"
                strjscript = strjscript & "window.opener." & HttpContext.Current.Request.QueryString("formname1") & ".value = '" & strSoftIDs & "';window.close();"

            Case Else '"region", "country", "software", "city"
                strjscript = strjscript & "window.opener." & HttpContext.Current.Request.QueryString("formname") & ".value = '" & dgLookup.Items(Index).Cells(3).Text & "';"
                strjscript = strjscript & "window.opener." & HttpContext.Current.Request.QueryString("formname1") & ".value = '" & dgLookup.Items(Index).Cells(2).Text & "';window.close();"

        End Select

        strjscript = strjscript & "</script" & ">" 'Don't Ask, Tool Bug
        litDate.Text = strjscript
    End Sub

    Private Sub dgLookup_SelectedIndexChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles dgLookup.SelectedIndexChanged
        CloseWindow(dgLookup.SelectedIndex)
    End Sub

    Sub AddUsersToDG(ByVal scUsers As SearchResultCollection)

        Dim sr As SearchResult
        Dim strName As String
        Dim intCount As Integer

        Dim dtUser As DataTable
        Dim dcolUsr As DataColumn
        Dim drowUsr As DataRow

        intCount = 0
        dtUser = New DataTable("Users")

        'Add Columns to DataTable

        If strLookup = "email" Then
            dcolUsr = New DataColumn("User Name")
            dtUser.Columns.Add(dcolUsr)
            dcolUsr = New DataColumn("Mail")
            dtUser.Columns.Add(dcolUsr)
        ElseIf strLookup = "UserOrGroup" And rdoGroup.Checked Then
            dcolUsr = New DataColumn("Description")
            dtUser.Columns.Add(dcolUsr)
            dcolUsr = New DataColumn("Group Name")
            dtUser.Columns.Add(dcolUsr)
        Else
            dcolUsr = New DataColumn("User Name")
            dtUser.Columns.Add(dcolUsr)
            dcolUsr = New DataColumn("ID")
            dtUser.Columns.Add(dcolUsr)
        End If

        Do While intCount < scUsers.Count

            drowUsr = dtUser.NewRow
            sr = scUsers.Item(intCount)

            If strLookup = "email" Then
                drowUsr("User Name") = sr.GetDirectoryEntry.Properties.Item("DisplayName").Value
                drowUsr("Mail") = sr.GetDirectoryEntry.Properties.Item("Mail").Value

            ElseIf strLookup = "UserOrGroup" And rdoGroup.Checked Then
                drowUsr("Description") = sr.GetDirectoryEntry.Properties.Item("Description").Value
                drowUsr("Group Name") = sr.GetDirectoryEntry.Properties.Item("cn").Value

            Else
                drowUsr("User Name") = sr.GetDirectoryEntry.Properties.Item("DisplayName").Value
                drowUsr("ID") = sr.GetDirectoryEntry.Properties.Item("cn").Value
            End If

            intCount += 1
            dtUser.Rows.Add(drowUsr)
        Loop

        dgLookup.DataSource = dtUser
        dgLookup.DataBind()

        sr = Nothing
        scUsers = Nothing

    End Sub

    Sub search()
        If Trim(txtSearch.Text).Length < 3 And LCase(strLookup) <> "region" And LCase(strLookup) <> "subregion" Then
            lblMessage.CssClass = "LabelRed"
            lblMessage.Text = "You must enter at least three characters to search by."
            txtInProg.Text = "Search Cancelled"
        ElseIf (InStr(LCase(txtSearch.Text), "c") > 0 Or InStr(LCase(txtSearch.Text), "d") > 0 Or InStr(LCase(txtSearch.Text), "n") > 0 Or InStr(LCase(txtSearch.Text), "p") > 0 Or InStr(LCase(txtSearch.Text), "r") > 0) And LCase(strLookup) = "person" Then
            lblMessage.CssClass = "LabelRed"
            lblMessage.Text = "Only KC Employees can be an owner or backup"
            txtInProg.Text = "Search Cancelled"
        Else
            lblMessage.Text = ""

            Select Case strLookup
                Case "server"
                    LoadLookupData(GetSQL("WHERE [Host Name] LIKE '%" & txtSearch.Text & "%' "))

                Case "software"
                    LoadLookupData(GetSQL("AND [UniqueName] LIKE '%" & txtSearch.Text & "%' "))

                Case "region", "city", "country", "subregion"
                    LoadLookupData(GetSQL("AND [DD_Value] LIKE '%" & txtSearch.Text & "%' "))

                Case "site"
                    LoadLookupData(GetSQL("WHERE [Site] LIKE '%" & txtSearch.Text & "%' "))

                Case Else
                    LoadLookupData(GetSQL(""))

            End Select
            txtInProg.Text = "Search Complete"
        End If
        GetTitle()
    End Sub

    Private Sub cmdSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdSearch.Click
        search()
    End Sub

    Private Sub txtSearch_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtSearch.TextChanged
        search()
    End Sub

    Private Sub cmdFinish_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdFinish.Click
        CloseWindow(0)
    End Sub

    Private Sub txtSearchTitle_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearchTitle.TextChanged

    End Sub
End Class

