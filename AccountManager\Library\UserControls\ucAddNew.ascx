﻿<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Control Language="vb" AutoEventWireup="false" Inherits="AccountManager.ucAddNew" CodeFile="ucAddNew.ascx.vb" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<table class="DisplayTables" style="width:100%">
    <colgroup>
        <col style="width:200px;" />
        <col style="width:auto;" />
    </colgroup>
	<tr>
		<td class="FunctionStep" colspan="2">Add Account</td>
	</tr>
	<tr>
		<td  colspan="2"><asp:label id="lblMessage" CssClass="LabelRed" Runat="server"></asp:label></td>
	</tr>
	<tr>
		<td>
            Account ID:
        </td>
        <td><table><tr><td><asp:TextBox id="txtAccount" Runat="server"> </asp:TextBox></td><td><dx:aspxbutton id="cmdLookup" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                            csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                            text="Lookup ID"></dx:aspxbutton></td></tr></table>

        </td>
	</tr>
	<asp:Panel id="pInsert" Runat="server" Visible="false">
		<tr>
			<td>Description:</td><td>
                <asp:Label ID="lblDesc" runat="server" Text=""></asp:Label></td>
		</tr>
        <tr>
			<td>Password Last Set Date:</td><td>
                <asp:Label ID="lblExpirationDate" runat="server" Text=""></asp:Label><asp:HiddenField ID="hfPasswordMustChangeAtNextLogon" runat="server" />
            </td>
		</tr>
        <tr>
			<td>Password Never Expires:</td><td>
                <asp:Label ID="lblAccountType" runat="server" Text=""></asp:Label></td>
		</tr>
        <tr>
			<td>Account is disabled:</td><td>
                <asp:Label ID="lblActEnabled" runat="server" Text=""></asp:Label></td>
		</tr>
        <tr>
			<td>Application Name:</td><td>
				<asp:TextBox id="txtAppName" MaxLength="80" Width="500px" Runat="server"></asp:TextBox></td>
		</tr>
        <tr>
			<td>Purpose:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtpurpose" Display="None" ErrorMessage="Please enter Pupose." SetFocusOnError="True" ValidationGroup="a"> </asp:RequiredFieldValidator></td><td>
				<asp:TextBox id="txtpurpose" MaxLength="400" Width="500px" Runat="server"></asp:TextBox></td>
		</tr>
		<tr>
			<td>Owner ID:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="txtOwnerID" Display="None" ErrorMessage="Please enter Owner ID." SetFocusOnError="True" ValidationGroup="a"> </asp:RequiredFieldValidator></td><td>
				<asp:TextBox id="txtOwnerID" MaxLength="6" Runat="server" ReadOnly="true"></asp:TextBox></td>
		</tr>
        <tr>
			<td>Delegate ID:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ControlToValidate="txtDelegateID" Display="None" ErrorMessage="Please enter Delegate." SetFocusOnError="True" ValidationGroup="a"> </asp:RequiredFieldValidator></td><td>
				<asp:TextBox id="txtDelegateID" MaxLength="6" Runat="server"></asp:TextBox></td>
		</tr>
		<%--<tr>
			<td colspan="2">
				<asp:label id="lblOwnType" Runat="server" visible="False">Owner Type</asp:label>&nbsp;
				<asp:dropdownlist id="ddlOwnType" Runat="server" visible="False">					
				</asp:dropdownlist></td>
		</tr>--%>
		<%--<tr>
			<td colspan="2">Exception Account:
				<asp:CheckBox id="chkExcept" Runat="server" Checked="False"></asp:CheckBox><br/>
				<i>Account does not require regular password changes.</i></td>
		</tr>--%>
		<tr>
			<td colspan="2">
                <table>
                    <tr>
                        <td style="width: 100px">
                            <dx:aspxbutton id="cmdInsert" runat="server" ValidationGroup="a" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                text="Insert" width="81px"></dx:aspxbutton>
                        </td>
                        <td style="width: 100px">
                             <dx:aspxbutton id="cmdReset" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                text="Reset" width="81px"></dx:aspxbutton>
                           <%-- <dx:aspxbutton id="cmdAddBackup" Visible="false" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                text="Add Backup" width="102px"></dx:aspxbutton>--%>
                        </td>
                    </tr>
                </table>
                &nbsp;
            </td>
		</tr>
	</asp:Panel>
	<tr>
		<td colspan="2">
            <asp:ValidationSummary ID="ValidationSummary2" ValidationGroup="a" runat="server" />
			<asp:label id="lblInstructions" Runat="server"></asp:label>
            <asp:TextBox ID="txtDesc" runat="server" Visible="False"></asp:TextBox>
            <br/>
            
		</td>
	</tr>
</table>
<%--<table>
	<tr>
		<td style="height: 352px; width: 615px; vertical-align: top; font-family: Verdana; text-align: left;">
            <dx:aspxgridview id="grdUsers" runat="server" autogeneratecolumns="False" cssfilepath="~/App_Themes/PlasticBlue/{0}/styles.css"
                csspostfix="PlasticBlue" width="639px">
<Styles CssPostfix="PlasticBlue" CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css">
<Header SortingImageSpacing="10px" ImageSpacing="10px"></Header>

<AlternatingRow ForeColor="InactiveCaptionText"></AlternatingRow>
</Styles>

<SettingsPager ShowDefaultImages="False">
<AllButton Text="All"></AllButton>

<NextPageButton Text="Next &gt;"></NextPageButton>

<PrevPageButton Text="&lt; Prev"></PrevPageButton>
</SettingsPager>

<ImagesFilterControl>
<LoadingPanel Url="~/App_Themes/PlasticBlue/Editors/Loading.gif"></LoadingPanel>
</ImagesFilterControl>

<Images SpriteCssFilePath="~/App_Themes/PlasticBlue/{0}/sprite.css">
<LoadingPanelOnStatusBar Url="~/App_Themes/PlasticBlue/GridView/gvLoadingOnStatusBar.gif"></LoadingPanelOnStatusBar>

<LoadingPanel Url="~/App_Themes/PlasticBlue/GridView/Loading.gif"></LoadingPanel>
</Images>
<Columns>
<dx:GridViewDataTextColumn FieldName="AccountID" GroupIndex="0" SortIndex="0" SortOrder="Ascending" VisibleIndex="0"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="UserNameDN" Caption="Owner/Backup" VisibleIndex="1"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="UserName" Caption="Owner/Backup ID" VisibleIndex="2"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="Access" Caption="Access" VisibleIndex="3"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="Description" Caption="Description" Visible="False" VisibleIndex="5"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="ExpirationDate" Caption="EXpirationDate" Visible="False" VisibleIndex="4"></dx:GridViewDataTextColumn>
</Columns>

<StylesEditors>
<CalendarHeader Spacing="11px"></CalendarHeader>

<ProgressBar Height="25px"></ProgressBar>
</StylesEditors>
</dx:aspxgridview>
        </td>
	</tr>
</table>--%>
