﻿<%@ Page MasterPageFile="~/MasterPage.master" Title="Delete Account" Language="vb" AutoEventWireup="false" Inherits="AccountManager.DeleteAccount"
    CodeFile="DeleteAccount.aspx.vb" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>

<%@ Register TagPrefix="uc1" TagName="Menu" Src="~/Library/UserControls/Menu.ascx" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<asp:content id="Content1" contentplaceholderid="ContentPlaceHolder1" runat="Server">
    <table>
        <tr>
            <td>
						<asp:label id="lblWelcome" CssClass="TableHeader" Runat="server">Request Deletion of a Production Account</asp:label><br/>
						<br/>
						<asp:label id="lblMessage" CssClass="LabelGreen" Runat="server"></asp:label>
                        <br />
                        <br />
						<asp:label id="lblErrMessage" CssClass="LabelRed" Runat="server"></asp:label><br/>
						<table>
							<tr>
								<td>
									<asp:Panel ID="pDelete" Runat="server">
										<table class="DisplayTables">
											<tr>
												<td>
													<table>
														<tr>
															<td style="height: 190px">
																<table>
																	<tr>
																		<td></td>
																	</tr>
																	<tr>
																		<td>Step 1: Review the account that you are deleting.</td>
																	</tr>
																	<tr>
																		<td>Step 2: Enter in comments detailing the change.</td>
																	</tr>
																	<tr>
																		<td>Step 3: Click the Delete button.</td>
																	</tr>
																	<tr>
																		<td>Note: The account will be disabled for 2 months and then deleted.</td>
																	</tr>
																	<tr>
																		<td><FONT color="red"><B>WARNING:</B></FONT>&nbsp;Once the account is disabled any 
																			process using this account will break.
																			<br/>
																			It is your responsibility to ensure that there are not any processes using this 
																			account.</td>
																	</tr>
																</table>
																<br/>
															</td>
														</tr>
														<asp:Panel id="pDel" Runat="server"></asp:Panel>
															<tr>
																<td>Account IDs:&nbsp;&nbsp;
																	<asp:Label id="lblAccount" Runat="server"></asp:Label></td>
															</tr>
															<tr>
																<td>
																	<asp:Label id="lblDescription" Runat="server" Visible="False"></asp:Label></td>
															</tr>
															<tr>
																<td>Why are you deleting these account?</td>
															</tr>
															<tr>
																<td>
																	<asp:textbox id="txtComments" Runat="server" Columns="40" Rows="4" TextMode="MultiLine"></asp:textbox></td>
															</tr></table>
															
												</td>
											</tr>
											<tr>
											   <td>
                                                   &nbsp;<table style="width: 345px">
                                                       <tr>
                                                           <td style="width: 77px; height: 21px">
                                                               <dx:aspxbutton id="cmdDel" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                   csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                                   text="Delete"></dx:aspxbutton>
                                                           </td>
                                                           <td style="width: 144px; height: 21px">
                                                               <dx:aspxbutton id="cmdReturn" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                   csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                                   text="Return to My Accounts" width="147px"></dx:aspxbutton>
                                                           </td>
                                                       </tr>
                                                   </table>
                                                   &nbsp;
                                               </td>
											</tr>
										</table>
										</asp:Panel>
									</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
</asp:content>
