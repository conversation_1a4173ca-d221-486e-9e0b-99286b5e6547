Imports DevExpress.Web.ASPxGrid
Imports System.Data

Namespace AccountManager

    Partial Class VerifyOwnership
        Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub
        Protected WithEvents cmdOwnerVerified As System.Web.UI.WebControls.Button


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

            GetAccountsForUser()
        End Sub

        Sub GetAccountsForUser()

            Dim usrcur As New UserInfo
            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader
            Dim arUsers As New ArrayList

            lblMessage.Text = "Verify Ownership " & usrcur.DisplayName & " (" & usrcur.GetUserID & ")"

            strSQL = "SELECT AccountID FROM vUsr " & _
                     "WHERE USERName = '" & usrcur.GetUserID & "'"

            srRead = dbGet.GetDataReaderByStringId(strSQL)

            Do While srRead.Read

                arUsers.Add(srRead.Item("AccountID"))

            Loop

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()

            LoadData(arUsers, usrcur.GetUserID)

        End Sub


        Sub LoadData(ByVal arList As ArrayList, ByVal strUserID As String)

            Dim intIndex As Integer
            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            strSQL = "SELECT * FROM vUsr WHERE ("
            Do While intIndex < arList.Count

                strSQL = strSQL & " AccountID = '" & arList.Item(intIndex) & "' OR"
                intIndex = intIndex + 1
            Loop

            strSQL = strSQL.Remove(strSQL.Length - 2, 2)
            strSQL = strSQL & ") AND ( UserName = '" & strUserID & "') "
            'Trace.Warn(strSQL)
            srRead = dbGet.GetDataReaderByStringId(strSQL)


            grdUsers.DataKeyField = "AccountID"
            grdUsers.DataSource = srRead
            grdUsers.DataBind()
            grdUsers.ExpandAllRows()

            Trace.Warn("grdUsers.Items.Count: " & grdUsers.Items.Count)

            If grdUsers.Items.Count > 12 Then
                grdUsers.PageIndexButtonCount = 12
            Else
                grdUsers.PageIndexButtonCount = grdUsers.Items.Count
            End If

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()

        End Sub


        Private Sub cmdOwnerVerified_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdOwnerVerified.Click

            Dim usrcur As New UserInfo
            Dim strUser As String
            Dim i As Integer
            Dim blnLoad As Boolean = True
            Dim hlpIns As New Helper

            For i = 0 To (grdUsers.GetSelectedRowCount()) - 1
                Dim selectedRow As Row = grdUsers.GetSelectedRow(i)

                If selectedRow.Level = grdUsers.GetGroupCount() Then
                    strUser = selectedRow.DataControllerRow("AccountID").ToString()
                    Dim dbUpd As New DataAccess
                    dbUpd.UpdateDBByStringId("sp_UM_UpdateOwnerVerified_ByID '" & strUser & "'")
                    hlpIns.InsertLog(strUser, [Global].GblActOwnVer, Now, "")
                End If
            Next i
            GetAccountsForUser()
            lblVerifyOwner.Text = "Ownership verified."
        End Sub

    End Class

End Namespace
