Imports Dundas.Gauges.WebControl
Imports System.Data
Imports system.Drawing
Namespace AccountManager

    Partial Class Stats
        Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub
        Protected WithEvents lblDisabled As System.Web.UI.WebControls.Label
        Protected WithEvents GaugeContainer1 As Dundas.Gauges.WebControl.GaugeContainer



        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
            LoadGauge()
            LoadChart()
        End Sub

        Sub LoadGauge()

            Dim hstGet As New History
            Dim intMax, intPointExpired, intPointWillExpire, intOK, intPDandDis As Integer

            hstGet.CollectHistory(1)

            intMax = hstGet.Total
            intPointExpired = hstGet.ExpiredNotDis
            intPointWillExpire = hstGet.ExpireSoon
            intOK = hstGet.GreenAccounts
            intPDandDis = hstGet.ExpireAndDis

            ' Get circular scale object
            Dim scStatsPD As CircularScale = gcStatsGauge.CircularGauges("PastDue").Scales("Default")
            Dim scStatsWE As CircularScale = gcStatsGauge.CircularGauges("WillExpire").Scales("Default")
            Dim scStatsOK As CircularScale = gcStatsGauge.CircularGauges("OK").Scales("Default")
            Dim scStatsTot As LinearScale = gcStatsGauge.LinearGauges("Total").Scales("Default")
            Dim scStatsPDDis As LinearScale = gcStatsGauge.LinearGauges("ExpireAndDis").Scales("Default")

            Dim lblTitleTotal As GaugeLabel = gcStatsGauge.Labels("TitleTotal")
            Dim lblPCTPD As GaugeLabel = gcStatsGauge.Labels("PercentPD")
            Dim lblPCTES As GaugeLabel = gcStatsGauge.Labels("PercentES")
            Dim lblPCTOK As GaugeLabel = gcStatsGauge.Labels("PercentOK")
            Dim lblTotPD As GaugeLabel = gcStatsGauge.Labels("TotalPD")
            Dim lblTotES As GaugeLabel = gcStatsGauge.Labels("TotalES")
            Dim lblTotOK As GaugeLabel = gcStatsGauge.Labels("TotalOK")
            Dim lblTotPDDis As GaugeLabel = gcStatsGauge.Labels("TitleDisExp")
            Dim lblTotReq As GaugeLabel = gcStatsGauge.Labels("TitleRequests")

            Dim intPctPastDue, intPctWillExpire, intPCTOK As Integer

            gcStatsGauge.AutoLayout = False

            intPctPastDue = (intPointExpired / intMax) * 100
            intPctWillExpire = (intPointWillExpire / intMax) * 100
            intPCTOK = (intOK / intMax) * 100

            ' Set scale range
            scStatsTot.Maximum = 100
            scStatsTot.Minimum = 0
            scStatsPD.Minimum = 0
            scStatsPD.Maximum = 100
            scStatsWE.Minimum = 0
            scStatsWE.Maximum = 100
            scStatsOK.Minimum = 0
            scStatsOK.Maximum = 100
            scStatsPDDis.Minimum = 0
            scStatsPDDis.Maximum = 100

            scStatsPD.Interval = 10
            scStatsPD.IntervalOffset = 10
            scStatsWE.Interval = 10
            scStatsWE.IntervalOffset = 10
            scStatsOK.Interval = 10
            scStatsOK.IntervalOffset = 10
            scStatsTot.Interval = 10
            scStatsTot.IntervalOffset = 0
            scStatsPDDis.Interval = 10
            scStatsPDDis.IntervalOffset = 10

            gcStatsGauge.CircularGauges("PastDue").Pointers("Default").Value = intPctPastDue
            gcStatsGauge.CircularGauges("WillExpire").Pointers("Default").Value = intPctWillExpire
            gcStatsGauge.CircularGauges("OK").Pointers("Default").Value = intPCTOK

            gcStatsGauge.LinearGauges("Total").Pointers("Default").Value = intMax / 100
            gcStatsGauge.LinearGauges("ExpireAndDis").Pointers("Default").Value = intPDandDis / 100
            gcStatsGauge.LinearGauges("PendingRequests").Pointers("Default").Value = hstGet.PendOwnReq

            'TitleDisExp
            lblPCTPD.Text = intPctPastDue & "%"
            lblPCTES.Text = intPctWillExpire & "%"
            lblPCTOK.Text = intPCTOK & "%"
            lblTotPD.Text = intPointExpired
            lblTotES.Text = intPointWillExpire
            lblTotOK.Text = intOK

            lblTitleTotal.Text = "Total # of Accounts: " & intMax
            lblTotPDDis.Text = "# Disabled & Past Due: " & intPDandDis
            lblTotReq.Text = "Number of Requests: " & hstGet.PendOwnReq
        End Sub

        Sub LoadChart()

            Dim dbGet As New DataAccess
            Dim srGet As SqlClient.SqlDataReader
            Dim strSQL As String

            strSQL = "SELECT * FROM tbUMHistory order by UpdateOn Desc"
            srGet = dbGet.GetDataReaderByStringId(strSQL)

            Chart1.Series("Past Due in 45 Days").Points.DataBindXY(srGet, "UpdateOn", srGet, "ExpiringSoon")
            Chart1.Series("Past Due in 45 Days").Color = Color.Yellow
            ' close the reader and the connection
            srGet.Close()
            dbGet.CloseConnections()

            srGet = dbGet.GetDataReaderByStringId(strSQL)
            Chart1.Series("Past Due").Points.DataBindXY(srGet, "UpdateOn", srGet, "ExpiredAccounts")
            Chart1.Series("Past Due").Color = Color.Red
            ' close the reader and the connection
            srGet.Close()
            dbGet.CloseConnections()

            srGet = dbGet.GetDataReaderByStringId(strSQL)
            Chart1.Series("OK Accounts").Points.DataBindXY(srGet, "UpdateOn", srGet, "OKAccounts")
            Chart1.Series("OK Accounts").Color = Color.Green
            srGet.Close()
            dbGet.CloseConnections()

            srGet = dbGet.GetDataReaderByStringId(strSQL)
            Chart1.Series("Total Accounts").Points.DataBindXY(srGet, "UpdateOn", srGet, "TotalAccounts")
            Chart1.Series("Total Accounts").Color = Color.Blue
            srGet.Close()
            dbGet.CloseConnections()

            srGet = dbGet.GetDataReaderByStringId(strSQL)
            Chart1.Series("Past Due & Disabled").Points.DataBindXY(srGet, "UpdateOn", srGet, "ExpiredAndDisAccounts")
            Chart1.Series("Past Due & Disabled").Color = Color.HotPink
            srGet.Close()
            dbGet.CloseConnections()

            srGet = dbGet.GetDataReaderByStringId(strSQL)
            Chart1.Series("Disabled").Points.DataBindXY(srGet, "UpdateOn", srGet, "DisabledAccounts")
            Chart1.Series("Disabled").Color = Color.Gray
            srGet.Close()
            dbGet.CloseConnections()

        End Sub

    End Class

End Namespace


