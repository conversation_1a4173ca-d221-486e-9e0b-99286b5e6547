﻿<%@ Page Language="vb" MasterPageFile="~/MasterPage.master" Title="Reports" AutoEventWireup="false"
    Inherits="AccountManager.Reports" CodeFile="Reports.aspx.vb" %>
<%@ Register TagPrefix="uc1" TagName="Menu" Src="~/Library/UserControls/Menu.ascx" %>
<%@ Register TagPrefix="UM" TagName="Service" Src="~/Library/UserControls/ucServices.ascx" %>
<%@ Register TagPrefix="UM" TagName="PastDue" Src="~/Library/UserControls/ucPastDue.ascx" %>
<%@ Register TagPrefix="UM" TagName="Except" Src="~/Library/UserControls/ucException.ascx" %>
<%@ Register TagPrefix="UM" TagName="LocalGrps" Src="~/Library/UserControls/ucLocalGrps.ascx" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server"><table><tr><td><asp:Label ID="lblWelcome" runat="server">Account Manager Reports</asp:Label><br /><br /><asp:Label ID="lblMessage" runat="server"></asp:Label> <br /><table class="DisplayTables"><tr><td>Select Report:&nbsp; <asp:RadioButton ID="rdoException" runat="server" Text="Exceptions" GroupName="rdoActions"
                                AutoPostBack="False" Checked="True"></asp:RadioButton>&nbsp; <asp:RadioButton ID="rdoPastDue" runat="server" Text="Past Due" GroupName="rdoActions"
                                AutoPostBack="False"></asp:RadioButton>&nbsp; <asp:RadioButton ID="rdoServices" runat="server" Text="Services" GroupName="rdoActions"
                                AutoPostBack="False"></asp:RadioButton>&nbsp; <asp:RadioButton ID="rdoLocalGrps" runat="server" Text="Local Groups" GroupName="rdoActions"
                                AutoPostBack="False"></asp:RadioButton>&nbsp; </td></tr><tr><td><dxwdc:ASPxButton ID="cmdGo" TabIndex="0" runat="server" Text="Continue">
                                <LookAndFeel Kind="Office2003">
                                    <EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black"
                                        BackColor="White">
                                    </EditorStyle>
                                    <LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
                                    <ScrollBarButtonStyle BackColor="#84ABE3">
                                        <Filters>
                                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal"
                                                StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
                                        </Filters>
                                    </ScrollBarButtonStyle>
                                    <ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
                                        ScrollBarMargin="1"></ElementsSettings>
                                    <PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8">
                                    </PopupStyle>
                                    <ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
                                        UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
                                        <HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
                                            <Filters>
                                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202">
                                                </dxwdc:LookAndFeelStyleGradientFilter>
                                            </Filters>
                                        </HotTrackStyle>
                                        <PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
                                            <Filters>
                                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80">
                                                </dxwdc:LookAndFeelStyleGradientFilter>
                                            </Filters>
                                        </PressedStyle>
                                        <Filters>
                                            <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254">
                                            </dxwdc:LookAndFeelStyleGradientFilter>
                                        </Filters>
                                    </ButtonStyle>
                                </LookAndFeel>
                            </dxwdc:ASPxButton> </td></tr><asp:Panel ID="txtSearch" runat="server" Visible="False"></asp:Panel><tr><td>Account ID: <asp:TextBox ID="txtAccountID" runat="server"></asp:TextBox></td></tr><tr><td><dxwdc:ASPxButton ID="cmdGetData" TabIndex="0" runat="server" Text="Show Report">
                                    <LookAndFeel Kind="Office2003">
                                        <EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black"
                                            BackColor="White">
                                        </EditorStyle>
                                        <LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
                                        <ScrollBarButtonStyle BackColor="#84ABE3">
                                            <Filters>
                                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal"
                                                    StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
                                            </Filters>
                                        </ScrollBarButtonStyle>
                                        <ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
                                            ScrollBarMargin="1"></ElementsSettings>
                                        <PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8">
                                        </PopupStyle>
                                        <ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
                                            UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
                                            <HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
                                                <Filters>
                                                    <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202">
                                                    </dxwdc:LookAndFeelStyleGradientFilter>
                                                </Filters>
                                            </HotTrackStyle>
                                            <PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
                                                <Filters>
                                                    <dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80">
                                                    </dxwdc:LookAndFeelStyleGradientFilter>
                                                </Filters>
                                            </PressedStyle>
                                            <Filters>
                                                <dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254">
                                                </dxwdc:LookAndFeelStyleGradientFilter>
                                            </Filters>
                                        </ButtonStyle>
                                    </LookAndFeel>
                                </dxwdc:ASPxButton> </td></tr></table><asp:Panel ID="pException" runat="server">
                    <table>
                        <tr>
                            <td>
                                <UM:Except ID="ucException" runat="server"></UM:Except>
                            </td>
                        </tr>
                    </table>
                </asp:Panel> <asp:Panel ID="pPastDue" runat="server">
                    <table>
                        <tr>
                            <td>
                                <UM:PastDue ID="ucPast" runat="server"></UM:PastDue>
                            </td>
                        </tr>
                    </table>
                </asp:Panel> <asp:Panel ID="pServices" runat="server">
                    <table>
                        <tr>
                            <td>
                                <UM:Service ID="ucService" runat="server"></UM:Service>
                            </td>
                        </tr>
                    </table>
                </asp:Panel> <asp:Panel ID="pLocalGrps" runat="server">
                    <table>
                        <tr>
                            <td>
                                <UM:LocalGrps ID="Service1" runat="server"></UM:LocalGrps>
                            </td>
                        </tr>
                    </table>
                </asp:Panel> </td></tr></table>
</asp:Content>
