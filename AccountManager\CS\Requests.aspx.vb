Imports DevExpress.Web.ASPxGrid
Imports System.Data
Namespace AccountManager

    Partial Class Requests1
        Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
            'If Not Page.IsPostBack Then
            LoadRequests()
            'End If
        End Sub

        Sub LoadRequests()

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            strSQL = "SELECT * FROM tbUMRequests WHERE Status = 'New' AND DateSubmitted < '" & Now.AddDays(-14) & "'"
            srRead = dbGet.GetDataReaderByStringId(strSQL)

            If srRead.HasRows Then
                'grdRequests.DataKeyField = "AccountID"
                grdRequests.DataSource = srRead
                grdRequests.DataBind()
                grdRequests.ExpandAllRows()
            Else
                lblMessage.Text = "No requests found."
            End If

            If grdRequests.Items.Count > 12 Then
                grdRequests.PageIndexButtonCount = 12
            Else
                grdRequests.PageIndexButtonCount = grdRequests.Items.Count
            End If

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()

        End Sub

        Function UpdateOwners()

            Dim strUserId, strComments As String
            Dim intReqId As Integer
            Dim i As Integer
            Dim blnLoad As Boolean = True
            Dim reqUpd As New Requests
            Dim usrcur As New UserInfo

            Dim hlpIns As New Helper

            For i = 0 To (grdRequests.GetSelectedRowCount()) - 1
                Dim selectedRow As Row = grdRequests.GetSelectedRow(i)

                If selectedRow.Level = grdRequests.GetGroupCount() Then

                    strUserId = selectedRow.DataControllerRow("AccountID").ToString()
                    strComments = selectedRow.DataControllerRow("Comments").ToString()
                    strComments = strComments & " Owner Update by " & usrcur.GetUserID

                    Dim usrOwn As New UserInfo(selectedRow.DataControllerRow("NewOwner").ToString())

                    reqUpd.Comments = strComments
                    reqUpd.AccountID = strUserId
                    reqUpd.OldOwner = selectedRow.DataControllerRow("OldOwner").ToString()
                    reqUpd.NewOwner = usrOwn.GetUserID
                    reqUpd.NewOwnerDN = usrOwn.DisplayName
                    intReqId = selectedRow.DataControllerRow("RequestID").ToString()
                    reqUpd.UpdateOwner()
                    reqUpd.UpdateRequest(intReqId, [Global].GblReqStatusDone)

                    hlpIns.InsertLog(strUserId, [Global].GblActOwnAccept, Now, "Computer Security Change")
                End If
            Next i

        End Function

        Function DeleteRequest()

            Dim strUserId, strComments As String
            Dim intReqId As Integer
            Dim i As Integer
            Dim reqUpd As New Requests
            Dim usrcur As New UserInfo
            Dim hlpIns As New Helper

            For i = 0 To (grdRequests.GetSelectedRowCount()) - 1
                Dim selectedRow As Row = grdRequests.GetSelectedRow(i)

                If selectedRow.Level = grdRequests.GetGroupCount() Then

                    strUserId = selectedRow.DataControllerRow("AccountID").ToString()
                    strComments = selectedRow.DataControllerRow("Comments").ToString()
                    strComments = strComments & " Request Deleted by " & usrcur.GetUserID

                    reqUpd.Comments = strComments
                    reqUpd.AccountID = strUserId
                    intReqId = selectedRow.DataControllerRow("RequestID").ToString()
                    reqUpd.UpdateRequest(intReqId, [Global].GblReqStatusDelete)
                    hlpIns.InsertLog(strUserId, [Global].GblActOwnDec, Now, "Computer Security Change")
                End If
            Next i

        End Function

        Private Sub cmdUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdUpdate.Click
            UpdateOwners()
            LoadRequests()
        End Sub

        Private Sub cmdDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdDelete.Click
            DeleteRequest()
            LoadRequests()
        End Sub
    End Class

End Namespace
