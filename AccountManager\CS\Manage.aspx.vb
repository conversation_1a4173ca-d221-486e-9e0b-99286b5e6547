Imports DevExpress.Web.ASPxGrid


Namespace AccountManager

Partial Class Orphan
    Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

    End Sub

    Protected WithEvents rdoAddNew As System.Web.UI.WebControls.RadioButton


    Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
        'CODEGEN: This method call is required by the Web Form Designer
        'Do not modify it using the code editor.
        InitializeComponent()
     
    End Sub

#End Region

    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'If Not Page.IsPostBack Then
                ' LoadView()
        Dim strUserID as string
	strUserID = Left(System.Web.HttpContext.Current.Request.ServerVariables("LOGON_USER").Split("\")(1), 7).ToString
         strUserID = UCase(right(strUserID,1))
	
	if strUserID = "A" or strUserID = "I" or strUserID = "S"  then
		'Ok
	Else
       
    	'response.redirect("../Default.aspx?? ID=" & strUserID )
	response.redirect("../Default.aspx")

	End IF
        'End If
    End Sub

        'Sub LoadView()
        '    pOwners.Visible = rdoNewOwner.Checked
        '    pDelOwners.Visible = rdoDelete.Checked
        '    pAddNew.Visible = rdoNewAccounts.Checked
        '    pDelAcct.Visible = rdoDelAccount.Checked
        '    pUpdAcct.Visible = rdoUpdAccount.Checked
        '    pOrphan.Visible = rdoShowOrphan.Checked
        '    pWrkSt.Visible = rdoWrkSt.Checked
        'End Sub

        'Private Sub cmdGo_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdGo.Click
        '   LoadView
        'End Sub

       
    End Class

End Namespace
