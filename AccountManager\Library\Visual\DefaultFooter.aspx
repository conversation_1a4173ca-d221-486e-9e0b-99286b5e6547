
<SCRIPT LANGUAGE="VBSCRIPT" RUNAT="SERVER">

'----------------------------------------------------
' Appends the standard footer to the file. Must have
' FileFuncs.asp included to support GetModifiedDate
' function.
'----------------------------------------------------
 Sub PrintFooter(strFile)

	Dim docDate
        'docDate = GetModifiedDate(strFile)
        docDate = "11/20/2008"
	Response.write("<BR>")
	Response.Write("<b>Last Updated: </b>" & FormatDateTime(docDate, vbShortDate) & "&nbsp;&nbsp")
	Response.Write("<b>Expires: </b>" & FormatDateTime(dateadd("yyyy",1,docDate), vbShortDate) 	& "&nbsp;&nbsp")
	response.write("<b>Owner: </b>ESM&nbsp;")
	Response.Write("<b>Contact: </b><A HREF=""mailto:<EMAIL>"">ESM Team</A>&nbsp;&nbsp")
	
End Sub 

</script>