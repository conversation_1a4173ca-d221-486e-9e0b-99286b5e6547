﻿// JScript File

function select_deselectAll (chkVal, idVal) 
{ 
	var frm = document.forms[0];
	// Loop through all elements
	for (i=0; i<frm.length; i++) 
	{	
	var ctrid = frm.elements[i].name;

		// Look for our Header Template's Checkbox
		if (idVal.indexOf ('CheckAll') != -1) 
		{
			// Check if main checkbox is checked, then select or deselect datagrid checkboxes 
			if(chkVal == true) 
			{
			    if (ctrid.indexOf ('Checkbox1') != -1)
				{
				    if(frm.elements[i].disabled == false)
				    {
					    frm.elements[i].checked = true;
				    }
				}
			} 
			else 
			{
			    if (ctrid.indexOf ('Checkbox1') != -1)
			    {
				    frm.elements[i].checked = false;

				}
			}
			// Work here with the Item Template's multiple checkboxes
		} 
		else if (idVal.indexOf ('Checkbox1') != -1) 
		{
			// Check if any of the checkboxes are not checked, and then uncheck top select all checkbox
			if(frm.elements[i].checked == false) 
			{
			
				frm.elements[1].checked = false; //Uncheck main select all checkbox  
			}
		}
	}
}
		
var selectedRecords = new RecordCollection();
var allRecordsSelected = false;    
   
function SelectRecord(keyValue){
    selectedRecords.Add(keyValue);
}

function DeselectRecord(keyValue){
    selectedRecords.Remove(keyValue);
}

function GetRecordSelected(keyValue){
    if(selectedRecords.Find(keyValue))
        return true;
    return false;
}   

function ProcessRows(id){
    var checkBox = document.getElementById(id);               
    allRecordsSelected = !allRecordsSelected;
    checkBox.checked = allRecordsSelected;
    for(var i = 0; i < grdUsers.GetRowCount(); i++){
   
    
        SetRowState(grdUsers.GetRow(i).GetKeyValue(), checkBox.checked, false);
    }
} 

function RemoveItem(objListBox,key){
    //alert ("remove item " + key);
    var intIndex = GetItemIndex(objListBox,key);
    if (intIndex != -1)
        objListBox.Remove(key);                
}

function GetItemIndex(objListBox,key) {    
    return objListBox.IndexOfValue(key);
}

function SetRowState( key, selected, postToServer,sender){   
 //var listID = document.getElementById("lstChecked");  
    if(selected)      {            
        //lstChecked.Add(key, key, "");
        ctl00_ContentPlaceHolder1_lstChecked.Add(key, key, "");
        }
    else{
        RemoveItem( ctl00_ContentPlaceHolder1_lstChecked,key)           
        //sender.checked = false;            
     }
}

function OnCheckBoxChanged(sender, key){				
 var checkBox = sender; 
	SetRowState(key,  sender.checked, false,sender);				
}	
