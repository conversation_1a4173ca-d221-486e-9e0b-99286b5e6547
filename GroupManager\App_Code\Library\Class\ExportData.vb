Imports System.Text


Namespace GroupManager

Public Class ExportData

    Dim strFileName, strFileLoc As String

    Sub New()

        Dim usrCur As New UserInfo

        strFileName = usrCur.GetUserID
        strFileLoc = [Global].GblServerPath & strFileName

    End Sub

    Function CreateCSV(ByVal SQL As String, ByVal Conn As String) As String

        Dim srData As SqlClient.SqlDataReader
        Dim dbData As New GroupManager_DB

        '  Try
        strFileLoc = strFileLoc & ".csv"
        Dim fDel As System.IO.File

        If fDel.Exists(strFileLoc) Then
            fDel.Delete(strFileLoc)
        End If

        Dim swWrite As New System.IO.StreamWriter(strFileLoc, False)
        Dim intCount As Integer
        Dim strValue As String
        Dim strLine As String

        srData = dbData.GetDataReaderByStringId(SQL, Conn)
        intCount = 0

        Do While intCount < srData.FieldCount

            strLine = srData.GetName(intCount)
            If UCase(strLine) <> "ID" And UCase(strLine) <> "IDEXCEPT" Then
                swWrite.Write(Replace(strLine, ",", " ") & ",")
            End If

            intCount += 1
        Loop

        swWrite.WriteLine("")

        Do While srData.Read

            intCount = 0
            Do While intCount < srData.FieldCount

                If UCase(srData.GetName(intCount)) <> "ID" And UCase(strLine) <> "IDEXCEPT" Then

                    strValue = ""
                    If Not srData.IsDBNull(intCount) Then
                        strLine = srData.Item(intCount)
                        strValue = Replace(strLine, ",", " ")
                    End If

                    swWrite.Write(strValue & ",")
                End If
                intCount += 1
            Loop
            swWrite.WriteLine("")
        Loop
        swWrite.Close()
        swWrite = Nothing

        Return [Global].GblDownloadPath & strFileName & ".csv"

        'Catch e As Exception
        'Finally
        srData.Close()
        srData = Nothing
        dbData.CloseConnections()
        'End Try

    End Function


End Class

End Namespace
