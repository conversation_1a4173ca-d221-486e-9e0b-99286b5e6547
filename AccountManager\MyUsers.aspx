<%@ Reference Page="~/Views/ChangePwd.aspx" %>
<%@ Reference Page="~/Views/ManageWorkStations.aspx" %>
<%@ Reference Page="~/Views/DeleteAccount.aspx" %>
<%@ Reference Page="~/Views/UpdateBackup.aspx" %>
<%@ Reference Page="~/Views/ViewServices.aspx" %>
<%@ Reference Page="~/Views/UnlockAccount.aspx" %>
<%@ Page Trace="false" Language="vb" AutoEventWireup="false" Inherits="AccountManager.MyUsers"
    CodeFile="MyUsers.aspx.vb" MasterPageFile="~/MasterPage.master" Title="My Accounts" %>

<%@ Register Assembly="DevExpress.Web.ASPxGridView.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridLookup" TagPrefix="dx" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=*******, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">	
    <style>
        .gvRowPadding{
            padding: 2px 10px 2px 5px;
        }
        .gvHeaderRow{
            padding: 2px 10px 2px 5px;
            font-size :large;
        }
    </style>
        <script>
            function OnGetRowValues(values) {
alblAccountID.SetText(values[0]);
alblDescription.SetText(values[1]);
alblAppName.SetText(values[2]);
alblPurpose.SetText(values[3]);
//alblExpirationDate.SetText(values[4].toDateString());
}
function OnPageCheckedChanged(s, e) {
            _handle = false;
            if (s.GetChecked())
                grid.SelectAllRowsOnPage();
            else
                grid.UnselectAllRowsOnPage();
        }

function OnAllCheckedChanged(s, e) {
            if (s.GetChecked())
                grid.SelectRows();
            else
                grid.UnselectRows();
        }

    </script>
    <table>
        <tr>
            <td style="width: 968px">
                <asp:Label ID="lblWelcome" runat="server" CssClass="TableHeader"></asp:Label><br /><br />
                <asp:Label ID="lblMessage" runat="server" CssClass="LabelRed"></asp:Label>&nbsp;
                <asp:Panel ID="pUsers" runat="server">
                    <table class="DisplayTables" width="100%">
                        <tr>
                            <td valign="top" >
                                <table>
                                    
                                    <tr>
                                        <td>
                                            <table border="1" width="1">
                                                <tr>
                                                    <td>
                                                        <table>
                                                            <tr>
                                                                <td class="FunctionStep">
                                                                    Filtering Options</td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    Account ID</td>
                                                               <td>
                                                                    Owner/Delegate/Backup</td>
                                                                <td>
                                                                    Access</td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    <asp:TextBox ID="txtFAccount" runat="server"></asp:TextBox></td>
                                                            <td>
                                                                <asp:DropDownList ID="ddlFOwner" runat="server">
                                                                </asp:DropDownList></td>
                                                                <td>
                                                                    <asp:DropDownList ID="ddlFAccess" runat="server">
                                                                    </asp:DropDownList></td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    Description</td>
                                                                <td>
                                                                    <asp:Label ID="lblAppNameFilter" runat="server" ToolTip="User entered field describing the application name this account is used for">Application Name</asp:Label></td>
                                                                <td>
                                                                    <asp:Label ID="Label1" runat="server" ToolTip="User entered field describing the purpose of the account">Account Purpose</asp:Label></td>
                                                            </tr>
                                                            
                                                            <tr>
                                                                <td>
                                                                    <asp:TextBox ID="txtFilterDesc" runat="server"></asp:TextBox></td>
                                                                <td>
                                                                    <asp:TextBox ID="txtFilterAppName" runat="server"></asp:TextBox></td>
                                                                <td>
                                                                    <asp:TextBox ID="txtFilterPurpose" runat="server"></asp:TextBox></td>
                                                            </tr>
                                                            <tr>
                                                                <td colspan="3" style="height: 22px">
                                                                    <asp:CheckBox ID="chkPastDue" runat="server" Text="Show Only Past Due Accounts" /></td>
                                                            </tr>
                                                            <tr>
                                                                <td>
                                                                    <dx:ASPxButton ID="cmdApplyF" runat="server" Text="Apply Filter" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css" CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css">
                                                                    </dx:ASPxButton>
                                                                    
                                                                </td>
                                                                <td colspan="2">
                                                                    <dx:ASPxButton ID="cmdClearF" runat="server" Text="Clear Filter" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css" CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css">
                                                                    </dx:ASPxButton>
                                                                 </td>
                                                            </tr>
                                                           
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                     <tr>
                                        <td style="height: 10px">
                                            &nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="height: 19px"><i>Account ID:
                                            <dx:ASPxLabel ID="AccountID" runat="server" ClientInstanceName="alblAccountID" Font-Size="10pt">
                                            </dx:ASPxLabel>
                                            
                                            </i></td>
                                    </tr>
                                    <tr>
                                        <td style="height: 19px">Description:
                                            <dx:ASPxLabel ID="Description" runat="server" ClientInstanceName="alblDescription" Font-Size="10pt">
                                            </dx:ASPxLabel></td>
                                    </tr>
                                    <tr>
                                        <td style="height: 19px">
                                            <em>Application Name:
                                                <dx:ASPxLabel ID="AppName" runat="server" ClientInstanceName="alblAppName" Font-Size="10pt">
                                                </dx:ASPxLabel>
                                            </em>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="height: 19px">
                                            <em>Account Purpose:
                                                <dx:ASPxLabel ID="Purpose" runat="server" ClientInstanceName="alblPurpose" ClientVisible="True" Font-Size="10pt">
                                                </dx:ASPxLabel>
                                            </em>
                                        </td>
                                    </tr>
                                    <tr><td><asp:CheckBox ID="chkUpdAll" Visible="False" runat="server" Text="Select all Visible Accounts in Grid" Enabled="False" /></td></tr>
                                    <tr>
                                        <td style="height: 10px">
                                            &nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <asp:GridView ID="gvPasswordPolicy" runat="server" AutoGenerateColumns="False" GridLines="Horizontal">
                                                <Columns>
                                                    <asp:TemplateField HeaderText="Password Policy">
                                                        <HeaderStyle HorizontalAlign="Left" CssClass="gvHeaderRow"  />
                                                        <ItemTemplate>
                                                            <asp:Label ID="lblPwdType" runat="server"  Text='<%#Eval("AccountType") + ": " %>' ></asp:Label><asp:Label ID="lblPwdMessage" runat="server" Text='<%#Eval("PwdMessage")%>' Font-Bold="False"></asp:Label>
                                                            
                                                        </ItemTemplate>
                                                        <ItemStyle CssClass="gvRowPadding" />
                                                    </asp:TemplateField>
                                                    
                                                </Columns>
                                            </asp:GridView>
                                       </td>
                                    </tr>
                                </table>
                            </td>
                           
                            <td colspan="4" style="width: 325px;">
                                Select Action:&nbsp;<br />
                                <asp:RadioButton ID="rdoNewOwner" runat="server" Text="Change Ownership" GroupName="rdoActions"
                                    AutoPostBack="False" Checked="True"></asp:RadioButton>&nbsp;<br />
                                <asp:RadioButton ID="rdoUpdBackup" runat="server" Text="Manage Delegate/Backups" GroupName="rdoActions"
                                    AutoPostBack="False"></asp:RadioButton>&nbsp;<br />
                                <asp:RadioButton ID="rdoMngDetails" runat="server" Text="Manage Account Details"
                                    GroupName="rdoActions" AutoPostBack="False"></asp:RadioButton>&nbsp;<br />
                                <asp:RadioButton ID="rdoMngWorkStations" runat="server" Text="Manage Computers" GroupName="rdoActions"
                                    AutoPostBack="False"></asp:RadioButton>&nbsp;<font color="orange"></font><br />
                                <asp:RadioButton ID="rdoUnlock" runat="server" Text="Unlock Account" GroupName="rdoActions"
                                    AutoPostBack="False"></asp:RadioButton>&nbsp;<font color="orange"></font><br />
                                <asp:RadioButton ID="rdoChangePwd" runat="server" Text="Change Password" GroupName="rdoActions"
                                    AutoPostBack="False"></asp:RadioButton>&nbsp;<br />
                                <asp:RadioButton ID="rdoDelete" runat="server" Text="Delete Account" GroupName="rdoActions"
                                    AutoPostBack="False"></asp:RadioButton><br />
                                <asp:RadioButton ID="rdoPwdExempt" runat="server" Text="Manufacturing Password Process" GroupName="rdoActions"
                                    AutoPostBack="False" visible="False"></asp:RadioButton>
                                <asp:RadioButton ID="rdoViewAdmins" runat="server" Text="View Local Administrators"
                                    GroupName="rdoActions" AutoPostBack="False" visible="False"></asp:RadioButton>&nbsp;<br />
                                <asp:RadioButton ID="rdoViewServices" runat="server" Text="View Services" GroupName="rdoActions"
                                    AutoPostBack="False" visible="False"></asp:RadioButton>
                                
                                 
                                <table style="width:100%; padding-left :19px;">
                                    <tr>
                                        <td>
                                 <dx:ASPxButton ID="cmdContinue" runat="server" Text="Continue" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css" CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css">
                                            </dx:ASPxButton>
                                </td>
                                        </tr>

                                </table>   
                            </td>
				<asp:RadioButton ID="rdopwdException" runat="server" AutoPostBack="False" GroupName="rdoActions"
                                    Text="Password Not Required to Change" Width="235px" visible="False" />
                            <%--<caption>
                                <br />
                            </caption>--%>
                        </tr>
                    </table>
                    <table>
                        <tr>
                            <td style="width: 15px">
                                <!--- Beginning  of data grid -->
                                <dx:ASPxGridView ID="grdUsers" runat="server" AutoGenerateColumns="False" KeyFieldName="AccountID"
                                    Width="968px" CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css" CssPostfix="PlasticBlue" Font-Size="Small" ClientInstanceName="grid">
                                    <Columns>
                                        <dx:GridViewCommandColumn Caption="Select" ShowSelectCheckbox="True" VisibleIndex="0">
                                            
											<HeaderTemplate>
											<ClearFilterButton Visible="True">
                                            </ClearFilterButton>
                        <dx:ASPxCheckBox ID="cbAll" runat="server" ClientInstanceName="cbAll" ToolTip="Select all rows"
                            BackColor="White" >
                            <ClientSideEvents CheckedChanged="OnAllCheckedChanged" />
                        </dx:ASPxCheckBox>
                        <dx:ASPxCheckBox ID="cbPage" runat="server" ClientInstanceName="cbPage" ToolTip="Select all rows within the page"
                            >
                            <ClientSideEvents CheckedChanged="OnPageCheckedChanged" />
                        </dx:ASPxCheckBox>
                    </HeaderTemplate>
                                        </dx:GridViewCommandColumn>
                                        <dx:GridViewDataTextColumn Caption="AccountID" FieldName="AccountID" VisibleIndex="1">
                                            <EditFormSettings Visible="False" />
                                        </dx:GridViewDataTextColumn>
                                        <dx:GridViewDataTextColumn Caption="User ID" FieldName="UserName" VisibleIndex="2">
                                        </dx:GridViewDataTextColumn>
                                        <dx:GridViewDataTextColumn FieldName="Description" VisibleIndex="3">
                                        </dx:GridViewDataTextColumn>
                                        <dx:GridViewDataTextColumn Caption="Password Change Due Date" FieldName="ExpirationDate" VisibleIndex="4">
                                        </dx:GridViewDataTextColumn>
                                         <dx:GridViewDataTextColumn Caption="Access" FieldName="Access" VisibleIndex="5">
                                        </dx:GridViewDataTextColumn>
                                        <%--<dx:GridViewDataTextColumn Caption="Password Exempt" FieldName="IsPwdExempt" VisibleIndex="6">
                                        </dx:GridViewDataTextColumn>--%>
                                        <dx:GridViewDataTextColumn Caption="Password Type" FieldName="AccountType" VisibleIndex="7">
                                        </dx:GridViewDataTextColumn>
                                      </Columns>
                                    <Settings ShowGroupPanel="True" ShowFooter="True" ShowFilterRow="false"  />
                                    <Styles CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css" CssPostfix="PlasticBlue">
                                        <Header ImageSpacing="10px" SortingImageSpacing="10px">
                                        </Header>
                                        <AlternatingRow BackColor="silver">
                                        </AlternatingRow>
                                    </Styles>
                                    <SettingsPager ShowDefaultImages="False" >
                                        <AllButton Text="All">
                                        </AllButton>
                                        <NextPageButton Text="Next &gt;">
                                        </NextPageButton>
                                        <PrevPageButton Text="&lt; Prev">
                                        </PrevPageButton>
                                    </SettingsPager>
                                    <ImagesFilterControl>
                                        <LoadingPanel Url="~/App_Themes/PlasticBlue/Editors/Loading.gif">
                                        </LoadingPanel>
                                    </ImagesFilterControl>
                                    <Images SpriteCssFilePath="~/App_Themes/PlasticBlue/{0}/sprite.css">
                                        <LoadingPanelOnStatusBar Url="~/App_Themes/PlasticBlue/GridView/gvLoadingOnStatusBar.gif">
                                        </LoadingPanelOnStatusBar>
                                        <LoadingPanel Url="~/App_Themes/PlasticBlue/GridView/Loading.gif">
                                        </LoadingPanel>
                                    </Images>
                                    <StylesEditors>
                                        <CalendarHeader Spacing="11px">
                                        </CalendarHeader>
                                        <ProgressBar Height="25px">
                                        </ProgressBar>
                                    </StylesEditors>
                                    <SettingsBehavior AllowFocusedRow="True" AllowSelectByRowClick="True" />
                                    <ClientSideEvents FocusedRowChanged="function(s, e) {
	grid.GetRowValues(grid.GetFocusedRowIndex(), 'AccountID;Description;AppName;Purpose;ExpirationDate', OnGetRowValues);
}" />
                                    <SettingsLoadingPanel Text="Loading......" />
                                </dx:ASPxGridView>
                                <script type="text/jscript"> aspxClassesWindowOnLoad(null);</script>
                             <!--- end of data grid --></td>
                        </tr>
                    </table>
                  </asp:Panel>
                        
                <dx:ASPxListBox ID="lstChecked" runat="server" ClientVisible="False">
                </dx:ASPxListBox>
            </td>
        </tr>
    </table>
</asp:Content>
