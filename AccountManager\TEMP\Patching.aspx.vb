Namespace AccountManager

Partial Class Patching
    Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()
        Me.SqlDataAdapter1 = New System.Data.SqlClient.SqlDataAdapter
        Me.SqlConnection1 = New System.Data.SqlClient.SqlConnection
        Me.SqlSelectCommand1 = New System.Data.SqlClient.SqlCommand
        '
        'SqlDataAdapter1
        '
        Me.SqlDataAdapter1.SelectCommand = Me.SqlSelectCommand1
        Me.SqlDataAdapter1.TableMappings.AddRange(New System.Data.Common.DataTableMapping() {New System.Data.Common.DataTableMapping("Table", "vServersAllFields", New System.Data.Common.DataColumnMapping() {New System.Data.Common.DataColumnMapping("Expr1", "Expr1"), New System.Data.Common.DataColumnMapping("ImageVersion", "ImageVersion")})})
        '
        'SqlConnection1
        '
        Me.SqlConnection1.ConnectionString = "workstation id=USTCAW298;packet size=4096;user id=asm0nepublic;data source=ustcas" & _
        "16;persist security info=True;initial catalog=asm0nedb;password=asm0nepublic"
        '
        'SqlSelectCommand1
        '
        Me.SqlSelectCommand1.CommandText = "SELECT COUNT(ImageVersion) AS Expr1, ImageVersion FROM dbo.vServersAllFields WHER" & _
        "E (sDomain = 'kcus' OR sDomain = 'kcinet' OR sDomain = 'kcinet' OR sDomain = 'kc" & _
        "test') AND (ImageVersion <> 'ESX') AND (ImageVersion <> 'Cache Appliance') GROUP" & _
        " BY ImageVersion"
        Me.SqlSelectCommand1.Connection = Me.SqlConnection1

    End Sub
    Protected WithEvents SqlDataAdapter1 As System.Data.SqlClient.SqlDataAdapter
    Protected WithEvents SqlConnection1 As System.Data.SqlClient.SqlConnection
    Protected WithEvents SqlSelectCommand1 As System.Data.SqlClient.SqlCommand


    Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
        'CODEGEN: This method call is required by the Web Form Designer
        'Do not modify it using the code editor.
        InitializeComponent()
    End Sub

#End Region

    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        'Put user code to initialize the page here
    End Sub

End Class

End Namespace
