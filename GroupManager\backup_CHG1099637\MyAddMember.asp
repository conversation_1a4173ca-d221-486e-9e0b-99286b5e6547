﻿<%@ Language=VBScript %>
<% Response.Expires=0 %>
<!--#include file="library\include\libFunctions.inc"-->
<%
	' Check if requested group is a local or global group.
	' If local then can display group radio button
	Dim sLocation, bIsLocalGroup
	sLocation = ParseLocation(CStr(Request.QueryString("GP")))
	'If sLocation <> "KCUS" Then
	'	bIsLocalGroup = "True"
	'Else
	'	bIsLocalGroup = "False"
	'End If
	
	bIsLocalGroup = "True"
%>
<HTML>
	<HEAD>
		<title>Add Member</title>
		<META NAME="GENERATOR" Content="Microsoft Visual Studio 6.0">
		<META HTTP-EQUIV="Expires" CONTENT="0">
		<meta http-equiv="content-type" content="text/html;charset=utf-8;" />
		<link rel="stylesheet" type="text/css" href="styles.css">
			<SCRIPT ID="clientEventHandlersJS" LANGUAGE="javascript">
<!--
	function SetClass() {
		if (window.event.srcElement.value == "User"){
			frmMemberInfo.imagebuttonSearch.style.display = "inline";
			lblMember.innerHTML = "User Name:";
			//lblExample.innerHTML = "(i.e. bxxxxx, wxxxxx)";
			lblExample.innerHTML = "(i.e. bxxxxx)";
		}
		else if (window.event.srcElement.value == "Group"){
			frmMemberInfo.imagebuttonSearch.style.display = "none";
			lblMember.innerHTML = "Group Name:";
		    //lblExample.innerHTML = "(i.e. ZGL-xxx-xxx)";
			lblExample.innerHTML = "";
		}
		else if (window.event.srcElement.value == "Computer"){
			frmMemberInfo.imagebuttonSearch.style.display = "none";
			lblMember.innerHTML = "Machine Name:";
			lblExample.innerHTML = "(i.e. B01234-2K1)";
		}
	  	else if (window.event.srcElement.value == "InetGroup"){
			frmMemberInfo.imagebuttonSearch.style.display = "none";
			lblMember.innerHTML = "Group Name:";
	  	    //lblExample.innerHTML = "(i.e. ZGL-xxx-xxx)";
			lblExample.innerHTML = "";
		}
	   else if (window.event.srcElement.value == "InetUser"){
			frmMemberInfo.imagebuttonSearch.style.display = "none";
			lblMember.innerHTML = "User Name:";
			//lblExample.innerHTML = "(i.e. bxxxxx, wxxxxx)";
			lblExample.innerHTML = "(i.e. bxxxxx)";
		}
	}
	
	function btnAddMember_onclick() {
		if (frmMemberInfo.txtMember.value == "") {
			alert("Please enter in the members name");
			frmMemberInfo.txtMember.focus();
			return;
		}
		frmMemberInfo.submit();
	}

	function window_onload() {
		// set display of radio button if local group
		var bIsLocal
		bIsLocal = "<%=bIsLocalGroup%>"
		if (bIsLocal == "True")
			rdoGroup.style.display = '';
		else
			rdoGroup.style.display = 'none';
			
		frmMemberInfo.txtMember.focus();
	}
	
	function openWindow(strUserIDControl)
				{	
 					var strWinStyles = 'width=425,Height=400,Top=250,Left=200,menubar=no,resizable=no,scrollbars=no,toolbar=no,statusbar=no';
 					var strInputUserID = window.document.getElementById(strUserIDControl).value;
					var goURL = "/GroupManager/controls/UserPopUp.aspx?UserID=" + strInputUserID + "&UserControlID=" + strUserIDControl;
					user_window=window.open(goURL, 'user_window', strWinStyles);
					user_window.focus();
					return false;
				}

//-->
			</SCRIPT>
	</HEAD>
	<BODY LANGUAGE="javascript" onload="return window_onload()">
		<script language="JavaScript" src="/GroupManager/Library/scripts/NoRightClick.js"></script>
		<!-- Start: Page Header -->
		<!-- #INCLUDE File="sideMenu.asp" -->
		<h4>
			Manage the membership of the Windows NT security groups that you are an owner 
			or authorizer of.
		</h4>
		<hr>
		<!-- End: Page Header -->
		<!-- Start: Function Step -->
		<!-- Task: Gather member information for add -->
		<%
	Dim objGroups, rsGroups
	Dim fldADSPath, fldName, fldDescription, fldAccess
	Set objGroups = Server.CreateObject("GroupManager_DB.Group")
	Set rsGroups = objGroups.GetMgrAccess(CStr(Request.QueryString("GP")),CStr(Session("ManagerID")))
	
	Set objGroups = Nothing
	
	If rsGroups.EOF = True Then
		Response.Write ("<SPAN class=FunctionStep>** There are no groups assigned to you. **<BR><BR>If you believe that this is in error, please contact Computer Security at _Admin, NT-Security.</SPAN>")
	Else
%>
		<span class="FunctionStep">Enter the member's information below:</span><br>
		<br>
		<table border="0" cellspacing="0" cellpadding="1">
			<tr>
				<td><img height="1" src="library/images/1ptrans.gif" width="15"></td>
				<td NOWRAP class="GroupInfoHeader">Name</td>
				<td><img height="1" src="library/images/1ptrans.gif" width="15"></td>
				<td NOWRAP class="GroupInfoHeader">Description</td>
				<td><img height="1" src="library/images/1ptrans.gif" width="15"></td>
				<td NOWRAP class="GroupInfoHeader" align="middle">Location</td>
				<td><img height="1" src="library/images/1ptrans.gif" width="15"></td>
				<td NOWRAP class="GroupInfoHeader" align="middle">Access</td>
			</tr>
			<%
		rsGroups.MoveFirst
		Set fldADSPath = rsGroups.fields(0)
		Set fldName = rsGroups.fields(1)
		Set fldDescription = rsGroups.fields(2)
		Set fldAccess = rsGroups.fields(3)

		Do Until rsGroups.EOF
%>
			<tr>
				<td align="right" valign="top"><img border="0" src="library/images/icoTools.gif" width="16"></td>
				<td class="GroupInfo" valign="top"><%=fldName.value%></td>
				<td>&nbsp;</td>
				<td class="GroupInfo" valign="top"><%=fldDescription.value%></td>
				<td>&nbsp;</td>
				<td class="GroupInfo" align="middle" valign="top"><%=ParseLocation(fldADSPath.value)%></td>
				<td>&nbsp;</td>
				<td class="GroupInfo" align="middle" valign="top"><%=fldAccess.value%></td>
			</tr>
			<%
		rsGroups.MoveNext
		Loop
		rsGroups.Close
		Set rsGroups = Nothing
	End If
%>
		</table>
		<TABLE BORDER="0" CELLSPACING="0" CELLPADDING="1">
			<FORM name="frmMemberInfo" action="mycheckmember.asp" method="post">
				<TR>
					<td><img height="1" src="library/images/1ptrans.gif" width="10"></td>
					<TD colspan="3" style="BORDER-BOTTOM: #6699cc 1px solid">
						<span class="FunctionStep">Member Information:</span>
					</TD>
				</TR>
				<TR>
					<TD><img height="8" src="library/images/1ptrans.gif" width="1"></TD>
					<TD></TD>
					<TD></TD>
					<TD></TD>
				</TR>
				<TR>
					<td><img height="1" src="library/images/1ptrans.gif" width="10"></td>
					<TD class="small" align="right" valign="top">Member Type:&nbsp;</TD>
					<TD class="small" valign="top">
						<SPAN id="rdoUser" STYLE="display:'';"><INPUT type="radio" name="rdoClass" id="User" value="User" CHECKED onclick="SetClass()">User</SPAN><BR>
						<SPAN id="rdoMach" STYLE="display:'';"><INPUT type="radio" name="rdoClass" id="Comp" value="Computer" onclick="SetClass()">Machine</SPAN><BR>
						<SPAN id="rdoGroup" STYLE="display:'';">
							<INPUT type="radio" name="rdoClass" id="Group" value="Group" onclick="SetClass()">Group</TD>
						</SPAN>
						
					<TD></TD>
				</TR>
				<%
				if sLocation <> "KCUS"	THEN
				%>		
				<TR>
				   <td><img height="1" src="library/images/1ptrans.gif" width="10"></td>
					<TD class="small" align="right" valign="top">KCINET Member Type:&nbsp;</TD>
						<TD class="small" valign="top">
						<SPAN id="rdoInetUser" STYLE="display:'';"><INPUT type="radio" name="rdoClass" id="InetUser" value="InetUser" onclick="SetClass()">KCINET User</SPAN><BR>
						<SPAN id="rdoInetGroup" STYLE="display:'';">
							<INPUT type="radio" name="rdoClass" id="InetGroup" value="InetGroup" onclick="SetClass()">KCINET Group</TD>	
						</SPAN>
					<TD></TD>
				</TR>
				<%
				END IF
				%>
				
				
				<TR>
					<td><img height="1" src="library/images/1ptrans.gif" width="15"></td>
					<TD class="small" align="right"><SPAN id="lblMember">User Name:</SPAN>&nbsp;</TD>
					<TD><INPUT type="text" id="txtMember" name="txtMember"> <input type="button" id="imagebuttonSearch" name="imagebuttonSearch" value="Find" src="/GroupManager/Library/Images/icoSearch.gif"
							onclick="javascript:openWindow('txtMember')"> &nbsp;&nbsp;<SPAN class="small" id="lblExample">(i.e. bxxxxx)<SPAN>
					</TD>
					<TD><INPUT type="hidden" name=GroupADSPath value="<%=CStr(Request.QueryString("GP"))%>"></TD>
<TD><INPUT type="hidden" name=Email value="<%=CStr(Request.QueryString("GP1"))%>"></TD>
				</TR>
				<TR>
					<TD><img height="1" src="library/images/1ptrans.gif" width="15"></TD>
					<TD></TD>
					<TD align="left"><INPUT type="button" value="+ Add Member" id="btnAddMember" name="btnAddMember" style="FONT-SIZE:8pt;HEIGHT:25px;WIDTH:100px"
							LANGUAGE="javascript" onclick="return btnAddMember_onclick()"></TD>
					<TD></TD>
				</TR>
				<TR>
					<TD><img height="8" src="library/images/1ptrans.gif" width="1"></TD>
					<TD></TD>
					<TD></TD>
					<TD></TD>
				</TR>
			</FORM>
		</TABLE>
		<!--#include File="Library/Visual/Footer/defaultfooter_new.htm"-->
		</td> </tr> </table>
	</BODY>
</HTML>
