'Title: Active Directory Class, UserInfo
'Created by: <PERSON> on 
'Purpose:  Retrieve User Information via their BID
'Assumptions:  Everyone logged on has a BID
'Effects:  You are able to retrive different information about the current
'           user.
'Inputs:  Request.ServerVariables("LOGON_USER")
'Returns:  UserID, Name, location, etc.
Imports System.DirectoryServices
Imports AccountManager.Global
Imports System.data

Namespace AccountManager

    Public Class UserInfo
        'This page allows other web pages to pull the user information from KC Active
        'directory based on the user id that is logged on.

        Dim arUsers As New ArrayList
        Private dtbAccountsForUser As DataTable
        Private dtbOwnersForAccounts As DataTable
        Private srOwnersForAccounts As SqlClient.SqlDataReader
        Dim strUserName, objNS
        Dim objUser
        Dim strUserEmail, strUserLocation, strUserPhone, strUserOffice, strUserEmployeeType As String
        Dim strClass, sPath, strPwdCantChange, strPwdNeverExpires, strDisabled, strDesc, strWorkstations As String
        Dim blnUserFound, blnLocked, blnUserMustChangePasswordOnNextLogon As Boolean
        Dim dtPwdLastSet As DateTime

        Dim sUser = System.Web.HttpContext.Current.Request.ServerVariables("LOGON_USER")
        Dim strFName, strLName As String

#Region "Variables"

        Property OwnersForAccountsData() As DataTable
            Get
                Return dtbOwnersForAccounts
            End Get
            Set(ByVal value As DataTable)
                dtbOwnersForAccounts = value
            End Set
        End Property
        Property AccountsArray() As ArrayList
            Get
                Return arUsers
            End Get
            Set(ByVal value As ArrayList)
                arUsers = value
            End Set
        End Property
        Property AccountsOwnedByUser() As DataTable
            Get
                Return dtbAccountsForUser
            End Get
            Set(ByVal value As DataTable)
                dtbAccountsForUser = value
            End Set
        End Property
        ReadOnly Property WorkStations()
            Get
                Return strWorkstations
            End Get
        End Property
        ReadOnly Property LastName()
            Get
                Return strLName
            End Get
        End Property
        ReadOnly Property FirstName()
            Get
                Return strFName
            End Get
        End Property
       
        ReadOnly Property Disabled()
            Get
                Return strDisabled
            End Get
        End Property
        ReadOnly Property PwdLastSet()
            Get
                Return dtPwdLastSet
            End Get
        End Property
        ReadOnly Property ObjectClass()
            Get
                Return strClass
            End Get
        End Property
        ReadOnly Property LdapPath()
            Get
                Return sPath
            End Get
        End Property
        ReadOnly Property UserFound()
            Get
                Return blnUserFound
            End Get
        End Property
        ReadOnly Property IsUserMustChangePasswordOnNextLogon() As Boolean
            Get
                Return blnUserMustChangePasswordOnNextLogon
            End Get
        End Property
        ReadOnly Property UserEmployeeType()
            Get
                Return strUserEmployeeType
            End Get
        End Property
        ReadOnly Property DisplayName()
            Get
                Return strUserName
            End Get
        End Property
        ReadOnly Property Description()
            Get
                Return strDesc
            End Get
        End Property
        ReadOnly Property PwdNeverExpires()
            Get
                Return strPwdNeverExpires
            End Get
        End Property

        ReadOnly Property IsLocked()
            Get
                Return blnLocked
            End Get
        End Property


        Function GetUserID()
            GetUserID = sUser
        End Function

        Function GetUserEmail()
            GetUserEmail = strUserEmail
        End Function

        Function GetUserLocation()
            GetUserLocation = strUserLocation
        End Function

        Function GetUserOffice()
            GetUserOffice = strUserOffice
        End Function

        Function GetUserPhone()
            GetUserPhone = strUserPhone
        End Function

        ReadOnly Property UserNameandID()
            Get
                Return Trim(strFName) & " " & Trim(strLName) & " (" & sUser & ")"
            End Get
        End Property

#End Region

        Public Shared Sub WriteTempLog(ByVal strLine As String)

            Try
                Dim foo As New System.IO.StreamWriter("D:\temp\AM.Log", True)
                foo.WriteLine(strLine)
                foo.Close()
            Catch ex As Exception

            End Try

        End Sub

        Function LoadOwnerDropDown(ByVal alList As ArrayList) As DropDownList

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader
            Dim intIndex As Integer = 0
            Dim ddlNew As New DropDownList

            strSQL = "SELECT Distinct UserName,UserNameDN FROM vUsr WHERE ("

            If alList.Count < 1 Then
                'pOwners.Visible = False
                'grdOwners.Visible = False
            Else
                Do While intIndex < alList.Count
                    strSQL = strSQL & " AccountID = '" & alList.Item(intIndex) & "' OR"
                    intIndex = intIndex + 1
                Loop
            End If

            strSQL = strSQL.Remove(strSQL.Length - 2, 2)
            strSQL = strSQL & ") Order By UserNameDN asc "
            srRead = dbGet.GetDataReaderByStringId(strSQL)

            Do While srRead.Read

                Dim intItem As Integer
                Dim strFUserID, strFUser As String

                strFUserID = srRead.Item("UserName")
                strFUser = srRead.Item("UserNameDN")

                intItem = ddlNew.Items.IndexOf(ddlNew.Items.FindByValue(Helper.IsValueNull(strFUserID)))
                If intItem < 0 Then
                    Dim liNew As New ListItem
                    liNew.Text = strFUser & " (" & strFUserID & ")"
                    liNew.Value = strFUserID
                    ddlNew.Items.Add(liNew)
                End If

            Loop

            srRead.Close()
            dbGet.CloseConnections()
            dbGet = Nothing
            srRead = Nothing

            Return ddlNew

        End Function

        Public Shared Function GetSessionUserList(ByVal blnForce As Boolean) As UserInfo

            If Not System.Web.HttpContext.Current.Session(GblAccountMANAGER_SESSION) Is Nothing And Not blnForce Then
                WriteTempLog("Using Existing Session")
                GetSessionUserList = CType(System.Web.HttpContext.Current.Session(GblAccountMANAGER_SESSION), UserInfo)

            Else
                WriteTempLog("New Session")

                Dim usrCur As New UserInfo

                usrCur.GetAccountsForUser(usrCur.SQLGetAccountsForUser("", ""))
                'usrCur.GetOwnersForAccounts(usrCur.SQLGetAllOwnersForAccounts)

                System.Web.HttpContext.Current.Session(GblAccountMANAGER_SESSION) = usrCur
                GetSessionUserList = usrCur

            End If

            Return GetSessionUserList

        End Function

        Sub SetSessionUserList(ByVal usrNew As UserInfo)

            System.Web.HttpContext.Current.Session(GblAccountMANAGER_SESSION) = usrNew

        End Sub

        Function SQLGetAccountsForUser(ByVal strWhere As String, ByVal UserFilter As String) As String

            If UserFilter <> "" Then
                SQLGetAccountsForUser = "SELECT * FROM vUsr WHERE (AccountID IN " & _
                                                  "(SELECT AccountID " & _
                                                  "FROM vUsr " & _
                                                  "WHERE UserName = '" & Me.GetUserID & "')) AND (UserName = '" & UserFilter & "') " & strWhere & "  Order by AccountID"
            Else
                SQLGetAccountsForUser = "SELECT * FROM vUsr WHERE (UserName = '" & Me.GetUserID & "') " & strWhere & "  Order by AccountID"
            End If

        End Function

        Function SQLGetAllOwnersForAccounts() As String

            SQLGetAllOwnersForAccounts = "SELECT * FROM vUsr WHERE (AccountID IN " & _
                                                            "(SELECT AccountID " & _
                                                            "FROM vUsr " & _
                                                            "WHERE UserName = '" & Me.GetUserID & "')) Order by AccountID"

        End Function

        Function GetAccountsForUser(ByVal SQL As String) As ArrayList

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim i As Integer = 0

            strSQL = SQL

            dtbAccountsForUser = dbGet.GetDataTable(strSQL)

            While i < dtbAccountsForUser.Rows.Count
                arUsers.Add(dtbAccountsForUser.Rows(i)("AccountID"))
                i = i + 1
            End While

            dbGet = Nothing
            Return arUsers

        End Function

        Function GetOwnersForAccounts(ByVal SQL As String) As DataTable

            Dim dbGet As New DataAccess
            Dim i As Integer = 0

            dtbOwnersForAccounts = dbGet.GetDataTable(SQL)
            dbGet = Nothing

            Return dtbOwnersForAccounts

        End Function

        Function DelegateCount(ByVal AccountID As String, ByVal Access As String) As Integer

            If dtbOwnersForAccounts Is Nothing Then
                GetOwnersForAccounts(SQLGetAllOwnersForAccounts())
            End If

            Dim sbFilter As New StringBuilder
            Dim strAnd As String = ""
            Dim dvFilteredView As DataView = dtbOwnersForAccounts.DefaultView

            sbFilter.Append(" (AccountID = '" & AccountID & "' AND Access = '" & Access & "') ")
            dvFilteredView.RowFilter = sbFilter.ToString()

            Return dvFilteredView.Count

        End Function

        'Title: New Constructor for UserInfo
        'Created by: Shane Z Smith on 5/13/02
        'Purpose:  To create a new instance of UserInfo with the BID of the current user logged in
        'Assumptions:  None
        'Effects:  Creates user info (name, id, email) based on the current user logged in
        'Inputs:  None
        'Returns:  None
        Public Sub New()

            Try

                blnUserFound = True
                Dim strLdap As String

                'strLdap = "LDAP://OU=Accounts,DC=kcc,DC=com"
                strLdap = GblAccountsLDAP

                Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLdap)
                Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
                Dim objSearchRes As System.DirectoryServices.SearchResult

                Call GetID()

                If AccountManager.Global.GblTestMode = "TRUE" Then
                    objSearcher.Filter = ("(&(objectclass=user)(sAMAccountName=" & sUser & "))")
                Else
                    objSearcher.Filter = ("(&(objectclass=user)(sAMAccountName=" & sUser & "))")
                End If

                objSearchRes = objSearcher.FindOne

                If objSearchRes Is Nothing Then
                    'Helper.WriteErrorLog("Problem finding user: " & newID, "C:\temp\Security.log")
                    blnUserFound = False
                Else
                    blnUserFound = True

                    sPath = objSearchRes.GetDirectoryEntry.Path
                    sUser = objSearchRes.GetDirectoryEntry.Properties.Item("cn").Value
                    strUserName = Replace(objSearchRes.GetDirectoryEntry.Properties.Item("DisplayName").Value, "'", "`")
                    strUserEmail = Chr(34) & strUserName & Chr(34) & " " & Replace(objSearchRes.GetDirectoryEntry.Properties.Item("Mail").Value, "'", "`")
                    strDesc = Replace(objSearchRes.GetDirectoryEntry.Properties.Item("Description").Value, "'", "`")
                    strFName = Replace(objSearchRes.GetDirectoryEntry.Properties.Item("GivenName").Value, "'", "`")
                    strLName = Replace(objSearchRes.GetDirectoryEntry.Properties.Item("SN").Value, "'", "`")

                    dtPwdLastSet = GetPasswordExpirationDate(sUser, objSearchRes)

                    Dim dsUser As System.DirectoryServices.DirectoryEntry = New System.DirectoryServices.DirectoryEntry(sPath)
                    With dsUser.Properties
                        strDisabled = dsUser.NativeObject.accountdisabled.ToString
                    End With

                End If

                If strUserEmail = "" Then
                    strUserEmail = sUser & "@USTCAX00.kcc.com" & ";"
                End If

            Catch e As Exception
                blnUserFound = False
                sUser = "Error with user id " & sUser & " UserInfo.vb: " & e.Message
            End Try

        End Sub

        'Parses the KCUS ID, by removeing the "KCUS\" and returning only the ID
        'This will not work for ID's larger then 6 characters (B12345A).  This is
        'primarly used to retrieve a user's general information (Email, Name, Phone, etc.).
        Private Sub GetID()

            Dim iLen = Len(sUser)
            Dim iFind = InStr(sUser, "\")

            If iFind > 0 Then
                sUser = Right(sUser, (iLen - iFind))
            End If

           ' If sUser.Length > 6 Then
               ' sUser = sUser.Remove(6, 1)
           ' End If

		If sUser.Length > 7 Then
                sUser = sUser.Remove(7, 1)
           End If

        End Sub

       

        'Title: New Constructor for UserInfo
        'Created by: Shane Z Smith on 5/13/02
        'Purpose:  To create a new instance of UserInfo with a specified BID
        'Assumptions:  None
        'Effects:  Creates user info (name, id, email) based on the ID it was sent
        'Inputs:  BID
        'Returns:  None
        Public Sub New(ByVal newID As String)

            Dim strCN As String
            Dim strLdap, key As String
            Dim values As Object

            'strLdap = "LDAP://DC=kcc,DC=com"
            strLdap = GblLDAP

            Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLdap)
            Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
            Dim objSearchRes As System.DirectoryServices.SearchResult

            'objSearcher.Filter = ("(sAMAccountName=" & newID & ")")
            objSearcher.Filter = ("(&(objectclass=user)(|(cn=" & newID & ")(sAMAccountName=" & newID & ")))")

            objSearchRes = objSearcher.FindOne

            If objSearchRes Is Nothing Then
                'Helper.WriteErrorLog("Problem finding user: " & newID, "C:\temp\Security.log")
                blnUserFound = False
            Else
                blnUserFound = True
                'path is the Active Directory path to that object
                'sPath = objSearchRes.GetDirectoryEntry.Path
                'CN is usually the BID of the person (unique identifier in AD)

                Dim propcoll As ResultPropertyCollection
                propcoll = objSearchRes.Properties

                For Each key In propcoll.PropertyNames
                    key = LCase(key)
                    If key.ToString.IndexOf("objectclass") > -1 Then

                        For Each values In propcoll(key)
                            values = LCase(values)
                            If values.ToString.IndexOf("group") > -1 Then
                                strClass = "GROUP"
                            Else
                                strClass = "USER"
                            End If
                        Next
                        Exit For
                    End If
                Next

                sPath = objSearchRes.GetDirectoryEntry.Path
                sUser = objSearchRes.GetDirectoryEntry.Properties.Item("cn").Value
                strUserName = Replace(objSearchRes.GetDirectoryEntry.Properties.Item("DisplayName").Value, "'", "`")
                strUserEmail = Chr(34) & strUserName & Chr(34) & " " & Replace(objSearchRes.GetDirectoryEntry.Properties.Item("Mail").Value, "'", "`")
                strDesc = Replace(objSearchRes.GetDirectoryEntry.Properties.Item("Description").Value, "'", "`")
                strFName = Replace(objSearchRes.GetDirectoryEntry.Properties.Item("GivenName").Value, "'", "`")
                strLName = Replace(objSearchRes.GetDirectoryEntry.Properties.Item("SN").Value, "'", "`")
                strWorkstations = objSearchRes.GetDirectoryEntry.Properties.Item("userworkstations").Value
                Me.blnLocked = Me.IsAccountLocked(sPath)

               
                'If CType(objSearchRes.GetDirectoryEntry.Properties("lockoutTime").Value, Integer) > 0 Then
                '    ' They're Locked Out
                '    Me.blnLocked = True
                'Else
                '    Me.blnLocked = False
                'End If

                blnUserMustChangePasswordOnNextLogon = False
                dtPwdLastSet = GetPasswordExpirationDate(sUser, objSearchRes)

                Dim dsUser As System.DirectoryServices.DirectoryEntry = New System.DirectoryServices.DirectoryEntry(sPath)
                With dsUser.Properties
                    strDisabled = dsUser.NativeObject.accountdisabled.ToString
                End With

                If strUserEmail = "" Then
                    strUserEmail = sUser & "@USTCAX00.kcc.com" & ";"
                End If
            End If

            objDirEnt = Nothing
            objSearcher = Nothing
            objSearchRes = Nothing

        End Sub

        Public Sub New(ByVal newID As String, ByVal IsOwnerDelegateBackup As Boolean)
            Dim strLdap As String

            'strLdap = "LDAP://DC=kcc,DC=com"
            strLdap = GblLDAP

            Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLdap)
            Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
            Dim objSearchRes As System.DirectoryServices.SearchResult

            objSearcher.Filter = ("(&(objectclass=user)(sAMAccountName=" & newID & "))")

            objSearchRes = objSearcher.FindOne

            If objSearchRes Is Nothing Then
                blnUserFound = False
                strUserEmployeeType = ""
            Else
                blnUserFound = True
                Dim propcoll As ResultPropertyCollection
                propcoll = objSearchRes.Properties

                sPath = objSearchRes.GetDirectoryEntry.Path
                sUser = objSearchRes.GetDirectoryEntry.Properties.Item("cn").Value
                strUserName = Replace(objSearchRes.GetDirectoryEntry.Properties.Item("DisplayName").Value, "'", "`")
                strUserEmail = Chr(34) & strUserName & Chr(34) & " " & Replace(objSearchRes.GetDirectoryEntry.Properties.Item("Mail").Value, "'", "`")
                strUserEmployeeType = objSearchRes.GetDirectoryEntry.Properties.Item("EmployeeType").Value
                strFName = Replace(objSearchRes.GetDirectoryEntry.Properties.Item("GivenName").Value, "'", "`")
                strLName = Replace(objSearchRes.GetDirectoryEntry.Properties.Item("SN").Value, "'", "`")

                If strUserEmail = "" Then
                    strUserEmail = sUser & "@kcc.com;"
                End If
            End If

            objDirEnt = Nothing
            objSearcher = Nothing
            objSearchRes = Nothing

        End Sub
        Function SoftDeleteAndDisableAccount(ByVal strLDAP As String, ByVal strNewDisplayName As String) As Boolean
            Dim hlpClean As New Helper
            strNewDisplayName = hlpClean.dbCleanUpString(strNewDisplayName)
            Dim IsSoftDeleted As Boolean = True
            Try
                Dim dirEntryResults As New DirectoryEntry(strLDAP)
                dirEntryResults.InvokeSet("displayName", strNewDisplayName)
                dirEntryResults.InvokeSet("AccountExpirationDate", Now)
                dirEntryResults.InvokeSet("AccountDisabled", True)

                dirEntryResults.CommitChanges()
                dirEntryResults.Close()
                IsSoftDeleted = True
            Catch ex As Exception
                IsSoftDeleted = True
            End Try
           

            Return IsSoftDeleted
        End Function
        'Title: GetPasswordExpirationDate
        'Created by: Shane Z Smith on 10/1/2002
        'Purpose:  Retrive the password expiration date of the user
        'Assumptions:  We assume that the GPO setting to make a user reset their password
        '               every two months is in affect. 
        'Effects:  returns the date when the users password will expire
        'Inputs: User ldap path
        'Returns: date or the value "not set" if the property can't be found
        Private Function GetPasswordExpirationDate(ByVal strAcctID As String, ByVal objSearchRes As System.DirectoryServices.SearchResult) As Date

            'Dim objValue, objUser As Object
            'Dim strPasswordExpirationDate As String
            Dim dtChanged As Date
            Dim fileTime As Long = CType(objSearchRes.Properties("pwdLastSet")(0), Long)
            If fileTime = 0 Then
                blnUserMustChangePasswordOnNextLogon = True
            End If
            dtChanged = DateTime.FromFileTime(fileTime)

            'If dtChanged < "1/1/1600" Then
            '    objUser = GetObject(GblADSPath & strAcctID)
            '    dtChanged = objUser.PasswordExpirationDate
            'End If

            GetPasswordExpirationDate = dtChanged.ToShortDateString

        End Function

        Public Function GetPasswordLastSetByLDAP(ByVal strLDAP As String, Optional ByVal DifferentDC As String = "") As Date
            Dim dtChanged As Date
            If Trim(DifferentDC) <> "" Then
                strLDAP = strLDAP.Replace("LDAP://", "LDAP://" & DifferentDC & "/")
            End If
            Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLDAP)
            Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
            Dim objSearchRes As System.DirectoryServices.SearchResult
            objSearchRes = objSearcher.FindOne
            If objSearchRes IsNot Nothing Then
                Dim strpasslast As Long = (objSearchRes.Properties("PwdLastSet")(0))
                If strpasslast = 0 Then
                    dtChanged = Date.Now
                Else
                    dtChanged = Date.FromFileTime(strpasslast)
                End If

                'Dim fileTime As Long = CType(objSearchRes.Properties("pwdLastSet")(0), Long)
                'dtChanged = DateTime.FromFileTime(fileTime)
            End If
            GetPasswordLastSetByLDAP = dtChanged.ToShortDateString

        End Function

        Function IsAccountNeverExpires(ByVal strUser As String) As String
            Try

                Const UF_PASSWORD_CANT_CHANGE = &H40
                Const UF_DONT_EXPIRE_PASSWD = &H10000
                Const E_ADS_PROPERTY_NOT_FOUND = &H8000500D
                Dim objUser, objCont, usrFlags

                objCont = GetObject(GblADSPath & strUser)

                usrFlags = objCont.Get("userFlags")

                If (usrFlags And UF_DONT_EXPIRE_PASSWD) Then
                    strPwdNeverExpires = "True"
                Else
                    strPwdNeverExpires = "False"
                End If

            Catch ex As Exception

            End Try
            Return strPwdNeverExpires
        End Function

        Public Function CkAcctStat(ByVal UsrAcctCtl As System.Int64, ByVal Key As String) As Boolean

            '***
            ' LDAP provider does not return the following, need to use WinNT provider
            '   ADS_UF_LOCKOUT
            '   ADS_UF_PASSWD_CANT_CHANGE
            '   ADS_UF_PASSWORD_EXPIRED
            '***

            Dim objHash As New Hashtable()                          '*bit mapped values array-
            objHash.Add("ADS_UF_ACCOUNTDISABLE", &H2)                   ' Account Disabled
            objHash.Add("ADS_UF_LOCKOUT", &H10)                         ' Account Locked out
            objHash.Add("ADS_UF_PASSWD_NOTREQD", &H20)                  ' Password Not Required
            objHash.Add("ADS_UF_PASSWD_CANT_CHANGE", &H40)              ' Cannot Change Password
            objHash.Add("ADS_UF_DONT_EXPIRE_PASSWD", &H10000)           ' Password Never Expires
            objHash.Add("ADS_UF_PASSWORD_EXPIRED", &H800000)            ' Password Expired

            Dim intFLGS As System.Int64 = UsrAcctCtl.ToString
            CkAcctStat = objHash(Key) And intFLGS
            objHash = Nothing
            Return CkAcctStat
        End Function

        Function IsAccountNeverExpiresByLDAP(ByVal strLDAP As String) As String
            'Try

            'Const UF_PASSWORD_CANT_CHANGE = &H40
            'Const UF_DONT_EXPIRE_PASSWD = &H10000
            'Const E_ADS_PROPERTY_NOT_FOUND = &H8000500D
            'Dim objUser, objCont, usrFlags
            'Dim objDirEnt As New DirectoryServices.DirectoryEntry(strLDAP)
            'Dim objSearcher As New System.DirectoryServices.DirectorySearcher(objDirEnt)
            'Dim objSearchRes As System.DirectoryServices.SearchResult

            'objSearchRes = objSearcher.FindOne
            Dim objSearchRes As New DirectoryEntry(strLDAP)
            If objSearchRes IsNot Nothing Then
                Dim AD_UAC As System.Int64 = objSearchRes.Properties("userAccountControl").Item(0)
                strPwdNeverExpires = CkAcctStat(AD_UAC, "ADS_UF_DONT_EXPIRE_PASSWD")
            End If
            'objDirEnt = Nothing
            'objSearcher = Nothing
            objSearchRes = Nothing
            'objCont = GetObject(strLDAP)

            'usrFlags = objCont.Get("userFlags")

            'If (usrFlags And UF_DONT_EXPIRE_PASSWD) Then
            '    strPwdNeverExpires = "True"
            'Else
            '    strPwdNeverExpires = "False"
            'End If

            'Catch ex As Exception

            'End Try
            Return strPwdNeverExpires
        End Function

        Function DoesUserAccountExpire(ByVal strUser As String)
            Try

                Const UF_PASSWORD_CANT_CHANGE = &H40
                Const UF_DONT_EXPIRE_PASSWD = &H10000
                Const E_ADS_PROPERTY_NOT_FOUND = &H8000500D
                Dim objUser, objCont, usrFlags

                objCont = GetObject(GblADSPath & strUser)

                usrFlags = objCont.Get("userFlags")

                If (usrFlags And UF_PASSWORD_CANT_CHANGE) Then
                    strPwdCantChange = "True"
                Else
                    strPwdCantChange = "False"
                End If

                If (usrFlags And UF_DONT_EXPIRE_PASSWD) Then
                    strPwdNeverExpires = "True"
                Else
                    strPwdNeverExpires = "False"
                End If

            Catch ex As Exception

            End Try
        End Function

        'Title: IsAccountLocked
        'Created by: Shane Z Smith on 10/1/2002
        'Purpose:  Unlock the users account
        'Assumptions:  That the domain controller give in the ldap path is running (pinging).
        '               Error code should be added whereever this function is called.
        '               When the Domain controller is unavailable a specific error is returned.
        'Effects:  Unlocks the user account by setting the value to FALSE
        'Inputs: Domain controller being used, user ldap path
        'Returns:  Returns an information meessage or an error message
        Function IsAccountLocked(ByVal LDAP As String) As Boolean

            Try

                Dim objUser As Object
                Dim strUserName As String

                objUser = GetObject(LDAP)
                IsAccountLocked = objUser.IsAccountLocked()

                Return IsAccountLocked

            Catch e As Exception
                Return False
            End Try

        End Function

        'Title: UnlockAccount
        'Created by: Shane Z Smith on 10/1/2002
        'Purpose:  Unlock the users account
        'Assumptions:  That the domain controller give in the ldap path is running (pinging).
        '               Error code should be added whereever this function is called.
        '               When the Domain controller is unavailable a specific error is returned.
        'Effects:  Unlocks the user account by setting the value to FALSE
        'Inputs: Domain controller being used, user ldap path
        'Returns:  Returns an information meessage or an error message
        Function UnlockAccount(ByVal LDAP As String) As String

            Try

                'If a domain controller is passed to the function use that one,
                'otherwise use the domain controller determined by this class
                'StrDC is defined by the GetDC Function

                Dim objUser As Object
                Dim strUserName As String

                objUser = GetObject(LDAP)
                strUserName = objUser.Get("name")
                objUser.IsAccountLocked = False
                objUser.SetInfo()
                objUser = Nothing

                objUser = GetObject(GblADSPath & strUserName)
                objUser.IsAccountLocked = False
                objUser.SetInfo()
                objUser = Nothing

                Return "Account Unlocked"

            Catch e As Exception
                Return "Unlock Account Error: " & e.Message
            End Try

        End Function

        'Title: SetPassword
        'Created by: Shane Z Smith on 10/1/2002
        'Purpose:  Sets the users password
        'Assumptions:  That the domain controller given in the ldap path is running (pinging).
        '               Error code should be added whereever this function is called.
        '               When the Domain controller is unavailable a specific error is returned.
        'Effects:  Resets the password entered in by the user logged in.
        'Inputs: Domain controller being used, user ldap path
        'Returns:  Returns an information meessage or an error message
        'Function SetPassword(ByVal NewPassword As String, ByVal strUserID As String, ByVal strOldPassword As String, ByVal DifferentDC As String) As String
        Function SetPassword(ByVal NewPassword As String, ByVal strUserID As String, ByVal DifferentDC As String) As String
            Try
                Dim ldap As String
                Dim strDC As String = ""
                'If a domain controller is passed to the function use that one,
                'otherwise use the domain controller determined by this class
                'StrDC is defined by the GetDC Function
                If Trim(DifferentDC) = "" Then
                    ldap = sPath.Replace("LDAP://", "LDAP://" & strDC & "/")
                Else
                    ldap = sPath.Replace("LDAP://", "LDAP://" & DifferentDC & "/")
                End If

                Dim dsUser As DirectoryEntry = New DirectoryEntry(ldap)

               ' dsUser.Invoke("ChangePassword", New Object() {strOldPassword, NewPassword})
                'dsUser.Invoke("SetPassword", New Object() {NewPassword})
                dsUser.Invoke("SetPassword", New Object() {NewPassword})

                dsUser.CommitChanges()
                ' If Not ldap.ToUpper.Contains("DC=KCTEST") Then
                UnlockAccount(sPath)
                'End If


                Return "Password Reset"

            Catch ex As Exception

                Return "Set Password Error: " & ex.Message & "|" & ex.InnerException.ToString()

            End Try

        End Function

        Function UpdateAccount()

            Dim dbUsers As New DataAccess
            Dim strSQL As String
            Dim srUsers As SqlClient.SqlDataReader
            Dim intDisabled As Integer
            Dim hlpLog As New Helper

            If UCase(strDisabled) = "FALSE" Then
                intDisabled = 0
            Else
                intDisabled = 1
            End If

            If IsDate(dtPwdLastSet) Then
                strSQL = "sp_UM_Update_ByID '" & sUser & "','" & strDesc & "','" & dtPwdLastSet.AddDays(365) & "'," & intDisabled
                dbUsers.UpdateDBByStringId(strSQL)
                dbUsers.CloseConnections()
            End If

            hlpLog.InsertLog(sUser, "Updating Account Properties", Now, "Password Last Set: " & dtPwdLastSet & " Account Disabled: " & intDisabled)

        End Function

        Function UpdateAccountExeption(ByVal blnAcctException As Integer)

            Dim dbUpd As New DataAccess
            Dim hlpClean As New Helper
            Dim strSQL As String

            strSQL = "sp_UM_UpdateActExcept_ByID '" & sUser & "'," & blnAcctException

            dbUpd.UpdateDBByStringId(strSQL)

        End Function


        Sub SetDescription(ByVal LDAP As String, ByVal strNewDescription As String)

            'If AccountManager.Global.GblTestMode <> "TRUE" Then

            Dim objUser As Object
            Dim strUserName, strExchange As String
            Dim hlpClean As New Helper

            strNewDescription = hlpClean.dbCleanUpString(strNewDescription)

            objUser = GetObject(LDAP)
            strUserName = objUser.Get("name")
            strExchange = objUser.msExchHomeServerName

            If strExchange = "" Then
                objUser.DisplayName = strNewDescription
            End If

            objUser.Description = strNewDescription
            UpdateDescription(strNewDescription, strUserName)

            objUser.SetInfo()
            objUser = Nothing
            'End If

        End Sub

        Sub SetDescriptionInAD(ByVal LDAP As String, ByVal strNewDescription As String, Optional ByVal strNewDisplayName As String = "")
            Dim hlpClean As New Helper
            strNewDescription = hlpClean.dbCleanUpString(strNewDescription)
            strNewDisplayName = hlpClean.dbCleanUpString(strNewDisplayName)
            Dim dirEntryResults As New DirectoryEntry(LDAP)
            dirEntryResults.InvokeSet("description", strNewDescription)
            If strNewDisplayName <> "" Then
                dirEntryResults.InvokeSet("DisplayName", strNewDisplayName)
            End If
            dirEntryResults.CommitChanges()
            dirEntryResults.Close()
            'If AccountManager.Global.GblTestMode <> "TRUE" Then

            'Dim objUser As Object
            'Dim strUserName, strExchange As String
            'Dim hlpClean As New Helper

            'strNewDescription = hlpClean.dbCleanUpString(strNewDescription)
            'strNewDisplayName = hlpClean.dbCleanUpString(strNewDisplayName)
            'objUser = GetObject(LDAP)
            'strUserName = objUser.Get("name")
            ''strExchange = objUser.msExchHomeServerName

            ''If strExchange = "" Then
            ''    objUser.DisplayName = strNewDisplayName
            ''End If
            'If strNewDisplayName <> "" Then
            '    objUser.DisplayName = strNewDisplayName
            'End If

            'objUser.Description = strNewDescription


            'objUser.SetInfo()
            'objUser = Nothing
            'End If

        End Sub

        Function AddWorkstation(ByVal LDAP As String, ByVal WorkStation As String)

            Dim objUser As Object
            Dim strUserName As String

            objUser = GetObject(LDAP)
            strUserName = objUser.Get("name")
            If strWorkstations <> "" Then
                objUser.userWorkstations = strWorkstations & "," & WorkStation
            Else
                objUser.userWorkstations = WorkStation
            End If

            'objUser.Put("userWorkstations", WorkStations)
            objUser.SetInfo()
            objUser = Nothing

        End Function

        Function SetWorkstation(ByVal LDAP As String, ByVal WorkStations As String)

            Dim objUser As Object
            Dim strUserName As String

            objUser = GetObject(LDAP)
            strUserName = objUser.Get("name")

            If WorkStations = "" Then
                Const ADS_PROPERTY_CLEAR = 1
                objUser.PutEx(ADS_PROPERTY_CLEAR, "userWorkstations", 0)
            Else
                objUser.userWorkstations = WorkStations
            End If

            'objUser.Put("userWorkstations", WorkStations)
            objUser.SetInfo()
            objUser = Nothing

        End Function

        Function UpdateDescription(ByVal strNewDesc As String, ByVal strAccount As String)

            Dim dbUpd As New DataAccess
            Dim hlpClean As New Helper
            Dim strSQL As String

            strSQL = "sp_UM_UpdateDesc_ByID '" & strAccount & "','" & strNewDesc & "'"

            dbUpd.UpdateDBByStringId(strSQL)

        End Function

        'Title: IsMember
        'Created by: Shane Z Smith on 10/1/2002
        'Purpose:  Determine if user is a member of a group
        'Assumptions:  None
        'Effects:  Queries active directory
        'Inputs: User LDAP path and the group that we want to know if the user is a member of
        'Returns: Returns true if user is a member of the group in question or false if they are 
        '           a member of.
        Function IsMember(ByVal LDAP As String, ByVal Group As String) As Boolean

            Dim dsUser As DirectoryEntry = New DirectoryEntry(LDAP)
            Dim objValue As Object
            Dim Key As String
            Dim strGroup, strCNUser, strCNGroup As String
            IsMember = False

            'Read the memberOf property to see which groups the user is a member of
            'Loop through all the groups
            For Each objValue In dsUser.Properties("memberOf")

                strGroup = LCase(objValue.ToString())

                'If the group is found within the current property then the user
                'is a member of the group
                'IndexOf returns -1 when nothing is found
                If strGroup.IndexOf(LCase(Group)) <> -1 Then
                    IsMember = True
                    Exit Function
                End If

            Next objValue

            dsUser.Close()
            dsUser = Nothing

        End Function
        Public Sub ADDUserToDenyGroup(ByVal usrPath As String)
            Try

                Dim dirEntry As New DirectoryEntry("LDAP://CN=DenyUser-PasswordException,OU=Other,OU=GlobalGroups,OU=Accounts,DC=kcc,DC=com")
                dirEntry.Properties("member").Add(usrPath)
                dirEntry.CommitChanges()
                dirEntry.Close()

            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
        End Sub
        Public Sub RemoveUserFromDenyGroup(ByVal usrPath As String)
            'Console.WriteLine("Remove DenyUser: " & usrPath)
            Try
                Dim dirEntry As New DirectoryEntry("LDAP://CN=DenyUser-PasswordException,OU=Other,OU=GlobalGroups,OU=Accounts,DC=kcc,DC=com")
                dirEntry.Properties("member").Remove(usrPath)
                dirEntry.CommitChanges()
                dirEntry.Close()

            Catch ex As Exception
                Console.WriteLine(ex.Message)
            End Try
        End Sub

    End Class
End Namespace
