﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 2013
VisualStudioVersion = 12.0.21005.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "AccountManager(3)", "http://localhost:57146", "{6EC5E014-D84A-4215-AE9C-03B77943B05F}"
	ProjectSection(WebsiteProperties) = preProject
		UseIISExpress = "true"
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv2.0"
		Debug.AspNetCompiler.VirtualPath = "/localhost_57146"
		Debug.AspNetCompiler.PhysicalPath = "..\AccountManager\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_57146\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/localhost_57146"
		Release.AspNetCompiler.PhysicalPath = "..\AccountManager\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_57146\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		SlnRelativePath = "..\AccountManager\"
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6EC5E014-D84A-4215-AE9C-03B77943B05F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6EC5E014-D84A-4215-AE9C-03B77943B05F}.Debug|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
