Imports System.Data
Imports System.Data.SqlClient
Imports AccountManager.Global

Namespace AccountManager

Partial Class DeleteAccount
    Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()
        End Sub
    Protected WithEvents lblNewOwner As System.Web.UI.WebControls.Label
        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub
#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
            ', Me.Load
            If Session("DeleteAccounts") = "" Then
                cmdDel.Enabled = False
                Me.lblErrMessage.Text = "Your session has expired. Please click on My Accounts on the left menu to begin again."
            Else
                If Not Page.IsPostBack Then
                    Dim strActDel As String
                    strActDel = Session("DeleteAccounts")
                    strActDel = strActDel.Trim(";")
                    strActDel = strActDel.Replace(";", ", ")

                    lblAccount.Text = strActDel

                    'Dim hlpCheck As New Helper
                    'Dim usrcur As New UserInfo
                    'If Not hlpCheck.IsOwner(lblAccount.Text, usrcur.GetUserID) Then
                    '    LoadUser()
                    '    Dim hlpGet As New Helper
                    '    Dim strUser As String

                    '    Try
                    '        Dim usrGet As New UserInfo(hlpGet.GetOwner(Request.QueryString("AccountID")))
                    '        strUser = usrGet.UserNameandID
                    '        usrGet = Nothing
                    '    Catch ex As Exception
                    '        strUser = "Error looking up account owner"
                    '    End Try

                    '    lblMessage.Text = "You are not the owner of " & lblAccount.Text & ".  Current owner for this account is " & strUser
                    '    lblMessage.CssClass = "LabelRed"
                    '    pDel.Visible = False
                    'End If
                End If
            End If
            'lblAccount.Text = Request.QueryString("AccountID")



        End Sub

        'Sub LoadUser()

        '    lblAccount.Text = "<strong>" & UCase(Request.QueryString("AccountID")) & "</strong>"

        'End Sub

    Private Sub cmdReturn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdReturn.Click
        Response.Redirect("/AccountManager/MyUsers.aspx")
    End Sub

    Private Sub cmdDel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdDel.Click
            If Session("DeleteAccounts") <> "" Then
                If txtComments.Text = "" Then
                    lblErrMessage.Text = "Please enter some comments explaining why this account is being deleted."

                Else

                    '--From Here---

                    ' Dim LoginUser As String
                    Dim LoginUserID As String

                    'LoginUser = Left(System.Web.HttpContext.Current.Request.ServerVariables("LOGON_USER").Split("\")(1), 7).ToString
                    'LoginUserID = Left(LoginUser, 6)
                    LoginUserID = DataAccess.getManager
                    Dim StrPass As String = ""
                    Dim StrFail As String = ""
                    Dim strSQL As String
                    Dim dbGet As New DataAccess
                    Dim srRead As SqlClient.SqlDataReader
                    Dim strAct As String() = Session("DeleteAccounts").ToString.Split(";")
                    For Each accountid As String In strAct
                        If accountid <> "" Then
                            Try
                                strSQL = "insert into dbo.tbUMActDelReq(AccountID,RequestBy,ReqDateTime,ReqStatus,comments) values ('" + accountid.Trim + "','" + LoginUserID.Trim + "',GetDate(),0,'" + txtComments.Text + "')"
                                srRead = dbGet.GetDataReaderByStringId(strSQL)
                                srRead.Close()
                                srRead = Nothing
                                dbGet.CloseConnections()

                                '--End Here----
                                StrPass = StrPass & accountid & ", "
                                Dim emSend As New Email
                                emSend.CopyAllBackups(accountid)
                                emSend.SendDeleteRequest("<EMAIL>", "Delete Account " & accountid, accountid, txtComments.Text)
                                'emSend.SendDeleteRequest("<EMAIL>", "Delete Account " & lblAccount.Text, lblAccount.Text, txtComments.Text)

                            Catch ex As Exception
                                StrFail = StrFail & accountid & ", "
                            End Try
                        End If
                    Next
                    'lblMessage.Text = "Email Sent to Global Computer Security.  Account will be disabled for two months and then deleted."
                    If StrPass <> "" Then
                        lblMessage.Text = "Your request has been submitted for Accounts - " & StrPass.Trim(",", " ")
                    End If

                    If StrFail <> "" Then
                        lblErrMessage.Text = "These are not submitted for deletion - " & StrFail.Trim(",", " ")
                    End If

                    cmdDel.Enabled = False
                    txtComments.Text = ""
                    Session("DeleteAccounts") = ""
                End If
            End If
        End Sub
End Class

End Namespace
