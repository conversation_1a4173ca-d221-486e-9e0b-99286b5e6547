<%@ OutputCache Duration="100" VaryByParam="none" %>
<%@ Control Language="vb" AutoEventWireup="false" Inherits="AccountManager.Menu"
    CodeFile="Menu.ascx.vb" %>
<style type="text/css">
       BODY { background: url(/Library/images/esmbkg.jpg);
              background-position: left top; 
        }
</style>
<div style="height: 100%; width: 100%; border: none 0;">
    <table cellspacing="0" cellpadding="0" border="0" =style="height: 100%; width: 100%; border: none 0; ">
        <tr>
            <td align="left">
                <asp:Image ID="imgInt" ImageUrl="/AccountManager/Library/Images/internal2.gif" runat="server" ImageAlign="TextTop" />
                <br />
                <asp:Image ID="imgWST" ImageUrl="/AccountManager/Library/Images/WSTlogo.jpg" Width="140px" runat="server"
                    ImageAlign="TextTop" />
                <br />
            </td>
        </tr>
        <tr>
            <td class="FunctionStep">
                Account Manager</td>
        </tr>
        <tr>
            <td align="left">
                <asp:Menu ID="mAM" runat="server" Font-Size="1.0em" DynamicMenuStyle-Font-Bold="true" 
                    DynamicHoverStyle-BackColor="ActiveCaption" StaticMenuItemStyle-VerticalPadding="2"
                    StaticMenuItemStyle-HorizontalPadding="2" BackColor="Transparent" DynamicHorizontalOffset="2"
                    Font-Names="Verdana" ForeColor="white" StaticSubMenuIndent="10px">
                    <Items>
                        <asp:MenuItem Text="Home" NavigateUrl="/AccountManager/Default.aspx" Value="Home"></asp:MenuItem>
                        <asp:MenuItem Text="My Accounts" NavigateUrl="/AccountManager/MyUsers.aspx" Value="My Accounts"></asp:MenuItem>
                        <asp:MenuItem Text="Pending Ownership<br> Changes" NavigateUrl="/AccountManager/Views/AcceptOwnership.aspx"
                            Value="Pending"></asp:MenuItem>
                        <asp:MenuItem Text="Find Owners" NavigateUrl="/AccountManager/Views/FindOwners.aspx" Value="Find Owners">
                        </asp:MenuItem>
                        <asp:MenuItem Text="Questions/Feedback" NavigateUrl="mailto:_Computer Security, Global"
                            Value="Questions/Feedback"></asp:MenuItem>
                    </Items>
                    <StaticMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                    <DynamicHoverStyle BackColor="#284E98" ForeColor="White" />
                    <DynamicMenuStyle BackColor="#B5C7DE" />
                    <StaticSelectedStyle BackColor="#507CD1" />
                    <DynamicSelectedStyle BackColor="#507CD1" />
                    <DynamicMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                    <StaticHoverStyle BackColor="#284E98" ForeColor="White" />
                </asp:Menu>
            </td>
        </tr>
        <tr>
            <td class="FunctionStep">
                <br />
                Documentation</td>
        </tr>
        <tr>
            <td align="left">
                <asp:Menu ID="Menu1" runat="server" Font-Size="1.0em" DynamicMenuStyle-Font-Bold="true"
                    DynamicHoverStyle-BackColor="ActiveCaption" StaticMenuItemStyle-VerticalPadding="2"
                    StaticMenuItemStyle-HorizontalPadding="2" BackColor="Transparent" DynamicHorizontalOffset="2"
                    Font-Names="Verdana" ForeColor="white" StaticSubMenuIndent="10px">
                    <Items>
                        <asp:MenuItem Text="FAQ" NavigateUrl="/AccountManager/Tutorials/FAQ.aspx" Value="FAQ"></asp:MenuItem>
                        <asp:MenuItem Text="QRC" NavigateUrl="http://onlineuniversity.kcc.com/gm/folder-1.11.706835?mode=EU&originalContext=1.11.678119" target="_blank"
                            Value="QRC"></asp:MenuItem>
                    </Items>
                    <StaticMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                    <DynamicHoverStyle BackColor="#284E98" ForeColor="White" />
                    <DynamicMenuStyle BackColor="#B5C7DE" />
                    <StaticSelectedStyle BackColor="#507CD1" />
                    <DynamicSelectedStyle BackColor="#507CD1" />
                    <DynamicMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                    <StaticHoverStyle BackColor="#284E98" ForeColor="White" />
                </asp:Menu>
            </td>
        </tr>
        <asp:Panel ID="pReports" runat="server">
            <tr>
                <td class="FunctionStep">
                    <br />
                    Reports</td>
            </tr>
            <tr>
                <td align="left">
                    <asp:Menu ID="Menu2" runat="server" Font-Size="1.0em" DynamicMenuStyle-Font-Bold="true"
                        DynamicHoverStyle-BackColor="ActiveCaption" StaticMenuItemStyle-VerticalPadding="2"
                        StaticMenuItemStyle-HorizontalPadding="2" BackColor="Transparent" DynamicHorizontalOffset="2"
                        Font-Names="Verdana" ForeColor="white" StaticSubMenuIndent="10px">
                        <Items>
                            <asp:MenuItem Text="Reports" NavigateUrl="/AccountManager/Reports/Reports.aspx" Value="Reports"></asp:MenuItem>
                            <asp:MenuItem Text="User Stats" NavigateUrl="/AccountManager/Reports/Stats.aspx" Value="Stats"></asp:MenuItem>
                        </Items>
                        <StaticMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                        <DynamicHoverStyle BackColor="#284E98" ForeColor="White" />
                        <DynamicMenuStyle BackColor="#B5C7DE" />
                        <StaticSelectedStyle BackColor="#507CD1" />
                        <DynamicSelectedStyle BackColor="#507CD1" />
                        <DynamicMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                        <StaticHoverStyle BackColor="#284E98" ForeColor="White" />
                    </asp:Menu>
                </td>
            </tr>
        </asp:Panel>
        <asp:Panel ID="pAdmin" runat="server">
            <tr>
                <td class="FunctionStep">
                    <br />
                    Administration</td>
            </tr>
            <tr>
                <td align="left">
                    <asp:Menu ID="Menu3" runat="server" Font-Size="1.0em" DynamicMenuStyle-Font-Bold="true"
                        DynamicHoverStyle-BackColor="ActiveCaption" StaticMenuItemStyle-VerticalPadding="2"
                        StaticMenuItemStyle-HorizontalPadding="2" BackColor="Transparent" DynamicHorizontalOffset="2"
                        Font-Names="Verdana" ForeColor="white" StaticSubMenuIndent="10px">
                        <Items>
                            <asp:MenuItem Text="Manage Accounts" NavigateUrl="/AccountManager/CS/Manage.aspx" Value="MGACT"></asp:MenuItem>
                            <asp:MenuItem Text="Requests" NavigateUrl="/AccountManager/CS/Requests.aspx" Value="Req"></asp:MenuItem>
                            <asp:MenuItem Text="User Logs" NavigateUrl="/AccountManager/CS/ViewLog.aspx" Value="logs"></asp:MenuItem>
                            <asp:MenuItem Text="Mass Owner Update" NavigateUrl="/AccountManager/CS/MassOwnerUpdate.aspx" Value="mass"></asp:MenuItem>
                        </Items>
                        <StaticMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                        <DynamicHoverStyle BackColor="#284E98" ForeColor="White" />
                        <DynamicMenuStyle BackColor="#B5C7DE" />
                        <StaticSelectedStyle BackColor="#507CD1" />
                        <DynamicSelectedStyle BackColor="#507CD1" />
                        <DynamicMenuItemStyle HorizontalPadding="5px" VerticalPadding="2px" />
                        <StaticHoverStyle BackColor="#284E98" ForeColor="White" />
                    </asp:Menu>
                </td>
            </tr>
        </asp:Panel>
    </table>
</div>
