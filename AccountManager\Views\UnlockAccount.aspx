﻿<%@ Page Language="vb" MasterPageFile="~/MasterPage.master" Title="Unlock Account"
    AutoEventWireup="false" Inherits="AccountManager.Migrated_UnlockAccount" CodeFile="UnlockAccount.aspx.vb" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>

<%@ Register TagPrefix="uc1" TagName="Menu" Src="~/Library/UserControls/Menu.ascx" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <table>
        <tr>
            <td>
                <asp:Label ID="lblWelcome" CssClass="TableHeader" runat="server">Unlock Production Account</asp:Label><br />
                <br />
                <asp:Label ID="lblMessage" CssClass="LabelRed" runat="server"></asp:Label><br />
                <table>
                    <tr>
                        <td>
                            <asp:Panel ID="pDelete" runat="server">
                                <table class="DisplayTables">
                                    <tr>
                                        <td>
                                            <table>
                                                <asp:Panel ID="pInfo" runat="server">
                                                    <tbody>
                                                        <tr>
                                                            <td>
                                                                Account ID:&nbsp;&nbsp;
                                                                <asp:Label ID="lblAccount" runat="server"></asp:Label></td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <asp:Label ID="lblDescription" runat="server"></asp:Label></td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <asp:Label ID="lblLocked" runat="server" CssClass="LabelRed"></asp:Label></td>
                                                        </tr>
                                                </asp:Panel>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            &nbsp;<table>
                                                <tr>
                                                    <td style="width: 100px">
                                                        <dx:ASPxButton ID="cmdUnlock" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                            CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                            Text="Unlock">
                                                        </dx:ASPxButton>
                                                    </td>
                                                    <td style="width: 120px">
                                                        <dx:ASPxButton ID="cmdReturn" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                            CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                            Text="Return to My Accounts">
                                                        </dx:ASPxButton>
                                                    </td>
                                                </tr>
                                            </table>
                                            &nbsp; &nbsp;&nbsp;
                                        </td>
                                    </tr>
                                </table>
                            </asp:Panel>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        </table>
</asp:Content>
    
