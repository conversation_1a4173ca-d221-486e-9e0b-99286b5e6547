﻿<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="uc1" TagName="Menu" Src="~/Library/UserControls/Menu.ascx" %>

<%@ Page MasterPageFile="~/MasterPage.master" Title="View Services" Language="vb"
    AutoEventWireup="false" Inherits="AccountManager.ViewServices" CodeFile="ViewServices.aspx.vb" %>

<asp:content id="Content1" contentplaceholderid="ContentPlaceHolder1" runat="Server"><table><tr><td><asp:label id="lblWelcome" Runat="server" CssClass="TableHeader">View Services where the account is used</asp:label><br/><br/><asp:label id="lblMessage" Runat="server"></asp:label><br/><table><tr><td><table class="DisplayTables"><tr><td>The following report shows the servers where the accounts selected are running services.<br/><font color="red"><strong>This report does NOT show all areas where the account is used. The report focuses on services running on servers only.</strong></font><br/></td></tr><tr><td><strong>Results Last Updated On:</strong> <dxwdc:ASPxLabel id="lblLastUpd" runat="server" Text="ASPxLabel" DataField="JobRunTimeKEy" DataSource="<%# grdServices %>">
																			</dxwdc:ASPxLabel></td></tr><tr><td>&nbsp;&nbsp; <dxwdc:ASPxButton id="cmdReturn" runat="server" Text="Return to My Accounts" tabIndex="0">
																				<LookAndFeel Kind="Office2003">
																					<EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black" BackColor="White"></EditorStyle>
																					<LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
																					<ScrollBarButtonStyle BackColor="#84ABE3">
																						<Filters>
																							<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																						</Filters>
																					</ScrollBarButtonStyle>
																					<ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
																						ScrollBarMargin="1"></ElementsSettings>
																					<PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"></PopupStyle>
																					<ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
																						UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
																						<HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
																							<Filters>
																								<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202"></dxwdc:LookAndFeelStyleGradientFilter>
																							</Filters>
																						</HotTrackStyle>
																						<PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
																							<Filters>
																								<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80"></dxwdc:LookAndFeelStyleGradientFilter>
																							</Filters>
																						</PressedStyle>
																						<Filters>
																							<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																						</Filters>
																					</ButtonStyle>
																				</LookAndFeel>
																			</dxwdc:ASPxButton> &nbsp;<dxwdc:aspxbutton id="cmdExport" tabIndex="0" runat="server" Text="Export">
																				<LookAndFeel Kind="Office2003">
																					<EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black" BackColor="White"></EditorStyle>
																					<LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
																					<ScrollBarButtonStyle BackColor="#84ABE3">
																						<Filters>
																							<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																						</Filters>
																					</ScrollBarButtonStyle>
																					<ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
																						ScrollBarMargin="1"></ElementsSettings>
																					<PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"></PopupStyle>
																					<ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
																						UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
																						<HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
																							<Filters>
																								<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202"></dxwdc:LookAndFeelStyleGradientFilter>
																							</Filters>
																						</HotTrackStyle>
																						<PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
																							<Filters>
																								<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80"></dxwdc:LookAndFeelStyleGradientFilter>
																							</Filters>
																						</PressedStyle>
																						<Filters>
																							<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																						</Filters>
																					</ButtonStyle>
																				</LookAndFeel>
																			</dxwdc:aspxbutton><LOOKANDFEEL Kind="Office2003"><EDITORSTYLE BackColor="White" ForeColor="Black" BorderColor="#6787B8" Font-Names="Verdana" Font-Size="8pt"></EDITORSTYLE>
																				<LABELSTYLE ForeColor="Black" Font-Names="Verdana" Font-Size="8pt"></LABELSTYLE>
																				<SCROLLBARBUTTONSTYLE BackColor="#84ABE3">
																					<FILTERS>
																						<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="221, 236, 254" GradientMode="Horizontal" EndColor="132, 171, 227"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
																					</FILTERS>
																				</SCROLLBARBUTTONSTYLE>
																				<ELEMENTSSETTINGS ScrollBarMargin="1" ScrollBarBackColor="247, 245, 241" DropDownButtonWidth="17px"
																					ScrollBarSize="17px"></ELEMENTSSETTINGS>
																				<POPUPSTYLE bordercolor="#6787B8" Font-Names="Verdana" Font-Size="8pt"></POPUPSTYLE>
																				<BUTTONSTYLE BackColor="#84ABE3" ForeColor="Black" BorderColor="#6787B8" Font-Names="Verdana"
																					Font-Size="8pt" Margin="1" Wrap="False" UsePressedStyle="True" UseHotTrackStyle="True">
																					<HOTTRACKSTYLE BackColor="#FFD599" ForeColor="Black" BorderColor="Navy">
																						<FILTERS>
																							<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="255, 243, 202" EndColor="255, 213, 153"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
																						</FILTERS>
																					</HOTTRACKSTYLE>
																					<PRESSEDSTYLE BackColor="#FFCA86" ForeColor="Black" BorderColor="Navy">
																						<FILTERS>
																							<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="254, 148, 80" EndColor="255, 202, 134"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
																						</FILTERS>
																					</PRESSEDSTYLE>
																					<FILTERS>
																						<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="221, 236, 254" EndColor="132, 171, 227"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
																					</FILTERS>
																				</BUTTONSTYLE>
																			</LOOKANDFEEL></td></tr><tr><td><asp:HyperLink ID="hlDownload" Target="_blank" Runat="server" Font-Bold="True" ForeColor="#000099"
																				Font-Size="14"></asp:HyperLink></td></tr></table></td></tr><tr><td><dxwg:aspxgrid id="grdServices" tabIndex="0" runat="server" BorderColor="#6787B8" BorderStyle="Solid"
																	ExpandBtnWidth="11px" HeaderHeight="25px" RowBtnWidth="18px" SelectedBackColor="49, 106, 197"
																	ExpandBtnHeight="11px" SearchBtnWidth="17px" StatusBarItemSpacing="0" PageIndexButtonCount="30"
																	AutoGenerateColumns="False">
																	<AlternatingItemStyle BackColor="#E7F2FE"></AlternatingItemStyle>
																	<ExpandBtnStyle BorderWidth="1px" BorderColor="#6787B8" BorderStyle="Solid" BackColor="#F9F9F9">
																		<HotTrackStyle BorderColor="Navy" BackColor="White"></HotTrackStyle>
																		<PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#D0D0D0"></PressedStyle>
																	</ExpandBtnStyle>
																	<ButtonBarStyle BorderWidth="1px" BorderColor="#6787B8" BorderStyle="Solid"></ButtonBarStyle>
																	<GroupPanelStyle Font-Bold="True" ForeColor="#DDECFE" BackColor="#3E6DB9"></GroupPanelStyle>
																	<SearchBtnStyle FixedWidth="True"></SearchBtnStyle>
																	<TitleStyle ForeColor="White" BackColor="#6787B8"></TitleStyle>
																	<GroupItemStyle FixedWidth="True" BorderColor="#6787B8" BackColor="#C1D8F7" Wrap="False"></GroupItemStyle>
																	<RowBtnStyle BorderStyle="None"></RowBtnStyle>
																	<ButtonBars>
																		<dxwg:ButtonBar ButtonBarType="Navigator">
																			<BarItems>
																				<dxwdc:BarButton ButtonType="MoveFirst"></dxwdc:BarButton>
																				<dxwdc:BarButton ButtonType="MovePrevPage"></dxwdc:BarButton>
																				<dxwdc:BarButton ButtonType="MovePrev"></dxwdc:BarButton>
																				<dxwdc:BarTwoStateEditorButton ButtonType="ChangePageSize"></dxwdc:BarTwoStateEditorButton>
																				<dxwdc:BarButton ButtonType="MoveNext"></dxwdc:BarButton>
																				<dxwdc:BarButton ButtonType="MoveNextPage"></dxwdc:BarButton>
																				<dxwdc:BarButton ButtonType="MoveLast"></dxwdc:BarButton>
																				<dxwdc:BarButton ButtonType="Refresh"></dxwdc:BarButton>
																			</BarItems>
																		</dxwg:ButtonBar>
																	</ButtonBars>
																	<HeaderStyle FixedWidth="True" Font-Bold="True" BorderStyle="None" FixedHeight="True" Wrap="False"></HeaderStyle>
																	<StatusBars>
																		<dxwg:StatusBar Height="20px" StatusBarType="Regular">
																			<BarItems>
																				<dxwdc:BarStatusSection StatusSectionType="Status"></dxwdc:BarStatusSection>
																				<dxwdc:BarStatusSection StatusSectionType="VisibleInterval"></dxwdc:BarStatusSection>
																				<dxwdc:BarStatusSection StatusSectionType="TotalVisible"></dxwdc:BarStatusSection>
																				<dxwdc:BarStatusSection StatusSectionType="TotalRows"></dxwdc:BarStatusSection>
																			</BarItems>
																		</dxwg:StatusBar>
																	</StatusBars>
																	<ItemStyle FixedWidth="True" VerticalAlign="Middle" Font-Size="7.5pt" BackColor="White" Wrap="False"></ItemStyle>
																	<HeaderDraggedStyle BorderWidth="1px" BorderColor="LightGray" BorderStyle="Solid">
																		<Filters>
																			<dxwdc:LookAndFeelStyleAlphaFilter FinishOpacity="50" FinishX="50"></dxwdc:LookAndFeelStyleAlphaFilter>
																		</Filters>
																	</HeaderDraggedStyle>
																	<LookAndFeel Kind="Office2003">
																		<EditorStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8" ForeColor="Black" BackColor="White"></EditorStyle>
																		<LabelStyle Font-Size="8pt" Font-Names="Verdana" ForeColor="Black"></LabelStyle>
																		<ScrollBarButtonStyle BackColor="#84ABE3">
																			<Filters>
																				<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" GradientMode="Horizontal" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																			</Filters>
																		</ScrollBarButtonStyle>
																		<ElementsSettings ScrollBarSize="17px" DropDownButtonWidth="17px" ScrollBarBackColor="247, 245, 241"
																			ScrollBarMargin="1"></ElementsSettings>
																		<PopupStyle Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"></PopupStyle>
																		<ButtonStyle UseHotTrackStyle="True" Font-Size="8pt" Font-Names="Verdana" BorderColor="#6787B8"
																			UsePressedStyle="True" ForeColor="Black" BackColor="#84ABE3" Wrap="False" Margin="1">
																			<HotTrackStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFD599">
																				<Filters>
																					<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 213, 153" StartColor="255, 243, 202"></dxwdc:LookAndFeelStyleGradientFilter>
																				</Filters>
																			</HotTrackStyle>
																			<PressedStyle BorderColor="Navy" ForeColor="Black" BackColor="#FFCA86">
																				<Filters>
																					<dxwdc:LookAndFeelStyleGradientFilter EndColor="255, 202, 134" StartColor="254, 148, 80"></dxwdc:LookAndFeelStyleGradientFilter>
																				</Filters>
																			</PressedStyle>
																			<Filters>
																				<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																			</Filters>
																		</ButtonStyle>
																	</LookAndFeel>
																	<GroupedHeaderStyle BorderWidth="1px" BorderColor="#6787B8" BorderStyle="Solid">
																		<HotTrackStyle BorderColor="Navy"></HotTrackStyle>
																		<PressedStyle BorderColor="Navy"></PressedStyle>
																	</GroupedHeaderStyle>
																	<BarBtnStyle BorderStyle="None">
																		<HotTrackStyle BorderStyle="Solid"></HotTrackStyle>
																		<PressedStyle BorderStyle="Solid"></PressedStyle>
																	</BarBtnStyle>
																	<FooterStyle FixedWidth="True" Font-Bold="True" FixedHeight="True" BackColor="#B0CBF1" Wrap="False"></FooterStyle>
																	<PreviewStyle ForeColor="#5881B9" BackColor="#F9FCFF"></PreviewStyle>
																	<BarBtnEditorStyle BorderStyle="None"></BarBtnEditorStyle>
																	<SearchEditorStyle BorderColor="White"></SearchEditorStyle>
																	<SearchItemStyle BackColor="#C1D8F7"></SearchItemStyle>
																	<Columns>
																		<dxwg:BoundColumn VisibleIndex="0" DataField="AccountName" HeaderText="Account" Width="300px"></dxwg:BoundColumn>
																		<dxwg:BoundColumn VisibleIndex="1" DataField="DisplayName" HeaderText="Service Name" Width="300px"></dxwg:BoundColumn>
																		<dxwg:BoundColumn VisibleIndex="2" DataField="Host" HeaderText="Server Name"></dxwg:BoundColumn>
																	</Columns>
																	<NavigatorButtons Ok="False" InsertRow="False" DeleteRow="False" Cancel="False" EditRow="False"></NavigatorButtons>
																	<FooterItemStyle BackColor="#B0CBF1"></FooterItemStyle>
																	<StatusBarStyle BorderStyle="None" BackColor="#DDECFE">
																		<Filters>
																			<dxwdc:LookAndFeelStyleGradientFilter EndColor="132, 171, 227" StartColor="221, 236, 254"></dxwdc:LookAndFeelStyleGradientFilter>
																		</Filters>
																	</StatusBarStyle>
																	<GroupIndentStyle BackColor="#C1D8F7"></GroupIndentStyle>
																</dxwg:aspxgrid></td></tr></table></td></tr></table>
									
							
							

</asp:content>
