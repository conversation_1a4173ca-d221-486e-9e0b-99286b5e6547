Imports DevExpress.Web.ASPxGrid
Imports System.Data
Namespace AccountManager

    Partial Class ucAddNew
        Inherits System.Web.UI.UserControl

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
            'Put user code to initialize the page here
            'If Not Page.IsPostBack Then
            '    Dim hlpFill As New Helper
            '    hlpFill.FillAccessDropDown(Me.ddlOwnType)
            'End If
            LoadInstruction()
        End Sub

        Sub LoadInstruction()

            'lblInstructions.Text = "" & _
            '    "Step 1: Enter Account ID you want to added. Click Lookup.<br/>" & _
            '    "Step 2: Update Description as needed:<br/>" & _
            '    "Step 3: Enter the owner ID:<br/>" & _
            '    "Step 4: Click the Insert button.<br/>" & _
            '    "Step 5: Enter the backup owner ID:<br/>" & _
            '    "Step 6: Click the Add Backup button.<br/>"
            lblInstructions.Text = "" & _
               "Step 1: Enter Account ID you want to added. Click Lookup.<br/>" & _
               "Step 3: Enter the Account Details.<br/>" & _
               "Step 4: Click the Insert button.<br/>"
        End Sub

        Sub InsertUser()
            Dim usrOwner As New UserInfo(txtOwnerID.Text, True)
            Dim usrDelegate As New UserInfo(txtDelegateID.Text, True)
            If (Not usrOwner.UserFound) Or usrOwner.UserEmployeeType <> "E" Then
                If usrOwner.UserFound Then
                    lblMessage.Text = "Non-User Account's Owner should permanent KC Employee."
                Else
                    lblMessage.Text = "Owner ID does not exist in AD."
                End If

            ElseIf (Not usrDelegate.UserFound) Or (usrDelegate.UserEmployeeType <> "C" And usrDelegate.UserEmployeeType <> "E") Then
                If usrDelegate.UserFound Then
                    lblMessage.Text = "Non-User Account's Delegate should have Employee Type as E or C."
                Else
                    lblMessage.Text = "Delegate ID does not exist in AD."
                End If

            Else
                Dim dataAccessObj As New DataAccess
                Dim dtExpire As DateTime = lblExpirationDate.Text
                Dim blnDisabled As Boolean = Convert.ToBoolean(lblActEnabled.Text)
                Dim IsExpired As Boolean = Not Convert.ToBoolean(lblAccountType.Text)
                Dim IsExpirationDate As Boolean = Convert.ToBoolean(hfPasswordMustChangeAtNextLogon.Value)
                If IsExpirationDate Then
                    dtExpire = Now().ToShortDateString
                End If
                Dim strAccountID As String = txtAccount.Text.Trim
                Dim strManager As String = DataAccess.getManager()
                If dataAccessObj.AddUMUser(strAccountID, lblDesc.Text, txtAppName.Text, txtpurpose.Text, dtExpire, IsExpirationDate, blnDisabled, IsExpired, strManager, txtOwnerID.Text, usrOwner.DisplayName, txtDelegateID.Text, usrDelegate.DisplayName) Then
                    lblMessage.Text = "Account has been added to Account Manager."
                    cmdInsert.Enabled = False
                Else
                    If dataAccessObj.IsError Then
                        lblMessage.Text = "Error Occurred: " & dataAccessObj.ErrorMsg
                    End If
                End If
                'Dim usrAccountID As New UserInfo(txtAccount.Text)
                'If usrAccountID.UserFound Then
                '    Dim strSQL As String
                '    Dim dbInsert As New DataAccess
                '    Dim hlpClean As New Helper
                '    Dim dtExpire As DateTime
                '    Dim intDis, intException As Integer

                '    ' intException = chkExcept.Checked 'Removed by Q07233
                '    intException = 0

                '    If UCase(usrAccountID.Disabled) = "TRUE" Then
                '        intDis = 0
                '    Else
                '        intDis = 0
                '    End If

                '    dtExpire = usrAccountID.PwdLastSet

                '    strSQL = "sp_UMUser_Add '" & UCase(txtAccount.Text) & "','" & hlpClean.dbCleanUpString(usrAccountID.Description) & "','KCUS','" & dtExpire.AddDays(365) & "','LOW'," & intDis & "," & intException

                '    dbInsert.UpdateDBByStringId(strSQL)
                '    UpdateRequest()
                '    strSQL = "sp_UMOwner_Add '" & txtAccount.Text & "'," & [Global].GblOwnerAccess & ",'" & usrOwn.GetUserID & "','" & hlpClean.dbCleanUpString(usrOwn.DisplayName) & "'"
                '    dbInsert.UpdateDBByStringId(strSQL)
                '    'usrAccountID.SetDescription(usrAccountID.LdapPath, txtDesc.Text) 'Commented During CHG0443348
                '    lblMessage.Text = "Account Added to Account Manager."

                '    'lblOwnType.Visible = True
                '    FindAccount()
                '    UpdateRequest()

                '    cmdAddBackup.Visible = True
                '    'ddlOwnType.Visible = True

                '    Dim usrcur As New UserInfo
                '    hlpClean.InsertLog(txtAccount.Text, "Account Added", Now, "Added by: " & usrcur.GetUserID)
                'Else
                '    lblMessage.Text = "Account " & txtAccount.Text & " does not exist in the domain.  Verify the account was added to the domain."
                'End If
            End If
        End Sub

        Sub UpdateRequest()

            Dim reqUpd As New Requests
            Dim hlpIns As New Helper

            reqUpd.UpdateAutoRequest(UCase(txtAccount.Text), [Global].GblReqStatusDone)
            hlpIns.InsertLog(UCase(txtAccount.Text), "Account Added", Now, "Computer Security Change")

        End Sub

        Sub FindAccount()

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            strSQL = "SELECT * FROM vUsr WHERE AccountID = '" & txtAccount.Text & "' ORDER BY Access"
            srRead = dbGet.GetDataReaderByStringId(strSQL)

            If srRead.HasRows Then

                'grdUsers.KeyFieldName = "ID"
                'grdUsers.DataSource = srRead
                'grdUsers.DataBind()
                'grdUsers.ExpandAll()
                'grdUsers.Visible = True
            Else
                'grdUsers.Visible = False
                lblMessage.Text = "No account found."
                '    EnableControls(False)
            End If

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()

        End Sub

        Sub AddBackup()

            '           Dim blnLoad As Boolean = True
            '           Dim reqUpd As New Requests
            '           Dim usrcur As New UserInfo
            '           Dim usrNew As New UserInfo(Trim(txtOwnerID.Text))
            '           Dim hlpIns As New Helper
            '    Dim OwnerType As String
            '           OwnerType = UCase(hlpIns.IsValidOwnerIDNew(txtOwnerID.Text))


            'If OwnerType ="C" or OwnerType = "E" then
            '           If usrNew.UserFound Then

            '               'add new owner/backup
            '               Dim strSQL As String
            '               Dim dbInsert As New DataAccess
            '               Dim hlpClean As New Helper
            '               Dim usrFnd As New UserInfo(txtAccount.Text)

            '               usrFnd.SetDescription(usrFnd.LdapPath, txtDesc.Text)

            '               strSQL = "sp_UMOwner_Add '" & txtAccount.Text & "'," & ddlOwnType.SelectedValue & ",'" & usrNew.GetUserID & "','" & hlpClean.dbCleanUpString(usrNew.DisplayName) & "'"
            '               dbInsert.UpdateDBByStringId(strSQL)

            '               hlpClean.InsertLog(txtAccount.Text, [Global].GblActOwnAdd, Now, usrcur.GetUserID & " Added as " & ddlOwnType.SelectedItem.Text)

            '               lblMessage.Text = "Owner/Backup  added."
            '               FindAccount()
            '           Else
            '               lblMessage.Text = "New owner " & txtOwnerID.Text & " does not exist in the domain."
            '           End If
            '     Else
            'lblMessage.Text = "Backup/Delegate is not valid."
            '    End If

        End Sub

        Private Sub cmdInsert_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdInsert.Click
            InsertUser()
        End Sub

        Private Sub cmdLookup_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdLookup.Click
            lblMessage.Text = ""
            Dim strActID As String = txtAccount.Text.Trim
            Dim IsActValid As Boolean = False
            If strActID <> "" Then
                If Not IsExistInAccountManager(strActID) Then
                    Dim usrAccountID As New UserInfo(strActID)
                    If usrAccountID.UserFound Then
                        Dim strDisplayName As String = usrAccountID.DisplayName
                        Dim strTempDisplayName As String = LCase(strDisplayName)
                        strTempDisplayName = Replace(strTempDisplayName, " ", "")
                        If strTempDisplayName.StartsWith("??dele") Then
                            pInsert.Visible = False
                            lblMessage.Text = "Account " & strActID & " is soft deleted in AD. Please verify the account from AD."
                        ElseIf strTempDisplayName.StartsWith("??hold") Then
                            pInsert.Visible = False
                            lblMessage.Text = "Account " & strActID & " is on hold in AD. Please verify the account from AD."
                        ElseIf usrAccountID.Disabled = True Then
                            pInsert.Visible = False
                            lblMessage.Text = "Account " & strActID & " is disabled in AD. Please verify the account from AD."
                            'ElseIf usrAccountID.IsUserMustChangePasswordOnNextLogon = True Then
                            '    pInsert.Visible = False
                            '    lblMessage.Text = "Option 'User must change password at next logon' is checked for Account " & strActID & " in AD. Please verify the account from AD."
                        Else
                            Dim strDesc As String = usrAccountID.Description
                            txtDesc.Text = strDesc
                            lblDesc.Text = strDesc
                            lblExpirationDate.Text = usrAccountID.PwdLastSet
                            hfPasswordMustChangeAtNextLogon.Value = usrAccountID.IsUserMustChangePasswordOnNextLogon
                            'lblAccountType.Text = usrAccountID.IsAccountNeverExpires(strActID)
                            Dim strLDAP As String = usrAccountID.LdapPath
                            'strLDAP = strLDAP.Replace("//", "//xxtcndc0.kctest.com/")
                            'lblAccountType.Text = strLDAP & usrAccountID.PwdNeverExpires
                            lblAccountType.Text = usrAccountID.IsAccountNeverExpiresByLDAP(strLDAP)
                            lblActEnabled.Text = usrAccountID.Disabled
                            Dim strDescTemp As String = strDesc.Trim("(", " ")
                            If strDescTemp.Length > 6 Then
                                txtOwnerID.Text = strDescTemp.Substring(0, 6)
                            End If
                            pInsert.Visible = True
                            IsActValid = True

                        End If
                    Else
                        pInsert.Visible = False
                        lblMessage.Text = "Account " & strActID & " does not exist in the domain.  Verify the account was added to the domain."
                    End If
                Else
                    pInsert.Visible = False
                    lblMessage.Text = "Account (" & strActID & " ) is already exist in Account Manager."
                End If
            Else
                lblMessage.Text = "Please enter account ID."
            End If

            txtAccount.Enabled = Not IsActValid
            cmdLookup.Enabled = Not IsActValid
        End Sub

        Private Function IsExistInAccountManager(ByVal strAccount As String) As Boolean
            Dim ret As Boolean = True
            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            strSQL = "SELECT * FROM vUsr WHERE AccountID = '" & txtAccount.Text & "' ORDER BY Access"
            srRead = dbGet.GetDataReaderByStringId(strSQL)

            If Not srRead.HasRows Then

                ret = False
            
            End If

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()
            Return ret
        End Function

        Private Sub cmdReset_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmdReset.Click
            txtAccount.Enabled = True
            cmdLookup.Enabled = True
            pInsert.Visible = False
            lblMessage.Text = ""
            cmdInsert.Enabled = True
            txtDelegateID.Text = ""
            txtOwnerID.Text = ""
            txtpurpose.Text = ""
            txtAppName.Text = ""
            lblActEnabled.Text = ""
            lblAccountType.Text = ""
            lblExpirationDate.Text = ""
            lblDesc.Text = ""
            txtDesc.Text = ""
        End Sub

        'Private Sub cmdAddBackup_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmdAddBackup.Click
        '    AddBackup()
        'End Sub
    End Class

End Namespace
