﻿<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Page MasterPageFile="~/MasterPage.master"Language="vb" AutoEventWireup="false" Inherits="AccountManager.MassOwnerUpdate" CodeFile="MassOwnerUpdate.aspx.vb" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register TagPrefix="uc1" TagName="Menu" src="~/Library/UserControls/Menu.ascx" %>
<asp:content id="Content1" contentplaceholderid="ContentPlaceHolder1" runat="Server">
    <table>
        <tr>
            <td>
						<asp:label id="lblWelcome" Runat="server" CssClass="TableHeader">Update Owners and Backups</asp:label><br/>
						<br/>
						<asp:label id="lblMessage" Runat="server" CssClass="LabelRed"></asp:label><br/>
						<br/>
						<table class="DisplayTables" cellspacing="0" cellpadding="0">
							<tr>
								<td colSpan="2">
									This page allows you to replace one user with another for all accounts he/she 
									is listed as the owner or backup.
									<br/>
									Step 1: Enter in the current User ID you want to update.
									<br/>
									Step 2: Enter in the new User ID.
									<br/>
									Step 3: Click the update button. All accounts are now updated.
								</td>
							</tr>
							<tr>
								<td colspan="2">Enter User ID:&nbsp;&nbsp;<asp:textbox id="txtOldUser" Runat="server"></asp:textbox></td>
							</tr>
							<tr>
								<td colspan="2">Enter New User ID:&nbsp;&nbsp;<asp:textbox id="txtNewuser" Runat="server"></asp:textbox></td>
							</tr>
							<tr>
								<td>
									<LOOKANDFEEL kind="Office2003"><EDITORSTYLE backcolor="White" forecolor="Black" bordercolor="#6787B8" font-names="Verdana" font-size="8pt"></EDITORSTYLE>
										<LABELSTYLE forecolor="Black" font-names="Verdana" font-size="8pt"></LABELSTYLE>
										<SCROLLBARBUTTONSTYLE backcolor="#84ABE3">
											<FILTERS>
												<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="221, 236, 254" GradientMode="Horizontal" EndColor="132, 171, 227"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
											</FILTERS>
										</SCROLLBARBUTTONSTYLE>
										<ELEMENTSSETTINGS scrollbarmargin="1" scrollbarbackcolor="247, 245, 241" dropdownbuttonwidth="17px"
											scrollbarsize="17px"></ELEMENTSSETTINGS>
										<POPUPSTYLE bordercolor="#6787B8" font-names="Verdana" font-size="8pt"></POPUPSTYLE>
										<BUTTONSTYLE backcolor="#84ABE3" forecolor="Black" bordercolor="#6787B8" font-names="Verdana"
											font-size="8pt" margin="1" wrap="False" usepressedstyle="True" usehottrackstyle="True">
											<HOTTRACKSTYLE backcolor="#FFD599" forecolor="Black" bordercolor="Navy">
												<FILTERS>
													<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="255, 243, 202" EndColor="255, 213, 153"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
												</FILTERS>
											</HOTTRACKSTYLE>
											<PRESSEDSTYLE backcolor="#FFCA86" forecolor="Black" bordercolor="Navy">
												<FILTERS>
													<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="254, 148, 80" EndColor="255, 202, 134"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
												</FILTERS>
											</PRESSEDSTYLE>
											<FILTERS>
												<DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER StartColor="221, 236, 254" EndColor="132, 171, 227"></DXWDC:LOOKANDFEELSTYLEGRADIENTFILTER>
											</FILTERS>
										</BUTTONSTYLE>
									</LOOKANDFEEL>
                                    <dx:aspxbutton id="cmdUpdate" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                        csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                        text="Update"></dx:aspxbutton>
								</td>
							</tr>
						</table>
					</td>
				</tr>
			</table>
</asp:content>