'Title: DataAccess
'Created by: <PERSON> on 4/30/02
'Purpose:  To get data from SQL stored procedures
'Assumptions:  None
'Effects:  Database actions
'Inputs:  All defined per function
'Returns:  DataSets and SQLDataReaders
Imports GroupManager.[Global]
Imports System.Data
Imports System.Data.SqlClient


Namespace GroupManager

Public Class GroupManager_DB
    Inherits System.Web.UI.Page


    Dim myConnection As SqlConnection
    Dim myCommand As SqlCommand




    'Title: Get Owners and Authorizers
    'Created by: <PERSON> on 4/30/02
    'Purpose:  Get Owners and Authorizers by the group name
    'Assumptions:  None
    'Effects:  Select Info only
    'Inputs:  The Stored Procedure and Group name the query is based on
    'Returns:  A SQLDataReader
    Function GetSQLDataREader(ByVal StoredProc As String, ByVal strQuery As String, ByVal param As String) As SqlDataReader

        Dim dr As SqlDataReader
        Dim sqlConnection As SqlConnection
        Dim sqlCommand As SqlCommand

        sqlConnection = New SqlConnection(gmConnectionString)
        sqlCommand = New SqlCommand(StoredProc, sqlConnection)
        sqlCommand.CommandType = CommandType.StoredProcedure

        sqlCommand.Parameters.Add(New SqlParameter(param, SqlDbType.VarChar, 150))
        sqlCommand.Parameters(param).Value = strQuery

        sqlConnection.Open()
        dr = sqlCommand.ExecuteReader(CommandBehavior.CloseConnection)

        Return dr
    End Function

    'Title: Get Owners and Authorizers
    'Created by: Shane Z Smith on 4/30/02
    'Purpose:  Get Owners and Authorizers by the group name
    'Assumptions:  None
    'Effects:  Select Info only
    'Inputs:  The Stored Procedure and Group name the query is based on
    'Returns:  A SQLDataReader
    Function GetSQLDataREader(ByVal StoredProc As String) As SqlDataReader

        Dim dr As SqlDataReader
        Dim sqlConnection As SqlConnection
        Dim sqlCommand As SqlCommand

        sqlConnection = New SqlConnection(gmConnectionString)
        sqlCommand = New SqlCommand(StoredProc, sqlConnection)
        sqlCommand.CommandType = CommandType.StoredProcedure

        sqlConnection.Open()
        dr = sqlCommand.ExecuteReader(CommandBehavior.CloseConnection)

        Return dr
    End Function



    Function GetDataReaderByStringId(ByVal SQLText As String, ByVal Conn As String) As SqlDataReader

        Dim dr As SqlDataReader

        myConnection = New SqlConnection(Conn)
        myCommand = New SqlCommand(SQLText, myConnection)
        myCommand.CommandType = CommandType.Text

        myConnection.Open()
        dr = myCommand.ExecuteReader()

        Return dr

    End Function

    Sub CloseConnections()
        Try
            myConnection.Close()
            myCommand.Connection.Close()
        Finally
            myConnection = Nothing
            myCommand = Nothing
        End Try
    End Sub

    'Title: Get Owners and Authorizers
    'Created by: Shane Z Smith on 4/30/02
    'Purpose:  Get a dataset based on the stored procedure, query string and paramaters in stored procedure
    'Assumptions:  None
    'Effects:  Select Info only
    'Inputs:  The Stored Procedure, strGroup (string query is querying on), and param(stored procedure input name)
    'Returns:  A DataSet
    Function GetDataSetByID(ByVal StoredProc As String, ByVal strQuery As String, ByVal param As String) As DataSet

        Dim DS As DataSet
        Dim dsCommand As SqlDataAdapter
        Dim dsCon As SqlConnection

        dsCon = New SqlConnection(gmConnectionString)

        dsCommand = New SqlDataAdapter(StoredProc, dsCon)
        dsCommand.SelectCommand.CommandType = CommandType.StoredProcedure
        'Create and add a parameter to Parameters collection for the stored procedure.
        dsCommand.SelectCommand.Parameters.Add(New SqlParameter(param, SqlDbType.VarChar, 150))

        dsCommand.SelectCommand.Parameters(param).Value = strQuery

        dsCommand.SelectCommand.Connection.Open()
        dsCommand.SelectCommand.ExecuteNonQuery()

        DS = New DataSet
        dsCommand.Fill(DS, "dsTable")


        dsCon.Close()
        dsCommand.SelectCommand.Connection.Close()

        Return DS

    End Function

    Sub DBUpdByStringId(ByVal SQLText As String, ByVal Conn As String)

        Dim myConnection As SqlConnection
        Dim myCommand As SqlCommand

        myConnection = New SqlConnection(Conn)
        myCommand = New SqlCommand(SQLText, myConnection)
        myCommand.CommandType = CommandType.Text

        myConnection.Open()
        myCommand.ExecuteReader()

        myConnection.Close()
        myCommand.Connection.Close()
        myConnection = Nothing
        myCommand = Nothing

    End Sub

End Class

End Namespace
