Imports DevExpress.Web.ASPxGrid
Imports DevExpress.Web.ASPxDataControls
Imports System.Data
Imports System.Collections
Imports System.Collections.Generic
Imports System.IO
Imports System.DirectoryServices


Namespace AccountManager

    Partial Class UpdateBackup
        Inherits System.Web.UI.Page

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles Me.Load
            lblErrorMessage.Text = ""
            lblMessage.Text = ""
            'lblMessage0.Text = lblMessage0.Text & "1"
            If Session("Accounts") = "" Then
                pOwners.Visible = False
                pBckUpd.Visible = False
                chkExpand.Visible = False
                grdOwners.Visible = False
                Me.lblErrorMessage.Text = "Your session has expired. Please click on My Accounts on the left menu to begin again."
            Else
                If Not Page.IsPostBack Then
                    Dim hlpFill As New Helper
                    hlpFill.FillAccessDropDown(ddlAddAccess)
                    Session("updatebackup.aspx.vb") = ""
                    LoadOwnersForGroups(Session("Accounts"), False)
                    chkDelAll.Checked = True
                    chkDelAll.Visible = False
                    grdOwners.ExpandAll()
                    If Trim(Session("NotOwnDel")) <> "" Then
                        UserMsgBox("You selected accounts you are not listed as the owner or delegate.  You will not be able to manage these accounts. " & Session("NotOwnDel"))
                        Session("NotOwnDel") = ""
                    End If
                Else
                    Dim eTarget As String = GetPostBackControl(Page)
                    If Not (eTarget.Contains("cmdDelUser") Or eTarget.Contains("cmdAddUser")) Then
                        LoadOwnersForGroups(Session("Accounts"), False)
                    End If

                End If
            End If



        End Sub
'change By jasmine --show User Display name from AD . this function makes AD connection and return display name :- CHG0983304
        Function getUserDisplayName(ByVal strUserID As String) As String
            Dim dEntry As DirectoryEntry = New DirectoryEntry("LDAP://DC=KCC,DC=COM")
            Dim dSearcher As DirectorySearcher = New DirectorySearcher(dEntry)
            Dim result As SearchResult

            dSearcher.ReferralChasing = &H40
            dSearcher.PageSize = 2000
            dSearcher.SearchScope = 2
            Dim retDisplayName As String = ""
            dSearcher.Filter = "(&(objectClass=user)(cn= " & strUserID & "))"
            dSearcher.PropertiesToLoad.Add("displayName")
            Try
                For Each result In dSearcher.FindAll
                    If Not result Is Nothing Then
                        With result.GetDirectoryEntry.Properties
                            Dim strObjLDAP As String = result.GetDirectoryEntry.Path
                            retDisplayName = .Item("displayName").Value
                        End With
                    End If
                Next
            Catch ex As Exception
                Console.WriteLine("Error occurred while getting the User details: " & ex.Message)
            End Try
            Return retDisplayName
        End Function
        'end change jasmine

        Public Function GetPostBackControl(ByVal page As Page) As String
            Dim postbackControlInstance As Control = DirectCast(Nothing, Control)
            Dim postbackControlName As String = page.Request.Params.Get("__EVENTTARGET")

            If Not Equals(postbackControlName, Nothing) AndAlso Not Equals(postbackControlName, String.Empty) Then
                postbackControlInstance = page.FindControl(postbackControlName)
            Else
                ' handle the Button control postbacks

                For i As Integer = 0 To page.Request.Form.Keys.Count - 1
                    postbackControlInstance = page.FindControl(page.Request.Form.Keys(i))
                    'Try
                    '    lblMessage0.Text = lblMessage0.Text & postbackControlInstance.ID & postbackControlInstance.GetType().ToString & ","
                    'Catch ex As Exception

                    'End Try

                    If TypeOf postbackControlInstance Is DevExpress.Web.ASPxEditors.ASPxButton Then

                        Return postbackControlInstance.ID
                    End If
                Next
            End If

            If postbackControlInstance Is Nothing Then
                Return ""
            Else
                Return postbackControlInstance.ID
            End If

        End Function

        Function CheckNumberOfBackups(ByVal strValues As String(), ByVal intItemCheck As Integer, ByVal blnBoth As Boolean) As Boolean

            Dim usrcur As UserInfo = UserInfo.GetSessionUserList(False)
            Dim intIndex As Integer = 0

            'ddlAddBackup.Items.Clear()
            CheckNumberOfBackups = True

            Do While intIndex < strValues.Length
                Trace.Warn("Inside checknumbak: " & strValues(intIndex))
                Trace.Warn("blnBoth: " & blnBoth)
                If strValues(intIndex) <> "" Then

                    If blnBoth Then
                        Trace.Warn("top usrcur.DelegateCount(strValues(intIndex), delegate): " & usrcur.DelegateCount(strValues(intIndex), "delegate"))
                        Trace.Warn("top back count" & usrcur.DelegateCount(strValues(intIndex), "backup"))
                        Trace.Warn(intItemCheck)
                        If usrcur.DelegateCount(strValues(intIndex), "delegate") < intItemCheck And usrcur.DelegateCount(strValues(intIndex), "backup") < intItemCheck Then

                            'If Not ddlAddBackup.Items.IndexOf(Me.ddlAddBackup.Items.FindByValue(strValues(intIndex))) > -1 Then
                            '    Me.ddlAddBackup.Items.Add(strValues(intIndex))
                            'End If

                            CheckNumberOfBackups = False

                        End If
                    Else
                        Trace.Warn(" usrcur.DelegateCount(strValues(intIndex), delegate): " & usrcur.DelegateCount(strValues(intIndex), "delegate"))
                        Trace.Warn("back count" & usrcur.DelegateCount(strValues(intIndex), "backup"))
                        Trace.Warn(intItemCheck)
                        If usrcur.DelegateCount(strValues(intIndex), "delegate") < intItemCheck Or usrcur.DelegateCount(strValues(intIndex), "backup") < intItemCheck Then

                            'Trace.Warn("index of " & ddlAddBackup.Items.IndexOf(Me.ddlAddBackup.Items.FindByValue(strValues(intIndex))))
                            If usrcur.DelegateCount(strValues(intIndex), "backup") < intItemCheck + 1 And usrcur.DelegateCount(strValues(intIndex), "delegate") < intItemCheck + 1 Then
                                '    If Not ddlAddBackup.Items.IndexOf(Me.ddlAddBackup.Items.FindByValue(strValues(intIndex))) > -1 Then
                                '        Me.ddlAddBackup.Items.Add(strValues(intIndex))
                                '    End If

                                CheckNumberOfBackups = False
                            End If

                        End If
                    End If

                End If
                intIndex = intIndex + 1
            Loop

            'If ddlAddBackup.Items.Count > 0 Then
            '    lblAddBackup.Visible = True
            '    Me.rdoBackups.Visible = True
            '    AccordionPane3.Visible = True
            '    ddlAddBackup.Visible = True
            'Else
            '    Me.rdoSelected.Checked = True
            '    Me.rdoBackups.Visible = False
            '    AccordionPane3.Visible = False
            '    Me.ddlAddBackup.Visible = False

            'End If

            Trace.Warn("anser CheckNumberOfBackups: " & CheckNumberOfBackups)
            usrcur = Nothing
        End Function

        Sub LoadOwnersForGroups(ByVal strAccounts As String, ByVal blnForce As Boolean)

            Dim strSQL As String
            Dim intIndex As Integer = 0
            Dim strValues As String
            strValues = strAccounts.Trim(";", " ")
            strValues = "'" & strAccounts.Replace(";", "','") & "'"

            'strValues = Split(strAccounts, ";")
            strSQL = "SELECT ID,AccountID, UserNameDN + ' (' + UserName + ')' as [User], Access,UserNameDN,UserName,Type FROM vUsr WHERE AccountID IN  (" & strValues & ") Order By AccountID,Access DESC"
            'lblAddBackup.Text = ""
            If strValues = "" Then
                pOwners.Visible = False
                grdOwners.Visible = False
            Else
                Dim dbGet As New DataAccess
                Dim dtOwners As DataTable
                dtOwners = dbGet.GetBackupByAccountID(strSQL)
 'change(CHG0983304) by jasmine --UserNameDN, manipulating the datatable rows
                For Each Row As DataRow In dtOwners.Rows
                    Dim oldusernamedn As String = Row("UserNameDn").ToString()
                    Dim userido As String = Row("UserName").ToString()
                    Dim newusernamedn As String = getUserDisplayName(userido.Trim())
                    Row("UserNameDN") = newusernamedn
                    Row("User") = newusernamedn & "(" & Row("UserName") & ")"
                Next
                'change end by jasmine --UserNameDN
                If dtOwners.Rows.Count > 0 Then
                    grdOwners.KeyFieldName = "ID"
                    grdOwners.DataSource = dtOwners
                    grdOwners.DataBind()

                    'Dim dv As DataView = dtOwners.DefaultView
                    'dv.RowFilter = "Type=2"
                    Dim htBackup As New Hashtable
                    Dim BackupList() As DataRow = dtOwners.Select("Type=2", "UserName")

                    For Each dtrow As DataRow In BackupList
                        Dim strAccountID As String = dtrow("AccountID")
                        Dim strUserName As String = dtrow("UserName")
                        If strAccountID <> "" Then
                            If Not htBackup.ContainsKey(strUserName) Then
                                htBackup.Add(strUserName, strUserName & ";" & strAccountID)
                            Else
                                Dim strValue As String = htBackup.Item(strUserName)
                                strValue = strValue & ";" & strAccountID
                                htBackup.Item(strUserName) = strValue
                            End If
                        End If
                    Next
                    ddlBackupList.Items.Clear()
                    ddlBackupList.DataSource = htBackup
                    ddlBackupList.DataTextField = "key"
                    ddlBackupList.DataValueField = "value"

                    ddlBackupList.DataBind()
                    ddlBackupList.Items.Insert(0, New ListItem("-Select Backup-", "0"))
                    If ddlAddAccess.Items.Count > 1 Then
                        Dim loginUser As String = DataAccess.getManager
                        Dim OwnerList() As DataRow = dtOwners.Select("Type=1 And UserName='" & loginUser & "'")
                        If OwnerList.Length = 0 Then
                            Try
                                ddlAddAccess.Items.Remove(ddlAddAccess.Items.FindByValue("3"))
                            Catch ex As Exception

                            End Try
                        End If
                    End If
                Else
                    grdOwners.Visible = False
                End If
                dbGet = Nothing
            End If
        End Sub

        Function DelBackups() As Boolean

            Dim strUserId As String
            Dim alUsersNotAdded As New ArrayList
            Dim strEmailBody As String = ""

            Dim alChanges As New ArrayList

            Dim strAccountforNoExist As String = ""
            Dim strAccountforNoAccess As String = ""
            Dim strAccountforChange As String = ""
            Dim strAccountforError As String = ""
            Dim strAccountforLessNoOfUser As String = ""
            Dim IsUserNoExist As Boolean = False
            Dim IsUserNoAccess As Boolean = False
            Dim IsAccountforChange As Boolean = False
            Dim IsAccountforError As Boolean = False
            Dim IsUserLessNoOfUser As Boolean = False

            DelBackups = False
            Dim dbDel As New DataAccess
            strUserId = ddlBackupList.SelectedItem.Text

            'Dim strAccounts As String = ddlBackupList.SelectedItem.Text
            Dim strLoginID As String = DataAccess.getManager
            Dim strAccounts() As String = Split(ddlBackupList.SelectedValue, ";")
            Dim i As Integer
            Dim intAccountCount As Integer = strAccounts.Length
            If intAccountCount > 1 Then
                For i = 1 To intAccountCount - 1
                    'For Each strAccountID As String In strAccounts.Split(";")
                    Dim strAccountID As String = strAccounts(i)
                    If strAccountID <> "" And strUserId <> strAccountID Then
                        Dim dtAccess As DataTable = dbDel.CheckTypeforLoginID_UserID(strAccountID, strUserId, strLoginID)
                        Dim intUserType As Integer = dtAccess.Rows(0)("UserType")
                        Dim intLoginType As Integer = dtAccess.Rows(0)("LoginType")
                        Dim intRowCounts As Integer = dtAccess.Rows(0)("CountsRow")
                        If intUserType <> 2 Then
                            IsUserNoExist = True
                            strAccountforNoExist = strAccountforNoExist & strAccountID & ","
                        ElseIf intLoginType <> 1 And intLoginType <> 3 Then
                            IsUserNoAccess = True
                            strAccountforNoAccess = strAccountforNoAccess & strAccountID & ","
                        ElseIf intRowCounts < 2 Then
                            IsUserLessNoOfUser = True
                            strAccountforLessNoOfUser = strAccountforLessNoOfUser & strAccountID & ","
                        Else
                            Dim strComments As String = String.Format("Deleted Backup:{0}, Comments:{1}", strUserId, txtDelComments.Text)
                            If dbDel.DeleteUMBackupWithLog(strAccountID, strUserId, strLoginID, strComments) Then
                                strAccountforChange = strAccountforChange & strAccountID & ","
                                IsAccountforChange = True
                            Else
                                strAccountforError = strAccountforError & strAccountID & ","
                                IsAccountforError = True
                            End If
                        End If
                    End If
                Next i
            Else
                lblErrorMessage.Text = "No Account Selected."
            End If
            

            If IsAccountforChange Then
                lblMessage.Text = String.Format("{0} has been successfully deleted as Backup from selected account - {1}", strUserId, strAccountforChange.Trim(","))
            End If

            Dim strErrorMsg As String = ""

            If IsUserNoAccess Then
                strErrorMsg = String.Format("{0}You do not have required access for Account - {1}<br/>", strErrorMsg, strAccountforNoAccess.Trim(","))
            End If
            If IsUserLessNoOfUser Then
                strErrorMsg = String.Format("{0}Every account must have one backup or one delegate. First add a backup or delegate before deleting backup from Account - {1}<br/>", strErrorMsg, strAccountforLessNoOfUser.Trim(","))
            End If
            If IsAccountforError Then
                strErrorMsg = String.Format("{0}Error occurred while deleting {1} as backup for Account - {2}<br/>", strErrorMsg, strUserId, strAccountforError.Trim(","))
            End If

            strErrorMsg = strErrorMsg.Trim("<br/>")
            If strErrorMsg <> "" Then
                lblErrorMessage.Text = strErrorMsg
            End If

            txtDelComments.Text = ""
            LoadOwnersForGroups(Session("Accounts"), True)
        End Function

        'Function DelBackups() As Boolean

        '    Dim strUserId, strBackupID, strAccess As String
        '    Dim alUsersNotAdded As New ArrayList
        '    Dim strEmailBody As String = ""
        '    Dim i As Integer
        '    Dim alChanges As New ArrayList
        '    'Dim strwriter As StreamWriter
        '    'strwriter = File.AppendText("C:\AMTRACE.LOG")

        '    DelBackups = False
        '    lblMessage.Text = ""
        '    lblNoDeleteOwner.Text = ""

        '    'strwriter.WriteLine("TEST")
        '    'strwriter.Flush()
        '    If Me.chkDelAll.Checked Then

        '        If Me.txtDelUser.Text <> "" Then

        '            Dim strValues As String()
        '            strValues = Split(GetListToUpdate("delete"), ";")

        '            Dim intDelIndex As Integer = 0

        '            Do While intDelIndex < strValues.Length

        '                If strValues(intDelIndex) <> "" Then
        '                    Dim strBackupValues As String()
        '                    Dim amDetails As New Account(strValues(intDelIndex))

        '                    If amDetails.GetDetails(Me.txtDelUser.Text) Then

        '                        If LCase(amDetails.Access) = "owner" Then
        '                            If lblNoDeleteOwner.Text = "" Then
        '                                lblNoDeleteOwner.Text = "You can not delete owners.  No action was taken for the following accounts: " & strValues(intDelIndex)
        '                            Else
        '                                lblNoDeleteOwner.Text = lblNoDeleteOwner.Text & "," & strValues(intDelIndex)
        '                            End If
        '                        ElseIf LCase(amDetails.Access) = "delegate" Then
        '                            If lblNoDeleteOwner.Text = "" Then
        '                                lblNoDeleteOwner.Text = "You can not delete delegate.  No action was taken for the following accounts: " & strValues(intDelIndex)
        '                            Else
        '                                lblNoDeleteOwner.Text = lblNoDeleteOwner.Text & "," & strValues(intDelIndex)
        '                            End If
        '                        Else

        '                            strBackupValues = Split(strValues(intDelIndex), ";")
        '                            Trace.Warn("before check num backups inside delete1")
        '                            If CheckNumberOfBackups(strBackupValues, 1, False) Then
        '                                Dim dbDel As New DataAccess
        '                                Dim hlpDel As New Helper

        '                                dbDel.UpdateDBByStringId("sp_UMOwner_Delete '" & strValues(intDelIndex) & "','" & Me.txtDelUser.Text & "'")
        '                                hlpDel.InsertLog(strValues(intDelIndex), [Global].GblActOwnDel, Now, "Deleted " & amDetails.Access & ": " & Me.txtDelUser.Text & " Comments: " & Me.txtDelComments.Text)

        '                                dbDel = Nothing
        '                                hlpDel = Nothing
        '                                lblNewOwner.Text = Me.txtDelUser.Text & " Deleted From All Accounts Listed"
        '                                DelBackups = True
        '                                alChanges.Add(strValues(intDelIndex))
        '                            Else
        '                                alUsersNotAdded.Add(strValues(intDelIndex))
        '                            End If
        '                        End If
        '                        amDetails = Nothing

        '                    Else
        '                        Trace.Warn("Ignored user: " & strValues(intDelIndex))
        '                    End If

        '                End If
        '                intDelIndex = intDelIndex + 1
        '            Loop

        '            If DelBackups Then
        '                Dim usrDel As New UserInfo(Me.txtDelUser.Text)
        '                Dim emOwners As New Email
        '                Dim strAction As String

        '                If usrDel.UserFound Then
        '                    strAction = usrDel.UserNameandID & " deleted from the following accounts"
        '                Else
        '                    strAction = Me.txtDelUser.Text & " deleted from the following accounts"
        '                End If

        '                emOwners.SendAccessChange("Backup/Delegate Changes", alChanges, Me.txtDelComments.Text, strAction)

        '                txtNewOwnerComments.Text = ""
        '                Me.txtDelUser.Text = ""
        '                emOwners = Nothing
        '                usrDel = Nothing
        '            End If
        '        Else
        '            DelBackups = False
        '        End If
        '    Else
        '        lblNewOwner.Text = ""
        '        Dim cnt As Integer = 0
        '        ' For i = 0 To (grdOwners.GetSelectedRowCount()) - 
        '        Dim fieldNames As List(Of String) = New List(Of String)()
        '        fieldNames.Add("AccountID")
        '        fieldNames.Add("UserName")
        '        fieldNames.Add("Access")
        '        Dim List As Object
        '        List = grdOwners.GetSelectedFieldValues(fieldNames.ToArray)
        '        For Each item As Object() In List
        '            For Each value As Object In item
        '                If i = 0 Then
        '                    strUserId = value.ToString()
        '                End If
        '                If i = 1 Then
        '                    strBackupID = value.ToString()
        '                End If
        '                If i = 2 Then
        '                    strAccess = value.ToString()
        '                End If
        '                i = i + 1
        '            Next value
        '            'strwriter.WriteLine(strUserId)
        '            'strwriter.WriteLine(strBackupID)
        '            'strwriter.WriteLine(strAccess)
        '            'strwriter.Flush()
        '            Trace.Warn("strUserID: " & strUserId)

        '            'Dim strValues As String()
        '            'strValues = Split(strUserId, ";")

        '            If LCase(strAccess) = "owner" Then

        '                If lblNoDeleteOwner.Text = "" Then
        '                    lblNoDeleteOwner.Text = "You can not delete owners.  No action was taken for the following accounts: " & strUserId
        '                Else
        '                    lblNoDeleteOwner.Text = lblNoDeleteOwner.Text & ", " & strUserId
        '                End If

        '            Else
        '                Trace.Warn("before check num backups inside delete2")
        '                'If CheckNumberOfBackups(strUserId, 1, False) Then

        '                Dim hlpDel As New Helper
        '                Dim dbDel As New DataAccess

        '                alChanges.Add(strUserId)

        '                strEmailBody = "<tr><td valign=top>" & strUserId & "</td><td valign=top>" & strBackupID & "</td> </tr><BR> " & strEmailBody
        '                dbDel.UpdateDBByStringId("sp_UMOwner_Delete '" & strUserId & "','" & strBackupID & "'")
        '                hlpDel.InsertLog(strUserId, [Global].GblActOwnDel, Now, "Deleted " & strAccess & ": " & strBackupID & " Comments: " & Me.txtDelComments.Text)

        '                lblNewOwner.Text = strAccess & " " & strBackupID & " deleted from " & strUserId & "<BR>" & lblNewOwner.Text

        '                Dim emOwners As New Email
        '                'emOwners.SendAccessChange("Deleted " & strAccess & " " & strBackupID & " from " & strUserId, alChanges, Me.txtDelComments.Text, "Backup/Delegate Changes", strBackupID)

        '                DelBackups = True
        '                emOwners = Nothing
        '                dbDel = Nothing
        '                hlpDel = Nothing
        '                alChanges.Clear()
        '                ' Else
        '                'alUsersNotAdded.Add(strUserId)
        '                'End If
        '            End If
        '            'End If
        '        Next

        '        If DelBackups Then
        '            txtDelComments.Text = ""
        '            Me.txtDelUser.Text = ""
        '        End If
        '    End If

        '    Dim intIndex As Integer

        '    If Not DelBackups Then
        '        If Me.chkDelAll.Checked Then
        '            If Me.txtDelUser.Text <> "" Then
        '                'lblNewOwner.Text = "No backups or delegates were deleted because the user " & Me.txtDelUser.Text & " was not found for any accounts listed."
        '                lblNewOwner.Text = "No backup was deleted because the user " & Me.txtDelUser.Text & " was not found as backup for this account."
        '            Else
        '                lblNewOwner.Text = "When deleting a user from all accounts listed you must enter a User ID in the text area."
        '            End If

        '        Else
        '            If Not alUsersNotAdded.Count > 0 Then
        '                lblNewOwner.Text = "No backups or delegates were deleted because none were selected."
        '            End If
        '        End If
        '    End If

        '    If alUsersNotAdded.Count > 0 Then
        '        lblMessage.Text = "Every account must have one backup or one delegate.  First add a backup or delegate before deleting users for the following accounts: <br/>"
        '        intIndex = 0
        '        Do While intIndex < alUsersNotAdded.Count
        '            lblMessage.Text = lblMessage.Text & alUsersNotAdded(intIndex) & "<br/>"
        '            intIndex = intIndex + 1
        '        Loop
        '    End If
        '    txtNewOwnerComments.Text = ""
        '    LoadOwnersForGroups(Session("Accounts"), True)
        '    alChanges = Nothing
        '    'strwriter.Close()
        '    'strwriter = Nothing

        'End Function

        Function GetListToUpdate(ByVal Type As String) As String

            GetListToUpdate = ""
            Dim blnAll As Boolean

            If LCase(Type) = "add" Then
                blnAll = Me.rdoAddAll.Checked
            Else
                blnAll = Me.chkDelAll.Checked
            End If

            If blnAll Then
                GetListToUpdate = Session("Accounts")
            Else

                '    If Me.rdoSelected.Checked Then
                '        'GET HIGHLIGHTED ROWS
                '        'Dim i As Integer
                '        'For i = 0 To (grdOwners.GetSelectedRowCount()) - 1

                '        '    Dim selectedRow As Row = grdOwners.GetSelectedRow(i)
                '        '    Dim intIndex As Integer = 0

                '        '    GetListToUpdate = selectedRow.DataControllerRow("AccountID").ToString() & ";" & GetListToUpdate
                '        'Next i

                '        Dim List As Object
                '        List = grdOwners.GetSelectedFieldValues("AccountID")
                '        For Each Account As Object In List
                '            GetListToUpdate = Account.ToString & ";" & GetListToUpdate
                '        Next


                '    ElseIf Me.rdoBackups.Checked Then

                '        '    'GET LIST FROM THE BACKUP DROPDOWN.  All accounts that are missing backups/delegates
                '        Dim i As Integer
                '        For i = 0 To (Me.ddlAddBackup.Items.Count) - 1

                '            Dim intIndex As Integer = 0

                '            GetListToUpdate = ddlAddBackup.Items(i).Value & ";" & GetListToUpdate
                '        Next i

                '    ElseIf Me.rdoDelegates.Checked Then

                '        '    'GET LIST FROM DELEGATE DROPDOWN.  
                '        '    'All Accounts that were previously attempted to be updated but failed because a delegate already exists.
                '        Dim i As Integer
                '        For i = 0 To (Me.ddlDelegates.Items.Count) - 1

                '            Dim intIndex As Integer = 0

                '            GetListToUpdate = ddlDelegates.Items(i).Value & ";" & GetListToUpdate
                '        Next i
                '    End If

            End If

            Trace.Warn("getlisttoupdate: " & GetListToUpdate)

            Return GetListToUpdate
        End Function

        Sub AddBackups(ByRef usrNew As UserInfo, ByVal strLoginID As String)
            Dim strAccountID As String
            Dim i As Integer
            Dim blnLoad As Boolean = True
            Dim hlpClean As New Helper
            Dim alChanges As New ArrayList
            Dim strOwnerMessage As String = ""
            Dim strStdMessage As String = ""
            Dim strAccountforAlreadyExist As String = ""
            Dim strAccountforNoAccess As String = ""
            Dim strAccountforChange As String = ""
            Dim strAccountforError As String = ""
            Dim IsUserAlreadyExist As Boolean = False
            Dim IsUserNoAccess As Boolean = False
            Dim IsAccountforChange As Boolean = False
            Dim IsAccountforError As Boolean = False
            Dim strValues As String()
            Dim intAccess As Integer = ddlAddAccess.SelectedValue
            Dim strAccess As String = ddlAddAccess.SelectedItem.Text
            strValues = Split(GetListToUpdate("add"), ";")
            For i = 0 To (strValues.Length) - 1
                Dim dbInsert As New DataAccess
                strAccountID = strValues(i)
                If strAccountID <> "" Then
                    Dim dtAccess As DataTable = dbInsert.CheckTypeforLoginID_UserID(strAccountID, usrNew.GetUserID, strLoginID)
                    Dim intUserType As Integer = dtAccess.Rows(0)("UserType")
                    Dim intLoginType As Integer = dtAccess.Rows(0)("LoginType")
                    
                    Dim IsAlreadyExist As Boolean = False

                    If intUserType > 0 Then
                        If intAccess = 2 Then
                            IsAlreadyExist = True
                        Else
                            If (intUserType = 3 Or intUserType = 1) And intAccess = 3 Then
                                IsAlreadyExist = True
                            End If
                        End If
                    End If

                    If IsAlreadyExist Then
                        strAccountforAlreadyExist = strAccountforAlreadyExist & strAccountID & ","
                        IsUserAlreadyExist = True
                    ElseIf intLoginType <> 1 And intAccess = 3 Then
                        strAccountforNoAccess = strAccountforNoAccess & strAccountID & ","
                        IsUserNoAccess = True
                    Else
                        If dbInsert.AddUMOwnerDelegateBackup(strAccountID, usrNew.GetUserID, usrNew.DisplayName, intAccess, strLoginID, txtNewOwnerComments.Text.Trim) Then
                            strAccountforChange = strAccountforChange & strAccountID & ","
                            IsAccountforChange = True
                        Else
                            strAccountforError = strAccountforError & strAccountID & ","
                            IsAccountforError = True
                        End If
                    End If
                End If
            Next i

            If IsAccountforChange Then
                lblMessage.Text = String.Format("{0} has been successfully added as {1} to selected account - {2}", usrNew.GetUserID, strAccess, strAccountforChange.Trim(","))
            End If

            Dim strErrorMsg As String = ""
            If IsUserAlreadyExist Then
                strErrorMsg = String.Format("{0}User is already assigned either as Delegate or Backup for Account - {1}<br/>", strErrorMsg, strAccountforAlreadyExist.Trim(","))
            End If
            If IsUserNoAccess Then
                strErrorMsg = String.Format("{0}You do not have required access for Account - {1}<br/>", strErrorMsg, strAccountforNoAccess.Trim(","))
            End If
            If IsAccountforError Then
                strErrorMsg = String.Format("{0}Error occurred while adding {1} as {2} for Account - {3}<br/>", strErrorMsg, usrNew.GetUserID, strAccess, strAccountforError.Trim(","))
            End If
            strErrorMsg = strErrorMsg.Trim("<br/>")
            If strErrorMsg <> "" Then
                lblErrorMessage.Text = strErrorMsg
            End If
            LoadOwnersForGroups(Session("Accounts"), True)
            txtAddUser.Text = ""
            txtNewOwnerComments.Text = ""
            'Me.lblMessage.Text = strStdMessage & "<BR><BR>" & strOwnerMessage

            'If blnLoad Then
            '    If alChanges.Count > 0 Then

            '        Dim emOwners As New Email
            '        emOwners.SendAccessChange(usrNew.DisplayName & " Added as " & Me.ddlAddAccess.SelectedItem.Text & " to Production Accounts", alChanges, Me.txtNewOwnerComments.Text, usrNew.UserNameandID & " added as " & ddlAddAccess.SelectedItem.Text & " to the following accounts")

            '        txtNewOwnerComments.Text = ""
            '        Me.txtAddUser.Text = ""
            '        emOwners = Nothing
            '        Me.lblMessage.Text = "Updated."
            '    Else
            '        Me.lblMessage.Text = Me.lblMessage.Text & "<BR>No accounts were updated and no emails were sent out. /  Secondary IDs are not allowed Please add normal ID."
            '    End If

            'End If

            'alChanges = Nothing

        End Sub

        Sub AddBackups()

            Dim usrNew As New UserInfo(Me.txtAddUser.Text, True)
            Dim usrCur As UserInfo = UserInfo.GetSessionUserList(False)

            
            If usrNew.GetUserID = usrCur.GetUserID Then
                lblErrorMessage.Text = "<Font Color=Red size=+1>You can not add yourself as the " & Me.ddlAddAccess.SelectedItem.Text & " of an account. You are listed as the owner or delegate.</font>"
            Else

                Dim strAccountID, strSQL As String
                Dim i As Integer
                Dim blnLoad As Boolean = True
                Dim hlpClean As New Helper
                Dim alChanges As New ArrayList
                Dim strOwnerMessage As String = ""
                Dim strStdMessage As String = ""
                Dim alDelegateCount As New ArrayList

                Session("DelegateIDCount") = ""

                If usrNew.UserFound Then
                    Dim strValues As String()

                    strValues = Split(GetListToUpdate("add"), ";")
                    'Me.ddlDelegates.Items.Clear()

                    For i = 0 To (strValues.Length) - 1

                        Dim dbInsert As New DataAccess

                        strAccountID = strValues(i)
                        If strAccountID <> "" Then

                            If hlpClean.IsOwner(strAccountID, usrNew.GetUserID) Then

                                'User they are trying to add is the owner
                                If Trim(strOwnerMessage) <> "" Then
                                    If strOwnerMessage.IndexOf(strAccountID) = -1 Then
                                        strOwnerMessage = strOwnerMessage & ", " & strAccountID
                                    End If
                                Else
                                    strOwnerMessage = "<Font Color=Red>There was an issue adding the " & Me.ddlAddAccess.SelectedItem.Text & " to some of the accounts.  You can not add " & Me.txtAddUser.Text & " to the following account(s) because he/she is already the owner. </font>Account(s): " & strAccountID
                                End If
                                blnLoad = False

                            Else
                                Dim blnInsert As Boolean = True

                                If LCase(Me.ddlAddAccess.SelectedItem.Text) = "delegate" Then
                                    If usrCur.DelegateCount(strAccountID, "delegate") > 0 Then 'And Not Me.rdoDelegates.Checked Then
                                        'Me.ddlDelegates.Items.Add(strAccountID)
                                        blnInsert = False
                                    End If
                                End If

                                If Me.ddlAddAccess.SelectedValue = 2 Or Me.ddlAddAccess.SelectedValue = 3 Then
                                    Dim strUserID As String
                                    strUserID = txtAddUser.Text
                                    strUserID = UCase(Right(strUserID, 1))

                                    If strUserID = "A" Or strUserID = "I" Or strUserID = "S" Then
                                        'Not Allowed - MSG Display
                                        Me.lblMessage.Text = "Secondary Ids are not allowed Please add normal ID."
                                    Else

                                        If blnInsert Then
                                            strSQL = "sp_UMOwner_Add '" & strAccountID & "'," & Me.ddlAddAccess.SelectedValue & ",'" & usrNew.GetUserID & "','" & hlpClean.dbCleanUpString(usrNew.DisplayName) & "'"
                                            alChanges.Add(strAccountID)

                                            dbInsert.UpdateDBByStringId(strSQL)
                                            hlpClean.InsertLog(strAccountID, [Global].GblActBackupAdded, Now, "New " & Me.ddlAddAccess.SelectedItem.Text & ": " & usrNew.GetUserID & "  Comments:" & txtNewOwnerComments.Text)
                                        End If
                                    End If
                                End If

                                '    'If Me.ddlDelegates.Items.Count > 0 And alChanges.Count > 0 Then
                                '    strStdMessage = Me.ddlAddAccess.SelectedItem.Text & " added to some account(s).  Some accounts already have delegates"
                                '    Else
                                '    If alChanges.Count > 0 Then
                                '        strStdMessage = Me.ddlAddAccess.SelectedItem.Text & " added to account(s)."
                                '    End If
                                'End If

                            End If
                        End If
                    Next i
                    Session("DelegateIDCount") = alDelegateCount
                Else
                    strStdMessage = "User ID " & Me.txtAddUser.Text & " does not exist. Please check the ID and try again."
                End If

                LoadOwnersForGroups(Session("Accounts"), True)

                Me.lblMessage.Text = strStdMessage & "<BR><BR>" & strOwnerMessage


                'If Me.ddlDelegates.Items.Count > 0 Then
                '    lblDel.Text = "These accounts already have delegates.  Check Overwrite All Existing Delegates option and click Add to perform your requested changes."
                '    lblDel.Visible = True
                '    Me.rdoDelegates.Visible = True
                'Else
                '    lblDel.Visible = False
                '    Me.rdoDelegates.Visible = False
                '    If Me.rdoDelegates.Checked Then
                '        Me.rdoDelegates.Checked = False
                '        Me.rdoSelected.Checked = True
                '    End If
                'End If

                'Me.ddlDelegates.Visible = Me.rdoDelegates.Visible

                If blnLoad Then
                    If alChanges.Count > 0 Then

                        Dim emOwners As New Email
                        emOwners.SendAccessChange(usrNew.DisplayName & " Added as " & Me.ddlAddAccess.SelectedItem.Text & " to Production Accounts", alChanges, Me.txtNewOwnerComments.Text, usrNew.UserNameandID & " added as " & ddlAddAccess.SelectedItem.Text & " to the following accounts")

                        txtNewOwnerComments.Text = ""
                        Me.txtAddUser.Text = ""
                        emOwners = Nothing
                        Me.lblMessage.Text = "Updated."
                    Else
                        Me.lblMessage.Text = Me.lblMessage.Text & "<BR>No accounts were updated and no emails were sent out. /  Secondary IDs are not allowed Please add normal ID."
                    End If

                End If

                alChanges = Nothing
            End If



        End Sub

        Private Sub cmdDelUser_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdDelUser.Click
            If Session("Accounts") <> "" Then
                lblMessage.Text = ""
                lblErrorMessage.Text = ""
                If ddlBackupList.SelectedIndex = 0 Then
                    lblErrorMessage.Text = "You must select Backup."
                ElseIf txtDelComments.Text = "" Then
                    lblErrorMessage.Text = "You must enter some comments."
                Else
                    DelBackups()
                    grdOwners.CollapseAll()
                    grdOwners.ExpandAll()
                End If

                Me.Tabs.ActiveTab = Me.tbpDel
            End If
            

        End Sub

        Private Sub cmdAddUser_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdAddUser.Click
            If Session("Accounts") <> "" Then
                lblMessage.Text = ""
                lblErrorMessage.Text = ""
                If txtNewOwnerComments.Text = "" Then
                    'lblNewOwner.Text = "You must enter some comments why this person is being added as a " & Me.ddlAddAccess.SelectedItem.Text & "."
                    lblErrorMessage.Text = "You must enter some comments."
                Else
                    Dim strUserID As String = (txtAddUser.Text.Trim).ToUpper
                    If strUserID <> "" Then
                        Dim strLoginID As String = DataAccess.getManager()
                        If strUserID = strLoginID.ToUpper Then
                            lblErrorMessage.Text = "You can not add yourself as the " & ddlAddAccess.SelectedItem.Text & " of an account. You are listed as the owner or delegate."
                        Else
 'changes (CHG0983304)by Jasmine: disbaled users cannot be added as delegate/authroizer condition
                            Dim struserdisable As New UserInfo(strUserID)

                            If (struserdisable.Disabled) Then
                                lblErrorMessage.Text = "Only Enabled users can be added as a " & ddlAddAccess.SelectedItem.Text & "."

                            Else
                                'end changes by jasmine
                            Dim usrNew As New UserInfo(strUserID, True)
                            If usrNew.UserFound Then
                                If usrNew.UserEmployeeType IsNot Nothing Then
                                    Dim strEmployeeType As String = (usrNew.UserEmployeeType.ToString).ToUpper
                                    If strEmployeeType = "E" Or strEmployeeType = "C" Then
                                        AddBackups(usrNew, strLoginID)
                                    Else
                                        lblErrorMessage.Text = "Only users can be added as a " & ddlAddAccess.SelectedItem.Text & "."
                                    End If
                                Else
                                    lblErrorMessage.Text = "Only users can be added as a " & ddlAddAccess.SelectedItem.Text & "."
                                End If
                            Else
                                lblErrorMessage.Text = "User ID " & strUserID & " does not exist. Please check the ID and try again."
                            End If
                        End If
End If
                        grdOwners.CollapseAll()
                        grdOwners.ExpandAll()
                        'Dim usrLoad As UserInfo = UserInfo.GetSessionUserList(True)
                    Else
                        'Me.lblNewOwner.Text = "You must enter a user ID before clicking Add."
                        lblErrorMessage.Text = "You must enter User ID."
                    End If

                End If

                Me.Tabs.ActiveTab = Me.tpAddUser
            End If
        End Sub

        Public Sub UserMsgBox(ByVal sMsg As String)

            Dim sb As New StringBuilder()
            Dim oFormObject As System.Web.UI.Control

            sMsg = sMsg.Replace("'", "\'")
            sMsg = sMsg.Replace(Chr(34), "\" & Chr(34))
            sMsg = sMsg.Replace(vbCrLf, "\n")
            sMsg = "<script language=javascript>alert(""" & sMsg & """)</script>"

            sb = New StringBuilder()
            sb.Append(sMsg)

            For Each oFormObject In Me.Controls
                If TypeOf oFormObject Is HtmlForm Then
                    Exit For
                End If
            Next

            ' Add the javascript after the form object so that the 
            ' message doesn't appear on a blank screen.
            oFormObject.Controls.AddAt(oFormObject.Controls.Count, New LiteralControl(sb.ToString()))

        End Sub


        Protected Sub chkExpand_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles chkExpand.CheckedChanged
            If chkExpand.Checked Then
                grdOwners.ExpandAll()
            Else
                grdOwners.CollapseAll()
            End If
        End Sub

        Protected Sub cmdReturn_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmdReturn.Click
            Response.Redirect("/AccountManager/MyUsers.aspx")
        End Sub

    End Class

End Namespace


