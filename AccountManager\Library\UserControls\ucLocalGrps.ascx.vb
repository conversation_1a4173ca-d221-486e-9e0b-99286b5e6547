Imports System.Data
Namespace AccountManager

    Partial Class ucLocalGrps
        Inherits System.Web.UI.UserControl

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        End Sub

        Function LoadRequests() As String

            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader
            Dim strSQL As String

            strSQL = "SELECT ID,HOST,Name,AsMemberOf,JobRunTimeKEy as [Last Time Run] " & _
                            "FROM SA_Common_LocalUsers_DEFAULT WHERE " & _
                            "Name LIKE '%" & txtAccountId.Text & "%'"

            srRead = dbGet.GetDataReaderByStringId(strSQL, [Global].GblSauConn)

            If srRead.HasRows Then
                grdLocalGrps.DataKeyField = "ID"
                grdLocalGrps.DataSource = srRead
                grdLocalGrps.DataBind()
            Else
                lblMessage.CssClass = "LabelRed"
                lblMessage.Text = "Account not found listed specifically in any local groups on servers."
            End If

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()
            Return strSQL

        End Function

        Private Sub cmdGet_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdGet.Click
            LoadRequests()
        End Sub

        Private Sub cmdExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdExport.Click
            Dim prtReport As New Print([Global].GblSauConn)

            hlDownload.NavigateUrl = prtReport.CreateCSV(LoadRequests)
            hlDownload.Text = "Download File"
        End Sub
    End Class
End Namespace
