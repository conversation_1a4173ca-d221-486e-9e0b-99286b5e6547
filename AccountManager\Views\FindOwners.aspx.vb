Imports System.Data
Imports System.DirectoryServices

Namespace AccountManager

    Partial Class FindOwners
        Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub
        Protected WithEvents pUsers As System.Web.UI.WebControls.Panel


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load, Me.Load
            If Not Page.IsPostBack Then
                EnableControls(False)

                Dim hlpFill As New Helper
                hlpFill.FillAccessDropDown(Me.ddlFAccess, True)
                hlpFill.AddBlank(ddlFAccess)
                ApplyFilter()
                FindAccount()
            Else
                ApplyFilter()
                FindAccount()

            End If
        End Sub
'change By jasmine --show User Display name from AD . this function makes AD connection and return display name :- CHG0983304
        Function getUserDisplayName(ByVal strUserID As String) As String
            Dim dEntry As DirectoryEntry = New DirectoryEntry("LDAP://DC=KCC,DC=COM")
            Dim dSearcher As DirectorySearcher = New DirectorySearcher(dEntry)
            Dim result As SearchResult

            dSearcher.ReferralChasing = &H40
            dSearcher.PageSize = 2000
            dSearcher.SearchScope = 2
            Dim retDisplayName As String = ""
            dSearcher.Filter = "(&(objectClass=user)(cn= " & strUserID & "))"
            dSearcher.PropertiesToLoad.Add("displayName")
            Try
                For Each result In dSearcher.FindAll
                    If Not result Is Nothing Then
                        With result.GetDirectoryEntry.Properties
                            Dim strObjLDAP As String = result.GetDirectoryEntry.Path
                            retDisplayName = .Item("displayName").Value
                        End With
                    End If
                Next
            Catch ex As Exception
                Console.WriteLine("Error occurred while getting the User details: " & ex.Message)
            End Try
            Return retDisplayName
        End Function
        'end change jasmine


        Function ApplyFilter() As String

            Dim strWhere As String = ""
            Dim strNot As String = ""
            Dim strAND As String = ""
            Dim strSQL As String = ""

            'If Me.ddlOption.SelectedValue = 0 Then
            '    strNot = " NOT "
            'End If

            If Trim(Me.txtFAccount.Text) <> "" Then
                strWhere = strAND & " (" & strNot & " AccountID like '%" & Trim(Me.txtFAccount.Text) & "%') "
                strAND = " AND "
            End If

            If Trim(Me.txtOwnerID.Text) <> "" Then
                strWhere = " (" & strNot & " UserName like '%" & Trim(Me.txtOwnerID.Text) & "%') " & strAND & strWhere
                strAND = " AND "
            End If

            If Trim(Me.txtOwnerName.Text) <> "" Then
                strWhere = " (" & strNot & " UserNameDN like '%" & Trim(Me.txtOwnerName.Text) & "%') " & strAND & strWhere
                strAND = " AND "
            End If

            If Me.ddlFAccess.SelectedValue <> "" Then
                strWhere = " (" & strNot & " Access = '" & Me.ddlFAccess.SelectedItem.Text & "') " & strAND & strWhere
                strAND = " AND "
            End If

            If Me.chkPastDue.Checked Then
                strWhere = " (ExpirationDate < GETDATE() )" & strAND & strWhere
                strAND = " AND "
            End If

            If Trim(Me.txtFilterDesc.Text) <> "" Then
                strWhere = " (" & strNot & " Description like '%" & Trim(Me.txtFilterDesc.Text) & "%') " & strAND & strWhere
                strAND = " AND "
            End If

            If Trim(Me.txtFilterPurpose.Text) <> "" Then
                strWhere = " (" & strNot & " Purpose like '%" & Trim(Me.txtFilterPurpose.Text) & "%') " & strAND & strWhere
                strAND = " AND "
            End If

            If Trim(Me.txtFilterAppName.Text) <> "" Then
                strWhere = " (" & strNot & " AppName like '%" & Trim(Me.txtFilterAppName.Text) & "%') " & strAND & strWhere
                strAND = " AND "
            End If

            If strWhere <> "" Then
                strSQL = "SELECT Distinct ID,AccountID, ExpirationDate,Description,	UserName,UserNameDN,Access,ActDisabled,ActException,AppName,Purpose,LastNotified FROM vUsr WHERE " & strWhere & " Order by AccountID ASC,Access DESC"

            End If

            Trace.Warn(strSQL)

            Return strSQL

        End Function

        Sub FindAccount()

            Dim strSQL As String
           
            strSQL = ApplyFilter()

            If strSQL = "" Then
                lblMessage.Text = "You have to enter or modify one of the filter options before clicking search."
            Else

                Dim dbGet As New DataAccess
                Dim srRead As SqlClient.SqlDataReader

                srRead = dbGet.GetDataReaderByStringId(strSQL)
 'change (CHG0983304)start by jasmine --userdisplayname --creating the datatable and passing it in grid
                If srRead.HasRows Then
                    Dim dataTable As New DataTable
                    DataTable.Load(srRead)
                    For Each row As DataRow In DataTable.Rows
                        Dim username As String = row("username").ToString()
                        Dim displayName As String = getUserDisplayName(username)
                        row("UsernameDN") = displayName
                    Next
                    EnableControls(True)

                    grdusers.DataSource = dataTable

                    'change end by jasmine
                'If srRead.HasRows Then
                   ' EnableControls(True)
                    'grdUsers.DataKeyField = "ID"
                    'grdUsers.DataSource = srRead
                    grdUsers.DataBind()
                    grdusers.ExpandAll()

                    'If grdUsers.Rows.Count < 40 Then
                    '    grdUsers.PageSize = grdUsers.Rows.Count + 1
                    'Else
                    '    grdUsers.PageSize = 40
                    'End If


                Else
                    lblMessage.Text = "No accounts found."
                    EnableControls(False)
                End If

                srRead.Close()
                srRead = Nothing
                dbGet.CloseConnections()
                dbGet = Nothing

            End If
            

        End Sub

        Sub EnableControls(ByVal value As Boolean)
            'alblDescription.Visible = value
            'alblDescription.Enabled = value
            'alblExpires.Visible = value
            'alblExpires.Enabled = value
            'alblAccountID.Enabled = value
            'alblAccountID.Visible = value
            'alblAppName.Enabled = value
            'alblAppName.Visible = value
            'alblPurpose.Enabled = value
            'alblPurpose.Visible = value
            grdUsers.Visible = value
            grdUsers.Enabled = value
        End Sub

        Private Sub cmdContinue_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdContinue.Click
            FindAccount()
        End Sub

        Protected Sub cmdClearF_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmdClearF.Click

            Me.txtFAccount.Text = ""
            Me.txtOwnerID.Text = ""
            Me.txtOwnerName.Text = ""
            Me.ddlFAccess.SelectedIndex = 0
            Me.chkPastDue.Checked = False
            Me.txtFilterDesc.Text = ""
            Me.txtFilterPurpose.Text = ""
            Me.txtFilterAppName.Text = ""
            EnableControls(False)

        End Sub

        Protected Sub cmdExport_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmdExport.Click
            Dim prtReport As New Print()

            hlDownload.NavigateUrl = prtReport.CreateCSV(ApplyFilter)
            hlDownload.Text = "Download File"

            FindAccount()
        End Sub
    End Class

End Namespace
