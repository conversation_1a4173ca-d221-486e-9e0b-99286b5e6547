Imports System.Data
Namespace AccountManager

    Partial Class ucDelAcct
        Inherits System.Web.UI.UserControl

#Region " Web Form Designer Generated Code "

        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region


        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load, Me.Load
            'Put user code to initialize the page here

            If Not Page.IsPostBack Then
                LoadInstruction()
                grdDelUsers.Visible = False
                cmdDelete.Visible = False
                cmdReset.Visible = False


            End If
        End Sub

        Sub LoadInstruction()

            lblInstructions.Text = "" & _
                "Step 1: Enter Account ID. Click Lookup ID.<br/>" & _
                "Step 2: Click on Delete.<br/>"
        End Sub

        Sub FindAccount()

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader

            strSQL = "SELECT * FROM vUsr WHERE AccountID = '" & txtAccount.Text & "' ORDER BY Access desc"
            srRead = dbGet.GetDataReaderByStringId(strSQL)
            hfOwnerDelBackup.Value = ""
            Dim strOwnerDelBackup As String = ""
            If srRead.HasRows Then
                'While srRead.Read
                '    lblDescription.Text = srRead.Item("Description").ToString
                '    Dim intType As Integer = srRead.Item("Type")
                '    Dim strUserName As String = srRead.Item("UserName").ToString
                '    strOwnerDelBackup = strOwnerDelBackup & intType.ToString & "-" & strUserName & ","
                '    ' Exit While
                'End While
                hfOwnerDelBackup.Value = strOwnerDelBackup

                grdDelUsers.KeyFieldName = "ID"
                grdDelUsers.DataSource = srRead
                grdDelUsers.DataBind()
                'grdUsers.ExpandAll()
                grdDelUsers.Visible = True
                cmdDelete.Visible = True

                cmdReset.Visible = True
                cmdLookup.Enabled = False
                txtAccount.Enabled = False


            Else
                lblDescription.Text = ""
                cmdDelete.Visible = False
                grdDelUsers.Visible = False
                cmdReset.Visible = False
                cmdLookup.Enabled = True
                txtAccount.Enabled = True
                lblMessage.Text = "No account found."
                '    EnableControls(False)
            End If

            srRead.Close()
            srRead = Nothing
            dbGet.CloseConnections()

        End Sub

        Private Sub cmdLookup_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdLookup.Click

            FindAccount()
        End Sub


        Private Sub cmdDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdDelete.Click
            Dim strAccount As String = txtAccount.Text.Trim
            Dim reqDel As New Requests
            Dim hlpLog As New Helper
            Dim usrcur As New UserInfo
            Dim ActObj As New UserInfo(strAccount)
            Dim IsSoftdeletedinAD As Boolean = False
            If ActObj.UserFound Then
                Dim time As Date = DateAdd("M", 2, Now)
                Dim dtStart As String = time.ToString("MMM-yyyy ")
                Dim strDisplayName As String = ActObj.DisplayName
                Dim strTempDisplayName As String = LCase(strDisplayName)
                strTempDisplayName = Replace(strTempDisplayName, " ", "")
                If Not (strTempDisplayName.StartsWith("??dele") Or strTempDisplayName.StartsWith("??hold")) Then
                    Dim strNewDisplayName As String = "??dele-" & dtStart & strDisplayName
                    'Soft Delete & Disable Account in AD.
                    IsSoftdeletedinAD = ActObj.SoftDeleteAndDisableAccount(ActObj.LdapPath, strNewDisplayName)
                End If
            End If
            Try
                reqDel.AccountID = strAccount
                reqDel.DeleteAccountID()
                Dim strLogsComments As String = ""
                If IsSoftdeletedinAD Then
                    strLogsComments = ". Account Deleted from DB and AD."
                    'hlpLog.InsertLog(txtAccount.Text, [Global].GblActDel, Now, "Account Deleted in DB and AD By: " & usrcur.UserNameandID)
                    lblMessage.Text = "Account has been Deleted."
                Else
                    strLogsComments = ". Account Deleted from DB."
                    'hlpLog.InsertLog(txtAccount.Text, [Global].GblActDel, Now, "Account Deleted in DB By: " & usrcur.UserNameandID)
                    lblMessage.Text = "Account has been Deleted from Account Manager. Be sure to delete the account from Active Directory."
                End If
                strLogsComments = "Owner Details: " & hfOwnerDelBackup.Value & strLogsComments
                If Len(strLogsComments) > 999 Then
                    strLogsComments = Mid(strLogsComments, 0, 999)
                End If
                hlpLog.InsertLog(txtAccount.Text, [Global].GblActDel, Now, strLogsComments)

                lblDescription.Text = ""
                cmdDelete.Visible = False
                grdDelUsers.Visible = False
                cmdReset.Visible = False
                cmdLookup.Enabled = True
                txtAccount.Enabled = True
            Catch ex As Exception
                lblMessage.Text = "Error: Account is not soft deleted from Database and AD. Please verify the account and try again."
            End Try
            
        End Sub

        Protected Sub cmdReset_Click(sender As Object, e As EventArgs) Handles cmdReset.Click
            lblDescription.Text = ""
            cmdDelete.Visible = False
            grdDelUsers.Visible = False
            cmdReset.Visible = False
            cmdLookup.Enabled = True
            txtAccount.Enabled = True
        End Sub
    End Class

End Namespace
