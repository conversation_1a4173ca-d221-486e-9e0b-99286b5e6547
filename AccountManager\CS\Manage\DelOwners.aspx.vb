Imports DevExpress.Web.ASPxGrid
Imports System.Data

Namespace AccountManager

Partial Class DelOwners
    Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

    End Sub


    Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
        'CODEGEN: This method call is required by the Web Form Designer
        'Do not modify it using the code editor.
        InitializeComponent()
    End Sub

#End Region

    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        LoadInstruction()
        FindAccount()
    End Sub

    Function DeleteAccounts()

        Dim strUserId, strComments, strOwner As String
        Dim intReqId As Integer
        Dim i As Integer
        Dim reqUpd As New Requests
            Dim usrcur As New UserInfo
            Dim hlpDel As New Helper

            Trace.Warn("DeleteAccounts")

            For i = 0 To (grdUsers.GetSelectedRowCount()) - 1
                Dim selectedRow As Row = grdUsers.GetSelectedRow(i)

                If selectedRow.Level = grdUsers.GetGroupCount() Then

                    strUserId = selectedRow.DataControllerRow("AccountID").ToString()
                    strOwner = selectedRow.DataControllerRow("UserName").ToString()
                    reqUpd.AccountID = strUserId
                    reqUpd.OldOwner = strOwner
                    Trace.Warn(strOwner)
                    Trace.Warn(strUserId)
                    'reqUpd.DeleteAccountID()
                    hlpDel.InsertLog(txtAccount.Text, [Global].GblActOwnDel, Now, usrcur.GetUserID & " Deleted")

                End If
            Next i

    End Function

    Sub FindAccount()

        Dim strSQL As String
        Dim dbGet As New DataAccess
        Dim srRead As SqlClient.SqlDataReader

        strSQL = "SELECT * FROM vUsr WHERE AccountID = '" & txtAccount.Text & "' "
        srRead = dbGet.GetDataReaderByStringId(strSQL)

        If srRead.HasRows Then

            grdUsers.DataKeyField = "AccountID"
            grdUsers.DataSource = srRead
            grdUsers.DataBind()
            grdUsers.ExpandAllRows()
            cmdDelete.Visible = True
            lblMessage.Text = "ffff"
        Else
            cmdDelete.Visible = False
            lblMessage.Text = "No account found."
            '    EnableControls(False)
        End If

        srRead.Close()
        srRead = Nothing
        dbGet.CloseConnections()

    End Sub

    Sub LoadInstruction()

            lblInstructions.Text = "" & _
                "Step 1: Enter Account ID you want to udpate. Click Search.<br/>" & _
                "Step 2: Highlight the user(s) you wish to remove. (control+click)<br/>" & _
                "Step 3: Click the Delete button.<br/>" & _
                "Note: Once the update button is clicked the owner/backup will be deleted from this account."
    End Sub

    Private Sub cmdDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdDelete.Click
        DeleteAccounts()
    End Sub

    Private Sub cmdSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdSearch.Click
        FindAccount()
    End Sub

    Private Sub grdReturn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles grdReturn.Click
        Response.Redirect("/AccountManager/CS/Manage.aspx")
    End Sub
End Class

End Namespace

