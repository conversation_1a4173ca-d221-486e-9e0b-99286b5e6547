Imports DevExpress.Web.ASPxGrid
Imports DevExpress.Web.ASPxGridView
Imports System.Data

Namespace AccountManager

Partial Class ucDelOwners
    Inherits System.Web.UI.UserControl

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

    End Sub
    Protected WithEvents lblNumOwners As System.Web.UI.WebControls.Label


    Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
        'CODEGEN: This method call is required by the Web Form Designer
        'Do not modify it using the code editor.
        InitializeComponent()
    End Sub

#End Region

        Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load, Me.Load
            LoadInstruction()
            grdDelUsers.JSProperties("cpIsUpdated") = ""
        End Sub

        'Function DeleteAccounts()

        '    If FindNumberOfAccounts() = 1 Then
        '        FindAccount()
        '        lblMessage.Text = "There is only ONE owner for this account. First add a new owner then delete the incorrect owner."
        '    Else
        '            Dim usrcur As New UserInfo
        '            Dim hlpDel As New Helper
        '            Dim dbDel As New DataAccess

        '            dbDel.UpdateDBByStringId("sp_UMOwner_Delete '" & txtAccount.Text & "','" & txtDeleteID.Text & "'")

        '            hlpDel.InsertLog(txtAccount.Text, [Global].GblActOwnDel, Now, usrcur.GetUserID & " Deleted Owner/Backup: " & txtDeleteID.Text)
        '        FindAccount()
        '    End If

        '    End Function

        Sub DeleteBackupbyID(ByVal intID As Integer)

            Dim usrcur As New UserInfo
            ' Dim hlpDel As New Helper
            Dim dbDel As New DataAccess
            Dim strQuery As String = String.Format("Insert into tbUMLog Select '{0}',AccountID,'Removed Backup', CAST(FORMAT(GetDate(),'yyyy-MM-dd HH:mm:ss') AS datetime),'Removed Backup: ' + UserName from tbUMOwners where ID={1};Delete from tbUMOwners where ID={1};", DataAccess.getManager(), intID)
            dbDel.UpdateDBByStringId(strQuery)

            ' hlpDel.InsertLog(txtAccount.Text, [Global].GblActOwnDel, Now, usrcur.GetUserID & " Deleted Owner/Backup: " & txtDeleteID.Text)


        End Sub

        Function FindNumberOfAccounts() As Integer

            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader
            Dim intRows As String = 0
            strSQL = "SELECT Count(*) FROM vUsr WHERE AccountID = '" & txtDelAccountID.Text & "' "
            srRead = dbGet.GetDataReaderByStringId(strSQL)

            If srRead.Read Then
                intRows = srRead.Item(0)
            Else
                intRows = -1
            End If
            srRead.Close()
            dbGet.CloseConnections()
            Return intRows
        End Function

    Sub FindAccount()
        lblMessage.Text = ""
        Dim strSQL As String
        Dim dbGet As New DataAccess
            Dim dtBackups As DataTable

            strSQL = "SELECT * FROM vUsr WHERE AccountID = '" & txtDelAccountID.Text & "' AND Type=2 order by Access desc" 'updated by gursewak singh AND Type=2
            dtBackups = dbGet.GetBackupByAccountID(strSQL)
            'grdDelUsers.KeyFieldName = "ID"
            grdDelUsers.DataSource = dtBackups
            grdDelUsers.DataBind()
            grdDelUsers.ExpandAll()
            If dtBackups.Rows.Count > 0 Then
               
                'cmdDelete.Visible = True
                lblMessage.Text = ""
            Else
                'cmdDelete.Visible = False
                If FindNumberOfAccounts() = 0 Then
                    lblMessage.Text = "No account found in Account Manager"
                Else
                    lblMessage.Text = "This account does not have backup in Account Manager"
                End If

            End If
        End Sub

    Sub LoadInstruction()

            lblInstructions.Text = "" & _
                "Step 1: Enter Account ID. Click Search.<br/>" & _
                "Step 2: Click the Delete button from gridview to delete backup.<br/>"
            '"Note: Once the delete button is clicked the owner/backup will be deleted from this account."
            '"Step 2: Highlight the user(s) you wish to remove. (control+click)<br/>" & _
    End Sub

        'Private Sub cmdDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdDelete.Click
        '    DeleteAccounts()
        'End Sub

        Private Sub cmdSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdDelSearch.Click
            FindAccount()
        End Sub

        Protected Sub grdUsers_RowDeleting(ByVal sender As Object, ByVal e As DevExpress.Web.Data.ASPxDataDeletingEventArgs)
           
            e.Cancel = True
           
            Dim id As Integer = e.Keys(grdDelUsers.KeyFieldName)
            DeleteBackupbyID(id)

            FindAccount()
            DirectCast(sender, ASPxGridView).JSProperties("cpIsUpdated") = "Backup has been deleted successfully."

        End Sub

        'Protected Sub grdUsers_CommandButtonInitialize(ByVal sender As Object, ByVal e As ASPxGridViewCommandButtonEventArgs) Handles grdDelUsers.CommandButtonInitialize
        '    If e.VisibleIndex = -1 Then
        '        Return
        '    End If

        '    Select Case e.ButtonType
        '        Case ColumnCommandButtonType.Delete
        '            e.Enabled = DeleteButtonVisibleCriteria(CType(sender, ASPxGridView), e.VisibleIndex)
        '    End Select
        'End Sub
        'Private Function DeleteButtonVisibleCriteria(ByVal grid As ASPxGridView, ByVal visibleIndex As Integer) As Boolean
        '    Dim row As Object = grid.GetRow(visibleIndex)

        '    Dim retval As Boolean = True


        '    Try
        '        Dim strAccess As String = (CType(row, DataRowView))("Type").ToString()
        '        If strAccess = "1" Or strAccess = "3" Then
        '            retval = False
        '        End If

        '    Catch ex As Exception
        '    End Try

        '    Return retval
        'End Function
    End Class


End Namespace

