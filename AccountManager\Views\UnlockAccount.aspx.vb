'===========================================================================
' This file was modified as part of an ASP.NET 2.0 Web project conversion.
' The class name was changed and the class modified to inherit from the abstract base class 
' in file 'App_Code\Migrated\Views\Stub_UnlockAccount_aspx_vb.vb'.
' During runtime, this allows other classes in your web application to bind and access 
' the code-behind page using the abstract base class.
' The associated content page 'Views\UnlockAccount.aspx' was also modified to refer to the new class name.
' For more information on this code pattern, please refer to http://go.microsoft.com/fwlink/?LinkId=46995 
'===========================================================================
Namespace AccountManager

'Partial Class UnlockAccount
Partial Class Migrated_UnlockAccount

Inherits UnlockAccount

#Region " Web Form Designer Generated Code "

    'This call is required by the Web Form Designer.
    <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

    End Sub


    Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
        'CODEGEN: This method call is required by the Web Form Designer
        'Do not modify it using the code editor.
        InitializeComponent()
    End Sub

#End Region

    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        If Not Page.IsPostBack Then
            Dim hlpCheck As New Helper
                Dim usrcur As New UserInfo
                If Not hlpCheck.IsOwnerOrBackup(Request.QueryString("AccountID"), usrcur.GetUserID) Then
                    lblMessage.Text = "You are not the owner or backup owner of " & lblAccount.Text & "."
                    lblMessage.CssClass = "LabelRed"
                    Me.pInfo.Visible = False
                Else
                    LoadUser()
                End If

        End If

    End Sub

    Overrides Sub LoadUser()
'    Sub LoadUser()
        lblAccount.Text = "<strong>" & UCase(Request.QueryString("AccountID")) & "</strong>"
        Dim usrGetInfo As New UserInfo(Request.QueryString("AccountID"))

        If usrGetInfo.IsLocked Then
            Me.lblLocked.Text = "Currently this account is locked out. Click the unlock button to fix."
            Me.cmdUnlock.Visible = True
        Else
            Me.lblLocked.Text = "This account is not locked out. No other action needs to be taken."
            Me.cmdUnlock.Visible = False
        End If

    End Sub

    Private Sub cmdUnlock_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdUnlock.Click
        Dim usrGetInfo As New UserInfo(Request.QueryString("AccountID"))
        Me.lblMessage.Text = usrGetInfo.UnlockAccount(usrGetInfo.LdapPath)
        Me.lblLocked.Text = ""
    End Sub

    Private Sub cmdReturn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cmdReturn.Click
        Response.Redirect("/AccountManager/MyUsers.aspx")
    End Sub
End Class

End Namespace
