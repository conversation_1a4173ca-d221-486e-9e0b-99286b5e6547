
Partial Class MasterPage
    Inherits System.Web.UI.MasterPage

'Start of CHG0248076
'Restrict access to administration section
    Private Sub Page_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        If Not Page.IsPostBack Then
            CheckAccess()
        End If
    End Sub

    Sub CheckAccess()
        Dim pUser As New System.Web.UI.Page

        'If pUser.User.IsInRole("KCUS\CompSec Standard Access") Then
	If pUser.User.IsInRole("KCUS\Admin Comp Security") Then
            pAdmin.Visible = True
            pReports.Visible = True
        Else
            pAdmin.Visible = False
            pReports.Visible = False
        End If   
    End Sub
'End of CHG0248076

End Class

