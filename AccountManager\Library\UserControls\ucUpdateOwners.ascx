﻿<%@ Control Language="vb" AutoEventWireup="false" Inherits="AccountManager.ucUpdateOwners" CodeFile="ucUpdateOwners.ascx.vb" %>
<%@ Register Assembly="DevExpress.Web.ASPxGridView.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>
<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=11.2.12.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>
<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<table class="DisplayTables" style="width:100%">
	<tr>
		<td class="FunctionStep" style="width: 471px">Add New Owner/Delegate/Backup</td>
	</tr>
	<tr>
		<td><asp:label id="lblMessage" Runat="server" CssClass="LabelRed"></asp:label></td>
	</tr>
	<tr>
        
		<td>
            <table>
                <colgroup>
                    <col style="width:150px;" />
                    <col style="width:auto;" />
                </colgroup>
                <tr><td>Account ID:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="txtAccount" Display="None" ErrorMessage="Please enter Account ID." SetFocusOnError="True" ValidationGroup="updOwnersb"> </asp:RequiredFieldValidator></td><td><table><tr><td><asp:textbox id="txtAccount" Runat="server"></asp:textbox></td><td><dx:aspxbutton id="cmdSearch" runat="server" ValidationGroup="updOwnersb" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                text="Search"></dx:aspxbutton></td></tr></table></td></tr>
                <asp:panel id="pUpdateOwner" Runat="server" Visible="False">
                    <tr><td>Display Name:</td><td><asp:textbox id="txtDisplayName" Runat="server" Width="500" Enabled="False"></asp:textbox></td></tr>
                <tr><td>Description:</td><td><asp:textbox id="txtDescription" Runat="server" Width="500" Enabled="False"></asp:textbox></td></tr>
                    <tr><td>User ID:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtNewOwner" Display="None" ErrorMessage="Please enter User ID." SetFocusOnError="True" ValidationGroup="updOwnersa"> </asp:RequiredFieldValidator></td><td ><asp:textbox id="txtNewOwner" Runat="server"></asp:textbox></td></tr>
                    <tr><td>Ownership Type:</td><td ><asp:dropdownlist id="ddlOwnType" Runat="server"></asp:dropdownlist></td></tr>
                    <tr><td></td>
		<td>
            <table><tr><td><dx:aspxbutton id="cmdUpdate" runat="server" ValidationGroup="updOwnersa" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                            csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                            text="Add User"></dx:aspxbutton></td><td><dx:aspxbutton id="cmdReset" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                            csspostfix="Office2003Blue" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                            text="Reset"></dx:aspxbutton></td></tr></table>
                        
                   
                    <%--<td style="width: 100px">
                        <dx:aspxbutton id="cmdDelete" runat="server" cssfilepath="~/App_Themes/Office2003Blue/{0}/styles.css"
                            csspostfix="Office2003Blue" enabled="False" spritecssfilepath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                            text="Delete Owner" visible="False"></dx:aspxbutton>--%>
                   
        </td>
	</tr>
                </asp:panel>
            </table>
			</td>
	</tr>
	

	<tr>
		<td style="width: 471px">
             <asp:ValidationSummary ID="ValidationSummary1" runat="server" ValidationGroup="updOwnersa" />
            <asp:ValidationSummary ID="ValidationSummary2" runat="server" ValidationGroup="updOwnersb" />
            <asp:label id="lblInstructions" Runat="server"></asp:label>
            <br/>
		    <asp:HiddenField ID="hfCurrentOwner" runat="server" />
            <asp:HiddenField ID="hfCurrentDelegate" runat="server" />
            <asp:HiddenField ID="hfCurrentBackup" runat="server" />
		</td>
	</tr>
	
	<tr>
		<td>
            <dx:aspxgridview id="grdUsers" runat="server"  autogeneratecolumns="False" cssfilepath="~/App_Themes/PlasticBlue/{0}/styles.css"
                csspostfix="PlasticBlue" width="540px">
<Styles CssPostfix="PlasticBlue" CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css">
<Header SortingImageSpacing="10px" ImageSpacing="10px"></Header>
</Styles>

<SettingsPager ShowDefaultImages="False">
<AllButton Text="All"></AllButton>

<NextPageButton Text="Next &gt;"></NextPageButton>

<PrevPageButton Text="&lt; Prev"></PrevPageButton>
</SettingsPager>

<ImagesFilterControl>
<LoadingPanel Url="~/App_Themes/PlasticBlue/Editors/Loading.gif"></LoadingPanel>
</ImagesFilterControl>

<Images SpriteCssFilePath="~/App_Themes/PlasticBlue/{0}/sprite.css">
<LoadingPanelOnStatusBar Url="~/App_Themes/PlasticBlue/GridView/gvLoadingOnStatusBar.gif"></LoadingPanelOnStatusBar>

<LoadingPanel Url="~/App_Themes/PlasticBlue/GridView/Loading.gif"></LoadingPanel>
</Images>
                 <SettingsBehavior AllowSort="false" />
<Columns>
     <%--GroupIndex="0" SortIndex="0" SortOrder="Ascending"--%>
<dx:GridViewDataTextColumn FieldName="AccountID" VisibleIndex="1"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="UserNameDN" Caption="User Name" VisibleIndex="3"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="UserName" Caption="User ID" VisibleIndex="2"></dx:GridViewDataTextColumn>
<dx:GridViewDataTextColumn FieldName="Access" Caption="Access" VisibleIndex="4"></dx:GridViewDataTextColumn>
</Columns>
<Settings ShowGroupPanel="False"></Settings>
<StylesEditors>
<CalendarHeader Spacing="11px"></CalendarHeader>

<ProgressBar Height="25px"></ProgressBar>
</StylesEditors>
</dx:aspxgridview>
        </td>
	</tr>
</table>
