GroupName,GroupLocation,ADSPath,UserName,Access,Description,
GDStageServiceAccts,KCUS,WinNT://KCUS/GDStageServiceAccts,E18722,Owner,Controls access for application and contractor IDs to EDMT dev server USTCFN01,
GDStageServiceAccts,KCUS,WinNT://KCUS/GDStageServiceAccts,W48997,Delegate,Controls access for application and contractor IDs to EDMT dev server USTCFN01,
Microsoft Azure RMS Access,KCUS,WinNT://KCUS/Microsoft Azure RMS Access,B46959,Authorizer,Users of this group have access to secure documents with Azure Rights Management Protection,
Microsoft Azure RMS Access,KCUS,WinNT://KCUS/Microsoft Azure RMS Access,E11888,Authorizer,Users of this group have access to secure documents with Azure Rights Management Protection,
Microsoft Azure RMS Access,KCUS,WinNT://KCUS/Microsoft Azure RMS Access,E18722,Owner,Users of this group have access to secure documents with Azure Rights Management Protection,
Microsoft Azure RMS Access,KCUS,WinNT://KCUS/Microsoft Azure RMS Access,W62002,<PERSON><PERSON>ate,Users of this group have access to secure documents with Azure Rights Management Protection,
KCSoftex-E3,<PERSON><PERSON>,WinNT://KCUS/KCSoftex-E3,E18722,Owner,The Group will be used for assigning E3 licenses to KCSoftex users,
KCSoftex-E3,KCUS,WinNT://KCUS/KCSoftex-E3,R65106,Authorizer,The Group will be used for assigning E3 licenses to KCSoftex users,
KCSoftex-E3,KCUS,WinNT://KCUS/KCSoftex-E3,R65111,Authorizer,The Group will be used for assigning E3 licenses to KCSoftex users,
KCSoftex-E3,KCUS,WinNT://KCUS/KCSoftex-E3,R65122,Authorizer,The Group will be used for assigning E3 licenses to KCSoftex users,
KCSoftex-E3,KCUS,WinNT://KCUS/KCSoftex-E3,W62002,Delegate,The Group will be used for assigning E3 licenses to KCSoftex users,
Allow-Comp-LiveMeeting Pilot,KCUS,WinNT://KCUS/Allow-Comp-LiveMeeting Pilot,E18722,Owner,Pilot group for testing Live meeting,
Allow-Comp-LiveMeeting Pilot,KCUS,WinNT://KCUS/Allow-Comp-LiveMeeting Pilot,Q06085,Authorizer,Pilot group for testing Live meeting,
Allow-Comp-LiveMeeting Pilot,KCUS,WinNT://KCUS/Allow-Comp-LiveMeeting Pilot,Q08911,Authorizer,Pilot group for testing Live meeting,
Allow-Comp-LiveMeeting Pilot,KCUS,WinNT://KCUS/Allow-Comp-LiveMeeting Pilot,W66442,Delegate,Pilot group for testing Live meeting,
Admin Citrix,KCUS,WinNT://KCUS/Admin Citrix,E18722,Owner,Administrators of Citrix Application Envrionment,
Admin Citrix,KCUS,WinNT://KCUS/Admin Citrix,W49040,Delegate,Administrators of Citrix Application Envrionment,
GRP01075_C,KCUS,WinNT://KCUS/GRP01075_C,E18722,Owner,Mailbox: Kimberly-Clark PostMaster,
GRP01075_C,KCUS,WinNT://KCUS/GRP01075_C,W62002,Delegate,Mailbox: Kimberly-Clark PostMaster,
GRP02396_C,KCUS,WinNT://KCUS/GRP02396_C,E18722,Owner,Mailbox: _Support  Messaging,
GRP02396_C,KCUS,WinNT://KCUS/GRP02396_C,W62002,Delegate,Mailbox: _Support  Messaging,
HostApp-MSDEvenflo,KCUS,WinNT://KCUS/HostApp-MSDEvenflo,E18722,Owner,Citrix Application security group for MS Dynamics Evenflo,
HostApp-MSDEvenflo,KCUS,WinNT://KCUS/HostApp-MSDEvenflo,Q13357,Authorizer,Citrix Application security group for MS Dynamics Evenflo,
HostApp-MSDEvenflo,KCUS,WinNT://KCUS/HostApp-MSDEvenflo,Q14718,Authorizer,Citrix Application security group for MS Dynamics Evenflo,
HostApp-MSDEvenflo,KCUS,WinNT://KCUS/HostApp-MSDEvenflo,Q15926,Authorizer,Citrix Application security group for MS Dynamics Evenflo,
HostApp-MSDEvenflo,KCUS,WinNT://KCUS/HostApp-MSDEvenflo,W49040,Delegate,Citrix Application security group for MS Dynamics Evenflo,
USTCA261_BudgetExtract,KCUS,WinNT://KCUS/USTCA261_BudgetExtract,E18722,Owner,Access to budget extract folder in USTCA261,
USTCA261_BudgetExtract,KCUS,WinNT://KCUS/USTCA261_BudgetExtract,W66442,Delegate,Access to budget extract folder in USTCA261,
GPO - Disable Remote Registry Service,KCUS,WinNT://KCUS/GPO - Disable Remote Registry Service,E18722,Owner,This is to Disable Remote Registry Service,
GPO - Disable Remote Registry Service,KCUS,WinNT://KCUS/GPO - Disable Remote Registry Service,Q06371,Authorizer,This is to Disable Remote Registry Service,
GPO - Disable Remote Registry Service,KCUS,WinNT://KCUS/GPO - Disable Remote Registry Service,W48997,Delegate,This is to Disable Remote Registry Service,
Allow-Comp-WSUS-Allow Local Admin to Choose,KCUS,WinNT://KCUS/Allow-Comp-WSUS-Allow Local Admin to Choose,E18722,Owner,Allows the Comp-WSUS-Allow Local Admin to Choose policy to apply,
Allow-Comp-WSUS-Allow Local Admin to Choose,KCUS,WinNT://KCUS/Allow-Comp-WSUS-Allow Local Admin to Choose,W66442,Delegate,Allows the Comp-WSUS-Allow Local Admin to Choose policy to apply,
FC-TestWin10Users,KCUS,WinNT://KCUS/FC-TestWin10Users,E18722,Owner,Fullclone Windows 10 Temporary VDI Users,
FC-TestWin10Users,KCUS,WinNT://KCUS/FC-TestWin10Users,Q07708,Authorizer,Fullclone Windows 10 Temporary VDI Users,
FC-TestWin10Users,KCUS,WinNT://KCUS/FC-TestWin10Users,Q13357,Authorizer,Fullclone Windows 10 Temporary VDI Users,
FC-TestWin10Users,KCUS,WinNT://KCUS/FC-TestWin10Users,Q14616,Authorizer,Fullclone Windows 10 Temporary VDI Users,
FC-TestWin10Users,KCUS,WinNT://KCUS/FC-TestWin10Users,Q14718,Authorizer,Fullclone Windows 10 Temporary VDI Users,
FC-TestWin10Users,KCUS,WinNT://KCUS/FC-TestWin10Users,Q15926,Authorizer,Fullclone Windows 10 Temporary VDI Users,
FC-TestWin10Users,KCUS,WinNT://KCUS/FC-TestWin10Users,W49040,Delegate,Fullclone Windows 10 Temporary VDI Users,
KCSoftex-E3M,KCUS,WinNT://KCUS/KCSoftex-E3M,E18722,Owner,The Group will be used for assigning E3M licenses to KCSoftex users,
KCSoftex-E3M,KCUS,WinNT://KCUS/KCSoftex-E3M,R65106,Authorizer,The Group will be used for assigning E3M licenses to KCSoftex users,
KCSoftex-E3M,KCUS,WinNT://KCUS/KCSoftex-E3M,R65111,Authorizer,The Group will be used for assigning E3M licenses to KCSoftex users,
KCSoftex-E3M,KCUS,WinNT://KCUS/KCSoftex-E3M,R65122,Authorizer,The Group will be used for assigning E3M licenses to KCSoftex users,
KCSoftex-E3M,KCUS,WinNT://KCUS/KCSoftex-E3M,W62002,Delegate,The Group will be used for assigning E3M licenses to KCSoftex users,
HostApp-SAPAnalyzer,KCUS,WinNT://KCUS/HostApp-SAPAnalyzer,E18722,Owner,Admin Access group for SAP Analyser,
HostApp-SAPAnalyzer,KCUS,WinNT://KCUS/HostApp-SAPAnalyzer,Q13357,Authorizer,Admin Access group for SAP Analyser,
HostApp-SAPAnalyzer,KCUS,WinNT://KCUS/HostApp-SAPAnalyzer,Q14718,Authorizer,Admin Access group for SAP Analyser,
HostApp-SAPAnalyzer,KCUS,WinNT://KCUS/HostApp-SAPAnalyzer,Q15926,Authorizer,Admin Access group for SAP Analyser,
HostApp-SAPAnalyzer,KCUS,WinNT://KCUS/HostApp-SAPAnalyzer,W49040,Delegate,Admin Access group for SAP Analyser,
GRP01115_C,KCUS,WinNT://KCUS/GRP01115_C,E18722,Owner,Mailbox: _Desktop Services  Projects,
GRP01115_C,KCUS,WinNT://KCUS/GRP01115_C,W48997,Delegate,Mailbox: _Desktop Services  Projects,
TFSDesktopServices,KCUS,WinNT://KCUS/TFSDesktopServices,E18722,Owner,Manages access to TFS for Desktop Services,
TFSDesktopServices,KCUS,WinNT://KCUS/TFSDesktopServices,q73491,Authorizer,Manages access to TFS for Desktop Services,
TFSDesktopServices,KCUS,WinNT://KCUS/TFSDesktopServices,W48997,Delegate,Manages access to TFS for Desktop Services,
DesktopServices_TWS_R,KCUS,WinNT://KCUS/DesktopServices_TWS_R,E18722,Owner,Controls read access to folders on TWS servers for Desktop Services team,
DesktopServices_TWS_R,KCUS,WinNT://KCUS/DesktopServices_TWS_R,W66442,Delegate,Controls read access to folders on TWS servers for Desktop Services team,
Exchange Organization Administrators,KCUS,WinNT://KCUS/Exchange Organization Administrators,E18722,Owner,Users in this group will have permission to read and modify all Exchange configuration. This group should not be deleted.,
Exchange Organization Administrators,KCUS,WinNT://KCUS/Exchange Organization Administrators,Q03582,Authorizer,Users in this group will have permission to read and modify all Exchange configuration. This group should not be deleted.,
Exchange Organization Administrators,KCUS,WinNT://KCUS/Exchange Organization Administrators,W62002,Delegate,Users in this group will have permission to read and modify all Exchange configuration. This group should not be deleted.,
Exchange View-Only Administrators,KCUS,WinNT://KCUS/Exchange View-Only Administrators,E18722,Owner,Users in this group will have permission to read all Exchange configuration. This group should not be deleted.,
Exchange View-Only Administrators,KCUS,WinNT://KCUS/Exchange View-Only Administrators,W62002,Delegate,Users in this group will have permission to read all Exchange configuration. This group should not be deleted.,
RTCUniversalUserAdmins,KCUS,WinNT://KCUS/RTCUniversalUserAdmins,E18722,Owner,Members can manage RTC users in this forest.,
RTCUniversalUserAdmins,KCUS,WinNT://KCUS/RTCUniversalUserAdmins,W62002,Delegate,Members can manage RTC users in this forest.,
RTCUniversalGlobalWriteGroup,KCUS,WinNT://KCUS/RTCUniversalGlobalWriteGroup,E18722,Owner,Members have write access to RTC global settings.,
RTCUniversalGlobalWriteGroup,KCUS,WinNT://KCUS/RTCUniversalGlobalWriteGroup,W62002,Delegate,Members have write access to RTC global settings.,
RTCUniversalGlobalReadOnlyGroup,KCUS,WinNT://KCUS/RTCUniversalGlobalReadOnlyGroup,W62002,Delegate,Members have read access to RTC global settings.,
RTCUniversalGlobalReadOnlyGroup,KCUS,WinNT://KCUS/RTCUniversalGlobalReadOnlyGroup,E18722,Owner,Members have read access to RTC global settings.,
USTC_KCMSG_TEST,KCUS,WinNT://KCUS/USTC_KCMSG_TEST,E18722,Owner,Message Date Security Test Group,
USTC_KCMSG_TEST,KCUS,WINNT://KCUS/USTC_KCMSG_TEST,Q73491,Authorizer,Message Date Security Test Group,
USTC_KCMSG_TEST,KCUS,WinNT://KCUS/USTC_KCMSG_TEST,W66442,Delegate,Message Date Security Test Group,
USTCA261_Admin,KCUS,WinNT://KCUS/USTCA261_Admin,E18722,Owner,Firefight access to USTCA261,
USTCA261_Admin,KCUS,WinNT://KCUS/USTCA261_Admin,W66442,Delegate,Firefight access to USTCA261,
PTWMachineAcctRights,KCUS,WinNT://KCUS/PTWMachineAcctRights,Q08911,Authorizer,Provides rights to create/delete machine accounts through the Machine Account Utility,
PTWMachineAcctRights,KCUS,WinNT://KCUS/PTWMachineAcctRights,W48997,Delegate,Provides rights to create/delete machine accounts through the Machine Account Utility,
PTWMachineAcctRights,KCUS,WinNT://KCUS/PTWMachineAcctRights,E18722,Owner,Provides rights to create/delete machine accounts through the Machine Account Utility,
PTWMachineAcctRights,KCUS,WinNT://KCUS/PTWMachineAcctRights,Q06085,Authorizer,Provides rights to create/delete machine accounts through the Machine Account Utility,
Windows 10 Build 1709 Exclusion,KCUS,WinNT://KCUS/Windows 10 Build 1709 Exclusion,E18722,Owner,To exclude the Windows 10 1709 deployment. This group contains Windows 7 PCs which is in long-term exception list preventing them to be upgraded to Win10 any version. ,
Windows 10 Build 1709 Exclusion,KCUS,WinNT://KCUS/Windows 10 Build 1709 Exclusion,Q04499,Authorizer,To exclude the Windows 10 1709 deployment. This group contains Windows 7 PCs which is in long-term exception list preventing them to be upgraded to Win10 any version. ,
Windows 10 Build 1709 Exclusion,KCUS,WinNT://KCUS/Windows 10 Build 1709 Exclusion,W48997,Delegate,To exclude the Windows 10 1709 deployment. This group contains Windows 7 PCs which is in long-term exception list preventing them to be upgraded to Win10 any version. ,
USTCA183_RoomWizard,KCUS,WinNT://KCUS/USTCA183_RoomWizard,E18722,Owner,RoomWizard application synchronizes a RoomWizard device with the associated conference room on Microsoft Exchange,
USTCA183_RoomWizard,KCUS,WinNT://KCUS/USTCA183_RoomWizard,W62002,Delegate,RoomWizard application synchronizes a RoomWizard device with the associated conference room on Microsoft Exchange,
QAADBReadOnly,KCUS,WinNT://KCUS/QAADBReadOnly,E18722,Owner,Read Group,
QAADBReadOnly,KCUS,WinNT://KCUS/QAADBReadOnly,W66442,Delegate,Read Group,
Horizon VDI admin,KCUS,WinNT://KCUS/Horizon VDI admin,E18722,Owner,Manage Admin access for Horizon VDI,
Horizon VDI admin,KCUS,WinNT://KCUS/Horizon VDI admin,Q13357,Authorizer,Manage Admin access for Horizon VDI,
Horizon VDI admin,KCUS,WinNT://KCUS/Horizon VDI admin,Q14718,Authorizer,Manage Admin access for Horizon VDI,
Horizon VDI admin,KCUS,WinNT://KCUS/Horizon VDI admin,Q15926,Authorizer,Manage Admin access for Horizon VDI,
Horizon VDI admin,KCUS,WinNT://KCUS/Horizon VDI admin,W49040,Delegate,Manage Admin access for Horizon VDI,
O365 Intune Application Manager,KCUS,WinNT://KCUS/O365 Intune Application Manager,e18722,Owner,O365 Intune Application Manager,
O365 Intune Application Manager,KCUS,WinNT://KCUS/O365 Intune Application Manager,W62002,Delegate,O365 Intune Application Manager,
Policy QA Pilot Computer,KCUS,WinNT://KCUS/Policy QA Pilot Computer,E18722,Owner,group for GPO testing in QA environment,
Policy QA Pilot Computer,KCUS,WinNT://KCUS/Policy QA Pilot Computer,q04499,Authorizer,group for GPO testing in QA environment,
Policy QA Pilot Computer,KCUS,WinNT://KCUS/Policy QA Pilot Computer,Q06085,Authorizer,group for GPO testing in QA environment,
Policy QA Pilot Computer,KCUS,WinNT://KCUS/Policy QA Pilot Computer,Q08911,Authorizer,group for GPO testing in QA environment,
Policy QA Pilot Computer,KCUS,WinNT://KCUS/Policy QA Pilot Computer,W66442,Delegate,group for GPO testing in QA environment,
Intune_MAM_Enabled_Users,KCUS,WinNT://KCUS/Intune_MAM_Enabled_Users,W66442,Delegate,Intune MAM enabled users,
Intune_MAM_Enabled_Users,KCUS,WinNT://KCUS/Intune_MAM_Enabled_Users,E18722,Owner,Intune MAM enabled users,
Intune_MAM_Enabled_Users,KCUS,WinNT://KCUS/Intune_MAM_Enabled_Users,E32872,Authorizer,Intune MAM enabled users,
Intune_MAM_Enabled_Users,KCUS,WinNT://KCUS/Intune_MAM_Enabled_Users,w48997,Authorizer,Intune MAM enabled users,
Messaging_Support,KCUS,WinNT://KCUS/Messaging_Support,E18722,Owner,Used to grant non-admin access for TCS,
Messaging_Support,KCUS,WinNT://KCUS/Messaging_Support,W62002,Delegate,Used to grant non-admin access for TCS,
AllowNetworkAccess_User,KCUS,WinNT://KCUS/AllowNetworkAccess_User,E18722,Owner,Desktop Svcs Group used to allow network access to a PC without PPCFF access,
AllowNetworkAccess_User,KCUS,WinNT://KCUS/AllowNetworkAccess_User,Q73491,Authorizer,Desktop Svcs Group used to allow network access to a PC without PPCFF access,
AllowNetworkAccess_User,KCUS,WinNT://KCUS/AllowNetworkAccess_User,W66442,Delegate,Desktop Svcs Group used to allow network access to a PC without PPCFF access,
Allow-Comp-WSUS-Windows7,KCUS,WinNT://KCUS/Allow-Comp-WSUS-Windows7,E18722,Owner,Group to install Windows7 updates using WSUS,
Allow-Comp-WSUS-Windows7,KCUS,WinNT://KCUS/Allow-Comp-WSUS-Windows7,Q06085,Authorizer,Group to install Windows7 updates using WSUS,
Allow-Comp-WSUS-Windows7,KCUS,WinNT://KCUS/Allow-Comp-WSUS-Windows7,Q08911,Authorizer,Group to install Windows7 updates using WSUS,
Allow-Comp-WSUS-Windows7,KCUS,WinNT://KCUS/Allow-Comp-WSUS-Windows7,W66442,Delegate,Group to install Windows7 updates using WSUS,
Hybrid Search Server Administrators,KCUS,WinNT://KCUS/Hybrid Search Server Administrators,E18722,Owner,Hybrid Search Server Administrators,
Hybrid Search Server Administrators,KCUS,WinNT://KCUS/Hybrid Search Server Administrators,Q08911,Authorizer,Hybrid Search Server Administrators,
Hybrid Search Server Administrators,KCUS,WinNT://KCUS/Hybrid Search Server Administrators,W49040,Delegate,Hybrid Search Server Administrators,
KCAPPS_PubEDTAdobeAdPro9,KCUS,WinNT://KCUS/KCAPPS_PubEDTAdobeAdPro9,E18722,Owner,\\kcapps\Share\public\EDT\Adobe\AdobePro9,
KCAPPS_PubEDTAdobeAdPro9,KCUS,WinNT://KCUS/KCAPPS_PubEDTAdobeAdPro9,W66442,Delegate,\\kcapps\Share\public\EDT\Adobe\AdobePro9,
PPCFF,KCUS,WinNT://KCUS/PPCFF,E18722,Owner,Allows remote administrator access to all PCs for fire fight purposes,
PPCFF,KCUS,WinNT://KCUS/PPCFF,W66442,Delegate,Allows remote administrator access to all PCs for fire fight purposes,
O365 Intune Administrators,KCUS,WinNT://KCUS/O365 Intune Administrators,E18722,Owner,O365 Intune Administrators,
O365 Intune Administrators,KCUS,WinNT://KCUS/O365 Intune Administrators,E32872,Authorizer,O365 Intune Administrators,
O365 Intune Administrators,KCUS,WinNT://KCUS/O365 Intune Administrators,W48997,Authorizer,O365 Intune Administrators,
O365 Intune Administrators,KCUS,WinNT://KCUS/O365 Intune Administrators,W66442,Delegate,O365 Intune Administrators,
O365 Conditional Access Administrators,KCUS,WinNT://KCUS/O365 Conditional Access Administrators,E18722,Owner,O365 Conditional Access Administrators,
O365 Conditional Access Administrators,KCUS,WinNT://KCUS/O365 Conditional Access Administrators,Q03582,Authorizer,O365 Conditional Access Administrators,
O365 Conditional Access Administrators,KCUS,WinNT://KCUS/O365 Conditional Access Administrators,W62002,Delegate,O365 Conditional Access Administrators,
DL012723,KCUS,WinNT://KCUS/DL012723,E18722,Owner,+GLOBAL_TCS  Desktop Engineering,
DL012723,KCUS,WinNT://KCUS/DL012723,Q06085,Authorizer,+GLOBAL_TCS  Desktop Engineering,
DL012723,KCUS,WinNT://KCUS/DL012723,Q06371,Authorizer,+GLOBAL_TCS  Desktop Engineering,
DL012723,KCUS,WinNT://KCUS/DL012723,Q08911,Authorizer,+GLOBAL_TCS  Desktop Engineering,
DL012723,KCUS,WinNT://KCUS/DL012723,W66442,Delegate,+GLOBAL_TCS  Desktop Engineering,
Windows-10 Boot files,KCUS,WinNT://KCUS/Windows-10 Boot files,E18722,Owner,This group will be used to place the boot files when it is modified,
Windows-10 Boot files,KCUS,WinNT://KCUS/Windows-10 Boot files,Q04499,Delegate,This group will be used to place the boot files when it is modified,
Office 365 Monthly Enterprise Channel,KCUS,WinNT://KCUS/Office 365 Monthly Enterprise Channel,E18722,Owner,This group will be used to enable the O365 Semi annual Channel Targeted updates through GPO,
Office 365 Monthly Enterprise Channel,KCUS,WinNT://KCUS/Office 365 Monthly Enterprise Channel,q04499,Delegate,This group will be used to enable the O365 Semi annual Channel Targeted updates through GPO,
Office 365 Monthly Enterprise Channel,KCUS,WinNT://KCUS/Office 365 Monthly Enterprise Channel,W62002,Authorizer,This group will be used to enable the O365 Semi annual Channel Targeted updates through GPO,
Workstations - Defender Exclusion,KCUS,WinNT://KCUS/Workstations - Defender Exclusion,W48997,Delegate,List of computers requiring exclusion for Defender,
Workstations - Defender Exclusion,KCUS,WinNT://KCUS/Workstations - Defender Exclusion,E18722,Owner,List of computers requiring exclusion for Defender,
Workstations - Defender Exclusion,KCUS,WinNT://KCUS/Workstations - Defender Exclusion,Q06203,Authorizer,List of computers requiring exclusion for Defender,
Workstations - Defender Exclusion,KCUS,WinNT://KCUS/Workstations - Defender Exclusion,Q06371,Authorizer,List of computers requiring exclusion for Defender,
Workstations - Defender Exclusion,KCUS,WinNT://KCUS/Workstations - Defender Exclusion,Q07983,Authorizer,List of computers requiring exclusion for Defender,
Admin SharePoint App Pool IDs – dev-qa,KCUS,WinNT://KCUS/Admin SharePoint App Pool IDs – dev-qa,E18722,Owner,Dev and QA SharePoint Application Pool Accounts,
Admin SharePoint App Pool IDs – dev-qa,KCUS,WinNT://KCUS/Admin SharePoint App Pool IDs – dev-qa,W49040,Delegate,Dev and QA SharePoint Application Pool Accounts,
EDT Join Script,KCUS,WinNT://KCUS/EDT Join Script,E18722,Owner,EDT Group Used for Joining Computers to the Domain,
EDT Join Script,KCUS,WinNT://KCUS/EDT Join Script,W48997,Delegate,EDT Group Used for Joining Computers to the Domain,
Mobile Email Access Personal,KCUS,WinNT://KCUS/Mobile Email Access Personal,E18722,Owner,Mobile Email Access Personal,
Mobile Email Access Personal,KCUS,WinNT://KCUS/Mobile Email Access Personal,W62002,Delegate,Mobile Email Access Personal,
Mobile Email Access Personal,KCUS,WinNT://KCUS/Mobile Email Access Personal,e11888,Authorizer,Mobile Email Access Personal,
Mobile Email Access Corp Cert,KCUS,WinNT://KCUS/Mobile Email Access Corp Cert,B62398,Authorizer,Grant Access for Corporate Devices using Certificates for Authentication,
Mobile Email Access Corp Cert,KCUS,WinNT://KCUS/Mobile Email Access Corp Cert,E18722,Owner,Grant Access for Corporate Devices using Certificates for Authentication,
Mobile Email Access Corp Cert,KCUS,WinNT://KCUS/Mobile Email Access Corp Cert,W62002,Delegate,Grant Access for Corporate Devices using Certificates for Authentication,
Allow-Comp-Communicator Pilot,KCUS,WinNT://KCUS/Allow-Comp-Communicator Pilot,E18722,Owner,Pilot group for Communicator,
Allow-Comp-Communicator Pilot,KCUS,WinNT://KCUS/Allow-Comp-Communicator Pilot,Q06085,Authorizer,Pilot group for Communicator,
Allow-Comp-Communicator Pilot,KCUS,WinNT://KCUS/Allow-Comp-Communicator Pilot,Q08911,Authorizer,Pilot group for Communicator,
Allow-Comp-Communicator Pilot,KCUS,WinNT://KCUS/Allow-Comp-Communicator Pilot,W66442,Delegate,Pilot group for Communicator,
Windows 10 Upgrade Mandatory Non GD PCs,KCUS,WinNT://KCUS/Windows 10 Upgrade Mandatory Non GD PCs,B45906,Authorizer,The members of this group will automatically receive the windows 10 1903 Push. Only Non GD PCs needs to be added to this group,
Windows 10 Upgrade Mandatory Non GD PCs,KCUS,WinNT://KCUS/Windows 10 Upgrade Mandatory Non GD PCs,E18722,Owner,The members of this group will automatically receive the windows 10 1903 Push. Only Non GD PCs needs to be added to this group,
Windows 10 Upgrade Mandatory Non GD PCs,KCUS,WinNT://KCUS/Windows 10 Upgrade Mandatory Non GD PCs,Q13243,Authorizer,The members of this group will automatically receive the windows 10 1903 Push. Only Non GD PCs needs to be added to this group,
Windows 10 Upgrade Mandatory Non GD PCs,KCUS,WinNT://KCUS/Windows 10 Upgrade Mandatory Non GD PCs,W45758,Authorizer,The members of this group will automatically receive the windows 10 1903 Push. Only Non GD PCs needs to be added to this group,
Windows 10 Upgrade Mandatory Non GD PCs,KCUS,WinNT://KCUS/Windows 10 Upgrade Mandatory Non GD PCs,W48997,Delegate,The members of this group will automatically receive the windows 10 1903 Push. Only Non GD PCs needs to be added to this group,
SCCM Client Remediation,KCUS,WinNT://KCUS/SCCM Client Remediation,E18722,Owner,SCCM client issue PCs will be added in this group for remediation,
SCCM Client Remediation,KCUS,WinNT://KCUS/SCCM Client Remediation,Q13243,Authorizer,SCCM client issue PCs will be added in this group for remediation,
SCCM Client Remediation,KCUS,WinNT://KCUS/SCCM Client Remediation,W48997,Delegate,SCCM client issue PCs will be added in this group for remediation,
GRP13578_C,KCUS,WinNT://KCUS/GRP13578_C,E18722,Owner,Mailbox: _Phish  Messaging,
GRP13578_C,KCUS,WinNT://KCUS/GRP13578_C,W62002,Delegate,Mailbox: _Phish  Messaging,
HostAppMenu-XA65-UA,KCUS,WinNT://KCUS/HostAppMenu-XA65-UA,E18722,Owner,User Acceptance Testing for Citrix Xenapp 6.5 Applications,
HostAppMenu-XA65-UA,KCUS,WinNT://KCUS/HostAppMenu-XA65-UA,Q07708,Authorizer,User Acceptance Testing for Citrix Xenapp 6.5 Applications,
HostAppMenu-XA65-UA,KCUS,WinNT://KCUS/HostAppMenu-XA65-UA,Q13357,Authorizer,User Acceptance Testing for Citrix Xenapp 6.5 Applications,
HostAppMenu-XA65-UA,KCUS,WinNT://KCUS/HostAppMenu-XA65-UA,Q14616,Authorizer,User Acceptance Testing for Citrix Xenapp 6.5 Applications,
HostAppMenu-XA65-UA,KCUS,WinNT://KCUS/HostAppMenu-XA65-UA,Q14718,Authorizer,User Acceptance Testing for Citrix Xenapp 6.5 Applications,
HostAppMenu-XA65-UA,KCUS,WinNT://KCUS/HostAppMenu-XA65-UA,Q15926,Authorizer,User Acceptance Testing for Citrix Xenapp 6.5 Applications,
HostAppMenu-XA65-UA,KCUS,WinNT://KCUS/HostAppMenu-XA65-UA,W49040,Delegate,User Acceptance Testing for Citrix Xenapp 6.5 Applications,
Mobility_SQL_Read,KCUS,WinNT://KCUS/Mobility_SQL_Read,E18722,Owner,Grants Read access to the eua0nedb database on USTCCA015S2\PROD1 and USTCCA014S2\DEV1,
Mobility_SQL_Read,KCUS,WinNT://KCUS/Mobility_SQL_Read,W66442,Delegate,Grants Read access to the eua0nedb database on USTCCA015S2\PROD1 and USTCCA014S2\DEV1,
USROP121printer,KCUS,WinNT://KCUS/USROP121printer,E18722,Owner,Access for the printer USROP121,
USROP121printer,KCUS,WinNT://KCUS/USROP121printer,W49040,Delegate,Access for the printer USROP121,
HostServerAdmin-ETL-BOBJDS,KCUS,WinNT://KCUS/HostServerAdmin-ETL-BOBJDS,E18722,Owner,Admin Access group for ETL BOBJ DS Citrix servers,
HostServerAdmin-ETL-BOBJDS,KCUS,WinNT://KCUS/HostServerAdmin-ETL-BOBJDS,Q07708,Authorizer,Admin Access group for ETL BOBJ DS Citrix servers,
HostServerAdmin-ETL-BOBJDS,KCUS,WinNT://KCUS/HostServerAdmin-ETL-BOBJDS,Q13357,Authorizer,Admin Access group for ETL BOBJ DS Citrix servers,
HostServerAdmin-ETL-BOBJDS,KCUS,WinNT://KCUS/HostServerAdmin-ETL-BOBJDS,Q14616,Authorizer,Admin Access group for ETL BOBJ DS Citrix servers,
HostServerAdmin-ETL-BOBJDS,KCUS,WinNT://KCUS/HostServerAdmin-ETL-BOBJDS,Q14718,Authorizer,Admin Access group for ETL BOBJ DS Citrix servers,
HostServerAdmin-ETL-BOBJDS,KCUS,WinNT://KCUS/HostServerAdmin-ETL-BOBJDS,Q15926,Authorizer,Admin Access group for ETL BOBJ DS Citrix servers,
HostServerAdmin-ETL-BOBJDS,KCUS,WinNT://KCUS/HostServerAdmin-ETL-BOBJDS,W49040,Delegate,Admin Access group for ETL BOBJ DS Citrix servers,
DL010858,KCUS,WinNT://KCUS/DL010858,B24834,Authorizer,+OSD_Applications  Development,
DL010858,KCUS,WinNT://KCUS/DL010858,E18722,Owner,+OSD_Applications  Development,
DL010858,KCUS,WinNT://KCUS/DL010858,W62002,Delegate,+OSD_Applications  Development,
QA SMS Rights,KCUS,WinNT://KCUS/QA SMS Rights,E18722,Owner,Allows access to the QA servers SMS directories,
QA SMS Rights,KCUS,WinNT://KCUS/QA SMS Rights,Q06085,Authorizer,Allows access to the QA servers SMS directories,
QA SMS Rights,KCUS,WinNT://KCUS/QA SMS Rights,W48997,Authorizer,Allows access to the QA servers SMS directories,
QA SMS Rights,KCUS,WinNT://KCUS/QA SMS Rights,W66442,Delegate,Allows access to the QA servers SMS directories,
USDQTestApps_C,KCUS,WinNT://KCUS/USDQTestApps_C,B00982,Authorizer,\\USDQA005\Applications,
USDQTestApps_C,KCUS,WinNT://KCUS/USDQTestApps_C,E18722,Owner,\\USDQA005\Applications,
USDQTestApps_C,KCUS,WinNT://KCUS/USDQTestApps_C,W66442,Delegate,\\USDQA005\Applications,
GRP06072_C,KCUS,WinNT://KCUS/GRP06072_C,E18722,Owner,Mailbox: _Support  SharePoint Alertss,
GRP06072_C,KCUS,WinNT://KCUS/GRP06072_C,W49040,Delegate,Mailbox: _Support  SharePoint Alertss,
DL007778,KCUS,WinNT://KCUS/DL007778,E18722,Owner,+GLOBAL_ITS  OCS Monitoring Reports Users,
DL007778,KCUS,WinNT://KCUS/DL007778,W62002,Delegate,+GLOBAL_ITS  OCS Monitoring Reports Users,
Desktop Access-SAP,KCUS,WinNT://KCUS/Desktop Access-SAP,E18722,Owner,Security group for managing SAP GUI,
Desktop Access-SAP,KCUS,WinNT://KCUS/Desktop Access-SAP,Q06085,Authorizer,Security group for managing SAP GUI,
Desktop Access-SAP,KCUS,WinNT://KCUS/Desktop Access-SAP,W66442,Delegate,Security group for managing SAP GUI,
HostAppMenu-3PL-UA,KCUS,WinNT://KCUS/HostAppMenu-3PL-UA,Q14718,Authorizer,User Acceptance for 3PL,
HostAppMenu-3PL-UA,KCUS,WinNT://KCUS/HostAppMenu-3PL-UA,Q15926,Authorizer,User Acceptance for 3PL,
HostAppMenu-3PL-UA,KCUS,WinNT://KCUS/HostAppMenu-3PL-UA,W49040,Delegate,User Acceptance for 3PL,
HostAppMenu-3PL-UA,KCUS,WinNT://KCUS/HostAppMenu-3PL-UA,E18722,Owner,User Acceptance for 3PL,
HostAppMenu-3PL-UA,KCUS,WinNT://KCUS/HostAppMenu-3PL-UA,Q07708,Authorizer,User Acceptance for 3PL,
HostAppMenu-3PL-UA,KCUS,WinNT://KCUS/HostAppMenu-3PL-UA,Q13357,Authorizer,User Acceptance for 3PL,
HostAppMenu-3PL-UA,KCUS,WinNT://KCUS/HostAppMenu-3PL-UA,Q14616,Authorizer,User Acceptance for 3PL,
Mobile Email Access,KCUS,WinNT://KCUS/Mobile Email Access,E18722,Owner,For access to Windows Mobile ActiveSync; member of KCINET group,
Mobile Email Access,KCUS,WinNT://KCUS/Mobile Email Access,W62002,Delegate,For access to Windows Mobile ActiveSync; member of KCINET group,
Personal Mobile Device Pilot,KCUS,WinNT://KCUS/Personal Mobile Device Pilot,E18722,Owner,Grant access to the user to sign the EUA form and enroll their personal mobile device as a pilot user,
Personal Mobile Device Pilot,KCUS,WinNT://KCUS/Personal Mobile Device Pilot,E32872,Delegate,Grant access to the user to sign the EUA form and enroll their personal mobile device as a pilot user,
GRP04865_C,KCUS,WinNT://KCUS/GRP04865_C,E18722,Owner,Mailbox: _Support  Consumer Internet Sites,
GRP04865_C,KCUS,WinNT://KCUS/GRP04865_C,W66442,Delegate,Mailbox: _Support  Consumer Internet Sites,
Organization Management,KCUS,WinNT://KCUS/Organization Management,E18722,Owner,Members of this management role group have permissions to manage Exchange objects and their properties in the Exchange organization. Members can also delegate role groups and management roles in the organization. This role group shouldn't be deleted.,
Organization Management,KCUS,WinNT://KCUS/Organization Management,W62002,Delegate,Members of this management role group have permissions to manage Exchange objects and their properties in the Exchange organization. Members can also delegate role groups and management roles in the organization. This role group shouldn't be deleted.,
View-Only Organization Management,KCUS,WinNT://KCUS/View-Only Organization Management,E18722,Owner,Members of this management role group can view recipient and configuration objects and their properties in the Exchange organization.,
View-Only Organization Management,KCUS,WinNT://KCUS/View-Only Organization Management,W62002,Delegate,Members of this management role group can view recipient and configuration objects and their properties in the Exchange organization.,
Recipient Management,KCUS,WinNT://KCUS/Recipient Management,E18722,Owner,Members of this management role group have rights to create  manage  and remove Exchange recipient objects in the Exchange organization.,
Recipient Management,KCUS,WinNT://KCUS/Recipient Management,W62002,Delegate,Members of this management role group have rights to create  manage  and remove Exchange recipient objects in the Exchange organization.,
UM Management,KCUS,WinNT://KCUS/UM Management,E18722,Owner,Members of this management role group can manage Unified Messaging organization  server  and recipient configuration.,
UM Management,KCUS,WinNT://KCUS/UM Management,W62002,Delegate,Members of this management role group can manage Unified Messaging organization  server  and recipient configuration.,
Discovery Management,KCUS,WinNT://KCUS/Discovery Management,E18722,Owner,Members of this management role group can perform searches of mailboxes in the Exchange organization for data that meets specific criteria.,
Discovery Management,KCUS,WinNT://KCUS/Discovery Management,W62002,Delegate,Members of this management role group can perform searches of mailboxes in the Exchange organization for data that meets specific criteria.,
Server Management,KCUS,WinNT://KCUS/Server Management,E18722,Owner,Members of this management role group have permissions to manage all Exchange servers within the Exchange organization  but members don't have permissions to perform operations that have global impact in the Exchange organization.,
Server Management,KCUS,WinNT://KCUS/Server Management,W62002,Delegate,Members of this management role group have permissions to manage all Exchange servers within the Exchange organization  but members don't have permissions to perform operations that have global impact in the Exchange organization.,
HostApp-PIPPO,KCUS,WinNT://KCUS/HostApp-PIPPO,E18722,Owner,Citrix Application security group for PIPPO,
HostApp-PIPPO,KCUS,WinNT://KCUS/HostApp-PIPPO,Q07708,Authorizer,Citrix Application security group for PIPPO,
HostApp-PIPPO,KCUS,WinNT://KCUS/HostApp-PIPPO,Q14616,Authorizer,Citrix Application security group for PIPPO,
HostApp-PIPPO,KCUS,WinNT://KCUS/HostApp-PIPPO,W49040,Delegate,Citrix Application security group for PIPPO,
HostApp-MSD,KCUS,WinNT://KCUS/HostApp-MSD,E18722,Owner,Citrix Application security group for MS Dynamics,
HostApp-MSD,KCUS,WinNT://KCUS/HostApp-MSD,Q07708,Authorizer,Citrix Application security group for MS Dynamics,
HostApp-MSD,KCUS,WinNT://KCUS/HostApp-MSD,Q14616,Authorizer,Citrix Application security group for MS Dynamics,
HostApp-MSD,KCUS,WinNT://KCUS/HostApp-MSD,W49040,Delegate,Citrix Application security group for MS Dynamics,
EDXfer,KCUS,WinNT://KCUS/EDXfer,E18722,Owner,Group with access to secure location for transfer of CEO audio/video files,
EDXfer,KCUS,WinNT://KCUS/EDXfer,W48997,Delegate,Group with access to secure location for transfer of CEO audio/video files,
GRP12842_C,KCUS,WinNT://KCUS/GRP12842_C,E18722,Owner,Mailbox: _Suspicious  Alerts,
GRP12842_C,KCUS,WinNT://KCUS/GRP12842_C,W62002,Delegate,Mailbox: _Suspicious  Alerts,
GRP12843_C,KCUS,WinNT://KCUS/GRP12843_C,E18722,Owner,Mailbox: _KCCRUA  KCC,
GRP12843_C,KCUS,WinNT://KCUS/GRP12843_C,W62002,Delegate,Mailbox: _KCCRUA  KCC,
Admin ECCM Support,KCUS,WinNT://KCUS/Admin ECCM Support,E18722,Owner,Administration for SharePoint & K2 Support,
Admin ECCM Support,KCUS,WinNT://KCUS/Admin ECCM Support,W48997,Delegate,Administration for SharePoint & K2 Support,
HostApp-RPWI-SRDC,KCUS,WinNT://KCUS/HostApp-RPWI-SRDC,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for Southern Regional DC,
HostApp-RPWI-SRDC,KCUS,WinNT://KCUS/HostApp-RPWI-SRDC,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Southern Regional DC,
HostApp-RPWI-SRDC,KCUS,WinNT://KCUS/HostApp-RPWI-SRDC,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Southern Regional DC,
HostApp-RPWI-SRDC,KCUS,WinNT://KCUS/HostApp-RPWI-SRDC,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for Southern Regional DC,
MS Visio Professional,KCUS,WinNT://KCUS/MS Visio Professional,E18722,Owner,Authorized Users – Visio Pro,
MS Visio Professional,KCUS,WinNT://KCUS/MS Visio Professional,Q13357,Authorizer,Authorized Users – Visio Pro,
MS Visio Professional,KCUS,WinNT://KCUS/MS Visio Professional,Q14718,Authorizer,Authorized Users – Visio Pro,
MS Visio Professional,KCUS,WinNT://KCUS/MS Visio Professional,Q15926,Authorizer,Authorized Users – Visio Pro,
MS Visio Professional,KCUS,WinNT://KCUS/MS Visio Professional,W66442,Delegate,Authorized Users – Visio Pro,
HostApp-IBMTools,KCUS,WinNT://KCUS/HostApp-IBMTools,E18722,Owner,Citrix Application security group for IBM Tools,
HostApp-IBMTools,KCUS,WinNT://KCUS/HostApp-IBMTools,Q07708,Authorizer,Citrix Application security group for IBM Tools,
HostApp-IBMTools,KCUS,WinNT://KCUS/HostApp-IBMTools,Q14616,Authorizer,Citrix Application security group for IBM Tools,
HostApp-IBMTools,KCUS,WinNT://KCUS/HostApp-IBMTools,W49040,Delegate,Citrix Application security group for IBM Tools,
DesktopServicesApps,KCUS,WinNT://KCUS/DesktopServicesApps,E18722,Owner,Access Control for Desktop Services Applications,
DesktopServicesApps,KCUS,WINNT://KCUS/DESKTOPSERVICESAPPS,Q73491,Authorizer,Access Control for Desktop Services Applications,
DesktopServicesApps,KCUS,WinNT://KCUS/DesktopServicesApps,W66442,Delegate,Access Control for Desktop Services Applications,
Trend_Admin,KCUS,WinNT://KCUS/Trend_Admin,E18722,Owner,Trend Server Administrators,
Trend_Admin,KCUS,WinNT://KCUS/Trend_Admin,W66442,Delegate,Trend Server Administrators,
Public Folder Management,KCUS,WinNT://KCUS/Public Folder Management,E18722,Owner,Members of this management role group can manage public folders. Members can create and delete public folders and manage public folder settings such as replicas  quotas  age limits  and permissions as well as mail-enable and mail-disable public folders.,
Public Folder Management,KCUS,WinNT://KCUS/Public Folder Management,W62002,Delegate,Members of this management role group can manage public folders. Members can create and delete public folders and manage public folder settings such as replicas  quotas  age limits  and permissions as well as mail-enable and mail-disable public folders.,
DL001916_C,KCUS,WinNT://KCUS/DL001916_C,E18722,Owner,Users who can send mails to DL001916,
DL001916_C,KCUS,WinNT://KCUS/DL001916_C,Q06085,Authorizer,Users who can send mails to DL001916,
DL001916_C,KCUS,WinNT://KCUS/DL001916_C,Q08911,Authorizer,Users who can send mails to DL001916,
DL001916_C,KCUS,WinNT://KCUS/DL001916_C,W62002,Delegate,Users who can send mails to DL001916,
EDMT Install,KCUS,WinNT://KCUS/EDMT Install,E18722,Owner,Desktop Team Installer ID Rights to Create Pipe Accounts,
EDMT Install,KCUS,WinNT://KCUS/EDMT Install,Q06085,Authorizer,Desktop Team Installer ID Rights to Create Pipe Accounts,
EDMT Install,KCUS,WinNT://KCUS/EDMT Install,Q08911,Authorizer,Desktop Team Installer ID Rights to Create Pipe Accounts,
EDMT Install,KCUS,WinNT://KCUS/EDMT Install,W48997,Delegate,Desktop Team Installer ID Rights to Create Pipe Accounts,
MS Project Standard,KCUS,WinNT://KCUS/MS Project Standard,E18722,Owner,Authorized Users – Project Standard,
MS Project Standard,KCUS,WinNT://KCUS/MS Project Standard,Q13357,Authorizer,Authorized Users – Project Standard,
MS Project Standard,KCUS,WinNT://KCUS/MS Project Standard,Q14718,Authorizer,Authorized Users – Project Standard,
MS Project Standard,KCUS,WinNT://KCUS/MS Project Standard,Q15926,Authorizer,Authorized Users – Project Standard,
MS Project Standard,KCUS,WinNT://KCUS/MS Project Standard,W66442,Delegate,Authorized Users – Project Standard,
MS Project Professional,KCUS,WinNT://KCUS/MS Project Professional,E18722,Owner,Authorized Users – Project Pro,
MS Project Professional,KCUS,WinNT://KCUS/MS Project Professional,Q13357,Authorizer,Authorized Users – Project Pro,
MS Project Professional,KCUS,WinNT://KCUS/MS Project Professional,Q14718,Authorizer,Authorized Users – Project Pro,
MS Project Professional,KCUS,WinNT://KCUS/MS Project Professional,Q15926,Authorizer,Authorized Users – Project Pro,
MS Project Professional,KCUS,WinNT://KCUS/MS Project Professional,W66442,Delegate,Authorized Users – Project Pro,
MS OneNote,KCUS,WinNT://KCUS/MS OneNote,W66442,Delegate,Authorized Users – OneNote,
MS OneNote,KCUS,WinNT://KCUS/MS OneNote,E18722,Owner,Authorized Users – OneNote,
MS OneNote,KCUS,WinNT://KCUS/MS OneNote,Q13357,Authorizer,Authorized Users – OneNote,
MS OneNote,KCUS,WinNT://KCUS/MS OneNote,Q14718,Authorizer,Authorized Users – OneNote,
MS OneNote,KCUS,WinNT://KCUS/MS OneNote,Q15926,Authorizer,Authorized Users – OneNote,
MS Access,KCUS,WinNT://KCUS/MS Access,E18722,Owner,Authorized Users – Access,
MS Access,KCUS,WinNT://KCUS/MS Access,Q13357,Authorizer,Authorized Users – Access,
MS Access,KCUS,WinNT://KCUS/MS Access,Q14718,Authorizer,Authorized Users – Access,
MS Access,KCUS,WinNT://KCUS/MS Access,Q15926,Authorizer,Authorized Users – Access,
MS Access,KCUS,WinNT://KCUS/MS Access,W66442,Delegate,Authorized Users – Access,
www.gcps.kcc.com_owners,KCUS,WinNT://KCUS/www.gcps.kcc.com_owners,E18722,Owner,Group for website owners,
www.gcps.kcc.com_owners,KCUS,WinNT://KCUS/www.gcps.kcc.com_owners,Q73491,Delegate,Group for website owners,
www.gcps.kcc.com_businesspartners,KCUS,WinNT://KCUS/www.gcps.kcc.com_businesspartners,E18722,Owner,Group for website buisnesspartners,
www.gcps.kcc.com_businesspartners,KCUS,WinNT://KCUS/www.gcps.kcc.com_businesspartners,Q73491,Delegate,Group for website buisnesspartners,
www.gcps.kcc.com_authorizers,KCUS,WinNT://KCUS/www.gcps.kcc.com_authorizers,E18722,Owner,Group for website Authorizers,
www.gcps.kcc.com_authorizers,KCUS,WinNT://KCUS/www.gcps.kcc.com_authorizers,Q73491,Delegate,Group for website Authorizers,
www.gcps.kcc.com_authorizers_technical,KCUS,WinNT://KCUS/www.gcps.kcc.com_authorizers_technical,E18722,Owner,Group for website authorizer_technical,
www.gcps.kcc.com_authorizers_technical,KCUS,WinNT://KCUS/www.gcps.kcc.com_authorizers_technical,Q73491,Delegate,Group for website authorizer_technical,
www.gcps.kcc.com_developers,KCUS,WinNT://KCUS/www.gcps.kcc.com_developers,E18722,Owner,Group for website developers,
www.gcps.kcc.com_developers,KCUS,WinNT://KCUS/www.gcps.kcc.com_developers,Q73491,Delegate,Group for website developers,
www.gcps.kcc.com_agencies,KCUS,WinNT://KCUS/www.gcps.kcc.com_agencies,E18722,Owner,Group for agencies,
www.gcps.kcc.com_agencies,KCUS,WinNT://KCUS/www.gcps.kcc.com_agencies,Q73491,Delegate,Group for agencies,
Microsoft Office - 64 Bit,KCUS,WinNT://KCUS/Microsoft Office - 64 Bit,E18722,Owner,This group will provide MS office 2013 & 2016 - 64 bit to the requested users,
Microsoft Office - 64 Bit,KCUS,WinNT://KCUS/Microsoft Office - 64 Bit,Q13243,Authorizer,This group will provide MS office 2013 & 2016 - 64 bit to the requested users,
Microsoft Office - 64 Bit,KCUS,WinNT://KCUS/Microsoft Office - 64 Bit,W48997,Delegate,This group will provide MS office 2013 & 2016 - 64 bit to the requested users,
GRP07974_C,KCUS,WinNT://KCUS/GRP07974_C,E18722,Owner,Mailbox:_Support  MassTransit,
GRP07974_C,KCUS,WinNT://KCUS/GRP07974_C,W49040,Delegate,Mailbox:_Support  MassTransit,
KCAPPS_CorpMisMsdn_C,KCUS,WinNT://KCUS/KCAPPS_CorpMisMsdn_C,E18722,Owner,\\kcapps\share\corporate\mis\msdn,
KCAPPS_CorpMisMsdn_C,KCUS,WinNT://KCUS/KCAPPS_CorpMisMsdn_C,W66442,Delegate,\\kcapps\share\corporate\mis\msdn,
KCAPPS_CorpMisTechnet_C,KCUS,WinNT://KCUS/KCAPPS_CorpMisTechnet_C,E18722,Owner,\\kcapps\share\corporate\mis\technet,
KCAPPS_CorpMisTechnet_C,KCUS,WinNT://KCUS/KCAPPS_CorpMisTechnet_C,W66442,Delegate,\\kcapps\share\corporate\mis\technet,
SCCMApps-MSAccess2016,KCUS,WinNT://KCUS/SCCMApps-MSAccess2016,E18722,Owner,MS Access 2016 Distribution Group,
SCCMApps-MSAccess2016,KCUS,WinNT://KCUS/SCCMApps-MSAccess2016,Q04499,Authorizer,MS Access 2016 Distribution Group,
SCCMApps-MSAccess2016,KCUS,WinNT://KCUS/SCCMApps-MSAccess2016,Q06085,Authorizer,MS Access 2016 Distribution Group,
SCCMApps-MSAccess2016,KCUS,WinNT://KCUS/SCCMApps-MSAccess2016,W48997,Delegate,MS Access 2016 Distribution Group,
APAdmin,KCUS,WinNT://KCUS/APAdmin,E18722,Owner,Nested in all AP WKS groups to provide Admin access to the AP desktop team.,
APAdmin,KCUS,WinNT://KCUS/APAdmin,Q06085,Authorizer,Nested in all AP WKS groups to provide Admin access to the AP desktop team.,
APAdmin,KCUS,WinNT://KCUS/APAdmin,Q08911,Authorizer,Nested in all AP WKS groups to provide Admin access to the AP desktop team.,
APAdmin,KCUS,WinNT://KCUS/APAdmin,W48997,Delegate,Nested in all AP WKS groups to provide Admin access to the AP desktop team.,
Password Wizard,KCUS,WinNT://KCUS/Password Wizard,E18722,Owner,Access to Reset Passwords on the Password Wizard Web Site,
Password Wizard,KCUS,WinNT://KCUS/Password Wizard,Q06371,Authorizer,Access to Reset Passwords on the Password Wizard Web Site,
Password Wizard,KCUS,WinNT://KCUS/Password Wizard,W48997,Delegate,Access to Reset Passwords on the Password Wizard Web Site,
HostServerAdmin-MSD,KCUS,WinNT://KCUS/HostServerAdmin-MSD,Q15926,Authorizer,Admin Access group for MS Dynamics Citrix servers,
HostServerAdmin-MSD,KCUS,WinNT://KCUS/HostServerAdmin-MSD,W49040,Delegate,Admin Access group for MS Dynamics Citrix servers,
HostServerAdmin-MSD,KCUS,WinNT://KCUS/HostServerAdmin-MSD,E18722,Owner,Admin Access group for MS Dynamics Citrix servers,
HostServerAdmin-MSD,KCUS,WinNT://KCUS/HostServerAdmin-MSD,Q07708,Authorizer,Admin Access group for MS Dynamics Citrix servers,
HostServerAdmin-MSD,KCUS,WinNT://KCUS/HostServerAdmin-MSD,Q13357,Authorizer,Admin Access group for MS Dynamics Citrix servers,
HostServerAdmin-MSD,KCUS,WinNT://KCUS/HostServerAdmin-MSD,Q14616,Authorizer,Admin Access group for MS Dynamics Citrix servers,
HostServerAdmin-MSD,KCUS,WinNT://KCUS/HostServerAdmin-MSD,Q14718,Authorizer,Admin Access group for MS Dynamics Citrix servers,
Win11_v22H2_Exclusion,KCUS,WinNT://KCUS/Win11_v22H2_Exclusion,E18722,Owner,Short term exception group to manage Windows 11 version 22H2 upgrade,
Win11_v22H2_Exclusion,KCUS,WinNT://KCUS/Win11_v22H2_Exclusion,Q13243,Authorizer,Short term exception group to manage Windows 11 version 22H2 upgrade,
Win11_v22H2_Exclusion,KCUS,WinNT://KCUS/Win11_v22H2_Exclusion,Q14717,Authorizer,Short term exception group to manage Windows 11 version 22H2 upgrade,
Win11_v22H2_Exclusion,KCUS,WinNT://KCUS/Win11_v22H2_Exclusion,W48997,Delegate,Short term exception group to manage Windows 11 version 22H2 upgrade,
HostApp-Hyperion-HFR,KCUS,WinNT://KCUS/HostApp-Hyperion-HFR,E18722,Owner,Application security group for Hyperion HFR hosted on Citrix,
HostApp-Hyperion-HFR,KCUS,WinNT://KCUS/HostApp-Hyperion-HFR,Q07708,Authorizer,Application security group for Hyperion HFR hosted on Citrix,
HostApp-Hyperion-HFR,KCUS,WinNT://KCUS/HostApp-Hyperion-HFR,Q14616,Authorizer,Application security group for Hyperion HFR hosted on Citrix,
HostApp-Hyperion-HFR,KCUS,WinNT://KCUS/HostApp-Hyperion-HFR,W49040,Delegate,Application security group for Hyperion HFR hosted on Citrix,
Hostapp-GLSU,KCUS,WinNT://KCUS/Hostapp-GLSU,E18722,Owner,Citrix Application security group for GLSU excel addin users,
Hostapp-GLSU,KCUS,WinNT://KCUS/Hostapp-GLSU,Q07708,Authorizer,Citrix Application security group for GLSU excel addin users,
Hostapp-GLSU,KCUS,WinNT://KCUS/Hostapp-GLSU,Q13357,Authorizer,Citrix Application security group for GLSU excel addin users,
Hostapp-GLSU,KCUS,WinNT://KCUS/Hostapp-GLSU,Q14616,Authorizer,Citrix Application security group for GLSU excel addin users,
Hostapp-GLSU,KCUS,WinNT://KCUS/Hostapp-GLSU,Q14718,Authorizer,Citrix Application security group for GLSU excel addin users,
Hostapp-GLSU,KCUS,WinNT://KCUS/Hostapp-GLSU,Q15926,Authorizer,Citrix Application security group for GLSU excel addin users,
Hostapp-GLSU,KCUS,WinNT://KCUS/Hostapp-GLSU,W49040,Delegate,Citrix Application security group for GLSU excel addin users,
O365 K-C Intune Reporting,KCUS,WinNT://KCUS/O365 K-C Intune Reporting,E18722,Owner,This group will be used to grant access to the Intune data warehouse,
O365 K-C Intune Reporting,KCUS,WinNT://KCUS/O365 K-C Intune Reporting,W62002,Delegate,This group will be used to grant access to the Intune data warehouse,
PST Exception,KCUS,WinNT://KCUS/PST Exception,E18722,Owner,Exception to pst freeze policy,
PST Exception,KCUS,WinNT://KCUS/PST Exception,Q03582,Authorizer,Exception to pst freeze policy,
PST Exception,KCUS,WinNT://KCUS/PST Exception,W62002,Delegate,Exception to pst freeze policy,
GRP08015_C,KCUS,WinNT://KCUS/GRP08015_C,B62398,Authorizer,Mailbox:_Apple vpp  USs,
GRP08015_C,KCUS,WinNT://KCUS/GRP08015_C,E18722,Owner,Mailbox:_Apple vpp  USs,
GRP08015_C,KCUS,WinNT://KCUS/GRP08015_C,W48997,Delegate,Mailbox:_Apple vpp  USs,
GRP07791_C,KCUS,WinNT://KCUS/GRP07791_C,E18722,Owner,Mailbox:_Support  Citrixs,
GRP07791_C,KCUS,WinNT://KCUS/GRP07791_C,Q13357,Authorizer,Mailbox:_Support  Citrixs,
GRP07791_C,KCUS,WinNT://KCUS/GRP07791_C,Q14718,Authorizer,Mailbox:_Support  Citrixs,
GRP07791_C,KCUS,WinNT://KCUS/GRP07791_C,Q15926,Authorizer,Mailbox:_Support  Citrixs,
GRP07791_C,KCUS,WinNT://KCUS/GRP07791_C,W49040,Delegate,Mailbox:_Support  Citrixs,
HostApp-WMS-FULLERTON,KCUS,WinNT://KCUS/HostApp-WMS-FULLERTON,E18722,Owner,Citrix Application Security group for hosting WMS FULLERTON Web Application,
HostApp-WMS-FULLERTON,KCUS,WinNT://KCUS/HostApp-WMS-FULLERTON,Q07708,Authorizer,Citrix Application Security group for hosting WMS FULLERTON Web Application,
HostApp-WMS-FULLERTON,KCUS,WinNT://KCUS/HostApp-WMS-FULLERTON,Q13357,Authorizer,Citrix Application Security group for hosting WMS FULLERTON Web Application,
HostApp-WMS-FULLERTON,KCUS,WinNT://KCUS/HostApp-WMS-FULLERTON,Q14616,Authorizer,Citrix Application Security group for hosting WMS FULLERTON Web Application,
HostApp-WMS-FULLERTON,KCUS,WinNT://KCUS/HostApp-WMS-FULLERTON,Q14718,Authorizer,Citrix Application Security group for hosting WMS FULLERTON Web Application,
HostApp-WMS-FULLERTON,KCUS,WinNT://KCUS/HostApp-WMS-FULLERTON,Q15926,Authorizer,Citrix Application Security group for hosting WMS FULLERTON Web Application,
HostApp-WMS-FULLERTON,KCUS,WinNT://KCUS/HostApp-WMS-FULLERTON,W49040,Delegate,Citrix Application Security group for hosting WMS FULLERTON Web Application,
ActiveSync Access Desk,KCUS,WinNT://KCUS/ActiveSync Access Desk,E18722,Owner,,
ActiveSync Access Desk,KCUS,WinNT://KCUS/ActiveSync Access Desk,W62002,Delegate,,
Office 2016 Exclusion,KCUS,WinNT://KCUS/Office 2016 Exclusion,E18722,Owner,This group is to exclude the users for Office 2016 upgrade,
Office 2016 Exclusion,KCUS,WinNT://KCUS/Office 2016 Exclusion,Q13243,Authorizer,This group is to exclude the users for Office 2016 upgrade,
Office 2016 Exclusion,KCUS,WinNT://KCUS/Office 2016 Exclusion,W48997,Delegate,This group is to exclude the users for Office 2016 upgrade,
SQLSpotlightExchange_Admin,KCUS,WinNT://KCUS/SQLSpotlightExchange_Admin,E18722,Owner,Group with admin privileges within the Spotlight on Exchange Database USTCAS41&SQL prod server,
SQLSpotlightExchange_Admin,KCUS,WinNT://KCUS/SQLSpotlightExchange_Admin,W62002,Delegate,Group with admin privileges within the Spotlight on Exchange Database USTCAS41&SQL prod server,
SCCM Duplicate GUID Fix,KCUS,WinNT://KCUS/SCCM Duplicate GUID Fix,E18722,Owner,The computers in this group will receive a task through GPO that fixes the SCCM Duplicate GUID issue.,
SCCM Duplicate GUID Fix,KCUS,WinNT://KCUS/SCCM Duplicate GUID Fix,Q04499,Authorizer,The computers in this group will receive a task through GPO that fixes the SCCM Duplicate GUID issue.,
SCCM Duplicate GUID Fix,KCUS,WinNT://KCUS/SCCM Duplicate GUID Fix,Q13243,Authorizer,The computers in this group will receive a task through GPO that fixes the SCCM Duplicate GUID issue.,
SCCM Duplicate GUID Fix,KCUS,WinNT://KCUS/SCCM Duplicate GUID Fix,W48997,Delegate,The computers in this group will receive a task through GPO that fixes the SCCM Duplicate GUID issue.,
HostServerAdmin-Controlm,KCUS,WinNT://KCUS/HostServerAdmin-Controlm,E18722,Owner,Admin Access group for Control-M Citrix servers,
HostServerAdmin-Controlm,KCUS,WinNT://KCUS/HostServerAdmin-Controlm,Q07708,Authorizer,Admin Access group for Control-M Citrix servers,
HostServerAdmin-Controlm,KCUS,WinNT://KCUS/HostServerAdmin-Controlm,Q13357,Authorizer,Admin Access group for Control-M Citrix servers,
HostServerAdmin-Controlm,KCUS,WinNT://KCUS/HostServerAdmin-Controlm,Q14616,Authorizer,Admin Access group for Control-M Citrix servers,
HostServerAdmin-Controlm,KCUS,WinNT://KCUS/HostServerAdmin-Controlm,Q14718,Authorizer,Admin Access group for Control-M Citrix servers,
HostServerAdmin-Controlm,KCUS,WinNT://KCUS/HostServerAdmin-Controlm,Q15926,Authorizer,Admin Access group for Control-M Citrix servers,
HostServerAdmin-Controlm,KCUS,WinNT://KCUS/HostServerAdmin-Controlm,W49040,Delegate,Admin Access group for Control-M Citrix servers,
GRP12586_C,KCUS,WinNT://KCUS/GRP12586_C,E18722,Owner,Mailbox: _Test  Messaging,
GRP12586_C,KCUS,WinNT://KCUS/GRP12586_C,q09038,Authorizer,Mailbox: _Test  Messaging,
GRP12586_C,KCUS,WinNT://KCUS/GRP12586_C,W62002,Delegate,Mailbox: _Test  Messaging,
Deny-Both-ICW Core,KCUS,WinNT://KCUS/Deny-Both-ICW Core,E18722,Owner,Deny policy group for the ICW platform,
Deny-Both-ICW Core,KCUS,WinNT://KCUS/Deny-Both-ICW Core,Q06085,Authorizer,Deny policy group for the ICW platform,
Deny-Both-ICW Core,KCUS,WinNT://KCUS/Deny-Both-ICW Core,Q08911,Authorizer,Deny policy group for the ICW platform,
Deny-Both-ICW Core,KCUS,WinNT://KCUS/Deny-Both-ICW Core,W48997,Delegate,Deny policy group for the ICW platform,
Deny-Both-Kiosk2000 Core,KCUS,WinNT://KCUS/Deny-Both-Kiosk2000 Core,E18722,Owner,Deny policy group on KIOSK to be used only w/ INstaller accoutns and "S" accounts,
Deny-Both-Kiosk2000 Core,KCUS,WinNT://KCUS/Deny-Both-Kiosk2000 Core,Q06085,Authorizer,Deny policy group on KIOSK to be used only w/ INstaller accoutns and "S" accounts,
Deny-Both-Kiosk2000 Core,KCUS,WinNT://KCUS/Deny-Both-Kiosk2000 Core,Q08911,Authorizer,Deny policy group on KIOSK to be used only w/ INstaller accoutns and "S" accounts,
Deny-Both-Kiosk2000 Core,KCUS,WinNT://KCUS/Deny-Both-Kiosk2000 Core,W48997,Delegate,Deny policy group on KIOSK to be used only w/ INstaller accoutns and "S" accounts,
KioskMachineAcctRights,KCUS,WinNT://KCUS/KioskMachineAcctRights,E18722,Owner,Provides rights to create/delete machine accounts through the Machine Account Utility,
KioskMachineAcctRights,KCUS,WinNT://KCUS/KioskMachineAcctRights,Q06085,Authorizer,Provides rights to create/delete machine accounts through the Machine Account Utility,
KioskMachineAcctRights,KCUS,WinNT://KCUS/KioskMachineAcctRights,Q08911,Authorizer,Provides rights to create/delete machine accounts through the Machine Account Utility,
KioskMachineAcctRights,KCUS,WinNT://KCUS/KioskMachineAcctRights,W48997,Delegate,Provides rights to create/delete machine accounts through the Machine Account Utility,
O365 Service Admins OKTA MFA enablement,KCUS,WinNT://KCUS/O365 Service Admins OKTA MFA enablement,E18722,Owner,Used to enable OKTA MFA for O365 Service Admins,
O365 Service Admins OKTA MFA enablement,KCUS,WinNT://KCUS/O365 Service Admins OKTA MFA enablement,Q03582,Authorizer,Used to enable OKTA MFA for O365 Service Admins,
O365 Service Admins OKTA MFA enablement,KCUS,WinNT://KCUS/O365 Service Admins OKTA MFA enablement,W62002,Delegate,Used to enable OKTA MFA for O365 Service Admins,
O365_AzureRightsManagement,KCUS,WinNT://KCUS/O365_AzureRightsManagement,E18722,Owner,provides access to the O365 Azure Rights Management service,
O365_AzureRightsManagement,KCUS,WinNT://KCUS/O365_AzureRightsManagement,W62002,Delegate,provides access to the O365 Azure Rights Management service,
iPad_Access - Personal Email Only,KCUS,WinNT://KCUS/iPad_Access - Personal Email Only,e11888,Authorizer,Provide Access to ActiveSync for personal iPads,
iPad_Access - Personal Email Only,KCUS,WinNT://KCUS/iPad_Access - Personal Email Only,E18722,Owner,Provide Access to ActiveSync for personal iPads,
iPad_Access - Personal Email Only,KCUS,WinNT://KCUS/iPad_Access - Personal Email Only,W66442,Delegate,Provide Access to ActiveSync for personal iPads,
RTCUniversalServerAdmins,KCUS,WinNT://KCUS/RTCUniversalServerAdmins,E18722,Owner,Members can manage all aspects of RTC servers in this forest.,
RTCUniversalServerAdmins,KCUS,WinNT://KCUS/RTCUniversalServerAdmins,W62002,Delegate,Members can manage all aspects of RTC servers in this forest.,
MSX Service Admin,KCUS,WinNT://KCUS/MSX Service Admin,E18722,Owner,Microsoft Exchange Server and Configuration Administrators,
MSX Service Admin,KCUS,WinNT://KCUS/MSX Service Admin,W62002,Delegate,Microsoft Exchange Server and Configuration Administrators,
USTCADM34_PSUsers,KCUS,WinNT://KCUS/USTCADM34_PSUsers,E18722,Owner,Users – US Neenah MassTransit Prod Admin,
USTCADM34_PSUsers,KCUS,WinNT://KCUS/USTCADM34_PSUsers,Q08911,Authorizer,Users – US Neenah MassTransit Prod Admin,
USTCADM34_PSUsers,KCUS,WinNT://KCUS/USTCADM34_PSUsers,W49040,Delegate,Users – US Neenah MassTransit Prod Admin,
DL010341,KCUS,WinNT://KCUS/DL010341,E18722,Owner,+NA_Marketing  BC Desktop Team,
DL010341,KCUS,WinNT://KCUS/DL010341,W66442,Delegate,+NA_Marketing  BC Desktop Team,
MFFdb_notify,KCUS,WinNT://KCUS/MFFdb_notify,E18722,Owner,This group can execute the Notify stored procedure for the Messaging Service Account Application,
MFFdb_notify,KCUS,WinNT://KCUS/MFFdb_notify,W62002,Delegate,This group can execute the Notify stored procedure for the Messaging Service Account Application,
GRP09899_C,KCUS,WinNT://KCUS/GRP09899_C,E18722,Owner,Mailbox:_Dep  KC Mobility,
GRP09899_C,KCUS,WinNT://KCUS/GRP09899_C,W62002,Delegate,Mailbox:_Dep  KC Mobility,
Enable Unattended sleep timeout,KCUS,WinNT://KCUS/Enable Unattended sleep timeout,E18722,Owner,This is to resolve the power settings issue in Windows 10 machines.,
Enable Unattended sleep timeout,KCUS,WinNT://KCUS/Enable Unattended sleep timeout,Q05282,Authorizer,This is to resolve the power settings issue in Windows 10 machines.,
Enable Unattended sleep timeout,KCUS,WinNT://KCUS/Enable Unattended sleep timeout,q07749,Delegate,This is to resolve the power settings issue in Windows 10 machines.,
Enable Unattended sleep timeout,KCUS,WinNT://KCUS/Enable Unattended sleep timeout,Q08911,Authorizer,This is to resolve the power settings issue in Windows 10 machines.,
ADCleanup_Exceptions,KCUS,WinNT://KCUS/ADCleanup_Exceptions,B00982,Authorizer,Exception group used for machine accounts/user IDs that cannot be automatically deleted from active directory by the ADCleanup Utility,
ADCleanup_Exceptions,KCUS,WinNT://KCUS/ADCleanup_Exceptions,E18722,Owner,Exception group used for machine accounts/user IDs that cannot be automatically deleted from active directory by the ADCleanup Utility,
ADCleanup_Exceptions,KCUS,WinNT://KCUS/ADCleanup_Exceptions,Q08911,Authorizer,Exception group used for machine accounts/user IDs that cannot be automatically deleted from active directory by the ADCleanup Utility,
ADCleanup_Exceptions,KCUS,WinNT://KCUS/ADCleanup_Exceptions,W66442,Delegate,Exception group used for machine accounts/user IDs that cannot be automatically deleted from active directory by the ADCleanup Utility,
ADAPP_ItsCompSvcsDeskSvcs_C,KCUS,WinNT://KCUS/ADAPP_ItsCompSvcsDeskSvcs_C,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Application\...\NA.US.kcc.com.AutoDeployApps\root\its\computerservices\desktopservices,
ADAPP_ItsCompSvcsDeskSvcs_C,KCUS,WinNT://KCUS/ADAPP_ItsCompSvcsDeskSvcs_C,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Application\...\NA.US.kcc.com.AutoDeployApps\root\its\computerservices\desktopservices,
Admin GDPTW GDKIOSK,KCUS,WinNT://KCUS/Admin GDPTW GDKIOSK,E18722,Owner,GDPTW and GDKIOSK administrators,
Admin GDPTW GDKIOSK,KCUS,WinNT://KCUS/Admin GDPTW GDKIOSK,Q06085,Authorizer,GDPTW and GDKIOSK administrators,
Admin GDPTW GDKIOSK,KCUS,WinNT://KCUS/Admin GDPTW GDKIOSK,Q08911,Authorizer,GDPTW and GDKIOSK administrators,
Admin GDPTW GDKIOSK,KCUS,WinNT://KCUS/Admin GDPTW GDKIOSK,W48997,Delegate,GDPTW and GDKIOSK administrators,
HostAppMenu-ITS-TCS,KCUS,WinNT://KCUS/HostAppMenu-ITS-TCS,E18722,Owner,HostAppMenu-ITS-TCS,
HostAppMenu-ITS-TCS,KCUS,WinNT://KCUS/HostAppMenu-ITS-TCS,Q13357,Authorizer,HostAppMenu-ITS-TCS,
HostAppMenu-ITS-TCS,KCUS,WinNT://KCUS/HostAppMenu-ITS-TCS,Q14718,Authorizer,HostAppMenu-ITS-TCS,
HostAppMenu-ITS-TCS,KCUS,WinNT://KCUS/HostAppMenu-ITS-TCS,Q15926,Authorizer,HostAppMenu-ITS-TCS,
HostAppMenu-ITS-TCS,KCUS,WinNT://KCUS/HostAppMenu-ITS-TCS,W49040,Delegate,HostAppMenu-ITS-TCS,
TFS_eCIA_Developers,KCUS,WinNT://KCUS/TFS_eCIA_Developers,B00372,Delegate,Controls access to eCIA Team Foundation Server Project,
TFS_eCIA_Developers,KCUS,WinNT://KCUS/TFS_eCIA_Developers,E18722,Owner,Controls access to eCIA Team Foundation Server Project,
Office-2013-32Bit-Exception,KCUS,WinNT://KCUS/Office-2013-32Bit-Exception,E18722,Owner,This group is used to deploy Office 2013 32-bit to the approved PCs,
Office-2013-32Bit-Exception,KCUS,WinNT://KCUS/Office-2013-32Bit-Exception,Q06435,Authorizer,This group is used to deploy Office 2013 32-bit to the approved PCs,
Office-2013-32Bit-Exception,KCUS,WinNT://KCUS/Office-2013-32Bit-Exception,Q39116,Authorizer,This group is used to deploy Office 2013 32-bit to the approved PCs,
Office-2013-32Bit-Exception,KCUS,WinNT://KCUS/Office-2013-32Bit-Exception,W48997,Delegate,This group is used to deploy Office 2013 32-bit to the approved PCs,
Office-2013-32Bit-Exception,KCUS,WinNT://KCUS/Office-2013-32Bit-Exception,W66442,Authorizer,This group is used to deploy Office 2013 32-bit to the approved PCs,
Remediate Feature Update Errors,KCUS,WinNT://KCUS/Remediate Feature Update Errors,E18722,Owner,This Group will be used to apply fix for Feature update errors,
Remediate Feature Update Errors,KCUS,WinNT://KCUS/Remediate Feature Update Errors,Q07749,Delegate,This Group will be used to apply fix for Feature update errors,
USTC_Encode_Admin,KCUS,WinNT://KCUS/USTC_Encode_Admin,E18722,Owner,Access to \\USTCAMP01\ENCODEDFILES,
USTC_Encode_Admin,KCUS,WinNT://KCUS/USTC_Encode_Admin,W66442,Delegate,Access to \\USTCAMP01\ENCODEDFILES,
GRP04785_c,KCUS,WinNT://KCUS/GRP04785_c,B45906,Delegate,Mailbox:_Desktop Services  Standards,
GRP04785_c,KCUS,WinNT://KCUS/GRP04785_c,E18722,Owner,Mailbox:_Desktop Services  Standards,
GRP01252_C,KCUS,WinNT://KCUS/GRP01252_C,b45906,Authorizer,Mailbox: _PC Business Center  US-Nee,
GRP01252_C,KCUS,WinNT://KCUS/GRP01252_C,E18722,Owner,Mailbox: _PC Business Center  US-Nee,
GRP01252_C,KCUS,WinNT://KCUS/GRP01252_C,W48997,Delegate,Mailbox: _PC Business Center  US-Nee,
Windows Group Manager Admins,KCUS,WinNT://KCUS/Windows Group Manager Admins,E18722,Owner,Access for Windows group manager tool,
Windows Group Manager Admins,KCUS,WinNT://KCUS/Windows Group Manager Admins,Q06085,Authorizer,Access for Windows group manager tool,
Windows Group Manager Admins,KCUS,WinNT://KCUS/Windows Group Manager Admins,W48997,Delegate,Access for Windows group manager tool,
GRP03852_C,KCUS,WinNT://KCUS/GRP03852_C,B45906,Delegate,Mailbox: _PC Management  Enterprise,
GRP03852_C,KCUS,WinNT://KCUS/GRP03852_C,E18722,Owner,Mailbox: _PC Management  Enterprise,
HostApp-RPWI-PNDC,KCUS,WinNT://KCUS/HostApp-RPWI-PNDC,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for Pacific Northwest DC,
HostApp-RPWI-PNDC,KCUS,WinNT://KCUS/HostApp-RPWI-PNDC,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Pacific Northwest DC,
HostApp-RPWI-PNDC,KCUS,WinNT://KCUS/HostApp-RPWI-PNDC,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Pacific Northwest DC,
HostApp-RPWI-PNDC,KCUS,WinNT://KCUS/HostApp-RPWI-PNDC,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for Pacific Northwest DC,
GRP01211_C,KCUS,WinNT://KCUS/GRP01211_C,E18722,Owner,Mailbox : _Desktop Services  Distribution,
GRP01211_C,KCUS,WinNT://KCUS/GRP01211_C,Q04499,Authorizer,Mailbox : _Desktop Services  Distribution,
GRP01211_C,KCUS,WinNT://KCUS/GRP01211_C,Q06085,Authorizer,Mailbox : _Desktop Services  Distribution,
GRP01211_C,KCUS,WinNT://KCUS/GRP01211_C,W48997,Delegate,Mailbox : _Desktop Services  Distribution,
Hostapp-TopsUK,KCUS,WinNT://KCUS/Hostapp-TopsUK,E18722,Owner,Security group to be added for Tops application in Citrix,
Hostapp-TopsUK,KCUS,WinNT://KCUS/Hostapp-TopsUK,Q13357,Authorizer,Security group to be added for Tops application in Citrix,
Hostapp-TopsUK,KCUS,WinNT://KCUS/Hostapp-TopsUK,Q14718,Authorizer,Security group to be added for Tops application in Citrix,
Hostapp-TopsUK,KCUS,WinNT://KCUS/Hostapp-TopsUK,Q15926,Authorizer,Security group to be added for Tops application in Citrix,
Hostapp-TopsUK,KCUS,WinNT://KCUS/Hostapp-TopsUK,W49040,Delegate,Security group to be added for Tops application in Citrix,
TMG Read-Only Access,KCUS,WinNT://KCUS/TMG Read-Only Access,E18722,Owner,Grants Read-Only Access to the Microsoft Threat Management Gateway (TMG),
TMG Read-Only Access,KCUS,WinNT://KCUS/TMG Read-Only Access,W62002,Delegate,Grants Read-Only Access to the Microsoft Threat Management Gateway (TMG),
MSX Server Admin,KCINET,WinNT://KCINET/MSX Server Admin,E18722,Owner,Microsoft Exchange Permissions Administrators,
Admin WorldSecure,KCINET,WinNT://KCINET/Admin WorldSecure,E18722,Owner,World Encryption Setup,
QATesters_AdminTest-id,KCUS,WinNT://KCUS/QATesters_AdminTest-id,E18722,Owner,Contains the Admin test IDs of the Desktop QA Testers,
QATesters_AdminTest-id,KCUS,WinNT://KCUS/QATesters_AdminTest-id,Q04499,Delegate,Contains the Admin test IDs of the Desktop QA Testers,
QATesters_NonAdminTest-id,KCUS,WinNT://KCUS/QATesters_NonAdminTest-id,E18722,Owner,Contains the Non-Admin test IDs of the Desktop QA Testers,
QATesters_NonAdminTest-id,KCUS,WinNT://KCUS/QATesters_NonAdminTest-id,Q04499,Delegate,Contains the Non-Admin test IDs of the Desktop QA Testers,
GRP04143_C,KCUS,WinNT://KCUS/GRP04143_C,E18722,Owner,Mailbox: _Desktop Services  ITS,
GRP04143_C,KCUS,WinNT://KCUS/GRP04143_C,Q06085,Delegate,Mailbox: _Desktop Services  ITS,
HostApp-TN,KCUS,WinNT://KCUS/HostApp-TN,E18722,Owner, Citrix Application Security group for hosting TrueNorth,
HostApp-TN,KCUS,WinNT://KCUS/HostApp-TN,Q07708,Authorizer, Citrix Application Security group for hosting TrueNorth,
HostApp-TN,KCUS,WinNT://KCUS/HostApp-TN,Q13357,Authorizer, Citrix Application Security group for hosting TrueNorth,
HostApp-TN,KCUS,WinNT://KCUS/HostApp-TN,Q14616,Authorizer, Citrix Application Security group for hosting TrueNorth,
HostApp-TN,KCUS,WinNT://KCUS/HostApp-TN,Q14718,Authorizer, Citrix Application Security group for hosting TrueNorth,
HostApp-TN,KCUS,WinNT://KCUS/HostApp-TN,Q15926,Authorizer, Citrix Application Security group for hosting TrueNorth,
HostApp-TN,KCUS,WinNT://KCUS/HostApp-TN,W49040,Delegate, Citrix Application Security group for hosting TrueNorth,
HostApp-PDF2XL,KCUS,WinNT://KCUS/HostApp-PDF2XL,E18722,Owner,Citrix Application Security group for hosting PDF2XL,
HostApp-PDF2XL,KCUS,WinNT://KCUS/HostApp-PDF2XL,Q13357,Authorizer,Citrix Application Security group for hosting PDF2XL,
HostApp-PDF2XL,KCUS,WinNT://KCUS/HostApp-PDF2XL,Q14718,Authorizer,Citrix Application Security group for hosting PDF2XL,
HostApp-PDF2XL,KCUS,WinNT://KCUS/HostApp-PDF2XL,Q15926,Authorizer,Citrix Application Security group for hosting PDF2XL,
HostApp-PDF2XL,KCUS,WinNT://KCUS/HostApp-PDF2XL,W49040,Delegate,Citrix Application Security group for hosting PDF2XL,
RTCUniversalServerReadOnlyGroup,KCUS,WinNT://KCUS/RTCUniversalServerReadOnlyGroup,E18722,Owner,Members have read access to RTC-related server AD objects in the forest.,
RTCUniversalServerReadOnlyGroup,KCUS,WinNT://KCUS/RTCUniversalServerReadOnlyGroup,W62002,Delegate,Members have read access to RTC-related server AD objects in the forest.,
RTCUniversalReadOnlyAdmins,KCUS,WinNT://KCUS/RTCUniversalReadOnlyAdmins,E18722,Owner,Members can only read RTC related server and user properties in this forest.,
RTCUniversalReadOnlyAdmins,KCUS,WinNT://KCUS/RTCUniversalReadOnlyAdmins,W62002,Delegate,Members can only read RTC related server and user properties in this forest.,
RTCUniversalGuestAccessGroup,KCUS,WinNT://KCUS/RTCUniversalGuestAccessGroup,E18722,Owner,Members have readonly access to various Office Communications Server 2007 resources in this forest.,
RTCUniversalGuestAccessGroup,KCUS,WinNT://KCUS/RTCUniversalGuestAccessGroup,W62002,Delegate,Members have readonly access to various Office Communications Server 2007 resources in this forest.,
HostApp-CSD,KCUS,WinNT://KCUS/HostApp-CSD,E18722,Owner,HostApp-CSD,
HostApp-CSD,KCUS,WinNT://KCUS/HostApp-CSD,Q07708,Authorizer,HostApp-CSD,
HostApp-CSD,KCUS,WinNT://KCUS/HostApp-CSD,Q14616,Authorizer,HostApp-CSD,
HostApp-CSD,KCUS,WinNT://KCUS/HostApp-CSD,W49040,Delegate,HostApp-CSD,
HostApp-RPWI-SWDC,KCUS,WinNT://KCUS/HostApp-RPWI-SWDC,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for South West DC,
HostApp-RPWI-SWDC,KCUS,WinNT://KCUS/HostApp-RPWI-SWDC,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for South West DC,
HostApp-RPWI-SWDC,KCUS,WinNT://KCUS/HostApp-RPWI-SWDC,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for South West DC,
HostApp-RPWI-SWDC,KCUS,WinNT://KCUS/HostApp-RPWI-SWDC,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for South West DC,
HostApp-RPWI-OGD,KCUS,WinNT://KCUS/HostApp-RPWI-OGD,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for Ogden,
HostApp-RPWI-OGD,KCUS,WinNT://KCUS/HostApp-RPWI-OGD,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Ogden,
HostApp-RPWI-OGD,KCUS,WinNT://KCUS/HostApp-RPWI-OGD,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Ogden,
HostApp-RPWI-OGD,KCUS,WinNT://KCUS/HostApp-RPWI-OGD,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for Ogden,
HostApp-RPWI-SEDC,KCUS,WinNT://KCUS/HostApp-RPWI-SEDC,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for South East DC,
HostApp-RPWI-SEDC,KCUS,WinNT://KCUS/HostApp-RPWI-SEDC,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for South East DC,
HostApp-RPWI-SEDC,KCUS,WinNT://KCUS/HostApp-RPWI-SEDC,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for South East DC,
HostApp-RPWI-SEDC,KCUS,WinNT://KCUS/HostApp-RPWI-SEDC,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for South East DC,
HostApp-RPWI-ERDC,KCUS,WinNT://KCUS/HostApp-RPWI-ERDC,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Eastern Regional DC,
HostApp-RPWI-ERDC,KCUS,WinNT://KCUS/HostApp-RPWI-ERDC,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for Eastern Regional DC,
HostApp-RPWI-ERDC,KCUS,WinNT://KCUS/HostApp-RPWI-ERDC,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for Eastern Regional DC,
HostApp-RPWI-ERDC,KCUS,WinNT://KCUS/HostApp-RPWI-ERDC,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Eastern Regional DC,
DL008291,KCUS,WinNT://KCUS/DL008291,E18722,Owner,+Global  Messaging team,
DL008291,KCUS,WinNT://KCUS/DL008291,W62002,Delegate,+Global  Messaging team,
HostApp-PartnerCitrix,KCUS,WinNT://KCUS/HostApp-PartnerCitrix,E18722,Owner,Citrix Application Security group for hosting published desktop,
HostApp-PartnerCitrix,KCUS,WinNT://KCUS/HostApp-PartnerCitrix,Q07708,Authorizer,Citrix Application Security group for hosting published desktop,
HostApp-PartnerCitrix,KCUS,WinNT://KCUS/HostApp-PartnerCitrix,Q13357,Authorizer,Citrix Application Security group for hosting published desktop,
HostApp-PartnerCitrix,KCUS,WinNT://KCUS/HostApp-PartnerCitrix,Q14616,Authorizer,Citrix Application Security group for hosting published desktop,
HostApp-PartnerCitrix,KCUS,WinNT://KCUS/HostApp-PartnerCitrix,Q14718,Authorizer,Citrix Application Security group for hosting published desktop,
HostApp-PartnerCitrix,KCUS,WinNT://KCUS/HostApp-PartnerCitrix,Q15926,Authorizer,Citrix Application Security group for hosting published desktop,
HostApp-PartnerCitrix,KCUS,WinNT://KCUS/HostApp-PartnerCitrix,W49040,Delegate,Citrix Application Security group for hosting published desktop,
GPO - Test Group 2,KCUS,WinNT://KCUS/GPO - Test Group 2,E18722,Owner,This group will be used for testing Group Policy Objects in DEV/QA before they are production ready,
GPO - Test Group 2,KCUS,WinNT://KCUS/GPO - Test Group 2,Q04499,Authorizer,This group will be used for testing Group Policy Objects in DEV/QA before they are production ready,
GPO - Test Group 2,KCUS,WinNT://KCUS/GPO - Test Group 2,Q39116,Authorizer,This group will be used for testing Group Policy Objects in DEV/QA before they are production ready,
GPO - Test Group 2,KCUS,WinNT://KCUS/GPO - Test Group 2,W48997,Delegate,This group will be used for testing Group Policy Objects in DEV/QA before they are production ready,
Outlook password fix removal,KCUS,WinNT://KCUS/Outlook password fix removal,E18722,Owner,this group is used to remove the outlook need password registry fix,
Outlook password fix removal,KCUS,WinNT://KCUS/Outlook password fix removal,Q06085,Authorizer,this group is used to remove the outlook need password registry fix,
Outlook password fix removal,KCUS,WinNT://KCUS/Outlook password fix removal,Q07749,Authorizer,this group is used to remove the outlook need password registry fix,
Outlook password fix removal,KCUS,WinNT://KCUS/Outlook password fix removal,W48997,Delegate,this group is used to remove the outlook need password registry fix,
Allow access to ATP,KCUS,WinNT://KCUS/Allow access to ATP,E18722,Owner,Allow access to ATP,
Allow access to ATP,KCUS,WinNT://KCUS/Allow access to ATP,W66442,Delegate,Allow access to ATP,
HostApp-WMSClient,KCUS,WinNT://KCUS/HostApp-WMSClient,E18722,Owner,Citrix Application Security group for hosting WMS Client.,
HostApp-WMSClient,KCUS,WinNT://KCUS/HostApp-WMSClient,Q07708,Authorizer,Citrix Application Security group for hosting WMS Client.,
HostApp-WMSClient,KCUS,WinNT://KCUS/HostApp-WMSClient,Q13357,Authorizer,Citrix Application Security group for hosting WMS Client.,
HostApp-WMSClient,KCUS,WinNT://KCUS/HostApp-WMSClient,Q14616,Authorizer,Citrix Application Security group for hosting WMS Client.,
HostApp-WMSClient,KCUS,WinNT://KCUS/HostApp-WMSClient,Q14718,Authorizer,Citrix Application Security group for hosting WMS Client.,
HostApp-WMSClient,KCUS,WinNT://KCUS/HostApp-WMSClient,Q15926,Authorizer,Citrix Application Security group for hosting WMS Client.,
HostApp-WMSClient,KCUS,WinNT://KCUS/HostApp-WMSClient,W49040,Delegate,Citrix Application Security group for hosting WMS Client.,
HostApp-RPWI-NorthCentralOF,KCUS,WinNT://KCUS/HostApp-RPWI-NorthCentralOF,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for NorthCentral OverFlow,
HostApp-RPWI-NorthCentralOF,KCUS,WinNT://KCUS/HostApp-RPWI-NorthCentralOF,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for NorthCentral OverFlow,
HostApp-RPWI-NorthCentralOF,KCUS,WinNT://KCUS/HostApp-RPWI-NorthCentralOF,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for NorthCentral OverFlow,
HostApp-RPWI-NorthCentralOF,KCUS,WinNT://KCUS/HostApp-RPWI-NorthCentralOF,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for NorthCentral OverFlow,
HostApp-RPWI-BIsland,KCUS,WinNT://KCUS/HostApp-RPWI-BIsland,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for Beech Island,
HostApp-RPWI-BIsland,KCUS,WinNT://KCUS/HostApp-RPWI-BIsland,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Beech Island,
HostApp-RPWI-BIsland,KCUS,WinNT://KCUS/HostApp-RPWI-BIsland,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Beech Island,
HostApp-RPWI-BIsland,KCUS,WinNT://KCUS/HostApp-RPWI-BIsland,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for Beech Island,
HostApp-CM-SPoint,KCUS,WinNT://KCUS/HostApp-CM-SPoint,E18722,Owner,Citrix Application Security group for hosting Contract Manufacturing SharePoint Site,
HostApp-CM-SPoint,KCUS,WinNT://KCUS/HostApp-CM-SPoint,Q07708,Authorizer,Citrix Application Security group for hosting Contract Manufacturing SharePoint Site,
HostApp-CM-SPoint,KCUS,WinNT://KCUS/HostApp-CM-SPoint,Q14616,Authorizer,Citrix Application Security group for hosting Contract Manufacturing SharePoint Site,
HostApp-CM-SPoint,KCUS,WinNT://KCUS/HostApp-CM-SPoint,W49040,Delegate,Citrix Application Security group for hosting Contract Manufacturing SharePoint Site,
HostApp-RPWI-NMilford,KCUS,WinNT://KCUS/HostApp-RPWI-NMilford,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for New Milford,
HostApp-RPWI-NMilford,KCUS,WinNT://KCUS/HostApp-RPWI-NMilford,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for New Milford,
HostApp-RPWI-NMilford,KCUS,WinNT://KCUS/HostApp-RPWI-NMilford,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for New Milford,
HostApp-RPWI-NMilford,KCUS,WinNT://KCUS/HostApp-RPWI-NMilford,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for New Milford,
HostApp-RPWI-Jenks,KCUS,WinNT://KCUS/HostApp-RPWI-Jenks,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for Jenks,
HostApp-RPWI-Jenks,KCUS,WinNT://KCUS/HostApp-RPWI-Jenks,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Jenks,
HostApp-RPWI-Jenks,KCUS,WinNT://KCUS/HostApp-RPWI-Jenks,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Jenks,
HostApp-RPWI-Jenks,KCUS,WinNT://KCUS/HostApp-RPWI-Jenks,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for Jenks,
HostApp-PromaxNA,KCUS,WinNT://KCUS/HostApp-PromaxNA,E18722,Owner,Citrix Application Security group for hosting NA Promax,
HostApp-PromaxNA,KCUS,WinNT://KCUS/HostApp-PromaxNA,Q07708,Authorizer,Citrix Application Security group for hosting NA Promax,
HostApp-PromaxNA,KCUS,WinNT://KCUS/HostApp-PromaxNA,Q14616,Authorizer,Citrix Application Security group for hosting NA Promax,
HostApp-PromaxNA,KCUS,WinNT://KCUS/HostApp-PromaxNA,W49040,Delegate,Citrix Application Security group for hosting NA Promax,
Personal Mobile Device Agreement - North America,KCUS,WinNT://KCUS/Personal Mobile Device Agreement - North America,E18722,Owner,Grant access to the user to click I agree after they have turned in their Signed End User Agreement and enroll their personal mobile device or iPad.,
Personal Mobile Device Agreement - North America,KCUS,WinNT://KCUS/Personal Mobile Device Agreement - North America,E32872,Delegate,Grant access to the user to click I agree after they have turned in their Signed End User Agreement and enroll their personal mobile device or iPad.,
Comp-GD2000 Policy,KCUS,WinNT://KCUS/Comp-GD2000 Policy,E18722,Owner,Group of machine accounts for policy pilots,
Comp-GD2000 Policy,KCUS,WinNT://KCUS/Comp-GD2000 Policy,Q06085,Authorizer,Group of machine accounts for policy pilots,
Comp-GD2000 Policy,KCUS,WinNT://KCUS/Comp-GD2000 Policy,Q08911,Authorizer,Group of machine accounts for policy pilots,
Comp-GD2000 Policy,KCUS,WinNT://KCUS/Comp-GD2000 Policy,W48997,Delegate,Group of machine accounts for policy pilots,
HostApp-Cognos,KCUS,WinNT://KCUS/HostApp-Cognos,E18722,Owner,Citrix Application Security group for hosting Cognos,
HostApp-Cognos,KCUS,WinNT://KCUS/HostApp-Cognos,Q07708,Authorizer,Citrix Application Security group for hosting Cognos,
HostApp-Cognos,KCUS,WinNT://KCUS/HostApp-Cognos,Q14616,Authorizer,Citrix Application Security group for hosting Cognos,
HostApp-Cognos,KCUS,WinNT://KCUS/HostApp-Cognos,W49040,Delegate,Citrix Application Security group for hosting Cognos,
HostApp-BOBJBI,KCUS,WinNT://KCUS/HostApp-BOBJBI,E18722,Owner, Citrix Application Security group for hosting BOBJ BI Tools,
HostApp-BOBJBI,KCUS,WinNT://KCUS/HostApp-BOBJBI,Q07708,Authorizer, Citrix Application Security group for hosting BOBJ BI Tools,
HostApp-BOBJBI,KCUS,WinNT://KCUS/HostApp-BOBJBI,Q14616,Authorizer, Citrix Application Security group for hosting BOBJ BI Tools,
HostApp-BOBJBI,KCUS,WinNT://KCUS/HostApp-BOBJBI,W49040,Delegate, Citrix Application Security group for hosting BOBJ BI Tools,
USTCFF94_PSUsers,KCUS,WinNT://KCUS/USTCFF94_PSUsers,Q04499,Authorizer,Users – US Neenah Manage Desktop Owned Group Policies Folder Firefight,
USTCFF94_PSUsers,KCUS,WinNT://KCUS/USTCFF94_PSUsers,W48997,Delegate,Users – US Neenah Manage Desktop Owned Group Policies Folder Firefight,
USTCFF94_PSUsers,KCUS,WinNT://KCUS/USTCFF94_PSUsers,E18722,Owner,Users – US Neenah Manage Desktop Owned Group Policies Folder Firefight,
USTCFF94_PSApprovers,KCUS,WinNT://KCUS/USTCFF94_PSApprovers,E18722,Owner,Approvers – US Neenah Manage Desktop Owned Group Policies Folder Firefight,
USTCFF94_PSApprovers,KCUS,WinNT://KCUS/USTCFF94_PSApprovers,Q04499,Authorizer,Approvers – US Neenah Manage Desktop Owned Group Policies Folder Firefight,
USTCFF94_PSApprovers,KCUS,WinNT://KCUS/USTCFF94_PSApprovers,W48997,Delegate,Approvers – US Neenah Manage Desktop Owned Group Policies Folder Firefight,
HostApp-RPWI-NCSpring,KCUS,WinNT://KCUS/HostApp-RPWI-NCSpring,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for Neenah Cold Spring,
HostApp-RPWI-NCSpring,KCUS,WinNT://KCUS/HostApp-RPWI-NCSpring,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Neenah Cold Spring,
HostApp-RPWI-NCSpring,KCUS,WinNT://KCUS/HostApp-RPWI-NCSpring,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Neenah Cold Spring,
HostApp-RPWI-NCSpring,KCUS,WinNT://KCUS/HostApp-RPWI-NCSpring,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for Neenah Cold Spring,
HostApp-RPWI-TOR,KCUS,WinNT://KCUS/HostApp-RPWI-TOR,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for Toronto,
HostApp-RPWI-TOR,KCUS,WinNT://KCUS/HostApp-RPWI-TOR,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Toronto,
HostApp-RPWI-TOR,KCUS,WinNT://KCUS/HostApp-RPWI-TOR,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Toronto,
HostApp-RPWI-TOR,KCUS,WinNT://KCUS/HostApp-RPWI-TOR,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for Toronto,
HostApp-KCP-Spoint,KCUS,WinNT://KCUS/HostApp-KCP-Spoint,E18722,Owner,Citrix Application Security group for hosting KCP Product Recovery SharePoint site,
HostApp-KCP-Spoint,KCUS,WinNT://KCUS/HostApp-KCP-Spoint,Q07708,Authorizer,Citrix Application Security group for hosting KCP Product Recovery SharePoint site,
HostApp-KCP-Spoint,KCUS,WinNT://KCUS/HostApp-KCP-Spoint,Q14616,Authorizer,Citrix Application Security group for hosting KCP Product Recovery SharePoint site,
HostApp-KCP-Spoint,KCUS,WinNT://KCUS/HostApp-KCP-Spoint,W49040,Delegate,Citrix Application Security group for hosting KCP Product Recovery SharePoint site,
HostApp-RPWI-Paris,KCUS,WinNT://KCUS/HostApp-RPWI-Paris,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for Paris,
HostApp-RPWI-Paris,KCUS,WinNT://KCUS/HostApp-RPWI-Paris,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Paris,
HostApp-RPWI-Paris,KCUS,WinNT://KCUS/HostApp-RPWI-Paris,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Paris,
HostApp-RPWI-Paris,KCUS,WinNT://KCUS/HostApp-RPWI-Paris,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for Paris,
HostApp-RPWI-NorthCentral,KCUS,WinNT://KCUS/HostApp-RPWI-NorthCentral,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for NorthCentral DC,
HostApp-RPWI-NorthCentral,KCUS,WinNT://KCUS/HostApp-RPWI-NorthCentral,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for NorthCentral DC,
HostApp-RPWI-NorthCentral,KCUS,WinNT://KCUS/HostApp-RPWI-NorthCentral,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for NorthCentral DC,
HostApp-RPWI-NorthCentral,KCUS,WinNT://KCUS/HostApp-RPWI-NorthCentral,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for NorthCentral DC,
HostApp-WMSEmulator,KCUS,WinNT://KCUS/HostApp-WMSEmulator,E18722,Owner,Citrix Application Security group for hosting WMS Emulator,
HostApp-WMSEmulator,KCUS,WinNT://KCUS/HostApp-WMSEmulator,Q07708,Authorizer,Citrix Application Security group for hosting WMS Emulator,
HostApp-WMSEmulator,KCUS,WinNT://KCUS/HostApp-WMSEmulator,Q13357,Authorizer,Citrix Application Security group for hosting WMS Emulator,
HostApp-WMSEmulator,KCUS,WinNT://KCUS/HostApp-WMSEmulator,Q14616,Authorizer,Citrix Application Security group for hosting WMS Emulator,
HostApp-WMSEmulator,KCUS,WinNT://KCUS/HostApp-WMSEmulator,Q14718,Authorizer,Citrix Application Security group for hosting WMS Emulator,
HostApp-WMSEmulator,KCUS,WinNT://KCUS/HostApp-WMSEmulator,Q15926,Authorizer,Citrix Application Security group for hosting WMS Emulator,
HostApp-WMSEmulator,KCUS,WinNT://KCUS/HostApp-WMSEmulator,W49040,Delegate,Citrix Application Security group for hosting WMS Emulator,
HostApp-PromaxAP,KCUS,WinNT://KCUS/HostApp-PromaxAP,E18722,Owner,Citrix Application Security group for hosting AP Promax,
HostApp-PromaxAP,KCUS,WinNT://KCUS/HostApp-PromaxAP,Q07708,Authorizer,Citrix Application Security group for hosting AP Promax,
HostApp-PromaxAP,KCUS,WinNT://KCUS/HostApp-PromaxAP,Q14616,Authorizer,Citrix Application Security group for hosting AP Promax,
HostApp-PromaxAP,KCUS,WinNT://KCUS/HostApp-PromaxAP,W49040,Delegate,Citrix Application Security group for hosting AP Promax,
HostApp-RPWI-MIDSouth,KCUS,WinNT://KCUS/HostApp-RPWI-MIDSouth,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for MIDSouth DC,
HostApp-RPWI-MIDSouth,KCUS,WinNT://KCUS/HostApp-RPWI-MIDSouth,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for MIDSouth DC,
HostApp-RPWI-MIDSouth,KCUS,WinNT://KCUS/HostApp-RPWI-MIDSouth,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for MIDSouth DC,
HostApp-RPWI-MIDSouth,KCUS,WinNT://KCUS/HostApp-RPWI-MIDSouth,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for MIDSouth DC,
HostApp-PromaxMI,KCUS,WinNT://KCUS/HostApp-PromaxMI,E18722,Owner,Citrix Application Security group for hosting Promax Model Interface,
HostApp-PromaxMI,KCUS,WinNT://KCUS/HostApp-PromaxMI,Q07708,Authorizer,Citrix Application Security group for hosting Promax Model Interface,
HostApp-PromaxMI,KCUS,WinNT://KCUS/HostApp-PromaxMI,Q14616,Authorizer,Citrix Application Security group for hosting Promax Model Interface,
HostApp-PromaxMI,KCUS,WinNT://KCUS/HostApp-PromaxMI,W49040,Delegate,Citrix Application Security group for hosting Promax Model Interface,
Mobile Registration Admins,KCUS,WinNT://KCUS/Mobile Registration Admins,E18722,Owner,Group is used for mobile device registration site administrator access,
Mobile Registration Admins,KCUS,WinNT://KCUS/Mobile Registration Admins,E32872,Authorizer,Group is used for mobile device registration site administrator access,
Mobile Registration Admins,KCUS,WinNT://KCUS/Mobile Registration Admins,W62002,Delegate,Group is used for mobile device registration site administrator access,
Desktop_Support,KCUS,WinNT://KCUS/Desktop_Support,E18722,Owner,http://desktop.helpdesk.kcc.com,
Desktop_Support,KCUS,WinNT://KCUS/Desktop_Support,W48997,Delegate,http://desktop.helpdesk.kcc.com,
Hostapp-Hadoop,KCUS,WinNT://KCUS/Hostapp-Hadoop,E18722,Owner,Citrix Application security group for Hadoop,
Hostapp-Hadoop,KCUS,WinNT://KCUS/Hostapp-Hadoop,Q07708,Authorizer,Citrix Application security group for Hadoop,
Hostapp-Hadoop,KCUS,WinNT://KCUS/Hostapp-Hadoop,Q13357,Authorizer,Citrix Application security group for Hadoop,
Hostapp-Hadoop,KCUS,WinNT://KCUS/Hostapp-Hadoop,Q14616,Authorizer,Citrix Application security group for Hadoop,
Hostapp-Hadoop,KCUS,WinNT://KCUS/Hostapp-Hadoop,Q14718,Authorizer,Citrix Application security group for Hadoop,
Hostapp-Hadoop,KCUS,WinNT://KCUS/Hostapp-Hadoop,Q15926,Authorizer,Citrix Application security group for Hadoop,
Hostapp-Hadoop,KCUS,WinNT://KCUS/Hostapp-Hadoop,W49040,Delegate,Citrix Application security group for Hadoop,
O365 First Release,KCUS,WinNT://KCUS/O365 First Release,E18722,Owner,Access to first release of O365 services,
O365 First Release,KCUS,WinNT://KCUS/O365 First Release,W62002,Delegate,Access to first release of O365 services,
USTCFF73_PSUsers,KCUS,WinNT://KCUS/USTCFF73_PSUsers,B10717,Delegate,Users - US KC North  SCCM Server Firefight,
USTCFF73_PSUsers,KCUS,WinNT://KCUS/USTCFF73_PSUsers,E18722,Owner,Users - US KC North  SCCM Server Firefight,
USTCFF74_PSUsers,KCUS,WinNT://KCUS/USTCFF74_PSUsers,B10717,Delegate,Users - US KC North  SCCM Server Firefight,
USTCFF74_PSUsers,KCUS,WinNT://KCUS/USTCFF74_PSUsers,E18722,Owner,Users - US KC North  SCCM Server Firefight,
IPass Pilot,KCUS,WinNT://KCUS/IPass Pilot,E18722,Owner,Users who will be accessing KC remotely,
IPass Pilot,KCUS,WinNT://KCUS/IPass Pilot,W48997,Delegate,Users who will be accessing KC remotely,
GRP10949_C,KCUS,WinNT://KCUS/GRP10949_C,E18722,Owner,Mailbox: _Support  Yammer Platform,
GRP10949_C,KCUS,WinNT://KCUS/GRP10949_C,W62002,Delegate,Mailbox: _Support  Yammer Platform,
Avecto Long Term Exception,KCUS,WinNT://KCUS/Avecto Long Term Exception,E18722,Owner,Security group for adding avecto exclusion groups,
Avecto Long Term Exception,KCUS,WinNT://KCUS/Avecto Long Term Exception,Q13243,Authorizer,Security group for adding avecto exclusion groups,
Avecto Long Term Exception,KCUS,WinNT://KCUS/Avecto Long Term Exception,W48997,Delegate,Security group for adding avecto exclusion groups,
HostApp-ControlM-Reporting,KCUS,WinNT://KCUS/HostApp-ControlM-Reporting,Q15926,Authorizer,Citrix Application access group for Control-M Reporting,
HostApp-ControlM-Reporting,KCUS,WinNT://KCUS/HostApp-ControlM-Reporting,W49040,Delegate,Citrix Application access group for Control-M Reporting,
HostApp-ControlM-Reporting,KCUS,WinNT://KCUS/HostApp-ControlM-Reporting,E18722,Owner,Citrix Application access group for Control-M Reporting,
HostApp-ControlM-Reporting,KCUS,WinNT://KCUS/HostApp-ControlM-Reporting,Q07708,Authorizer,Citrix Application access group for Control-M Reporting,
HostApp-ControlM-Reporting,KCUS,WinNT://KCUS/HostApp-ControlM-Reporting,Q13357,Authorizer,Citrix Application access group for Control-M Reporting,
HostApp-ControlM-Reporting,KCUS,WinNT://KCUS/HostApp-ControlM-Reporting,Q14616,Authorizer,Citrix Application access group for Control-M Reporting,
HostApp-ControlM-Reporting,KCUS,WinNT://KCUS/HostApp-ControlM-Reporting,Q14718,Authorizer,Citrix Application access group for Control-M Reporting,
O365 Usage Summary Reports Reader,KCUS,WinNT://KCUS/O365 Usage Summary Reports Reader,E18722,Owner,Provide accees to AAD role Usage Summary Reports Reader,
O365 Usage Summary Reports Reader,KCUS,WinNT://KCUS/O365 Usage Summary Reports Reader,W62002,Delegate,Provide accees to AAD role Usage Summary Reports Reader,
Sharepoint_ProfileIMG_Importer,KCUS,WinNT://KCUS/Sharepoint_ProfileIMG_Importer,E18722,Owner,Sharepoint SSP Profile Importer Accounts,
Sharepoint_ProfileIMG_Importer,KCUS,WinNT://KCUS/Sharepoint_ProfileIMG_Importer,W49040,Delegate,Sharepoint SSP Profile Importer Accounts,
Hybrid Search Farm Administrators,KCUS,WinNT://KCUS/Hybrid Search Farm Administrators,E18722,Owner,Hybrid Search Farm Administrators,
Hybrid Search Farm Administrators,KCUS,WinNT://KCUS/Hybrid Search Farm Administrators,Q08911,Authorizer,Hybrid Search Farm Administrators,
Hybrid Search Farm Administrators,KCUS,WinNT://KCUS/Hybrid Search Farm Administrators,W49040,Delegate,Hybrid Search Farm Administrators,
Hybrid Search Web Application Policy - Full Control,KCUS,WinNT://KCUS/Hybrid Search Web Application Policy - Full Control,E18722,Owner,Hybrid Search Web Application Policy - Full Control,
Hybrid Search Web Application Policy - Full Control,KCUS,WinNT://KCUS/Hybrid Search Web Application Policy - Full Control,Q08911,Authorizer,Hybrid Search Web Application Policy - Full Control,
Hybrid Search Web Application Policy - Full Control,KCUS,WinNT://KCUS/Hybrid Search Web Application Policy - Full Control,W49040,Delegate,Hybrid Search Web Application Policy - Full Control,
Hybrid Search Service Administrators,KCUS,WinNT://KCUS/Hybrid Search Service Administrators,E18722,Owner,Hybrid Search Service Administrators,
Hybrid Search Service Administrators,KCUS,WinNT://KCUS/Hybrid Search Service Administrators,Q08911,Authorizer,Hybrid Search Service Administrators,
Hybrid Search Service Administrators,KCUS,WinNT://KCUS/Hybrid Search Service Administrators,W49040,Delegate,Hybrid Search Service Administrators,
Hybrid Search Web Application Policy - Read,KCUS,WinNT://KCUS/Hybrid Search Web Application Policy - Read,E18722,Owner,Hybrid Search Web Application Policy - Read,
Hybrid Search Web Application Policy - Read,KCUS,WinNT://KCUS/Hybrid Search Web Application Policy - Read,Q08911,Authorizer,Hybrid Search Web Application Policy - Read,
Hybrid Search Web Application Policy - Read,KCUS,WinNT://KCUS/Hybrid Search Web Application Policy - Read,W49040,Delegate,Hybrid Search Web Application Policy - Read,
Comp-Allow Sniffer Auditing Policy,KCUS,WinNT://KCUS/Comp-Allow Sniffer Auditing Policy,E18722,Owner,Allows the Comp-Allow Sniffer Auditing Policy to apply,
Comp-Allow Sniffer Auditing Policy,KCUS,WinNT://KCUS/Comp-Allow Sniffer Auditing Policy,W66442,Delegate,Allows the Comp-Allow Sniffer Auditing Policy to apply,
HostApp-PromaxKR,KCUS,WinNT://KCUS/HostApp-PromaxKR,E18722,Owner,Citrix Application Security group for PromaxKR,
HostApp-PromaxKR,KCUS,WinNT://KCUS/HostApp-PromaxKR,Q07708,Authorizer,Citrix Application Security group for PromaxKR,
HostApp-PromaxKR,KCUS,WinNT://KCUS/HostApp-PromaxKR,Q13357,Authorizer,Citrix Application Security group for PromaxKR,
HostApp-PromaxKR,KCUS,WinNT://KCUS/HostApp-PromaxKR,Q14616,Authorizer,Citrix Application Security group for PromaxKR,
HostApp-PromaxKR,KCUS,WinNT://KCUS/HostApp-PromaxKR,Q14718,Authorizer,Citrix Application Security group for PromaxKR,
HostApp-PromaxKR,KCUS,WinNT://KCUS/HostApp-PromaxKR,Q15926,Authorizer,Citrix Application Security group for PromaxKR,
HostApp-PromaxKR,KCUS,WinNT://KCUS/HostApp-PromaxKR,W49040,Delegate,Citrix Application Security group for PromaxKR,
Admin SharePoint Central Administration,KCUS,WinNT://KCUS/Admin SharePoint Central Administration,E18722,Owner,Access to Central Administration with Sharepoint,
Admin SharePoint Central Administration,KCUS,WinNT://KCUS/Admin SharePoint Central Administration,W49040,Delegate,Access to Central Administration with Sharepoint,
Desktop_McAfee_ePO_Admins,KCUS,WinNT://KCUS/Desktop_McAfee_ePO_Admins,E18722,Owner,Group will be used to grant administrator access to the McAfee ePO server for the desktop team,
Desktop_McAfee_ePO_Admins,KCUS,WinNT://KCUS/Desktop_McAfee_ePO_Admins,Q04499,Authorizer,Group will be used to grant administrator access to the McAfee ePO server for the desktop team,
Desktop_McAfee_ePO_Admins,KCUS,WinNT://KCUS/Desktop_McAfee_ePO_Admins,Q06085,Delegate,Group will be used to grant administrator access to the McAfee ePO server for the desktop team,
Desktop_McAfee_ePO_Admins,KCUS,WinNT://KCUS/Desktop_McAfee_ePO_Admins,W48997,Authorizer,Group will be used to grant administrator access to the McAfee ePO server for the desktop team,
PBI_LC_FREEUSER,KCUS,WinNT://KCUS/PBI_LC_FREEUSER,U15405,Authorizer,Power BI Licensing - Free User,
PBI_LC_FREEUSER,KCUS,WinNT://KCUS/PBI_LC_FREEUSER,W48386,Authorizer,Power BI Licensing - Free User,
PBI_LC_FREEUSER,KCUS,WinNT://KCUS/PBI_LC_FREEUSER,W48951,Authorizer,Power BI Licensing - Free User,
PBI_LC_FREEUSER,KCUS,WinNT://KCUS/PBI_LC_FREEUSER,B03885,Owner,Power BI Licensing - Free User,
PBI_LC_FREEUSER,KCUS,WinNT://KCUS/PBI_LC_FREEUSER,E18722,Authorizer,Power BI Licensing - Free User,
PBI_LC_FREEUSER,KCUS,WinNT://KCUS/PBI_LC_FREEUSER,L37902,Delegate,Power BI Licensing - Free User,
PBI_LC_FREEUSER,KCUS,WinNT://KCUS/PBI_LC_FREEUSER,L90398,Authorizer,Power BI Licensing - Free User,
Intune Access for hash Upload,KCUS,WinNT://KCUS/Intune Access for hash Upload,E18722,Owner,Providing access to site support to upload hardware hash,
Intune Access for hash Upload,KCUS,WinNT://KCUS/Intune Access for hash Upload,Q04499,Authorizer,Providing access to site support to upload hardware hash,
Intune Access for hash Upload,KCUS,WinNT://KCUS/Intune Access for hash Upload,W48997,Delegate,Providing access to site support to upload hardware hash,
HostAppMenu-KC-NonBSDContractors,KCUS,WinNT://KCUS/HostAppMenu-KC-NonBSDContractors,E18722,Owner,Hosted Group for Non BSD Contractors,
HostAppMenu-KC-NonBSDContractors,KCUS,WinNT://KCUS/HostAppMenu-KC-NonBSDContractors,Q13357,Authorizer,Hosted Group for Non BSD Contractors,
HostAppMenu-KC-NonBSDContractors,KCUS,WinNT://KCUS/HostAppMenu-KC-NonBSDContractors,Q14718,Authorizer,Hosted Group for Non BSD Contractors,
HostAppMenu-KC-NonBSDContractors,KCUS,WinNT://KCUS/HostAppMenu-KC-NonBSDContractors,Q15926,Authorizer,Hosted Group for Non BSD Contractors,
HostAppMenu-KC-NonBSDContractors,KCUS,WinNT://KCUS/HostAppMenu-KC-NonBSDContractors,W49040,Delegate,Hosted Group for Non BSD Contractors,
MessagingSBP_C_P,KCUS,WinNT://KCUS/MessagingSBP_C_P,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Content\Production\NA.US.kcc.com\root\Messaging-SBP,
MessagingSBP_C_P,KCUS,WinNT://KCUS/MessagingSBP_C_P,W62002,Delegate,\\kcc.com\webservices\WebSites\Intranet\Content\Production\NA.US.kcc.com\root\Messaging-SBP,
MessagingSBP_C_Q,KCUS,WinNT://KCUS/MessagingSBP_C_Q,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Content\QA\NA.US.kcc.com\root\Messaging-SBP,
MessagingSBP_C_Q,KCUS,WinNT://KCUS/MessagingSBP_C_Q,W62002,Delegate,\\kcc.com\webservices\WebSites\Intranet\Content\QA\NA.US.kcc.com\root\Messaging-SBP,
GRP01815_C,KCUS,WinNT://KCUS/GRP01815_C,E18722,Owner,Mailbox: _Admin  Password Wizards,
GRP01815_C,KCUS,WinNT://KCUS/GRP01815_C,Q06085,Authorizer,Mailbox: _Admin  Password Wizards,
GRP01815_C,KCUS,WinNT://KCUS/GRP01815_C,W66442,Delegate,Mailbox: _Admin  Password Wizards,
MISCOMPSVCSEDT_C_P,KCUS,WinNT://KCUS/MISCOMPSVCSEDT_C_P,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Content\Prod\NA.US.KCC.COM\MIS\COMPSVCS\EDT\,
MISCOMPSVCSEDT_C_P,KCUS,WinNT://KCUS/MISCOMPSVCSEDT_C_P,Q73491,Authorizer,\\kcc.com\webservices\WebSites\Intranet\Content\Prod\NA.US.KCC.COM\MIS\COMPSVCS\EDT\,
MISCOMPSVCSEDT_C_P,KCUS,WinNT://KCUS/MISCOMPSVCSEDT_C_P,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Content\Prod\NA.US.KCC.COM\MIS\COMPSVCS\EDT\,
MISCOMPSVCSEDT_C_Q,KCUS,WinNT://KCUS/MISCOMPSVCSEDT_C_Q,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Content\QA\NA.US.KCC.COM\MIS\COMPSVCS\EDT\,
MISCOMPSVCSEDT_C_Q,KCUS,WinNT://KCUS/MISCOMPSVCSEDT_C_Q,Q73491,Authorizer,\\kcc.com\webservices\WebSites\Intranet\Content\QA\NA.US.KCC.COM\MIS\COMPSVCS\EDT\,
MISCOMPSVCSEDT_C_Q,KCUS,WinNT://KCUS/MISCOMPSVCSEDT_C_Q,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Content\QA\NA.US.KCC.COM\MIS\COMPSVCS\EDT\,
MISCOMPSVCSEDTEXECSUPP_RSTR_C_P,KCUS,WinNT://KCUS/MISCOMPSVCSEDTEXECSUPP_RSTR_C_P,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Content\Prod\NA.US.KCC.COM\MIS\COMPSVCS\EDT\EXECUTIVE SUPPORT -RSTR\,
MISCOMPSVCSEDTEXECSUPP_RSTR_C_P,KCUS,WinNT://KCUS/MISCOMPSVCSEDTEXECSUPP_RSTR_C_P,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Content\Prod\NA.US.KCC.COM\MIS\COMPSVCS\EDT\EXECUTIVE SUPPORT -RSTR\,
MISCOMPSVCSEDTEXECSUPP_RSTR_C_Q,KCUS,WinNT://KCUS/MISCOMPSVCSEDTEXECSUPP_RSTR_C_Q,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Content\QA\NA.US.KCC.COM\MIS\COMPSVCS\EDT\EXECUTIVE SUPPORT -RSTR\,
MISCOMPSVCSEDTEXECSUPP_RSTR_C_Q,KCUS,WinNT://KCUS/MISCOMPSVCSEDTEXECSUPP_RSTR_C_Q,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Content\QA\NA.US.KCC.COM\MIS\COMPSVCS\EDT\EXECUTIVE SUPPORT -RSTR\,
MISCOMPSVCSEDTEXECSUPP_RSTR_R_D,KCUS,WinNT://KCUS/MISCOMPSVCSEDTEXECSUPP_RSTR_R_D,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Content\NA.US.KCC.COM\MIS\COMPSVCS\EDT\EXECUTIVE SUPPORT -RSTR\,
MISCOMPSVCSEDTEXECSUPP_RSTR_R_D,KCUS,WinNT://KCUS/MISCOMPSVCSEDTEXECSUPP_RSTR_R_D,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Content\NA.US.KCC.COM\MIS\COMPSVCS\EDT\EXECUTIVE SUPPORT -RSTR\,
MISCOMPSVCSEDTEXECSUPP_RSTR_R_P,KCUS,WinNT://KCUS/MISCOMPSVCSEDTEXECSUPP_RSTR_R_P,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Content\Prod\NA.US.KCC.COM\MIS\COMPSVCS\EDT\EXECUTIVE SUPPORT -RSTR\,
MISCOMPSVCSEDTEXECSUPP_RSTR_R_P,KCUS,WinNT://KCUS/MISCOMPSVCSEDTEXECSUPP_RSTR_R_P,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Content\Prod\NA.US.KCC.COM\MIS\COMPSVCS\EDT\EXECUTIVE SUPPORT -RSTR\,
MISCOMPSVCSEDTEXECSUPP_RSTR_R_Q,KCUS,WinNT://KCUS/MISCOMPSVCSEDTEXECSUPP_RSTR_R_Q,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Content\QA\NA.US.KCC.COM\MIS\COMPSVCS\EDT\EXECUTIVE SUPPORT -RSTR\,
MISCOMPSVCSEDTEXECSUPP_RSTR_R_Q,KCUS,WinNT://KCUS/MISCOMPSVCSEDTEXECSUPP_RSTR_R_Q,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Content\QA\NA.US.KCC.COM\MIS\COMPSVCS\EDT\EXECUTIVE SUPPORT -RSTR\,
MISCOMPSVCSEMAIL_C_P,KCUS,WinNT://KCUS/MISCOMPSVCSEMAIL_C_P,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Content\Prod\NA.US.KCC.COM\MIS\COMPSVCS\EMAIL\,
MISCOMPSVCSEMAIL_C_P,KCUS,WinNT://KCUS/MISCOMPSVCSEMAIL_C_P,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Content\Prod\NA.US.KCC.COM\MIS\COMPSVCS\EMAIL\,
MISCOMPSVCSEMAIL_C_Q,KCUS,WinNT://KCUS/MISCOMPSVCSEMAIL_C_Q,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Content\QA\NA.US.KCC.COM\MIS\COMPSVCS\EMAIL\,
MISCOMPSVCSEMAIL_C_Q,KCUS,WinNT://KCUS/MISCOMPSVCSEMAIL_C_Q,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Content\QA\NA.US.KCC.COM\MIS\COMPSVCS\EMAIL\,
Project Server 2010 - Dev - Admin,KCUS,WinNT://KCUS/Project Server 2010 - Dev - Admin,E18722,Owner,Use for Project Server 2010 in development,
Project Server 2010 - Dev - Admin,KCUS,WinNT://KCUS/Project Server 2010 - Dev - Admin,W49040,Delegate,Use for Project Server 2010 in development,
HostApp-RPWI-Mau,KCUS,WinNT://KCUS/HostApp-RPWI-Mau,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for MAUMELLE,
HostApp-RPWI-Mau,KCUS,WinNT://KCUS/HostApp-RPWI-Mau,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for MAUMELLE,
HostApp-RPWI-Mau,KCUS,WinNT://KCUS/HostApp-RPWI-Mau,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for MAUMELLE,
HostApp-RPWI-Mau,KCUS,WinNT://KCUS/HostApp-RPWI-Mau,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for MAUMELLE,
MISCSCSM_C_P,KCUS,WinNT://KCUS/MISCSCSM_C_P,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Application\Prod\NA.US.KCC.COM.APP\MIS\COMPSVCS\CSM\,
MISCSCSM_C_P,KCUS,WinNT://KCUS/MISCSCSM_C_P,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Application\Prod\NA.US.KCC.COM.APP\MIS\COMPSVCS\CSM\,
MISCSCSM_C_Q,KCUS,WinNT://KCUS/MISCSCSM_C_Q,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Application\QA\NA.US.KCC.COM.APP\MIS\COMPSVCS\CSM\,
MISCSCSM_C_Q,KCUS,WinNT://KCUS/MISCSCSM_C_Q,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Application\QA\NA.US.KCC.COM.APP\MIS\COMPSVCS\CSM\,
O365 Group Creators,KCUS,WinNT://KCUS/O365 Group Creators,E18722,Owner,Used to control who can create Office 365 Groups,
O365 Group Creators,KCUS,WinNT://KCUS/O365 Group Creators,W62002,Delegate,Used to control who can create Office 365 Groups,
HostApp-WMS-SDDC,KCUS,WinNT://KCUS/HostApp-WMS-SDDC,E18722,Owner,Citrix Application Security group for hosting WMS SDDC Web Application,
HostApp-WMS-SDDC,KCUS,WinNT://KCUS/HostApp-WMS-SDDC,Q07708,Authorizer,Citrix Application Security group for hosting WMS SDDC Web Application,
HostApp-WMS-SDDC,KCUS,WinNT://KCUS/HostApp-WMS-SDDC,Q13357,Authorizer,Citrix Application Security group for hosting WMS SDDC Web Application,
HostApp-WMS-SDDC,KCUS,WinNT://KCUS/HostApp-WMS-SDDC,Q14616,Authorizer,Citrix Application Security group for hosting WMS SDDC Web Application,
HostApp-WMS-SDDC,KCUS,WinNT://KCUS/HostApp-WMS-SDDC,Q14718,Authorizer,Citrix Application Security group for hosting WMS SDDC Web Application,
HostApp-WMS-SDDC,KCUS,WinNT://KCUS/HostApp-WMS-SDDC,Q15926,Authorizer,Citrix Application Security group for hosting WMS SDDC Web Application,
HostApp-WMS-SDDC,KCUS,WinNT://KCUS/HostApp-WMS-SDDC,W49040,Delegate,Citrix Application Security group for hosting WMS SDDC Web Application,
Exchange Trusted Subsystem,KCUS,WinNT://KCUS/Exchange Trusted Subsystem,E18722,Owner,This group contains Exchange servers that run Exchange cmdlets on behalf of users via Management service. Its members will have permission to read and modify all Exchange configuration  as well as user accounts and groups. This group should not be deleted.,
Exchange Trusted Subsystem,KCUS,WinNT://KCUS/Exchange Trusted Subsystem,W62002,Delegate,This group contains Exchange servers that run Exchange cmdlets on behalf of users via Management service. Its members will have permission to read and modify all Exchange configuration  as well as user accounts and groups. This group should not be deleted.,
USTCFF96_PSApprovers,KCUS,WinNT://KCUS/USTCFF96_PSApprovers,E18722,Owner,Approvers – US KC Neenah O365 Production Tenant Folder Firefight,
USTCFF96_PSApprovers,KCUS,WinNT://KCUS/USTCFF96_PSApprovers,W62002,Delegate,Approvers – US KC Neenah O365 Production Tenant Folder Firefight,
Hostapp-PulseGlint,KCUS,WinNT://KCUS/Hostapp-PulseGlint,E18722,Owner,Access provision group for Glint Pulse Survey published through Chrome in Citrix,
Hostapp-PulseGlint,KCUS,WinNT://KCUS/Hostapp-PulseGlint,W49040,Delegate,Access provision group for Glint Pulse Survey published through Chrome in Citrix,
O365-E3-Unattended,KCUS,WinNT://KCUS/O365-E3-Unattended,E18722,Owner,To assign members with E3 Unattended licenses,
O365-E3-Unattended,KCUS,WinNT://KCUS/O365-E3-Unattended,W49040,Authorizer,To assign members with E3 Unattended licenses,
O365-E3-Unattended,KCUS,WinNT://KCUS/O365-E3-Unattended,W62002,Delegate,To assign members with E3 Unattended licenses,
LogCaptureApps,KCUS,WinNT://KCUS/LogCaptureApps,E18722,Owner,This group will provide the members access to the Log Capture Utility,
LogCaptureApps,KCUS,WinNT://KCUS/LogCaptureApps,Q73491,Authorizer,This group will provide the members access to the Log Capture Utility,
LogCaptureApps,KCUS,WinNT://KCUS/LogCaptureApps,W66442,Delegate,This group will provide the members access to the Log Capture Utility,
HostApp-DocDirect,KCUS,WinNT://KCUS/HostApp-DocDirect,E18722,Owner,Citrix Application Security group for Document Direct Application,
HostApp-DocDirect,KCUS,WinNT://KCUS/HostApp-DocDirect,Q07708,Authorizer,Citrix Application Security group for Document Direct Application,
HostApp-DocDirect,KCUS,WinNT://KCUS/HostApp-DocDirect,Q14616,Authorizer,Citrix Application Security group for Document Direct Application,
HostApp-DocDirect,KCUS,WinNT://KCUS/HostApp-DocDirect,W49040,Delegate,Citrix Application Security group for Document Direct Application,
Smartphone_Authorizers,KCUS,WinNT://KCUS/Smartphone_Authorizers,B62398,Authorizer,Authorizers for smartphone devices,
Smartphone_Authorizers,KCUS,WinNT://KCUS/Smartphone_Authorizers,E18722,Owner,Authorizers for smartphone devices,
Smartphone_Authorizers,KCUS,WinNT://KCUS/Smartphone_Authorizers,W66442,Delegate,Authorizers for smartphone devices,
HostApp-SAPDS,KCUS,WinNT://KCUS/HostApp-SAPDS,E18722,Owner,Delivery group or application access for SAP Designer Service,
HostApp-SAPDS,KCUS,WinNT://KCUS/HostApp-SAPDS,W49040,Delegate,Delivery group or application access for SAP Designer Service,
HostAppMenu-SAPDS,KCUS,WinNT://KCUS/HostAppMenu-SAPDS,E18722,Owner,SAP Designer Service application Citrix access group for users,
HostAppMenu-SAPDS,KCUS,WinNT://KCUS/HostAppMenu-SAPDS,U04272,Authorizer,SAP Designer Service application Citrix access group for users,
HostAppMenu-SAPDS,KCUS,WinNT://KCUS/HostAppMenu-SAPDS,W49040,Delegate,SAP Designer Service application Citrix access group for users,
HostAppMenu-SAPDS,KCUS,WinNT://KCUS/HostAppMenu-SAPDS,W66496,Authorizer,SAP Designer Service application Citrix access group for users,
DesktopTest,KCUS,WinNT://KCUS/DesktopTest,E18722,Owner,Testing group used when developing Desktop solutions.,
DesktopTest,KCUS,WinNT://KCUS/DesktopTest,Q73491,Delegate,Testing group used when developing Desktop solutions.,
O365_PrjOnlPrem,KCUS,WinNT://KCUS/O365_PrjOnlPrem,E18722,Owner,To manage MS Project Online Premium Licenses,
O365_PrjOnlPrem,KCUS,WinNT://KCUS/O365_PrjOnlPrem,W49040,Delegate,To manage MS Project Online Premium Licenses,
O365_PrjOnlPrem,KCUS,WinNT://KCUS/O365_PrjOnlPrem,W66442,Authorizer,To manage MS Project Online Premium Licenses,
HostServerAdmin-Stars,KCUS,WinNT://KCUS/HostServerAdmin-Stars,E18722,Owner,Admin Access group for Stars Citrix servers,
HostServerAdmin-Stars,KCUS,WinNT://KCUS/HostServerAdmin-Stars,Q07708,Authorizer,Admin Access group for Stars Citrix servers,
HostServerAdmin-Stars,KCUS,WinNT://KCUS/HostServerAdmin-Stars,Q13357,Authorizer,Admin Access group for Stars Citrix servers,
HostServerAdmin-Stars,KCUS,WinNT://KCUS/HostServerAdmin-Stars,Q14616,Authorizer,Admin Access group for Stars Citrix servers,
HostServerAdmin-Stars,KCUS,WinNT://KCUS/HostServerAdmin-Stars,Q14718,Authorizer,Admin Access group for Stars Citrix servers,
HostServerAdmin-Stars,KCUS,WinNT://KCUS/HostServerAdmin-Stars,Q15926,Authorizer,Admin Access group for Stars Citrix servers,
HostServerAdmin-Stars,KCUS,WinNT://KCUS/HostServerAdmin-Stars,W49040,Delegate,Admin Access group for Stars Citrix servers,
ACC Citrix Admins,KCUS,WinNT://KCUS/ACC Citrix Admins,E18722,Owner,Citrix Admin Group for AAC Team,
ACC Citrix Admins,KCUS,WinNT://KCUS/ACC Citrix Admins,W49040,Delegate,Citrix Admin Group for AAC Team,
HostApp-Sharp,KCUS,WinNT://KCUS/HostApp-Sharp,E18722,Owner,Citrix Security group for hosting Sharp web link,
HostApp-Sharp,KCUS,WinNT://KCUS/HostApp-Sharp,Q07708,Authorizer,Citrix Security group for hosting Sharp web link,
HostApp-Sharp,KCUS,WinNT://KCUS/HostApp-Sharp,Q13357,Authorizer,Citrix Security group for hosting Sharp web link,
HostApp-Sharp,KCUS,WinNT://KCUS/HostApp-Sharp,Q14616,Authorizer,Citrix Security group for hosting Sharp web link,
HostApp-Sharp,KCUS,WinNT://KCUS/HostApp-Sharp,Q14718,Authorizer,Citrix Security group for hosting Sharp web link,
HostApp-Sharp,KCUS,WinNT://KCUS/HostApp-Sharp,Q15926,Authorizer,Citrix Security group for hosting Sharp web link,
HostApp-Sharp,KCUS,WinNT://KCUS/HostApp-Sharp,W49040,Delegate,Citrix Security group for hosting Sharp web link,
MassTransit PROD - Administrators,KCUS,WinNT://KCUS/MassTransit PROD - Administrators,E18722,Owner,,
MassTransit PROD - Administrators,KCUS,WinNT://KCUS/MassTransit PROD - Administrators,Q08911,Authorizer,,
MassTransit PROD - Administrators,KCUS,WinNT://KCUS/MassTransit PROD - Administrators,W49040,Delegate,,
USTCFN01 DS Admin,KCUS,WinNT://KCUS/USTCFN01 DS Admin,Q04499,Authorizer,Management of Desktop Services development server – USTCFN01,
USTCFN01 DS Admin,KCUS,WinNT://KCUS/USTCFN01 DS Admin,W48997,Delegate,Management of Desktop Services development server – USTCFN01,
USTCFN01 DS Admin,KCUS,WinNT://KCUS/USTCFN01 DS Admin,E18722,Owner,Management of Desktop Services development server – USTCFN01,
SCCMApp-Adobe Illustrator CC,KCUS,WinNT://KCUS/SCCMApp-Adobe Illustrator CC,E18722,Owner,SCCM Package for Adobe Illustrator Creative Cloud,
SCCMApp-Adobe Illustrator CC,KCUS,WinNT://KCUS/SCCMApp-Adobe Illustrator CC,W48997,Delegate,SCCM Package for Adobe Illustrator Creative Cloud,
SCCMApp-Adobe Audition CC,KCUS,WinNT://KCUS/SCCMApp-Adobe Audition CC,E18722,Owner,SCCM Package for Adobe Audition Creative Cloud,
SCCMApp-Adobe Audition CC,KCUS,WinNT://KCUS/SCCMApp-Adobe Audition CC,W48997,Delegate,SCCM Package for Adobe Audition Creative Cloud,
SCCMApp-Adobe Dreamweaver CC,KCUS,WinNT://KCUS/SCCMApp-Adobe Dreamweaver CC,E18722,Owner,SCCM Package for Adobe Dreamweaver Creative Cloud,
SCCMApp-Adobe Dreamweaver CC,KCUS,WinNT://KCUS/SCCMApp-Adobe Dreamweaver CC,W48997,Delegate,SCCM Package for Adobe Dreamweaver Creative Cloud,
SCCMApp-Adobe Premiere Pro CC,KCUS,WinNT://KCUS/SCCMApp-Adobe Premiere Pro CC,E18722,Owner,SCCM Package forAdobe Premiere Pro Creative Cloud,
SCCMApp-Adobe Premiere Pro CC,KCUS,WinNT://KCUS/SCCMApp-Adobe Premiere Pro CC,W48997,Delegate,SCCM Package forAdobe Premiere Pro Creative Cloud,
MassTransit DEV - Administrators,KCUS,WinNT://KCUS/MassTransit DEV - Administrators,E18722,Owner,,
MassTransit DEV - Administrators,KCUS,WinNT://KCUS/MassTransit DEV - Administrators,Q08911,Authorizer,,
MassTransit DEV - Administrators,KCUS,WinNT://KCUS/MassTransit DEV - Administrators,W49040,Delegate,,
USTCA320_Admin,KCUS,WinNT://KCUS/USTCA320_Admin,E18722,Owner,Administrator Access on USTCA320,
USTCA320_Admin,KCUS,WinNT://KCUS/USTCA320_Admin,W66442,Delegate,Administrator Access on USTCA320,
FC-DEVUsers,KCUS,WinNT://KCUS/FC-DEVUsers,E18722,Owner,CTS DEV VDI Users,
FC-DEVUsers,KCUS,WinNT://KCUS/FC-DEVUsers,Q07708,Authorizer,CTS DEV VDI Users,
FC-DEVUsers,KCUS,WinNT://KCUS/FC-DEVUsers,Q13357,Authorizer,CTS DEV VDI Users,
FC-DEVUsers,KCUS,WinNT://KCUS/FC-DEVUsers,Q14616,Authorizer,CTS DEV VDI Users,
FC-DEVUsers,KCUS,WinNT://KCUS/FC-DEVUsers,Q14718,Authorizer,CTS DEV VDI Users,
FC-DEVUsers,KCUS,WinNT://KCUS/FC-DEVUsers,Q15926,Authorizer,CTS DEV VDI Users,
FC-DEVUsers,KCUS,WinNT://KCUS/FC-DEVUsers,W49040,Delegate,CTS DEV VDI Users,
HostApp-ITS-Apps,KCUS,WinNT://KCUS/HostApp-ITS-Apps,E18722,Owner,Citrix access group for ITS-Apps,
HostApp-ITS-Apps,KCUS,WinNT://KCUS/HostApp-ITS-Apps,Q07708,Authorizer,Citrix access group for ITS-Apps,
HostApp-ITS-Apps,KCUS,WinNT://KCUS/HostApp-ITS-Apps,Q14616,Authorizer,Citrix access group for ITS-Apps,
HostApp-ITS-Apps,KCUS,WinNT://KCUS/HostApp-ITS-Apps,W49040,Delegate,Citrix access group for ITS-Apps,
FC-KimTechUsers,KCUS,WinNT://KCUS/FC-KimTechUsers,E18722,Owner,KimTech Facility VDI Users,
FC-KimTechUsers,KCUS,WinNT://KCUS/FC-KimTechUsers,Q07708,Authorizer,KimTech Facility VDI Users,
FC-KimTechUsers,KCUS,WinNT://KCUS/FC-KimTechUsers,Q13357,Authorizer,KimTech Facility VDI Users,
FC-KimTechUsers,KCUS,WinNT://KCUS/FC-KimTechUsers,Q14616,Authorizer,KimTech Facility VDI Users,
FC-KimTechUsers,KCUS,WinNT://KCUS/FC-KimTechUsers,Q14718,Authorizer,KimTech Facility VDI Users,
FC-KimTechUsers,KCUS,WinNT://KCUS/FC-KimTechUsers,Q15926,Authorizer,KimTech Facility VDI Users,
FC-KimTechUsers,KCUS,WinNT://KCUS/FC-KimTechUsers,w49040,Delegate,KimTech Facility VDI Users,
HostAppMenu-ITS-Pilot1,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot1,E18722,Owner,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot1,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot1,Q07708,Authorizer,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot1,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot1,Q13357,Authorizer,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot1,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot1,Q14616,Authorizer,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot1,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot1,Q14718,Authorizer,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot1,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot1,Q15926,Authorizer,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot1,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot1,W49040,Delegate,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot2,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot2,E18722,Owner,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot2,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot2,Q07708,Authorizer,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot2,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot2,Q13357,Authorizer,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot2,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot2,Q14616,Authorizer,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot2,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot2,Q14718,Authorizer,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot2,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot2,Q15926,Authorizer,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot2,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot2,W49040,Delegate,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot3,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot3,Q14718,Authorizer,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot3,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot3,Q15926,Authorizer,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot3,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot3,W49040,Delegate,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot3,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot3,E18722,Owner,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot3,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot3,Q07708,Authorizer,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot3,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot3,Q13357,Authorizer,Citrix access group used for user pilot testing,
HostAppMenu-ITS-Pilot3,KCUS,WinNT://KCUS/HostAppMenu-ITS-Pilot3,Q14616,Authorizer,Citrix access group used for user pilot testing,
TSUsers_USTCTD03,KCUS,WinNT://KCUS/TSUsers_USTCTD03,E18722,Owner,Terminal Server Access to USTCTD03,
TSUsers_USTCTD03,KCUS,WinNT://KCUS/TSUsers_USTCTD03,W66442,Delegate,Terminal Server Access to USTCTD03,
HostApp-HSD,KCUS,WinNT://KCUS/HostApp-HSD,E18722,Owner,Generic Citrix Desktop access,
HostApp-HSD,KCUS,WinNT://KCUS/HostApp-HSD,Q07708,Authorizer,Generic Citrix Desktop access,
HostApp-HSD,KCUS,WinNT://KCUS/HostApp-HSD,Q13357,Authorizer,Generic Citrix Desktop access,
HostApp-HSD,KCUS,WinNT://KCUS/HostApp-HSD,Q14616,Authorizer,Generic Citrix Desktop access,
HostApp-HSD,KCUS,WinNT://KCUS/HostApp-HSD,Q14718,Authorizer,Generic Citrix Desktop access,
HostApp-HSD,KCUS,WinNT://KCUS/HostApp-HSD,Q15926,Authorizer,Generic Citrix Desktop access,
HostApp-HSD,KCUS,WinNT://KCUS/HostApp-HSD,W49040,Delegate,Generic Citrix Desktop access,
Admin_RicohStreamlineNX,KCUS,WinNT://KCUS/Admin_RicohStreamlineNX,E18722,Owner,Ricoh StreamlineNX Administrators,
Admin_RicohStreamlineNX,KCUS,WinNT://KCUS/Admin_RicohStreamlineNX,L20681,Authorizer,Ricoh StreamlineNX Administrators,
Admin_RicohStreamlineNX,KCUS,WinNT://KCUS/Admin_RicohStreamlineNX,W49040,Delegate,Ricoh StreamlineNX Administrators,
HostApp-PurchasingHomePage,KCUS,WinNT://KCUS/HostApp-PurchasingHomePage,E18722,Owner,KC Purchasing Home Page,
HostApp-PurchasingHomePage,KCUS,WinNT://KCUS/HostApp-PurchasingHomePage,Q07708,Authorizer,KC Purchasing Home Page,
HostApp-PurchasingHomePage,KCUS,WinNT://KCUS/HostApp-PurchasingHomePage,Q13357,Authorizer,KC Purchasing Home Page,
HostApp-PurchasingHomePage,KCUS,WinNT://KCUS/HostApp-PurchasingHomePage,Q14616,Authorizer,KC Purchasing Home Page,
HostApp-PurchasingHomePage,KCUS,WinNT://KCUS/HostApp-PurchasingHomePage,Q14718,Authorizer,KC Purchasing Home Page,
HostApp-PurchasingHomePage,KCUS,WinNT://KCUS/HostApp-PurchasingHomePage,Q15926,Authorizer,KC Purchasing Home Page,
HostApp-PurchasingHomePage,KCUS,WinNT://KCUS/HostApp-PurchasingHomePage,W49040,Delegate,KC Purchasing Home Page,
HostApp-SungardAvantGard-TR,KCUS,WinNT://KCUS/HostApp-SungardAvantGard-TR,E18722,Owner,Sungard AvantGard - Treasury,
HostApp-SungardAvantGard-TR,KCUS,WinNT://KCUS/HostApp-SungardAvantGard-TR,Q07708,Authorizer,Sungard AvantGard - Treasury,
HostApp-SungardAvantGard-TR,KCUS,WinNT://KCUS/HostApp-SungardAvantGard-TR,Q14616,Authorizer,Sungard AvantGard - Treasury,
HostApp-SungardAvantGard-TR,KCUS,WinNT://KCUS/HostApp-SungardAvantGard-TR,W49040,Delegate,Sungard AvantGard - Treasury,
USDQSAPEOWIN_C,KCUS,WinNT://KCUS/USDQSAPEOWIN_C,b00982,Authorizer,\\USDQA007\Apps\SAPEOWIN,
USDQSAPEOWIN_C,KCUS,WinNT://KCUS/USDQSAPEOWIN_C,E18722,Owner,\\USDQA007\Apps\SAPEOWIN,
USDQSAPEOWIN_C,KCUS,WinNT://KCUS/USDQSAPEOWIN_C,W66442,Delegate,\\USDQA007\Apps\SAPEOWIN,
HostServerAdmin-Isolated,KCUS,WinNT://KCUS/HostServerAdmin-Isolated,E18722,Owner,Citrix Admin Access group for Isolated Application team,
HostServerAdmin-Isolated,KCUS,WinNT://KCUS/HostServerAdmin-Isolated,Q07708,Authorizer,Citrix Admin Access group for Isolated Application team,
HostServerAdmin-Isolated,KCUS,WinNT://KCUS/HostServerAdmin-Isolated,Q13357,Authorizer,Citrix Admin Access group for Isolated Application team,
HostServerAdmin-Isolated,KCUS,WinNT://KCUS/HostServerAdmin-Isolated,Q14616,Authorizer,Citrix Admin Access group for Isolated Application team,
HostServerAdmin-Isolated,KCUS,WinNT://KCUS/HostServerAdmin-Isolated,Q14718,Authorizer,Citrix Admin Access group for Isolated Application team,
HostServerAdmin-Isolated,KCUS,WinNT://KCUS/HostServerAdmin-Isolated,Q15926,Authorizer,Citrix Admin Access group for Isolated Application team,
HostServerAdmin-Isolated,KCUS,WinNT://KCUS/HostServerAdmin-Isolated,W49040,Delegate,Citrix Admin Access group for Isolated Application team,
RES05276_C,KCUS,WinNT://KCUS/RES05276_C,E18722,Owner,Resource: _ResGrp_USDQ  MS HUB,
RES05276_C,KCUS,WinNT://KCUS/RES05276_C,W48997,Delegate,Resource: _ResGrp_USDQ  MS HUB,
RES05276_DL,KCUS,WinNT://KCUS/RES05276_DL,E18722,Owner,Resource: _ResGrp_USDQ  MS HUB,
RES05276_DL,KCUS,WinNT://KCUS/RES05276_DL,W48997,Delegate,Resource: _ResGrp_USDQ  MS HUB,
RES05277_C,KCUS,WinNT://KCUS/RES05277_C,E18722,Owner,Resource: _ResGrp_USNO  MS HUB,
RES05277_C,KCUS,WinNT://KCUS/RES05277_C,W48997,Delegate,Resource: _ResGrp_USNO  MS HUB,
HostApp-FileShare,KCUS,WinNT://KCUS/HostApp-FileShare,Q15926,Authorizer,Citrix access group for App-FireShare,
HostApp-FileShare,KCUS,WinNT://KCUS/HostApp-FileShare,W49040,Delegate,Citrix access group for App-FireShare,
HostApp-FileShare,KCUS,WinNT://KCUS/HostApp-FileShare,E18722,Owner,Citrix access group for App-FireShare,
HostApp-FileShare,KCUS,WinNT://KCUS/HostApp-FileShare,Q07708,Authorizer,Citrix access group for App-FireShare,
HostApp-FileShare,KCUS,WinNT://KCUS/HostApp-FileShare,Q13357,Authorizer,Citrix access group for App-FireShare,
HostApp-FileShare,KCUS,WinNT://KCUS/HostApp-FileShare,Q14616,Authorizer,Citrix access group for App-FireShare,
HostApp-FileShare,KCUS,WinNT://KCUS/HostApp-FileShare,Q14718,Authorizer,Citrix access group for App-FireShare,
RES05277_DL,KCUS,WinNT://KCUS/RES05277_DL,E18722,Owner,Resource: _ResGrp_USNO  MS HUB,
RES05277_DL,KCUS,WinNT://KCUS/RES05277_DL,W48997,Delegate,Resource: _ResGrp_USNO  MS HUB,
MCOE_Reporting_DB_ADM_C,KCUS,WinNT://KCUS/MCOE_Reporting_DB_ADM_C,E18722,Owner,,
MCOE_Reporting_DB_ADM_C,KCUS,WinNT://KCUS/MCOE_Reporting_DB_ADM_C,E32872,Delegate,,
MCOE_Reporting_DB_ADM_C,KCUS,WinNT://KCUS/MCOE_Reporting_DB_ADM_C,W48997,Authorizer,,
GRP04357_C,KCUS,WinNT://KCUS/GRP04357_C,E17447,Delegate,Mailbox: _Mobile Email Support  Europe,
GRP04357_C,KCUS,WinNT://KCUS/GRP04357_C,E18722,Owner,Mailbox: _Mobile Email Support  Europe,
HostServerAdmin-PIPPO,KCUS,WinNT://KCUS/HostServerAdmin-PIPPO,E18722,Owner,Citrix Server access security group for PIPPO,
HostServerAdmin-PIPPO,KCUS,WinNT://KCUS/HostServerAdmin-PIPPO,Q07708,Authorizer,Citrix Server access security group for PIPPO,
HostServerAdmin-PIPPO,KCUS,WinNT://KCUS/HostServerAdmin-PIPPO,Q13357,Authorizer,Citrix Server access security group for PIPPO,
HostServerAdmin-PIPPO,KCUS,WinNT://KCUS/HostServerAdmin-PIPPO,Q14616,Authorizer,Citrix Server access security group for PIPPO,
HostServerAdmin-PIPPO,KCUS,WinNT://KCUS/HostServerAdmin-PIPPO,Q14718,Authorizer,Citrix Server access security group for PIPPO,
HostServerAdmin-PIPPO,KCUS,WinNT://KCUS/HostServerAdmin-PIPPO,Q15926,Authorizer,Citrix Server access security group for PIPPO,
HostServerAdmin-PIPPO,KCUS,WinNT://KCUS/HostServerAdmin-PIPPO,W49040,Delegate,Citrix Server access security group for PIPPO,
HostApp-MF,KCUS,WinNT://KCUS/HostApp-MF,E18722,Owner,Citrix Application security group for Mainframe,
HostApp-MF,KCUS,WinNT://KCUS/HostApp-MF,Q07708,Authorizer,Citrix Application security group for Mainframe,
HostApp-MF,KCUS,WinNT://KCUS/HostApp-MF,Q14616,Authorizer,Citrix Application security group for Mainframe,
HostApp-MF,KCUS,WinNT://KCUS/HostApp-MF,W49040,Delegate,Citrix Application security group for Mainframe,
Allow-Comp-Prevent Wireless Policy,KCUS,WinNT://KCUS/Allow-Comp-Prevent Wireless Policy,E18722,Owner,Security group to deny the default wireless policy,
Allow-Comp-Prevent Wireless Policy,KCUS,WinNT://KCUS/Allow-Comp-Prevent Wireless Policy,Q06085,Authorizer,Security group to deny the default wireless policy,
Allow-Comp-Prevent Wireless Policy,KCUS,WinNT://KCUS/Allow-Comp-Prevent Wireless Policy,Q08911,Authorizer,Security group to deny the default wireless policy,
Allow-Comp-Prevent Wireless Policy,KCUS,WinNT://KCUS/Allow-Comp-Prevent Wireless Policy,W66442,Delegate,Security group to deny the default wireless policy,
PC Remote wipe,KCUS,WinNT://KCUS/PC Remote wipe,W66442,Delegate,This group is used to remotely wipe a PC using machine name,
PC Remote wipe,KCUS,WinNT://KCUS/PC Remote wipe,E18722,Owner,This group is used to remotely wipe a PC using machine name,
TCSDesktop,KCUS,WinNT://KCUS/TCSDesktop,E18722,Owner,Group for all TCS employees assigned to Desktop Services,
TCSDesktop,KCUS,WinNT://KCUS/TCSDesktop,Q06085,Delegate,Group for all TCS employees assigned to Desktop Services,
HostApp-ETL-BOBJDS,KCUS,WinNT://KCUS/HostApp-ETL-BOBJDS,E18722,Owner,Citrix Application security group for BOBJDS ETL Tool,
HostApp-ETL-BOBJDS,KCUS,WinNT://KCUS/HostApp-ETL-BOBJDS,Q07708,Authorizer,Citrix Application security group for BOBJDS ETL Tool,
HostApp-ETL-BOBJDS,KCUS,WinNT://KCUS/HostApp-ETL-BOBJDS,Q14616,Authorizer,Citrix Application security group for BOBJDS ETL Tool,
HostApp-ETL-BOBJDS,KCUS,WinNT://KCUS/HostApp-ETL-BOBJDS,W49040,Delegate,Citrix Application security group for BOBJDS ETL Tool,
Deny-Both-Terminal Services Citrix-Presentation Settings,KCUS,WinNT://KCUS/Deny-Both-Terminal Services Citrix-Presentation Settings,E18722,Owner,Deny Policy Group on Citrix Envrionment,
Deny-Both-Terminal Services Citrix-Presentation Settings,KCUS,WinNT://KCUS/Deny-Both-Terminal Services Citrix-Presentation Settings,Q13357,Authorizer,Deny Policy Group on Citrix Envrionment,
Deny-Both-Terminal Services Citrix-Presentation Settings,KCUS,WinNT://KCUS/Deny-Both-Terminal Services Citrix-Presentation Settings,Q14718,Authorizer,Deny Policy Group on Citrix Envrionment,
Deny-Both-Terminal Services Citrix-Presentation Settings,KCUS,WinNT://KCUS/Deny-Both-Terminal Services Citrix-Presentation Settings,Q15926,Authorizer,Deny Policy Group on Citrix Envrionment,
Deny-Both-Terminal Services Citrix-Presentation Settings,KCUS,WinNT://KCUS/Deny-Both-Terminal Services Citrix-Presentation Settings,W49040,Delegate,Deny Policy Group on Citrix Envrionment,
HostApp-3PL-RDP,KCUS,WinNT://KCUS/HostApp-3PL-RDP,Q14616,Authorizer,Citrix Application security group for INTAP004 and INTAP005,
HostApp-3PL-RDP,KCUS,WinNT://KCUS/HostApp-3PL-RDP,W49040,Delegate,Citrix Application security group for INTAP004 and INTAP005,
HostApp-3PL-RDP,KCUS,WinNT://KCUS/HostApp-3PL-RDP,E18722,Owner,Citrix Application security group for INTAP004 and INTAP005,
HostApp-3PL-RDP,KCUS,WinNT://KCUS/HostApp-3PL-RDP,Q07708,Authorizer,Citrix Application security group for INTAP004 and INTAP005,
USTCA287_Admin,KCUS,WinNT://KCUS/USTCA287_Admin,E18722,Owner,Administrator Access on USTCA287,
USTCA287_Admin,KCUS,WinNT://KCUS/USTCA287_Admin,W66442,Delegate,Administrator Access on USTCA287,
USTCA291_Admin,KCUS,WinNT://KCUS/USTCA291_Admin,W66442,Delegate,Administrator Access on USTCA291,
USTCA291_Admin,KCUS,WinNT://KCUS/USTCA291_Admin,E18722,Owner,Administrator Access on USTCA291,
HostServerAdmin-Hyperion,KCUS,WinNT://KCUS/HostServerAdmin-Hyperion,E18722,Owner,Admin Access group for Hyperion Citrix servers,
HostServerAdmin-Hyperion,KCUS,WinNT://KCUS/HostServerAdmin-Hyperion,Q07708,Authorizer,Admin Access group for Hyperion Citrix servers,
HostServerAdmin-Hyperion,KCUS,WinNT://KCUS/HostServerAdmin-Hyperion,Q13357,Authorizer,Admin Access group for Hyperion Citrix servers,
HostServerAdmin-Hyperion,KCUS,WinNT://KCUS/HostServerAdmin-Hyperion,Q14616,Authorizer,Admin Access group for Hyperion Citrix servers,
HostServerAdmin-Hyperion,KCUS,WinNT://KCUS/HostServerAdmin-Hyperion,Q14718,Authorizer,Admin Access group for Hyperion Citrix servers,
HostServerAdmin-Hyperion,KCUS,WinNT://KCUS/HostServerAdmin-Hyperion,Q15926,Authorizer,Admin Access group for Hyperion Citrix servers,
HostServerAdmin-Hyperion,KCUS,WinNT://KCUS/HostServerAdmin-Hyperion,W49040,Delegate,Admin Access group for Hyperion Citrix servers,
Release management - Ring 2,KCUS,WinNT://KCUS/Release management - Ring 2,E18722,Owner,This group include North America IT Users,
Release management - Ring 2,KCUS,WinNT://KCUS/Release management - Ring 2,Q06085,Authorizer,This group include North America IT Users,
Release management - Ring 2,KCUS,WinNT://KCUS/Release management - Ring 2,Q08911,Authorizer,This group include North America IT Users,
Release management - Ring 2,KCUS,WinNT://KCUS/Release management - Ring 2,W66442,Delegate,This group include North America IT Users,
HostApp-EU-Essbase,KCUS,WinNT://KCUS/HostApp-EU-Essbase,E18722,Owner,Citrix Application Security group for Essbase EU – Excel addin,
HostApp-EU-Essbase,KCUS,WinNT://KCUS/HostApp-EU-Essbase,Q07708,Authorizer,Citrix Application Security group for Essbase EU – Excel addin,
HostApp-EU-Essbase,KCUS,WinNT://KCUS/HostApp-EU-Essbase,Q13357,Authorizer,Citrix Application Security group for Essbase EU – Excel addin,
HostApp-EU-Essbase,KCUS,WinNT://KCUS/HostApp-EU-Essbase,Q14616,Authorizer,Citrix Application Security group for Essbase EU – Excel addin,
HostApp-EU-Essbase,KCUS,WinNT://KCUS/HostApp-EU-Essbase,Q14718,Authorizer,Citrix Application Security group for Essbase EU – Excel addin,
HostApp-EU-Essbase,KCUS,WinNT://KCUS/HostApp-EU-Essbase,Q15926,Authorizer,Citrix Application Security group for Essbase EU – Excel addin,
HostApp-EU-Essbase,KCUS,WinNT://KCUS/HostApp-EU-Essbase,W49040,Delegate,Citrix Application Security group for Essbase EU – Excel addin,
MSX View Admin,KCUS,WinNT://KCUS/MSX View Admin,E18722,Owner,Grants rights to view Exchange System Manager functions  without add/delete/modify,
MSX View Admin,KCUS,WinNT://KCUS/MSX View Admin,W62002,Delegate,Grants rights to view Exchange System Manager functions  without add/delete/modify,
USTCShareGD2000_C,KCUS,WinNT://KCUS/USTCShareGD2000_C,E18722,Owner,\\USTCFN01\SHARE\GDStage,
USTCShareGD2000_C,KCUS,WinNT://KCUS/USTCShareGD2000_C,Q04499,Authorizer,\\USTCFN01\SHARE\GDStage,
USTCShareGD2000_C,KCUS,WinNT://KCUS/USTCShareGD2000_C,W48997,Delegate,\\USTCFN01\SHARE\GDStage,
Hostapp-Tableau,KCUS,WinNT://KCUS/Hostapp-Tableau,E18722,Owner,Citrix access group for Tableau application,
Hostapp-Tableau,KCUS,WinNT://KCUS/Hostapp-Tableau,Q07708,Authorizer,Citrix access group for Tableau application,
Hostapp-Tableau,KCUS,WinNT://KCUS/Hostapp-Tableau,Q13357,Authorizer,Citrix access group for Tableau application,
Hostapp-Tableau,KCUS,WinNT://KCUS/Hostapp-Tableau,Q14616,Authorizer,Citrix access group for Tableau application,
Hostapp-Tableau,KCUS,WinNT://KCUS/Hostapp-Tableau,Q14718,Authorizer,Citrix access group for Tableau application,
Hostapp-Tableau,KCUS,WinNT://KCUS/Hostapp-Tableau,Q15926,Authorizer,Citrix access group for Tableau application,
Hostapp-Tableau,KCUS,WinNT://KCUS/Hostapp-Tableau,W49040,Delegate,Citrix access group for Tableau application,
USTCSMSStageBS_C,KCUS,WinNT://KCUS/USTCSMSStageBS_C,E18722,Owner,\\USTCFN08\SHARE\SMSStageBS,
USTCSMSStageBS_C,KCUS,WinNT://KCUS/USTCSMSStageBS_C,W48997,Delegate,\\USTCFN08\SHARE\SMSStageBS,
O365-SME-E3M,KCUS,WinNT://KCUS/O365-SME-E3M,E18722,Owner,Members of this group will be excluded from automated O365 licensing,
O365-SME-E3M,KCUS,WinNT://KCUS/O365-SME-E3M,Q03582,Authorizer,Members of this group will be excluded from automated O365 licensing,
O365-SME-E3M,KCUS,WinNT://KCUS/O365-SME-E3M,W62002,Delegate,Members of this group will be excluded from automated O365 licensing,
HostAppMenu-SSH,KCUS,WinNT://KCUS/HostAppMenu-SSH,E18722,Owner,Citrix User Security group for Putty,
HostAppMenu-SSH,KCUS,WinNT://KCUS/HostAppMenu-SSH,Q07708,Authorizer,Citrix User Security group for Putty,
HostAppMenu-SSH,KCUS,WinNT://KCUS/HostAppMenu-SSH,Q13357,Authorizer,Citrix User Security group for Putty,
HostAppMenu-SSH,KCUS,WinNT://KCUS/HostAppMenu-SSH,Q14616,Authorizer,Citrix User Security group for Putty,
HostAppMenu-SSH,KCUS,WinNT://KCUS/HostAppMenu-SSH,Q14718,Authorizer,Citrix User Security group for Putty,
HostAppMenu-SSH,KCUS,WinNT://KCUS/HostAppMenu-SSH,Q15926,Authorizer,Citrix User Security group for Putty,
HostAppMenu-SSH,KCUS,WinNT://KCUS/HostAppMenu-SSH,W49040,Delegate,Citrix User Security group for Putty,
RTCUniversalUserReadOnlyGroup,KCUS,WinNT://KCUS/RTCUniversalUserReadOnlyGroup,E18722,Owner,Members have read access to RTC-related user attributes or property sets.,
RTCUniversalUserReadOnlyGroup,KCUS,WinNT://KCUS/RTCUniversalUserReadOnlyGroup,W62002,Delegate,Members have read access to RTC-related user attributes or property sets.,
Hostappmenu-Googlechrome,KCUS,WinNT://KCUS/Hostappmenu-Googlechrome,E18722,Owner,Citrix User Security group for Google chrome,
Hostappmenu-Googlechrome,KCUS,WinNT://KCUS/Hostappmenu-Googlechrome,Q07708,Authorizer,Citrix User Security group for Google chrome,
Hostappmenu-Googlechrome,KCUS,WinNT://KCUS/Hostappmenu-Googlechrome,Q13357,Authorizer,Citrix User Security group for Google chrome,
Hostappmenu-Googlechrome,KCUS,WinNT://KCUS/Hostappmenu-Googlechrome,Q14616,Authorizer,Citrix User Security group for Google chrome,
Hostappmenu-Googlechrome,KCUS,WinNT://KCUS/Hostappmenu-Googlechrome,Q14718,Authorizer,Citrix User Security group for Google chrome,
Hostappmenu-Googlechrome,KCUS,WinNT://KCUS/Hostappmenu-Googlechrome,Q15926,Authorizer,Citrix User Security group for Google chrome,
Hostappmenu-Googlechrome,KCUS,WinNT://KCUS/Hostappmenu-Googlechrome,W49040,Delegate,Citrix User Security group for Google chrome,
Deny-Both-PTW2000 Core,KCUS,WinNT://KCUS/Deny-Both-PTW2000 Core,E18722,Owner,Deny Policy group on PTW's to be used only w/ Installer accounts and "S" accounts,
Deny-Both-PTW2000 Core,KCUS,WinNT://KCUS/Deny-Both-PTW2000 Core,Q06085,Authorizer,Deny Policy group on PTW's to be used only w/ Installer accounts and "S" accounts,
Deny-Both-PTW2000 Core,KCUS,WinNT://KCUS/Deny-Both-PTW2000 Core,Q08911,Authorizer,Deny Policy group on PTW's to be used only w/ Installer accounts and "S" accounts,
Deny-Both-PTW2000 Core,KCUS,WinNT://KCUS/Deny-Both-PTW2000 Core,W48997,Delegate,Deny Policy group on PTW's to be used only w/ Installer accounts and "S" accounts,
MS Visio Standard,KCUS,WinNT://KCUS/MS Visio Standard,E18722,Owner,Authorized Users – Visio Standard,
MS Visio Standard,KCUS,WinNT://KCUS/MS Visio Standard,Q13357,Authorizer,Authorized Users – Visio Standard,
MS Visio Standard,KCUS,WinNT://KCUS/MS Visio Standard,Q14718,Authorizer,Authorized Users – Visio Standard,
MS Visio Standard,KCUS,WinNT://KCUS/MS Visio Standard,Q15926,Authorizer,Authorized Users – Visio Standard,
MS Visio Standard,KCUS,WinNT://KCUS/MS Visio Standard,W66442,Delegate,Authorized Users – Visio Standard,
HostApp-RD,KCUS,WinNT://KCUS/HostApp-RD,E18722,Owner,Citrix Application security group for Remote Desktop,
HostApp-RD,KCUS,WinNT://KCUS/HostApp-RD,Q07708,Authorizer,Citrix Application security group for Remote Desktop,
HostApp-RD,KCUS,WinNT://KCUS/HostApp-RD,Q13357,Authorizer,Citrix Application security group for Remote Desktop,
HostApp-RD,KCUS,WinNT://KCUS/HostApp-RD,Q14616,Authorizer,Citrix Application security group for Remote Desktop,
HostApp-RD,KCUS,WinNT://KCUS/HostApp-RD,Q14718,Authorizer,Citrix Application security group for Remote Desktop,
HostApp-RD,KCUS,WinNT://KCUS/HostApp-RD,Q15926,Authorizer,Citrix Application security group for Remote Desktop,
HostApp-RD,KCUS,WinNT://KCUS/HostApp-RD,W49040,Delegate,Citrix Application security group for Remote Desktop,
Exchange Public Folder Administrators,KCUS,WinNT://KCUS/Exchange Public Folder Administrators,E18722,Owner,Users in this group can manage Exchange public folder attributes in the Exchange Information Store and perform select public folder operations. This group should not be deleted.,
Exchange Public Folder Administrators,KCUS,WinNT://KCUS/Exchange Public Folder Administrators,W62002,Delegate,Users in this group can manage Exchange public folder attributes in the Exchange Information Store and perform select public folder operations. This group should not be deleted.,
Enable-Hybrid-DeviceJoin,KCUS,WinNT://KCUS/Enable-Hybrid-DeviceJoin,E18722,Owner,This group is used to join a device to hybrid domain join,
Enable-Hybrid-DeviceJoin,KCUS,WinNT://KCUS/Enable-Hybrid-DeviceJoin,Q04499,Authorizer,This group is used to join a device to hybrid domain join,
Enable-Hybrid-DeviceJoin,KCUS,WinNT://KCUS/Enable-Hybrid-DeviceJoin,Q07749,Authorizer,This group is used to join a device to hybrid domain join,
Enable-Hybrid-DeviceJoin,KCUS,WinNT://KCUS/Enable-Hybrid-DeviceJoin,Q08911,Authorizer,This group is used to join a device to hybrid domain join,
Enable-Hybrid-DeviceJoin,KCUS,WinNT://KCUS/Enable-Hybrid-DeviceJoin,W48997,Delegate,This group is used to join a device to hybrid domain join,
O365 PowerBI for O365 Users,KCUS,WinNT://KCUS/O365 PowerBI for O365 Users,B46959,Authorizer,Controls access to PowerBI for O365,
O365 PowerBI for O365 Users,KCUS,WinNT://KCUS/O365 PowerBI for O365 Users,E18722,Owner,Controls access to PowerBI for O365,
O365 PowerBI for O365 Users,KCUS,WinNT://KCUS/O365 PowerBI for O365 Users,W62002,Delegate,Controls access to PowerBI for O365,
O365 PowerBI Free Users,KCUS,WinNT://KCUS/O365 PowerBI Free Users,B46959,Authorizer,Controls access to PowerBI Free,
O365 PowerBI Free Users,KCUS,WinNT://KCUS/O365 PowerBI Free Users,E18722,Owner,Controls access to PowerBI Free,
O365 PowerBI Free Users,KCUS,WinNT://KCUS/O365 PowerBI Free Users,W62002,Delegate,Controls access to PowerBI Free,
Edge for Business Exception,KCUS,WinNT://KCUS/Edge for Business Exception,E18722,Owner,Restrict the Edge for Business upgrade,
Edge for Business Exception,KCUS,WinNT://KCUS/Edge for Business Exception,W48997,Delegate,Restrict the Edge for Business upgrade,
Exchange Recipient Administrators,KCUS,WinNT://KCUS/Exchange Recipient Administrators,E18722,Owner,Users in this group can manage Exchange user attributes in the Active Directory and perform select mailbox operations. This group should not be deleted.,
Exchange Recipient Administrators,KCUS,WinNT://KCUS/Exchange Recipient Administrators,W62002,Delegate,Users in this group can manage Exchange user attributes in the Active Directory and perform select mailbox operations. This group should not be deleted.,
GDMachineAcctRights,KCUS,WinNT://KCUS/GDMachineAcctRights,E18722,Owner,Provides rights to create/delete machine accounts through the Machine Account Utility,
GDMachineAcctRights,KCUS,WinNT://KCUS/GDMachineAcctRights,Q06085,Authorizer,Provides rights to create/delete machine accounts through the Machine Account Utility,
GDMachineAcctRights,KCUS,WinNT://KCUS/GDMachineAcctRights,Q08911,Authorizer,Provides rights to create/delete machine accounts through the Machine Account Utility,
GDMachineAcctRights,KCUS,WinNT://KCUS/GDMachineAcctRights,W48997,Delegate,Provides rights to create/delete machine accounts through the Machine Account Utility,
Exclusion - Disable OneDrive sync,KCUS,WinNT://KCUS/Exclusion - Disable OneDrive sync,E18722,Owner,Exclusion group for disabling OneDrive Sync in Shared machines,
Exclusion - Disable OneDrive sync,KCUS,WinNT://KCUS/Exclusion - Disable OneDrive sync,Q04499,Delegate,Exclusion group for disabling OneDrive Sync in Shared machines,
Exclusion - Disable OneDrive sync,KCUS,WinNT://KCUS/Exclusion - Disable OneDrive sync,Q39116,Authorizer,Exclusion group for disabling OneDrive Sync in Shared machines,
HostServerAdmin-BOBJBI,KCUS,WinNT://KCUS/HostServerAdmin-BOBJBI,E18722,Owner,Citrix Server access security group for BOBJBI,
HostServerAdmin-BOBJBI,KCUS,WinNT://KCUS/HostServerAdmin-BOBJBI,Q07708,Authorizer,Citrix Server access security group for BOBJBI,
HostServerAdmin-BOBJBI,KCUS,WinNT://KCUS/HostServerAdmin-BOBJBI,Q13357,Authorizer,Citrix Server access security group for BOBJBI,
HostServerAdmin-BOBJBI,KCUS,WinNT://KCUS/HostServerAdmin-BOBJBI,Q14616,Authorizer,Citrix Server access security group for BOBJBI,
HostServerAdmin-BOBJBI,KCUS,WinNT://KCUS/HostServerAdmin-BOBJBI,Q14718,Authorizer,Citrix Server access security group for BOBJBI,
HostServerAdmin-BOBJBI,KCUS,WinNT://KCUS/HostServerAdmin-BOBJBI,Q15926,Authorizer,Citrix Server access security group for BOBJBI,
HostServerAdmin-BOBJBI,KCUS,WinNT://KCUS/HostServerAdmin-BOBJBI,W49040,Delegate,Citrix Server access security group for BOBJBI,
MISCOMPSVCSEDTEXECSUPP_rstr_c,KCUS,WinNT://KCUS/MISCOMPSVCSEDTEXECSUPP_rstr_c,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Content\NA.US.kcc.com\MIS\COMPSVCS\EDT\EXECUTIVE SUPPORT _rstr,
MISCOMPSVCSEDTEXECSUPP_rstr_c,KCUS,WinNT://KCUS/MISCOMPSVCSEDTEXECSUPP_rstr_c,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Content\NA.US.kcc.com\MIS\COMPSVCS\EDT\EXECUTIVE SUPPORT _rstr,
TSUsers_USTCTD01,KCUS,WinNT://KCUS/TSUsers_USTCTD01,E18722,Owner,Terminal Server Access to USTCTD01,
TSUsers_USTCTD01,KCUS,WinNT://KCUS/TSUsers_USTCTD01,W66442,Delegate,Terminal Server Access to USTCTD01,
IT_User_Information_DEV_Users,KCUS,WinNT://KCUS/IT_User_Information_DEV_Users,B00932,Delegate,This group will control access to the IT User Information Web Application - dev,
IT_User_Information_DEV_Users,KCUS,WinNT://KCUS/IT_User_Information_DEV_Users,E18722,Owner,This group will control access to the IT User Information Web Application - dev,
IT_User_Information_PROD_Agents,KCUS,WinNT://KCUS/IT_User_Information_PROD_Agents,B00932,Delegate,Perform lookups of general user information plus additional service desk activities,
IT_User_Information_PROD_Agents,KCUS,WinNT://KCUS/IT_User_Information_PROD_Agents,E18722,Owner,Perform lookups of general user information plus additional service desk activities,
IT_User_Information_QA_Agents,KCUS,WinNT://KCUS/IT_User_Information_QA_Agents,B00932,Delegate,Perform lookups of general user information plus additional service desk activities - qa,
IT_User_Information_QA_Agents,KCUS,WinNT://KCUS/IT_User_Information_QA_Agents,E18722,Owner,Perform lookups of general user information plus additional service desk activities - qa,
IT_User_Information_PROD_Admins,KCUS,WinNT://KCUS/IT_User_Information_PROD_Admins,B00932,Delegate,Perform lookups of general user information plus additional administration of the application,
IT_User_Information_PROD_Admins,KCUS,WinNT://KCUS/IT_User_Information_PROD_Admins,E18722,Owner,Perform lookups of general user information plus additional administration of the application,
ITS - Testing Personal Email (Recipient Management),KCUS,WinNT://KCUS/ITS - Testing Personal Email (Recipient Management),E18722,Owner,Grant access to only user objects in Draper OU. Currently only usncts80 in that OU.,
ITS - Testing Personal Email (Recipient Management),KCUS,WinNT://KCUS/ITS - Testing Personal Email (Recipient Management),E32872,Delegate,Grant access to only user objects in Draper OU. Currently only usncts80 in that OU.,
Corporate Mobile Device Agreement - Eastern Europe,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement - Eastern Europe,E18722,Owner,Use this group for people who have signed the PMDA in EU,
Corporate Mobile Device Agreement - Eastern Europe,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement - Eastern Europe,E32872,Delegate,Use this group for people who have signed the PMDA in EU,
Corporate Mobile Device Agreement - Bahrain,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement - Bahrain,E18722,Owner,Use for people who have signed the PMDA in Bahrain,
Corporate Mobile Device Agreement - Bahrain,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement - Bahrain,E32872,Delegate,Use for people who have signed the PMDA in Bahrain,
Corporate Mobile Device Agreement - Turkey,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement - Turkey,E18722,Owner,Use for people who have signed the PMDA in Turkey,
Corporate Mobile Device Agreement - Turkey,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement - Turkey,E32872,Delegate,Use for people who have signed the PMDA in Turkey,
Corporate Mobile Device Agreement - Brazil,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement - Brazil,E18722,Owner,Use this group for people who have signed the PMDA in Brazil,
Corporate Mobile Device Agreement - Brazil,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement - Brazil,E32872,Delegate,Use this group for people who have signed the PMDA in Brazil,
Admin ICW,KCUS,WinNT://KCUS/Admin ICW,E18722,Owner,Admin group to not receive policy for the ICW only. (i or s IDs only),
Admin ICW,KCUS,WinNT://KCUS/Admin ICW,Q06085,Authorizer,Admin group to not receive policy for the ICW only. (i or s IDs only),
Admin ICW,KCUS,WinNT://KCUS/Admin ICW,Q08911,Authorizer,Admin group to not receive policy for the ICW only. (i or s IDs only),
Admin ICW,KCUS,WinNT://KCUS/Admin ICW,W48997,Delegate,Admin group to not receive policy for the ICW only. (i or s IDs only),
MSX Directory Admin,KCUS,WinNT://KCUS/MSX Directory Admin,E18722,Owner,Microsoft Exchange Directory Administrators,
MSX Directory Admin,KCUS,WinNT://KCUS/MSX Directory Admin,W62002,Delegate,Microsoft Exchange Directory Administrators,
MSX Server Admin,KCUS,WinNT://KCUS/MSX Server Admin,E18722,Owner,Microsoft Exchange Server Administrators (Configuration),
MSX Server Admin,KCUS,WinNT://KCUS/MSX Server Admin,Q03582,Authorizer,Microsoft Exchange Server Administrators (Configuration),
MSX Server Admin,KCUS,WinNT://KCUS/MSX Server Admin,W62002,Delegate,Microsoft Exchange Server Administrators (Configuration),
DesktopGPOAdmins,KCUS,WinNT://KCUS/DesktopGPOAdmins,E18722,Owner,Gives members access to modify existing Desktop owned GPOs,
DesktopGPOAdmins,KCUS,WinNT://KCUS/DesktopGPOAdmins,W48997,Delegate,Gives members access to modify existing Desktop owned GPOs,
MISCSCSM_C,KCUS,WinNT://KCUS/MISCSCSM_C,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Application\NA.US.KCC.COM.APP\root\MIS\COMPSVCS\CSM,
MISCSCSM_C,KCUS,WinNT://KCUS/MISCSCSM_C,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Application\NA.US.KCC.COM.APP\root\MIS\COMPSVCS\CSM,
USTCPipeStge_C,KCUS,WinNT://KCUS/USTCPipeStge_C,W48997,Delegate,\\USTCFN01\SHARE\PIPESTGE,
USTCPipeStge_C,KCUS,WinNT://KCUS/USTCPipeStge_C,E18722,Owner,\\USTCFN01\SHARE\PIPESTGE,
IACHMedSvcTwrDesktop_R,KCUS,WinNT://KCUS/IACHMedSvcTwrDesktop_R,B92167,Delegate,\\iachfn01\KC Share\Medium Sensitive\Service Towers\Desktop,
IACHMedSvcTwrDesktop_R,KCUS,WinNT://KCUS/IACHMedSvcTwrDesktop_R,E18722,Owner,\\iachfn01\KC Share\Medium Sensitive\Service Towers\Desktop,
IACHMedSvcTwrDesktop_R,KCUS,WinNT://KCUS/IACHMedSvcTwrDesktop_R,Q06085,Authorizer,\\iachfn01\KC Share\Medium Sensitive\Service Towers\Desktop,
IACHMedSvcTwrDesktop_R,KCUS,WinNT://KCUS/IACHMedSvcTwrDesktop_R,Q08911,Authorizer,\\iachfn01\KC Share\Medium Sensitive\Service Towers\Desktop,
IACHLowSvcTwrDesktop_C,KCUS,WinNT://KCUS/IACHLowSvcTwrDesktop_C,B92167,Delegate,\\iachfn01\KC Share\Low Sensitive\Service Towers\Desktop,
IACHLowSvcTwrDesktop_C,KCUS,WinNT://KCUS/IACHLowSvcTwrDesktop_C,E18722,Owner,\\iachfn01\KC Share\Low Sensitive\Service Towers\Desktop,
IACHLowSvcTwrDesktop_C,KCUS,WinNT://KCUS/IACHLowSvcTwrDesktop_C,Q06085,Authorizer,\\iachfn01\KC Share\Low Sensitive\Service Towers\Desktop,
IACHLowSvcTwrDesktop_C,KCUS,WinNT://KCUS/IACHLowSvcTwrDesktop_C,Q08911,Authorizer,\\iachfn01\KC Share\Low Sensitive\Service Towers\Desktop,
KCInstall,KCUS,WinNT://KCUS/KCInstall,E18722,Owner,Enterprise Desktop Installers,
KCInstall,KCUS,WinNT://KCUS/KCInstall,q04499,Authorizer,Enterprise Desktop Installers,
KCInstall,KCUS,WinNT://KCUS/KCInstall,Q06085,Authorizer,Enterprise Desktop Installers,
KCInstall,KCUS,WinNT://KCUS/KCInstall,Q08911,Authorizer,Enterprise Desktop Installers,
KCInstall,KCUS,WinNT://KCUS/KCInstall,W66442,Delegate,Enterprise Desktop Installers,
USTCFF96_PSUsers,KCUS,WinNT://KCUS/USTCFF96_PSUsers,E18722,Owner,Users – US KC Neenah O365 Production Tenant Folder Firefight,
USTCFF96_PSUsers,KCUS,WinNT://KCUS/USTCFF96_PSUsers,W62002,Delegate,Users – US KC Neenah O365 Production Tenant Folder Firefight,
NONGD Workstations-Pilot Testing,KCUS,WinNT://KCUS/NONGD Workstations-Pilot Testing,E18722,Owner,This group will use for the NONGD PCs pilot testing,
NONGD Workstations-Pilot Testing,KCUS,WinNT://KCUS/NONGD Workstations-Pilot Testing,Q06085,Delegate,This group will use for the NONGD PCs pilot testing,
O365-Dom-Int-Calling-Plan,KCUS,WinNT://KCUS/O365-Dom-Int-Calling-Plan,B91148,Authorizer,Members are licensed for Microsoft Teams Domestic and International Calling Plan,
O365-Dom-Int-Calling-Plan,KCUS,WinNT://KCUS/O365-Dom-Int-Calling-Plan,e18722,Owner,Members are licensed for Microsoft Teams Domestic and International Calling Plan,
O365-Dom-Int-Calling-Plan,KCUS,WinNT://KCUS/O365-Dom-Int-Calling-Plan,W62002,Delegate,Members are licensed for Microsoft Teams Domestic and International Calling Plan,
O365-Audio-Conferencing,KCUS,WinNT://KCUS/O365-Audio-Conferencing,B91148,Authorizer,Members are licensed for Microsoft Teams Audio Conferencing capabilities,
O365-Audio-Conferencing,KCUS,WinNT://KCUS/O365-Audio-Conferencing,e18722,Owner,Members are licensed for Microsoft Teams Audio Conferencing capabilities,
O365-Audio-Conferencing,KCUS,WinNT://KCUS/O365-Audio-Conferencing,W62002,Delegate,Members are licensed for Microsoft Teams Audio Conferencing capabilities,
HostApp-SSH,KCUS,WinNT://KCUS/HostApp-SSH,E18722,Owner,Citrix Application Security group for hosting Putty,
HostApp-SSH,KCUS,WinNT://KCUS/HostApp-SSH,Q07708,Authorizer,Citrix Application Security group for hosting Putty,
HostApp-SSH,KCUS,WinNT://KCUS/HostApp-SSH,Q13357,Authorizer,Citrix Application Security group for hosting Putty,
HostApp-SSH,KCUS,WinNT://KCUS/HostApp-SSH,Q14616,Authorizer,Citrix Application Security group for hosting Putty,
HostApp-SSH,KCUS,WinNT://KCUS/HostApp-SSH,Q14718,Authorizer,Citrix Application Security group for hosting Putty,
HostApp-SSH,KCUS,WinNT://KCUS/HostApp-SSH,Q15926,Authorizer,Citrix Application Security group for hosting Putty,
HostApp-SSH,KCUS,WinNT://KCUS/HostApp-SSH,W49040,Delegate,Citrix Application Security group for hosting Putty,
Hygiene Management,KCUS,WinNT://KCUS/Hygiene Management,E18722,Owner,Members of this management role group can manage Exchange anti-spam features and grant permissions for antivirus products to integrate with Exchange.,
Hygiene Management,KCUS,WinNT://KCUS/Hygiene Management,W62002,Delegate,Members of this management role group can manage Exchange anti-spam features and grant permissions for antivirus products to integrate with Exchange.,
O365 Billing Administrator,KCUS,WinNT://KCUS/O365 Billing Administrator,E18722,Owner,Assign the Billing Administrator role to users who need to do the following,
O365 Billing Administrator,KCUS,WinNT://KCUS/O365 Billing Administrator,W62002,Delegate,Assign the Billing Administrator role to users who need to do the following,
O365-WinAutoP-Requestors,KCUS,WinNT://KCUS/O365-WinAutoP-Requestors,Q06085,Authorizer,ACL for new Autopilot user registration at Mobility portal,
O365-WinAutoP-Requestors,KCUS,WinNT://KCUS/O365-WinAutoP-Requestors,Q39116,Authorizer,ACL for new Autopilot user registration at Mobility portal,
O365-WinAutoP-Requestors,KCUS,WinNT://KCUS/O365-WinAutoP-Requestors,W45758,Authorizer,ACL for new Autopilot user registration at Mobility portal,
O365-WinAutoP-Requestors,KCUS,WinNT://KCUS/O365-WinAutoP-Requestors,W48997,Delegate,ACL for new Autopilot user registration at Mobility portal,
O365-WinAutoP-Requestors,KCUS,WinNT://KCUS/O365-WinAutoP-Requestors,B45906,Authorizer,ACL for new Autopilot user registration at Mobility portal,
O365-WinAutoP-Requestors,KCUS,WinNT://KCUS/O365-WinAutoP-Requestors,B70279,Authorizer,ACL for new Autopilot user registration at Mobility portal,
O365-WinAutoP-Requestors,KCUS,WinNT://KCUS/O365-WinAutoP-Requestors,E18722,Owner,ACL for new Autopilot user registration at Mobility portal,
O365-WinAutoP-Requestors,KCUS,WinNT://KCUS/O365-WinAutoP-Requestors,E38114,Authorizer,ACL for new Autopilot user registration at Mobility portal,
O365-WinAutoP-Requestors,KCUS,WinNT://KCUS/O365-WinAutoP-Requestors,Q03582,Authorizer,ACL for new Autopilot user registration at Mobility portal,
HostServerAdmin-Tops,KCUS,WinNT://KCUS/HostServerAdmin-Tops,Q15926,Authorizer,Citrix Server Admin Group for Tops application Team,
HostServerAdmin-Tops,KCUS,WinNT://KCUS/HostServerAdmin-Tops,W49040,Delegate,Citrix Server Admin Group for Tops application Team,
HostServerAdmin-Tops,KCUS,WinNT://KCUS/HostServerAdmin-Tops,E18722,Owner,Citrix Server Admin Group for Tops application Team,
HostServerAdmin-Tops,KCUS,WinNT://KCUS/HostServerAdmin-Tops,Q07708,Authorizer,Citrix Server Admin Group for Tops application Team,
HostServerAdmin-Tops,KCUS,WinNT://KCUS/HostServerAdmin-Tops,Q13357,Authorizer,Citrix Server Admin Group for Tops application Team,
HostServerAdmin-Tops,KCUS,WinNT://KCUS/HostServerAdmin-Tops,Q14616,Authorizer,Citrix Server Admin Group for Tops application Team,
HostServerAdmin-Tops,KCUS,WinNT://KCUS/HostServerAdmin-Tops,Q14718,Authorizer,Citrix Server Admin Group for Tops application Team,
HostApp-WMS-WFM-NWDC,KCUS,WinNT://KCUS/HostApp-WMS-WFM-NWDC,E18722,Owner,Citrix Application Security group for hosting from North West Distrbution Center,
HostApp-WMS-WFM-NWDC,KCUS,WinNT://KCUS/HostApp-WMS-WFM-NWDC,q07708,Authorizer,Citrix Application Security group for hosting from North West Distrbution Center,
HostApp-WMS-WFM-NWDC,KCUS,WinNT://KCUS/HostApp-WMS-WFM-NWDC,Q14616,Authorizer,Citrix Application Security group for hosting from North West Distrbution Center,
HostApp-WMS-WFM-NWDC,KCUS,WinNT://KCUS/HostApp-WMS-WFM-NWDC,W49040,Delegate,Citrix Application Security group for hosting from North West Distrbution Center,
HostAppMenu-PublishedDesktop,KCUS,WinNT://KCUS/HostAppMenu-PublishedDesktop,E18722,Owner,HostAppMenu-PublishedDesktop,
HostAppMenu-PublishedDesktop,KCUS,WinNT://KCUS/HostAppMenu-PublishedDesktop,Q07708,Authorizer,HostAppMenu-PublishedDesktop,
HostAppMenu-PublishedDesktop,KCUS,WinNT://KCUS/HostAppMenu-PublishedDesktop,Q13357,Authorizer,HostAppMenu-PublishedDesktop,
HostAppMenu-PublishedDesktop,KCUS,WinNT://KCUS/HostAppMenu-PublishedDesktop,Q14616,Authorizer,HostAppMenu-PublishedDesktop,
HostAppMenu-PublishedDesktop,KCUS,WinNT://KCUS/HostAppMenu-PublishedDesktop,Q14718,Authorizer,HostAppMenu-PublishedDesktop,
HostAppMenu-PublishedDesktop,KCUS,WinNT://KCUS/HostAppMenu-PublishedDesktop,Q15926,Authorizer,HostAppMenu-PublishedDesktop,
HostAppMenu-PublishedDesktop,KCUS,WinNT://KCUS/HostAppMenu-PublishedDesktop,W66442,Delegate,HostAppMenu-PublishedDesktop,
CA-DESKTOP-SERVICES,KCUS,WinNT://KCUS/CA-DESKTOP-SERVICES,E18722,Owner,Desktop Services team members identified as Change Assessors.,
CA-DESKTOP-SERVICES,KCUS,WinNT://KCUS/CA-DESKTOP-SERVICES,W66442,Delegate,Desktop Services team members identified as Change Assessors.,
GRP09274_C,KCUS,WinNT://KCUS/GRP09274_C,E18722,Owner,Mailbox: _Messaging  Communication,
GRP09274_C,KCUS,WinNT://KCUS/GRP09274_C,Q03582,Authorizer,Mailbox: _Messaging  Communication,
GRP09274_C,KCUS,WinNT://KCUS/GRP09274_C,W62002,Delegate,Mailbox: _Messaging  Communication,
O365 Global Reader,KCUS,WinNT://KCUS/O365 Global Reader,E18722,Owner,Read settings and administrative information across O365,
O365 Global Reader,KCUS,WinNT://KCUS/O365 Global Reader,Q03582,Authorizer,Read settings and administrative information across O365,
O365 Global Reader,KCUS,WinNT://KCUS/O365 Global Reader,W62002,Delegate,Read settings and administrative information across O365,
PBI_WS_AP_MessagingPlatform_ADHOC_VIEWER_P,KCUS,WinNT://KCUS/PBI_WS_AP_MessagingPlatform_ADHOC_VIEWER_P,E18722,Owner,Power BI VIEWER Role for AP MessagingPlatform  ADHOC Workspace,
PBI_WS_AP_MessagingPlatform_ADHOC_VIEWER_P,KCUS,WinNT://KCUS/PBI_WS_AP_MessagingPlatform_ADHOC_VIEWER_P,W62002,Delegate,Power BI VIEWER Role for AP MessagingPlatform  ADHOC Workspace,
KCFILES_CorpMISCompSvcsEDMgmtAdmnKCTm_C,KCUS,WinNT://KCUS/KCFILES_CorpMISCompSvcsEDMgmtAdmnKCTm_C,E18722,Owner,\\kcfiles\Share\Corporate\MIS\Computer Services\ENTERPRISE DESKTOP MANAGEMENT\Admin\KC Team,
KCFILES_CorpMISCompSvcsEDMgmtAdmnKCTm_C,KCUS,WinNT://KCUS/KCFILES_CorpMISCompSvcsEDMgmtAdmnKCTm_C,W66442,Delegate,\\kcfiles\Share\Corporate\MIS\Computer Services\ENTERPRISE DESKTOP MANAGEMENT\Admin\KC Team,
Hostapp-YKePIM,KCUS,WinNT://KCUS/Hostapp-YKePIM,E18722,Owner,Citrix Application security group for ePIM Korea,
Hostapp-YKePIM,KCUS,WinNT://KCUS/Hostapp-YKePIM,Q07708,Authorizer,Citrix Application security group for ePIM Korea,
Hostapp-YKePIM,KCUS,WinNT://KCUS/Hostapp-YKePIM,Q13357,Authorizer,Citrix Application security group for ePIM Korea,
Hostapp-YKePIM,KCUS,WinNT://KCUS/Hostapp-YKePIM,Q14616,Authorizer,Citrix Application security group for ePIM Korea,
Hostapp-YKePIM,KCUS,WinNT://KCUS/Hostapp-YKePIM,Q14718,Authorizer,Citrix Application security group for ePIM Korea,
Hostapp-YKePIM,KCUS,WinNT://KCUS/Hostapp-YKePIM,Q15926,Authorizer,Citrix Application security group for ePIM Korea,
Hostapp-YKePIM,KCUS,WinNT://KCUS/Hostapp-YKePIM,W49040,Delegate,Citrix Application security group for ePIM Korea,
EDMT Contractors,KCUS,WinNT://KCUS/EDMT Contractors,E18722,Owner,Group used to manage Contractor access to Dev and QA servers,
EDMT Contractors,KCUS,WinNT://KCUS/EDMT Contractors,W48997,Delegate,Group used to manage Contractor access to Dev and QA servers,
SCCMApp-Adobe InDesign CC,KCUS,WinNT://KCUS/SCCMApp-Adobe InDesign CC,E18722,Owner,SCCM Package for Adobe InDesign Creative Cloud,
SCCMApp-Adobe InDesign CC,KCUS,WinNT://KCUS/SCCMApp-Adobe InDesign CC,W48997,Delegate,SCCM Package for Adobe InDesign Creative Cloud,
SCCMApp-Adobe After Effects CC,KCUS,WinNT://KCUS/SCCMApp-Adobe After Effects CC,W48997,Delegate,SCCM Package for  Adobe After Effects Creative Cloud,
SCCMApp-Adobe After Effects CC,KCUS,WinNT://KCUS/SCCMApp-Adobe After Effects CC,E18722,Owner,SCCM Package for  Adobe After Effects Creative Cloud,
SCCMApp-Adobe Photoshop CC,KCUS,WinNT://KCUS/SCCMApp-Adobe Photoshop CC,E18722,Owner,SCCM Package for Adobe Photoshop Creative Cloud,
SCCMApp-Adobe Photoshop CC,KCUS,WinNT://KCUS/SCCMApp-Adobe Photoshop CC,W48997,Delegate,SCCM Package for Adobe Photoshop Creative Cloud,
FC-MuleSoftUsers,KCUS,WinNT://KCUS/FC-MuleSoftUsers,E18722,Owner,MuleSoft Developers VDI Users,
FC-MuleSoftUsers,KCUS,WinNT://KCUS/FC-MuleSoftUsers,Q07708,Authorizer,MuleSoft Developers VDI Users,
FC-MuleSoftUsers,KCUS,WinNT://KCUS/FC-MuleSoftUsers,Q13357,Authorizer,MuleSoft Developers VDI Users,
FC-MuleSoftUsers,KCUS,WinNT://KCUS/FC-MuleSoftUsers,Q14616,Authorizer,MuleSoft Developers VDI Users,
FC-MuleSoftUsers,KCUS,WinNT://KCUS/FC-MuleSoftUsers,Q14718,Authorizer,MuleSoft Developers VDI Users,
FC-MuleSoftUsers,KCUS,WinNT://KCUS/FC-MuleSoftUsers,Q15926,Authorizer,MuleSoft Developers VDI Users,
FC-MuleSoftUsers,KCUS,WinNT://KCUS/FC-MuleSoftUsers,w49040,Delegate,MuleSoft Developers VDI Users,
HostApp-ClarityWorkbench,KCUS,WinNT://KCUS/HostApp-ClarityWorkbench,Q15926,Authorizer,Citrix Application Group for Clarity Open Workbench,
HostApp-ClarityWorkbench,KCUS,WinNT://KCUS/HostApp-ClarityWorkbench,W49040,Delegate,Citrix Application Group for Clarity Open Workbench,
HostApp-ClarityWorkbench,KCUS,WinNT://KCUS/HostApp-ClarityWorkbench,E18722,Owner,Citrix Application Group for Clarity Open Workbench,
HostApp-ClarityWorkbench,KCUS,WinNT://KCUS/HostApp-ClarityWorkbench,Q07708,Authorizer,Citrix Application Group for Clarity Open Workbench,
HostApp-ClarityWorkbench,KCUS,WinNT://KCUS/HostApp-ClarityWorkbench,Q13357,Authorizer,Citrix Application Group for Clarity Open Workbench,
HostApp-ClarityWorkbench,KCUS,WinNT://KCUS/HostApp-ClarityWorkbench,Q14616,Authorizer,Citrix Application Group for Clarity Open Workbench,
HostApp-ClarityWorkbench,KCUS,WinNT://KCUS/HostApp-ClarityWorkbench,Q14718,Authorizer,Citrix Application Group for Clarity Open Workbench,
QATesters_S-id,KCUS,WinNT://KCUS/QATesters_S-id,E18722,Owner,Desktop QA Testers - S-id's,
QATesters_S-id,KCUS,WinNT://KCUS/QATesters_S-id,Q04499,Delegate,Desktop QA Testers - S-id's,
QATesters_I-id,KCUS,WinNT://KCUS/QATesters_I-id,E18722,Owner,Desktop QA Testers - I-id's,
QATesters_I-id,KCUS,WinNT://KCUS/QATesters_I-id,Q04499,Delegate,Desktop QA Testers - I-id's,
O365_Flow,KCUS,WinNT://KCUS/O365_Flow,B46959,Authorizer,O365 O365_Flow Access,
O365_Flow,KCUS,WinNT://KCUS/O365_Flow,E18722,Owner,O365 O365_Flow Access,
O365_Flow,KCUS,WinNT://KCUS/O365_Flow,L12964,Authorizer,O365 O365_Flow Access,
O365_Flow,KCUS,WinNT://KCUS/O365_Flow,W62002,Delegate,O365 O365_Flow Access,
O365_PowerApps,KCUS,WinNT://KCUS/O365_PowerApps,B46959,Authorizer,O365 O365_PowerApps Access,
O365_PowerApps,KCUS,WinNT://KCUS/O365_PowerApps,E18722,Owner,O365 O365_PowerApps Access,
O365_PowerApps,KCUS,WinNT://KCUS/O365_PowerApps,L12964,Authorizer,O365 O365_PowerApps Access,
O365_PowerApps,KCUS,WinNT://KCUS/O365_PowerApps,W62002,Delegate,O365 O365_PowerApps Access,
O365_StaffHub,KCUS,WinNT://KCUS/O365_StaffHub,B46959,Authorizer,O365 O365_StaffHub Access,
O365_StaffHub,KCUS,WinNT://KCUS/O365_StaffHub,E18722,Owner,O365 O365_StaffHub Access,
O365_StaffHub,KCUS,WinNT://KCUS/O365_StaffHub,W62002,Delegate,O365 O365_StaffHub Access,
O365_Skype,KCUS,WinNT://KCUS/O365_Skype,B46959,Authorizer,O365 O365_Skype Access,
O365_Skype,KCUS,WinNT://KCUS/O365_Skype,E18722,Owner,O365 O365_Skype Access,
O365_Skype,KCUS,WinNT://KCUS/O365_Skype,W62002,Delegate,O365 O365_Skype Access,
O365 Kaizala Administrators,KCUS,WinNT://KCUS/O365 Kaizala Administrators,E18722,Owner,Can manage settings for Microsoft Kaizala,
O365 Kaizala Administrators,KCUS,WinNT://KCUS/O365 Kaizala Administrators,W62002,Delegate,Can manage settings for Microsoft Kaizala,
Desktop Services,KCUS,WinNT://KCUS/Desktop Services,E18722,Owner,For general team rights,
Desktop Services,KCUS,WinNT://KCUS/Desktop Services,W66442,Delegate,For general team rights,
Intunes_Access_Readonly,KCUS,WinNT://KCUS/Intunes_Access_Readonly,B70279,Authorizer,Members of this group has Microsoft intunes portal access,
Intunes_Access_Readonly,KCUS,WinNT://KCUS/Intunes_Access_Readonly,E18722,Owner,Members of this group has Microsoft intunes portal access,
Intunes_Access_Readonly,KCUS,WinNT://KCUS/Intunes_Access_Readonly,Q04499,Authorizer,Members of this group has Microsoft intunes portal access,
Intunes_Access_Readonly,KCUS,WinNT://KCUS/Intunes_Access_Readonly,W48997,Authorizer,Members of this group has Microsoft intunes portal access,
Intunes_Access_Readonly,KCUS,WinNT://KCUS/Intunes_Access_Readonly,W66442,Delegate,Members of this group has Microsoft intunes portal access,
Hybrid Search Database Owners,KCUS,WinNT://KCUS/Hybrid Search Database Owners,E18722,Owner,Hybrid Search Database Owners,
Hybrid Search Database Owners,KCUS,WinNT://KCUS/Hybrid Search Database Owners,Q08911,Authorizer,Hybrid Search Database Owners,
Hybrid Search Database Owners,KCUS,WinNT://KCUS/Hybrid Search Database Owners,W49040,Delegate,Hybrid Search Database Owners,
KCFILES_WksAdmin,KCUS,WinNT://KCUS/KCFILES_WksAdmin,E18722,Owner,Newtown Square-Site Computer Account Administrators,
KCFILES_WksAdmin,KCUS,WinNT://KCUS/KCFILES_WksAdmin,Q06085,Authorizer,Newtown Square-Site Computer Account Administrators,
KCFILES_WksAdmin,KCUS,WinNT://KCUS/KCFILES_WksAdmin,Q08911,Authorizer,Newtown Square-Site Computer Account Administrators,
KCFILES_WksAdmin,KCUS,WinNT://KCUS/KCFILES_WksAdmin,W48997,Delegate,Newtown Square-Site Computer Account Administrators,
HostApp-3PL-CitrixCentral,KCUS,WinNT://KCUS/HostApp-3PL-CitrixCentral,E18722,Owner,Citrix Appliction Security group for hosting Citrix Central SharePoint site,
HostApp-3PL-CitrixCentral,KCUS,WinNT://KCUS/HostApp-3PL-CitrixCentral,Q07708,Authorizer,Citrix Appliction Security group for hosting Citrix Central SharePoint site,
HostApp-3PL-CitrixCentral,KCUS,WinNT://KCUS/HostApp-3PL-CitrixCentral,Q13357,Authorizer,Citrix Appliction Security group for hosting Citrix Central SharePoint site,
HostApp-3PL-CitrixCentral,KCUS,WinNT://KCUS/HostApp-3PL-CitrixCentral,Q14616,Authorizer,Citrix Appliction Security group for hosting Citrix Central SharePoint site,
HostApp-3PL-CitrixCentral,KCUS,WinNT://KCUS/HostApp-3PL-CitrixCentral,Q14718,Authorizer,Citrix Appliction Security group for hosting Citrix Central SharePoint site,
HostApp-3PL-CitrixCentral,KCUS,WinNT://KCUS/HostApp-3PL-CitrixCentral,Q15926,Authorizer,Citrix Appliction Security group for hosting Citrix Central SharePoint site,
HostApp-3PL-CitrixCentral,KCUS,WinNT://KCUS/HostApp-3PL-CitrixCentral,W49040,Delegate,Citrix Appliction Security group for hosting Citrix Central SharePoint site,
HostApp-RPWI-Mobile,KCUS,WinNT://KCUS/HostApp-RPWI-Mobile,E18722,Owner,Citrix application security group for hosting Red Prairie Web Interface for Mobile,
HostApp-RPWI-Mobile,KCUS,WinNT://KCUS/HostApp-RPWI-Mobile,Q07708,Authorizer,Citrix application security group for hosting Red Prairie Web Interface for Mobile,
HostApp-RPWI-Mobile,KCUS,WinNT://KCUS/HostApp-RPWI-Mobile,Q14616,Authorizer,Citrix application security group for hosting Red Prairie Web Interface for Mobile,
HostApp-RPWI-Mobile,KCUS,WinNT://KCUS/HostApp-RPWI-Mobile,W49040,Delegate,Citrix application security group for hosting Red Prairie Web Interface for Mobile,
SMSPkg ED,KCUS,WinNT://KCUS/SMSPkg ED,E18722,Owner,SMS Pkg Chg for ED team - Neenah,
SMSPkg ED,KCUS,WinNT://KCUS/SMSPkg ED,Q06085,Authorizer,SMS Pkg Chg for ED team - Neenah,
SMSPkg ED,KCUS,WinNT://KCUS/SMSPkg ED,Q08911,Authorizer,SMS Pkg Chg for ED team - Neenah,
SMSPkg ED,KCUS,WinNT://KCUS/SMSPkg ED,W66442,Delegate,SMS Pkg Chg for ED team - Neenah,
O365-Teams-Communication-Admin,KCUS,WinNT://KCUS/O365-Teams-Communication-Admin,B80845,Delegate,Members of this group are assigned the role of O365 Teams Communication Admins,
O365-Teams-Communication-Admin,KCUS,WinNT://KCUS/O365-Teams-Communication-Admin,B91148,Authorizer,Members of this group are assigned the role of O365 Teams Communication Admins,
O365-Teams-Communication-Admin,KCUS,WinNT://KCUS/O365-Teams-Communication-Admin,E18722,Owner,Members of this group are assigned the role of O365 Teams Communication Admins,
HostApp-VirtualDesktop,KCUS,WinNT://KCUS/HostApp-VirtualDesktop,E18722,Owner,Security Group to provide access to HSD Virtual Desktop in Citrix,
HostApp-VirtualDesktop,KCUS,WinNT://KCUS/HostApp-VirtualDesktop,W49040,Delegate,Security Group to provide access to HSD Virtual Desktop in Citrix,
HostAppMenu-VirtualDesktop,KCUS,WinNT://KCUS/HostAppMenu-VirtualDesktop,E18722,Owner,Security Group to provide access to users for Citrix HSD Virtual Desktop,
HostAppMenu-VirtualDesktop,KCUS,WinNT://KCUS/HostAppMenu-VirtualDesktop,W49040,Delegate,Security Group to provide access to users for Citrix HSD Virtual Desktop,
HostAppMenu-KC-KCIsrael,KCUS,WinNT://KCUS/HostAppMenu-KC-KCIsrael,E18722,Owner,Hosted Group for KC Israel,
HostAppMenu-KC-KCIsrael,KCUS,WinNT://KCUS/HostAppMenu-KC-KCIsrael,Q07708,Authorizer,Hosted Group for KC Israel,
HostAppMenu-KC-KCIsrael,KCUS,WinNT://KCUS/HostAppMenu-KC-KCIsrael,Q14616,Authorizer,Hosted Group for KC Israel,
HostAppMenu-KC-KCIsrael,KCUS,WinNT://KCUS/HostAppMenu-KC-KCIsrael,W49040,Delegate,Hosted Group for KC Israel,
HostAppMenu-KC-KCUsers,KCUS,WinNT://KCUS/HostAppMenu-KC-KCUsers,E18722,Owner,Hosted Group for KC,
HostAppMenu-KC-KCUsers,KCUS,WinNT://KCUS/HostAppMenu-KC-KCUsers,Q07708,Authorizer,Hosted Group for KC,
HostAppMenu-KC-KCUsers,KCUS,WinNT://KCUS/HostAppMenu-KC-KCUsers,Q13357,Authorizer,Hosted Group for KC,
HostAppMenu-KC-KCUsers,KCUS,WinNT://KCUS/HostAppMenu-KC-KCUsers,Q14616,Authorizer,Hosted Group for KC,
HostAppMenu-KC-KCUsers,KCUS,WinNT://KCUS/HostAppMenu-KC-KCUsers,Q14718,Authorizer,Hosted Group for KC,
HostAppMenu-KC-KCUsers,KCUS,WinNT://KCUS/HostAppMenu-KC-KCUsers,Q15926,Authorizer,Hosted Group for KC,
HostAppMenu-KC-KCUsers,KCUS,WinNT://KCUS/HostAppMenu-KC-KCUsers,W49040,Delegate,Hosted Group for KC,
InTune_Allowed_QA,KCUS,WinNT://KCUS/InTune_Allowed_QA,E18722,Owner,Allowed group for InTune enrollment in QA environment,
InTune_Allowed_QA,KCUS,WinNT://KCUS/InTune_Allowed_QA,W66442,Delegate,Allowed group for InTune enrollment in QA environment,
CSViewOnlyAdministrator,KCUS,WinNT://KCUS/CSViewOnlyAdministrator,E18722,Owner,Members of this group can view the Lync Server 2010 deployment  including server information  in order to monitor deployment health.,
CSViewOnlyAdministrator,KCUS,WinNT://KCUS/CSViewOnlyAdministrator,W62002,Delegate,Members of this group can view the Lync Server 2010 deployment  including server information  in order to monitor deployment health.,
Outlook password fix,KCUS,WinNT://KCUS/Outlook password fix,E18722,Owner,Fix for the outlook need password issue,
Outlook password fix,KCUS,WinNT://KCUS/Outlook password fix,Q04499,Authorizer,Fix for the outlook need password issue,
Outlook password fix,KCUS,WinNT://KCUS/Outlook password fix,Q06085,Authorizer,Fix for the outlook need password issue,
Outlook password fix,KCUS,WinNT://KCUS/Outlook password fix,Q06342,Authorizer,Fix for the outlook need password issue,
Outlook password fix,KCUS,WinNT://KCUS/Outlook password fix,Q11326,Authorizer,Fix for the outlook need password issue,
Outlook password fix,KCUS,WinNT://KCUS/Outlook password fix,Q11327,Authorizer,Fix for the outlook need password issue,
Outlook password fix,KCUS,WinNT://KCUS/Outlook password fix,Q12450,Authorizer,Fix for the outlook need password issue,
Outlook password fix,KCUS,WinNT://KCUS/Outlook password fix,W48997,Delegate,Fix for the outlook need password issue,
WSUS_Reporter,KCUS,WinNT://KCUS/WSUS_Reporter,E18722,Owner,Allows users to access WSUS reports only,
WSUS_Reporter,KCUS,WinNT://KCUS/WSUS_Reporter,W66442,Delegate,Allows users to access WSUS reports only,
GRP06249_C,KCUS,WinNT://KCUS/GRP06249_C,L20681,Authorizer,Mailbox: _Support  Printer NAs,
GRP06249_C,KCUS,WinNT://KCUS/GRP06249_C,W49040,Delegate,Mailbox: _Support  Printer NAs,
GRP06249_C,KCUS,WinNT://KCUS/GRP06249_C,E18722,Owner,Mailbox: _Support  Printer NAs,
IC-USBScanClone,KCUS,WinNT://KCUS/IC-USBScanClone,E18722,Owner,USB Scan users,
IC-USBScanClone,KCUS,WinNT://KCUS/IC-USBScanClone,Q13357,Authorizer,USB Scan users,
IC-USBScanClone,KCUS,WinNT://KCUS/IC-USBScanClone,Q14718,Authorizer,USB Scan users,
IC-USBScanClone,KCUS,WinNT://KCUS/IC-USBScanClone,Q15926,Authorizer,USB Scan users,
IC-USBScanClone,KCUS,WinNT://KCUS/IC-USBScanClone,W48997,Delegate,USB Scan users,
HP_SecurityManager_Admin,KCUS,WinNT://KCUS/HP_SecurityManager_Admin,E18722,Owner,HPSM and HPJWA Admin Access,
HP_SecurityManager_Admin,KCUS,WinNT://KCUS/HP_SecurityManager_Admin,w49040,Delegate,HPSM and HPJWA Admin Access,
TSUsers_USTCTD02,KCUS,WinNT://KCUS/TSUsers_USTCTD02,E18722,Owner,Terminal Server Access to USTCTD02,
TSUsers_USTCTD02,KCUS,WinNT://KCUS/TSUsers_USTCTD02,W66442,Delegate,Terminal Server Access to USTCTD02,
SCCMApp-Adobe Lightroom,KCUS,WinNT://KCUS/SCCMApp-Adobe Lightroom,E18722,Owner,Package for Adobe Lightroom,
SCCMApp-Adobe Lightroom,KCUS,WinNT://KCUS/SCCMApp-Adobe Lightroom,W48997,Delegate,Package for Adobe Lightroom,
SCCMApp-Adobe Captivate,KCUS,WinNT://KCUS/SCCMApp-Adobe Captivate,E18722,Owner,Package for Adobe Captivate,
SCCMApp-Adobe Captivate,KCUS,WinNT://KCUS/SCCMApp-Adobe Captivate,W48997,Delegate,Package for Adobe Captivate,
USDQCriticalBackups_C,KCUS,WinNT://KCUS/USDQCriticalBackups_C,E18722,Owner,\\usdqa003\install$\Critical Backups,
USDQCriticalBackups_C,KCUS,WinNT://KCUS/USDQCriticalBackups_C,W66442,Delegate,\\usdqa003\install$\Critical Backups,
KCFILES_CorpMISCompSvcsEDMgmt_C,KCUS,WinNT://KCUS/KCFILES_CorpMISCompSvcsEDMgmt_C,E18722,Owner,\\KCFiles\Share\Corporate\MIS\Computer Services\Enterprise Desktop Mgmt,
KCFILES_CorpMISCompSvcsEDMgmt_C,KCUS,WinNT://KCUS/KCFILES_CorpMISCompSvcsEDMgmt_C,W66442,Delegate,\\KCFiles\Share\Corporate\MIS\Computer Services\Enterprise Desktop Mgmt,
KCFILES_CorpMISCompSvcsEDMgmt_R,KCUS,WinNT://KCUS/KCFILES_CorpMISCompSvcsEDMgmt_R,W66442,Delegate,\\KCFiles\Share\Corporate\MIS\Computer Services\Enterprise Desktop Mgmt,
KCFILES_CorpMISCompSvcsEDMgmt_R,KCUS,WinNT://KCUS/KCFILES_CorpMISCompSvcsEDMgmt_R,E18722,Owner,\\KCFiles\Share\Corporate\MIS\Computer Services\Enterprise Desktop Mgmt,
DL014405,KCUS,WinNT://KCUS/DL014405,E18722,Owner,+GLOBAL_ITS  TCS KC RC1,
DL014405,KCUS,WinNT://KCUS/DL014405,Q06085,Delegate,+GLOBAL_ITS  TCS KC RC1,
DL014405,KCUS,WinNT://KCUS/DL014405,W48997,Authorizer,+GLOBAL_ITS  TCS KC RC1,
Desktop Services L2,KCUS,WinNT://KCUS/Desktop Services L2,E18722,Owner,Group of all Second Level team members,
Desktop Services L2,KCUS,WinNT://KCUS/Desktop Services L2,Q06085,Delegate,Group of all Second Level team members,
HostApp-3PL-PManager,KCUS,WinNT://KCUS/HostApp-3PL-PManager,E18722,Owner,Citrix Application Security group for hosting Password Manager for 3PL,
HostApp-3PL-PManager,KCUS,WinNT://KCUS/HostApp-3PL-PManager,Q07708,Authorizer,Citrix Application Security group for hosting Password Manager for 3PL,
HostApp-3PL-PManager,KCUS,WinNT://KCUS/HostApp-3PL-PManager,Q13357,Authorizer,Citrix Application Security group for hosting Password Manager for 3PL,
HostApp-3PL-PManager,KCUS,WinNT://KCUS/HostApp-3PL-PManager,Q14616,Authorizer,Citrix Application Security group for hosting Password Manager for 3PL,
HostApp-3PL-PManager,KCUS,WinNT://KCUS/HostApp-3PL-PManager,Q14718,Authorizer,Citrix Application Security group for hosting Password Manager for 3PL,
HostApp-3PL-PManager,KCUS,WinNT://KCUS/HostApp-3PL-PManager,Q15926,Authorizer,Citrix Application Security group for hosting Password Manager for 3PL,
HostApp-3PL-PManager,KCUS,WinNT://KCUS/HostApp-3PL-PManager,W49040,Delegate,Citrix Application Security group for hosting Password Manager for 3PL,
PBI_WS_AP_MessagingPlatform_ADHOC_CONTRIB_P,KCUS,WinNT://KCUS/PBI_WS_AP_MessagingPlatform_ADHOC_CONTRIB_P,E18722,Owner,Power BI CONTRIBUTOR Role for AP MessagingPlatform  ADHOC Workspace,
PBI_WS_AP_MessagingPlatform_ADHOC_CONTRIB_P,KCUS,WinNT://KCUS/PBI_WS_AP_MessagingPlatform_ADHOC_CONTRIB_P,W62002,Delegate,Power BI CONTRIBUTOR Role for AP MessagingPlatform  ADHOC Workspace,
GRP10344_C,KCUS,WinNT://KCUS/GRP10344_C,B62398,Authorizer,Mailbox: _Desktop Services  Mac Support,
GRP10344_C,KCUS,WinNT://KCUS/GRP10344_C,E18722,Owner,Mailbox: _Desktop Services  Mac Support,
GRP10344_C,KCUS,WinNT://KCUS/GRP10344_C,E32872,Delegate,Mailbox: _Desktop Services  Mac Support,
O365 Yammer Administrator,KCUS,WinNT://KCUS/O365 Yammer Administrator,E18722,Owner,This group would be used for managing the Yammer Administration.,
O365 Yammer Administrator,KCUS,WinNT://KCUS/O365 Yammer Administrator,U02802,Delegate,This group would be used for managing the Yammer Administration.,
EuropeAdmin,KCUS,WinNT://KCUS/EuropeAdmin,E18722,Owner,Nested in all Europe WKS groups to provide Admin access to the EU desktop team,
EuropeAdmin,KCUS,WinNT://KCUS/EuropeAdmin,W48997,Delegate,Nested in all Europe WKS groups to provide Admin access to the EU desktop team,
PSynchHelpAccts,KCUS,WinNT://KCUS/PSynchHelpAccts,E18722,Owner,,
PSynchHelpAccts,KCUS,WinNT://KCUS/PSynchHelpAccts,W66442,Delegate,,
PIPE support group - AUYDPIPEAdmin,KCUS,WinNT://KCUS/PIPE support group - AUYDPIPEAdmin,E18722,Owner,PIPE support group - AUYDPIPEAdmin,
PIPE support group - AUYDPIPEAdmin,KCUS,WinNT://KCUS/PIPE support group - AUYDPIPEAdmin,Q06085,Authorizer,PIPE support group - AUYDPIPEAdmin,
PIPE support group - AUYDPIPEAdmin,KCUS,WinNT://KCUS/PIPE support group - AUYDPIPEAdmin,Q08911,Authorizer,PIPE support group - AUYDPIPEAdmin,
PIPE support group - AUYDPIPEAdmin,KCUS,WinNT://KCUS/PIPE support group - AUYDPIPEAdmin,W66442,Delegate,PIPE support group - AUYDPIPEAdmin,
Windows Update Exception,KCUS,WinNT://KCUS/Windows Update Exception,E18722,Owner,Used to disable computer getting Windows Update,
Windows Update Exception,KCUS,WinNT://KCUS/Windows Update Exception,Q06085,Authorizer,Used to disable computer getting Windows Update,
Windows Update Exception,KCUS,WinNT://KCUS/Windows Update Exception,W48997,Delegate,Used to disable computer getting Windows Update,
HostApp-Outlook-ML,KCUS,WinNT://KCUS/HostApp-Outlook-ML,E18722,Owner,Citrix application security group for Multi-lingual Outlook,
HostApp-Outlook-ML,KCUS,WinNT://KCUS/HostApp-Outlook-ML,Q07708,Authorizer,Citrix application security group for Multi-lingual Outlook,
HostApp-Outlook-ML,KCUS,WinNT://KCUS/HostApp-Outlook-ML,Q13357,Authorizer,Citrix application security group for Multi-lingual Outlook,
HostApp-Outlook-ML,KCUS,WinNT://KCUS/HostApp-Outlook-ML,Q14616,Authorizer,Citrix application security group for Multi-lingual Outlook,
HostApp-Outlook-ML,KCUS,WinNT://KCUS/HostApp-Outlook-ML,Q14718,Authorizer,Citrix application security group for Multi-lingual Outlook,
HostApp-Outlook-ML,KCUS,WinNT://KCUS/HostApp-Outlook-ML,Q15926,Authorizer,Citrix application security group for Multi-lingual Outlook,
HostApp-Outlook-ML,KCUS,WinNT://KCUS/HostApp-Outlook-ML,W49040,Delegate,Citrix application security group for Multi-lingual Outlook,
FC-SAPUsers,KCUS,WinNT://KCUS/FC-SAPUsers,E18722,Owner,CTS SAP VDI Users,
FC-SAPUsers,KCUS,WinNT://KCUS/FC-SAPUsers,Q07708,Authorizer,CTS SAP VDI Users,
FC-SAPUsers,KCUS,WinNT://KCUS/FC-SAPUsers,Q13357,Authorizer,CTS SAP VDI Users,
FC-SAPUsers,KCUS,WinNT://KCUS/FC-SAPUsers,Q14616,Authorizer,CTS SAP VDI Users,
FC-SAPUsers,KCUS,WinNT://KCUS/FC-SAPUsers,Q14718,Authorizer,CTS SAP VDI Users,
FC-SAPUsers,KCUS,WinNT://KCUS/FC-SAPUsers,Q15926,Authorizer,CTS SAP VDI Users,
FC-SAPUsers,KCUS,WinNT://KCUS/FC-SAPUsers,W49040,Delegate,CTS SAP VDI Users,
GRP08676_C,KCUS,WinNT://KCUS/GRP08676_C,E18722,Owner,_Support  Chat Servers,
GRP08676_C,KCUS,WinNT://KCUS/GRP08676_C,Q08911,Authorizer,_Support  Chat Servers,
GRP08676_C,KCUS,WinNT://KCUS/GRP08676_C,W49040,Delegate,_Support  Chat Servers,
IACHLowSvcTwrMessaging_C,KCUS,WinNT://KCUS/IACHLowSvcTwrMessaging_C,E18722,Owner,\\iachfn01\KC Share\Low Sensitive\Service Towers\Messaging,
IACHLowSvcTwrMessaging_C,KCUS,WinNT://KCUS/IACHLowSvcTwrMessaging_C,W62002,Delegate,\\iachfn01\KC Share\Low Sensitive\Service Towers\Messaging,
IACHLowSvcTwrDesktop_R,KCUS,WinNT://KCUS/IACHLowSvcTwrDesktop_R,B92167,Delegate,\\iachfn01\KC Share\Low Sensitive\Service Towers\Desktop,
IACHLowSvcTwrDesktop_R,KCUS,WinNT://KCUS/IACHLowSvcTwrDesktop_R,E18722,Owner,\\iachfn01\KC Share\Low Sensitive\Service Towers\Desktop,
IACHLowSvcTwrDesktop_R,KCUS,WinNT://KCUS/IACHLowSvcTwrDesktop_R,Q06085,Authorizer,\\iachfn01\KC Share\Low Sensitive\Service Towers\Desktop,
IACHLowSvcTwrDesktop_R,KCUS,WinNT://KCUS/IACHLowSvcTwrDesktop_R,Q08911,Authorizer,\\iachfn01\KC Share\Low Sensitive\Service Towers\Desktop,
IACHLowSvcTwrMessaging_R,KCUS,WinNT://KCUS/IACHLowSvcTwrMessaging_R,E18722,Owner,\\iachfn01\KC Share\Low Sensitive\Service Towers\Messaging,
IACHLowSvcTwrMessaging_R,KCUS,WinNT://KCUS/IACHLowSvcTwrMessaging_R,W62002,Delegate,\\iachfn01\KC Share\Low Sensitive\Service Towers\Messaging,
IT_User_Information_QA_Users,KCUS,WinNT://KCUS/IT_User_Information_QA_Users,B00932,Delegate,This group will control access to the IT User Information Web Application - QA,
IT_User_Information_QA_Users,KCUS,WinNT://KCUS/IT_User_Information_QA_Users,E18722,Owner,This group will control access to the IT User Information Web Application - QA,
HostAppMenu-INTAP23,KCUS,WinNT://KCUS/HostAppMenu-INTAP23,E18722,Owner,Citrix Application Security group for INTAP WMS Servers,
HostAppMenu-INTAP23,KCUS,WinNT://KCUS/HostAppMenu-INTAP23,Q07708,Authorizer,Citrix Application Security group for INTAP WMS Servers,
HostAppMenu-INTAP23,KCUS,WinNT://KCUS/HostAppMenu-INTAP23,Q13357,Authorizer,Citrix Application Security group for INTAP WMS Servers,
HostAppMenu-INTAP23,KCUS,WinNT://KCUS/HostAppMenu-INTAP23,Q14616,Authorizer,Citrix Application Security group for INTAP WMS Servers,
HostAppMenu-INTAP23,KCUS,WinNT://KCUS/HostAppMenu-INTAP23,Q14718,Authorizer,Citrix Application Security group for INTAP WMS Servers,
HostAppMenu-INTAP23,KCUS,WinNT://KCUS/HostAppMenu-INTAP23,Q15926,Authorizer,Citrix Application Security group for INTAP WMS Servers,
HostAppMenu-INTAP23,KCUS,WinNT://KCUS/HostAppMenu-INTAP23,W49040,Delegate,Citrix Application Security group for INTAP WMS Servers,
WTSWebDevelopment,KCUS,WinNT://KCUS/WTSWebDevelopment,E18722,Owner,Windows Terminal Server Web Development - Access applies to \\USTCAW77\Inetpub\wts.dev.kcc.com,
WTSWebDevelopment,KCUS,WinNT://KCUS/WTSWebDevelopment,W66442,Delegate,Windows Terminal Server Web Development - Access applies to \\USTCAW77\Inetpub\wts.dev.kcc.com,
GD2000_Admin,KCUS,WinNT://KCUS/GD2000_Admin,E18722,Owner,Global Desktop 2000 Administrators,
GD2000_Admin,KCUS,WinNT://KCUS/GD2000_Admin,Q06085,Authorizer,Global Desktop 2000 Administrators,
GD2000_Admin,KCUS,WinNT://KCUS/GD2000_Admin,W48997,Delegate,Global Desktop 2000 Administrators,
DesktopServices_TWS_C,KCUS,WinNT://KCUS/DesktopServices_TWS_C,E18722,Owner,Controls access to folders on TWS servers for Desktop Services team,
DesktopServices_TWS_C,KCUS,WinNT://KCUS/DesktopServices_TWS_C,W66442,Delegate,Controls access to folders on TWS servers for Desktop Services team,
KCFILES_CorpMISCompSvcsEDMgmtProjects_R,KCUS,WinNT://KCUS/KCFILES_CorpMISCompSvcsEDMgmtProjects_R,E18722,Owner,\\KCFILES\Share\Corporate\Computer Services\ENTERPRISE DESKTOP MANAGEMENT\PROJECTS,
KCFILES_CorpMISCompSvcsEDMgmtProjects_R,KCUS,WinNT://KCUS/KCFILES_CorpMISCompSvcsEDMgmtProjects_R,W66442,Delegate,\\KCFILES\Share\Corporate\Computer Services\ENTERPRISE DESKTOP MANAGEMENT\PROJECTS,
KCFILES_CorpMISCompSvcsEDMgmtProjects_C,KCUS,WinNT://KCUS/KCFILES_CorpMISCompSvcsEDMgmtProjects_C,E18722,Owner,\\KCFILES\Share\Corporate\Computer Services\ENTERPRISE DESKTOP MANAGEMENT\PROJECTS,
KCFILES_CorpMISCompSvcsEDMgmtProjects_C,KCUS,WinNT://KCUS/KCFILES_CorpMISCompSvcsEDMgmtProjects_C,W66442,Delegate,\\KCFILES\Share\Corporate\Computer Services\ENTERPRISE DESKTOP MANAGEMENT\PROJECTS,
HostApp-IBControlSite,KCUS,WinNT://KCUS/HostApp-IBControlSite,W49040,Delegate,Citrix Application Security group for Inventory Batch control site,
HostApp-IBControlSite,KCUS,WinNT://KCUS/HostApp-IBControlSite,E18722,Owner,Citrix Application Security group for Inventory Batch control site,
HostApp-IBControlSite,KCUS,WinNT://KCUS/HostApp-IBControlSite,Q07708,Authorizer,Citrix Application Security group for Inventory Batch control site,
HostApp-IBControlSite,KCUS,WinNT://KCUS/HostApp-IBControlSite,Q13357,Authorizer,Citrix Application Security group for Inventory Batch control site,
HostApp-IBControlSite,KCUS,WinNT://KCUS/HostApp-IBControlSite,Q14616,Authorizer,Citrix Application Security group for Inventory Batch control site,
HostApp-IBControlSite,KCUS,WinNT://KCUS/HostApp-IBControlSite,Q14718,Authorizer,Citrix Application Security group for Inventory Batch control site,
HostApp-IBControlSite,KCUS,WinNT://KCUS/HostApp-IBControlSite,Q15926,Authorizer,Citrix Application Security group for Inventory Batch control site,
Admin Studio Production Catalog Users,KCUS,WinNT://KCUS/Admin Studio Production Catalog Users,E18722,Owner,Users who will have access to the Admin Studio production catalog,
Admin Studio Production Catalog Users,KCUS,WinNT://KCUS/Admin Studio Production Catalog Users,W48997,Delegate,Users who will have access to the Admin Studio production catalog,
Admin Studio Development Catalog Public,KCUS,WinNT://KCUS/Admin Studio Development Catalog Public,E18722,Owner,Controls read-only access to the Admin Studio Development Catalog,
Admin Studio Development Catalog Public,KCUS,WinNT://KCUS/Admin Studio Development Catalog Public,W48997,Delegate,Controls read-only access to the Admin Studio Development Catalog,
IACHMedSvcTwrDesktop_C,KCUS,WinNT://KCUS/IACHMedSvcTwrDesktop_C,B92167,Delegate,\\iachfn01\KC Share\Medium Sensitive\Service Towers\Desktop,
IACHMedSvcTwrDesktop_C,KCUS,WinNT://KCUS/IACHMedSvcTwrDesktop_C,E18722,Owner,\\iachfn01\KC Share\Medium Sensitive\Service Towers\Desktop,
IACHMedSvcTwrDesktop_C,KCUS,WinNT://KCUS/IACHMedSvcTwrDesktop_C,Q06085,Authorizer,\\iachfn01\KC Share\Medium Sensitive\Service Towers\Desktop,
IACHMedSvcTwrDesktop_C,KCUS,WinNT://KCUS/IACHMedSvcTwrDesktop_C,Q08911,Authorizer,\\iachfn01\KC Share\Medium Sensitive\Service Towers\Desktop,
Admin SharePoint Central Administration - Dev-QA,KCUS,WinNT://KCUS/Admin SharePoint Central Administration - Dev-QA,E18722,Owner,Dev and QA SharePoint Application Accounts,
Admin SharePoint Central Administration - Dev-QA,KCUS,WinNT://KCUS/Admin SharePoint Central Administration - Dev-QA,W49040,Delegate,Dev and QA SharePoint Application Accounts,
Admin Studio Production Catalog Public,KCUS,WinNT://KCUS/Admin Studio Production Catalog Public,E18722,Owner,Controls who has read-only access to the Admin Studio Production Catalog,
Admin Studio Production Catalog Public,KCUS,WinNT://KCUS/Admin Studio Production Catalog Public,W48997,Delegate,Controls who has read-only access to the Admin Studio Production Catalog,
DL000273,KCUS,WinNT://KCUS/DL000273,E18722,Owner,+NA_EDMT  QAA Tech Tip,
DL000273,KCUS,WinNT://KCUS/DL000273,Q06085,Authorizer,+NA_EDMT  QAA Tech Tip,
DL000273,KCUS,WinNT://KCUS/DL000273,Q08911,Authorizer,+NA_EDMT  QAA Tech Tip,
DL000273,KCUS,WinNT://KCUS/DL000273,W62002,Delegate,+NA_EDMT  QAA Tech Tip,
USDQLegalThompsonD_C,KCUS,WinNT://KCUS/USDQLegalThompsonD_C,B04983,Authorizer,\\usdqfn01\share\legal\users\thompsond,
USDQLegalThompsonD_C,KCUS,WinNT://KCUS/USDQLegalThompsonD_C,E18722,Owner,\\usdqfn01\share\legal\users\thompsond,
USDQLegalThompsonD_C,KCUS,WinNT://KCUS/USDQLegalThompsonD_C,W66442,Delegate,\\usdqfn01\share\legal\users\thompsond,
USDQMIS_C,KCUS,WinNT://KCUS/USDQMIS_C,b00982,Authorizer,\\usdqfn01\share\mis,
USDQMIS_C,KCUS,WinNT://KCUS/USDQMIS_C,E18722,Owner,\\usdqfn01\share\mis,
USDQMIS_C,KCUS,WinNT://KCUS/USDQMIS_C,W66442,Delegate,\\usdqfn01\share\mis,
HostApp-StayInFront,KCUS,WinNT://KCUS/HostApp-StayInFront,e18722,Owner,Citrix Access for StayInFront application,
HostApp-StayInFront,KCUS,WinNT://KCUS/HostApp-StayInFront,w49040,Delegate,Citrix Access for StayInFront application,
Personal Mobile Device Agreement - Spain,KCUS,WinNT://KCUS/Personal Mobile Device Agreement - Spain,E18722,Owner,Grant access to the user to click I agree after they have turned in their Signed End User Agreement and enroll their personal mobile device.,
Personal Mobile Device Agreement - Spain,KCUS,WinNT://KCUS/Personal Mobile Device Agreement - Spain,E32872,Delegate,Grant access to the user to click I agree after they have turned in their Signed End User Agreement and enroll their personal mobile device.,
USTCGDStage_C,KCUS,WinNT://KCUS/USTCGDStage_C,E18722,Owner,\\USTCFN01\smspkgd$,
USTCGDStage_C,KCUS,WinNT://KCUS/USTCGDStage_C,W48997,Delegate,\\USTCFN01\smspkgd$,
HostApp-ERP-SPoint,KCUS,WinNT://KCUS/HostApp-ERP-SPoint,Q13357,Authorizer,Citrix Application Security for hosting ERP Educational Sharepoint site,
HostApp-ERP-SPoint,KCUS,WinNT://KCUS/HostApp-ERP-SPoint,Q14616,Authorizer,Citrix Application Security for hosting ERP Educational Sharepoint site,
HostApp-ERP-SPoint,KCUS,WinNT://KCUS/HostApp-ERP-SPoint,Q14718,Authorizer,Citrix Application Security for hosting ERP Educational Sharepoint site,
HostApp-ERP-SPoint,KCUS,WinNT://KCUS/HostApp-ERP-SPoint,Q15926,Authorizer,Citrix Application Security for hosting ERP Educational Sharepoint site,
HostApp-ERP-SPoint,KCUS,WinNT://KCUS/HostApp-ERP-SPoint,W49040,Delegate,Citrix Application Security for hosting ERP Educational Sharepoint site,
HostApp-ERP-SPoint,KCUS,WinNT://KCUS/HostApp-ERP-SPoint,E18722,Owner,Citrix Application Security for hosting ERP Educational Sharepoint site,
HostApp-ERP-SPoint,KCUS,WinNT://KCUS/HostApp-ERP-SPoint,Q07708,Authorizer,Citrix Application Security for hosting ERP Educational Sharepoint site,
HostApp-3PL-SAP,KCUS,WinNT://KCUS/HostApp-3PL-SAP,E18722,Owner,Citrix Application Security group for hosted SAP for 3PL,
HostApp-3PL-SAP,KCUS,WinNT://KCUS/HostApp-3PL-SAP,Q07708,Authorizer,Citrix Application Security group for hosted SAP for 3PL,
HostApp-3PL-SAP,KCUS,WinNT://KCUS/HostApp-3PL-SAP,Q13357,Authorizer,Citrix Application Security group for hosted SAP for 3PL,
HostApp-3PL-SAP,KCUS,WinNT://KCUS/HostApp-3PL-SAP,Q14616,Authorizer,Citrix Application Security group for hosted SAP for 3PL,
HostApp-3PL-SAP,KCUS,WinNT://KCUS/HostApp-3PL-SAP,Q14718,Authorizer,Citrix Application Security group for hosted SAP for 3PL,
HostApp-3PL-SAP,KCUS,WinNT://KCUS/HostApp-3PL-SAP,Q15926,Authorizer,Citrix Application Security group for hosted SAP for 3PL,
HostApp-3PL-SAP,KCUS,WinNT://KCUS/HostApp-3PL-SAP,W49040,Delegate,Citrix Application Security group for hosted SAP for 3PL,
HostApp-3PL-PManagerQRC,KCUS,WinNT://KCUS/HostApp-3PL-PManagerQRC,E18722,Owner,Citrix Application Security group for hosting Password Manager QRC,
HostApp-3PL-PManagerQRC,KCUS,WinNT://KCUS/HostApp-3PL-PManagerQRC,Q07708,Authorizer,Citrix Application Security group for hosting Password Manager QRC,
HostApp-3PL-PManagerQRC,KCUS,WinNT://KCUS/HostApp-3PL-PManagerQRC,Q13357,Authorizer,Citrix Application Security group for hosting Password Manager QRC,
HostApp-3PL-PManagerQRC,KCUS,WinNT://KCUS/HostApp-3PL-PManagerQRC,Q14616,Authorizer,Citrix Application Security group for hosting Password Manager QRC,
HostApp-3PL-PManagerQRC,KCUS,WinNT://KCUS/HostApp-3PL-PManagerQRC,Q14718,Authorizer,Citrix Application Security group for hosting Password Manager QRC,
HostApp-3PL-PManagerQRC,KCUS,WinNT://KCUS/HostApp-3PL-PManagerQRC,Q15926,Authorizer,Citrix Application Security group for hosting Password Manager QRC,
HostApp-3PL-PManagerQRC,KCUS,WinNT://KCUS/HostApp-3PL-PManagerQRC,W49040,Delegate,Citrix Application Security group for hosting Password Manager QRC,
DL000394,KCUS,WinNT://KCUS/DL000394,E18722,Owner,+NA_EDMT  QAA Remote Coordinators,
DL000394,KCUS,WinNT://KCUS/DL000394,Q06085,Authorizer,+NA_EDMT  QAA Remote Coordinators,
DL000394,KCUS,WinNT://KCUS/DL000394,Q08911,Authorizer,+NA_EDMT  QAA Remote Coordinators,
DL000394,KCUS,WinNT://KCUS/DL000394,W62002,Delegate,+NA_EDMT  QAA Remote Coordinators,
DL000175,KCUS,WinNT://KCUS/DL000175,E18722,Owner,+NA_ITS  Messaging Alert,
DL000175,KCUS,WinNT://KCUS/DL000175,W62002,Delegate,+NA_ITS  Messaging Alert,
Dev and QA Collection,KCUS,WinNT://KCUS/Dev and QA Collection,E18722,Owner,To add dev and QA machines to SCCM Collection,
Dev and QA Collection,KCUS,WinNT://KCUS/Dev and QA Collection,q04499,Authorizer,To add dev and QA machines to SCCM Collection,
Dev and QA Collection,KCUS,WinNT://KCUS/Dev and QA Collection,W48997,Delegate,To add dev and QA machines to SCCM Collection,
HostApp-DistCOE,KCUS,WinNT://KCUS/HostApp-DistCOE,E18722,Owner,Citrix Application Security group for Distribution COE – HC Inbound Reports,
HostApp-DistCOE,KCUS,WinNT://KCUS/HostApp-DistCOE,Q07708,Authorizer,Citrix Application Security group for Distribution COE – HC Inbound Reports,
HostApp-DistCOE,KCUS,WinNT://KCUS/HostApp-DistCOE,Q14616,Authorizer,Citrix Application Security group for Distribution COE – HC Inbound Reports,
HostApp-DistCOE,KCUS,WinNT://KCUS/HostApp-DistCOE,W49040,Delegate,Citrix Application Security group for Distribution COE – HC Inbound Reports,
GRP10303_C,KCUS,WinNT://KCUS/GRP10303_C,E18722,Owner,Mailbox: _Desktop  Security Management,
GRP10303_C,KCUS,WinNT://KCUS/GRP10303_C,Q06085,Delegate,Mailbox: _Desktop  Security Management,
MS SharePoint,KCUS,WinNT://KCUS/MS SharePoint,E18722,Owner,Authorized Users – MS SharePoint Designer 2007,
MS SharePoint,KCUS,WinNT://KCUS/MS SharePoint,Q13357,Authorizer,Authorized Users – MS SharePoint Designer 2007,
MS SharePoint,KCUS,WinNT://KCUS/MS SharePoint,Q14718,Authorizer,Authorized Users – MS SharePoint Designer 2007,
MS SharePoint,KCUS,WinNT://KCUS/MS SharePoint,Q15926,Authorizer,Authorized Users – MS SharePoint Designer 2007,
MS SharePoint,KCUS,WinNT://KCUS/MS SharePoint,W66442,Delegate,Authorized Users – MS SharePoint Designer 2007,
ThinkCell,KCUS,WinNT://KCUS/ThinkCell,W66442,Authorizer,This group is to target to ThinkCell application through SCCM,
ThinkCell,KCUS,WinNT://KCUS/ThinkCell,b45906,Authorizer,This group is to target to ThinkCell application through SCCM,
ThinkCell,KCUS,WinNT://KCUS/ThinkCell,E18722,Owner,This group is to target to ThinkCell application through SCCM,
ThinkCell,KCUS,WinNT://KCUS/ThinkCell,Q06371,Authorizer,This group is to target to ThinkCell application through SCCM,
ThinkCell,KCUS,WinNT://KCUS/ThinkCell,Q39116,Authorizer,This group is to target to ThinkCell application through SCCM,
ThinkCell,KCUS,WinNT://KCUS/ThinkCell,W48997,Delegate,This group is to target to ThinkCell application through SCCM,
Desktop Services Programmers,KCUS,WinNT://KCUS/Desktop Services Programmers,E18722,Owner,Provides access to programming resources for Desktop Services solutions.,
Desktop Services Programmers,KCUS,WinNT://KCUS/Desktop Services Programmers,Q73491,Delegate,Provides access to programming resources for Desktop Services solutions.,
Desktop Security Products Exclusion,KCUS,WinNT://KCUS/Desktop Security Products Exclusion,E18722,Owner,To exclude the security products deployment,
Desktop Security Products Exclusion,KCUS,WinNT://KCUS/Desktop Security Products Exclusion,Q13243,Authorizer,To exclude the security products deployment,
Desktop Security Products Exclusion,KCUS,WinNT://KCUS/Desktop Security Products Exclusion,W48997,Delegate,To exclude the security products deployment,
DL013126,KCUS,WinNT://KCUS/DL013126,B00372,Delegate,+GLOBAL  MSFTPremierComms,
DL013126,KCUS,WinNT://KCUS/DL013126,B46959,Owner,+GLOBAL  MSFTPremierComms,
DL013126,KCUS,WinNT://KCUS/DL013126,E18722,Authorizer,+GLOBAL  MSFTPremierComms,
HostApp-Dist-Spoint,KCUS,WinNT://KCUS/HostApp-Dist-Spoint,E18722,Owner,Citrix Application Security group for Distribution Sharepoint site,
HostApp-Dist-Spoint,KCUS,WinNT://KCUS/HostApp-Dist-Spoint,Q07708,Authorizer,Citrix Application Security group for Distribution Sharepoint site,
HostApp-Dist-Spoint,KCUS,WinNT://KCUS/HostApp-Dist-Spoint,Q13357,Authorizer,Citrix Application Security group for Distribution Sharepoint site,
HostApp-Dist-Spoint,KCUS,WinNT://KCUS/HostApp-Dist-Spoint,Q14616,Authorizer,Citrix Application Security group for Distribution Sharepoint site,
HostApp-Dist-Spoint,KCUS,WinNT://KCUS/HostApp-Dist-Spoint,Q14718,Authorizer,Citrix Application Security group for Distribution Sharepoint site,
HostApp-Dist-Spoint,KCUS,WinNT://KCUS/HostApp-Dist-Spoint,Q15926,Authorizer,Citrix Application Security group for Distribution Sharepoint site,
HostApp-Dist-Spoint,KCUS,WinNT://KCUS/HostApp-Dist-Spoint,W49040,Delegate,Citrix Application Security group for Distribution Sharepoint site,
CSServerAdministrator,KCUS,WinNT://KCUS/CSServerAdministrator,E18722,Owner,Members of this group can manage  monitor and troubleshoot Lync Server 2010 and services.,
CSServerAdministrator,KCUS,WinNT://KCUS/CSServerAdministrator,W62002,Delegate,Members of this group can manage  monitor and troubleshoot Lync Server 2010 and services.,
Admin P-synch,KCUS,WinNT://KCUS/Admin P-synch,E18722,Owner,P-synch Administrators,
Admin P-synch,KCUS,WinNT://KCUS/Admin P-synch,W48997,Delegate,P-synch Administrators,
USTCPipeStge_R,KCUS,WinNT://KCUS/USTCPipeStge_R,E18722,Owner,\\USTCFN01\SHARE\PIPESTGE,
USTCPipeStge_R,KCUS,WinNT://KCUS/USTCPipeStge_R,W48997,Delegate,\\USTCFN01\SHARE\PIPESTGE,
HostApp-BOBJWorkDay,KCUS,WinNT://KCUS/HostApp-BOBJWorkDay,E18722,Owner,Citrix Application Security group for hosting BOBJ tools for Worday,
HostApp-BOBJWorkDay,KCUS,WinNT://KCUS/HostApp-BOBJWorkDay,Q07708,Authorizer,Citrix Application Security group for hosting BOBJ tools for Worday,
HostApp-BOBJWorkDay,KCUS,WinNT://KCUS/HostApp-BOBJWorkDay,Q14616,Authorizer,Citrix Application Security group for hosting BOBJ tools for Worday,
HostApp-BOBJWorkDay,KCUS,WinNT://KCUS/HostApp-BOBJWorkDay,W49040,Delegate,Citrix Application Security group for hosting BOBJ tools for Worday,
Hostapp-SkypeforBusiness 2015,KCUS,WinNT://KCUS/Hostapp-SkypeforBusiness 2015,E18722,Owner,Citrix access group for MS SkypeforBusiness 2015,
Hostapp-SkypeforBusiness 2015,KCUS,WinNT://KCUS/Hostapp-SkypeforBusiness 2015,Q07708,Authorizer,Citrix access group for MS SkypeforBusiness 2015,
Hostapp-SkypeforBusiness 2015,KCUS,WinNT://KCUS/Hostapp-SkypeforBusiness 2015,Q13357,Authorizer,Citrix access group for MS SkypeforBusiness 2015,
Hostapp-SkypeforBusiness 2015,KCUS,WinNT://KCUS/Hostapp-SkypeforBusiness 2015,Q14616,Authorizer,Citrix access group for MS SkypeforBusiness 2015,
Hostapp-SkypeforBusiness 2015,KCUS,WinNT://KCUS/Hostapp-SkypeforBusiness 2015,Q14718,Authorizer,Citrix access group for MS SkypeforBusiness 2015,
Hostapp-SkypeforBusiness 2015,KCUS,WinNT://KCUS/Hostapp-SkypeforBusiness 2015,Q15926,Authorizer,Citrix access group for MS SkypeforBusiness 2015,
Hostapp-SkypeforBusiness 2015,KCUS,WinNT://KCUS/Hostapp-SkypeforBusiness 2015,W49040,Delegate,Citrix access group for MS SkypeforBusiness 2015,
DesktopADGroups,KCUS,WinNT://KCUS/DesktopADGroups,E18722,Owner,Gives Desktop Services team members rights to add machine accounts to specific AD groups,
DesktopADGroups,KCUS,WinNT://KCUS/DesktopADGroups,Q06085,Authorizer,Gives Desktop Services team members rights to add machine accounts to specific AD groups,
DesktopADGroups,KCUS,WinNT://KCUS/DesktopADGroups,Q08911,Authorizer,Gives Desktop Services team members rights to add machine accounts to specific AD groups,
DesktopADGroups,KCUS,WinNT://KCUS/DesktopADGroups,W66442,Delegate,Gives Desktop Services team members rights to add machine accounts to specific AD groups,
MachineAcctCreation,KCUS,WinNT://KCUS/MachineAcctCreation,E18722,Owner,IDs in this group are able to create Computer objects in the Workstations OU,
MachineAcctCreation,KCUS,WinNT://KCUS/MachineAcctCreation,W66442,Delegate,IDs in this group are able to create Computer objects in the Workstations OU,
VMWareDesktopsWksAdmin,KCUS,WinNT://KCUS/VMWareDesktopsWksAdmin,E18722,Owner,Citrix VMWare Desktops Computer Account Administrators,
VMWareDesktopsWksAdmin,KCUS,WinNT://KCUS/VMWareDesktopsWksAdmin,W49040,Delegate,Citrix VMWare Desktops Computer Account Administrators,
Policy Pilot Users,KCUS,WinNT://KCUS/Policy Pilot Users,W66442,Delegate,Used to apply user-side policy pilots,
Policy Pilot Users,KCUS,WinNT://KCUS/Policy Pilot Users,E18722,Owner,Used to apply user-side policy pilots,
Policy Pilot Users,KCUS,WinNT://KCUS/Policy Pilot Users,Q06085,Authorizer,Used to apply user-side policy pilots,
Policy Pilot Users,KCUS,WinNT://KCUS/Policy Pilot Users,Q08911,Authorizer,Used to apply user-side policy pilots,
SMTPAccessGroup,KCINET,WINNT://KCINET/SMTPACCESSGROUP,E18722,Owner,SMTPAccessGroup,
Deny-Both-Pipe2000 Core,KCUS,WinNT://KCUS/Deny-Both-Pipe2000 Core,E18722,Owner,PIPE OU Deny Policy Group,
Deny-Both-Pipe2000 Core,KCUS,WinNT://KCUS/Deny-Both-Pipe2000 Core,Q06085,Authorizer,PIPE OU Deny Policy Group,
Deny-Both-Pipe2000 Core,KCUS,WinNT://KCUS/Deny-Both-Pipe2000 Core,Q08911,Authorizer,PIPE OU Deny Policy Group,
Deny-Both-Pipe2000 Core,KCUS,WinNT://KCUS/Deny-Both-Pipe2000 Core,W48997,Delegate,PIPE OU Deny Policy Group,
EDMT AdminElevated,KCUS,WinNT://KCUS/EDMT AdminElevated,E18722,Owner,Group used by ED Team to elevate priviledges,
EDMT AdminElevated,KCUS,WINNT://KCUS/EDMT ADMINELEVATED,Q73491,Authorizer,Group used by ED Team to elevate priviledges,
EDMT AdminElevated,KCUS,WinNT://KCUS/EDMT AdminElevated,W48997,Delegate,Group used by ED Team to elevate priviledges,
USTCFN08_SystemDIR_C,KCUS,WinNT://KCUS/USTCFN08_SystemDIR_C,E18722,Owner,\\USTCFN08\system\public\edt,
USTCFN08_SystemDIR_C,KCUS,WinNT://KCUS/USTCFN08_SystemDIR_C,W48997,Delegate,\\USTCFN08\system\public\edt,
USDAP002,KCUS,WinNT://KCUS/USDAP002,E18722,Owner,Dallas Turtle Creek- Executive Color LaserJet 4600,
USDAP002,KCUS,WinNT://KCUS/USDAP002,W66442,Delegate,Dallas Turtle Creek- Executive Color LaserJet 4600,
Admin Citrix,KCINET,WINNT://KCINET/ADMIN CITRIX,E18722,Owner,Admin Citrix,
Admin Citrix,KCINET,WinNT://KCINET/Admin Citrix,Q08911,Authorizer,Admin Citrix,
Admin Citrix,KCINET,WinNT://KCINET/Admin Citrix,W49040,Delegate,Admin Citrix,
KCAPPS_CorpMISCSEDApps_C,KCUS,WinNT://KCUS/KCAPPS_CorpMISCSEDApps_C,E18722,Owner,\\KCApps\Share\Corporate\MIS\Computer Services\Enterprise Desktop Management\EDApps,
KCAPPS_CorpMISCSEDApps_C,KCUS,WinNT://KCUS/KCAPPS_CorpMISCSEDApps_C,W66442,Delegate,\\KCApps\Share\Corporate\MIS\Computer Services\Enterprise Desktop Management\EDApps,
Intune_Allowed_Prod,KCUS,WinNT://KCUS/Intune_Allowed_Prod,E18722,Owner,Allowed Intune MDM users,
Intune_Allowed_Prod,KCUS,WinNT://KCUS/Intune_Allowed_Prod,W66442,Delegate,Allowed Intune MDM users,
HostApp-Hyper-FA,KCUS,WinNT://KCUS/HostApp-Hyper-FA,E18722,Owner,Citrix Application Security group for hosting Hyperion NA,
HostApp-Hyper-FA,KCUS,WinNT://KCUS/HostApp-Hyper-FA,Q07708,Authorizer,Citrix Application Security group for hosting Hyperion NA,
HostApp-Hyper-FA,KCUS,WinNT://KCUS/HostApp-Hyper-FA,Q14616,Authorizer,Citrix Application Security group for hosting Hyperion NA,
HostApp-Hyper-FA,KCUS,WinNT://KCUS/HostApp-Hyper-FA,W49040,Delegate,Citrix Application Security group for hosting Hyperion NA,
Admin EDT,KCUS,WinNT://KCUS/Admin EDT,E18722,Owner,Enterprise Desktop admin's,
Admin EDT,KCUS,WinNT://KCUS/Admin EDT,W48997,Delegate,Enterprise Desktop admin's,
SharePoint Unattended Service Account,KCUS,WinNT://KCUS/SharePoint Unattended Service Account,E18722,Owner,Group that contains SharePoint Unattended Service accounts,
SharePoint Unattended Service Account,KCUS,WinNT://KCUS/SharePoint Unattended Service Account,W49040,Delegate,Group that contains SharePoint Unattended Service accounts,
HostServerAdmin-Navisworks,KCUS,WinNT://KCUS/HostServerAdmin-Navisworks,E18722,Owner,Admin Access group for Navisworks Citrix servers,
HostServerAdmin-Navisworks,KCUS,WinNT://KCUS/HostServerAdmin-Navisworks,Q07708,Authorizer,Admin Access group for Navisworks Citrix servers,
HostServerAdmin-Navisworks,KCUS,WinNT://KCUS/HostServerAdmin-Navisworks,Q13357,Authorizer,Admin Access group for Navisworks Citrix servers,
HostServerAdmin-Navisworks,KCUS,WinNT://KCUS/HostServerAdmin-Navisworks,Q14616,Authorizer,Admin Access group for Navisworks Citrix servers,
HostServerAdmin-Navisworks,KCUS,WinNT://KCUS/HostServerAdmin-Navisworks,Q14718,Authorizer,Admin Access group for Navisworks Citrix servers,
HostServerAdmin-Navisworks,KCUS,WinNT://KCUS/HostServerAdmin-Navisworks,Q15926,Authorizer,Admin Access group for Navisworks Citrix servers,
HostServerAdmin-Navisworks,KCUS,WinNT://KCUS/HostServerAdmin-Navisworks,W49040,Delegate,Admin Access group for Navisworks Citrix servers,
Hostapp-Lync,KCUS,WinNT://KCUS/Hostapp-Lync,E18722,Owner,Citrix access group for MS Lync,
Hostapp-Lync,KCUS,WinNT://KCUS/Hostapp-Lync,Q07708,Authorizer,Citrix access group for MS Lync,
Hostapp-Lync,KCUS,WinNT://KCUS/Hostapp-Lync,Q13357,Authorizer,Citrix access group for MS Lync,
Hostapp-Lync,KCUS,WinNT://KCUS/Hostapp-Lync,Q14616,Authorizer,Citrix access group for MS Lync,
Hostapp-Lync,KCUS,WinNT://KCUS/Hostapp-Lync,Q14718,Authorizer,Citrix access group for MS Lync,
Hostapp-Lync,KCUS,WinNT://KCUS/Hostapp-Lync,Q15926,Authorizer,Citrix access group for MS Lync,
Hostapp-Lync,KCUS,WinNT://KCUS/Hostapp-Lync,W49040,Delegate,Citrix access group for MS Lync,
GPO - Test Group 1,KCUS,WinNT://KCUS/GPO - Test Group 1,E18722,Owner,This group will be used for testing Group Policy Objects before they are production ready,
GPO - Test Group 1,KCUS,WinNT://KCUS/GPO - Test Group 1,Q04499,Authorizer,This group will be used for testing Group Policy Objects before they are production ready,
GPO - Test Group 1,KCUS,WinNT://KCUS/GPO - Test Group 1,Q06085,Authorizer,This group will be used for testing Group Policy Objects before they are production ready,
GPO - Test Group 1,KCUS,WinNT://KCUS/GPO - Test Group 1,Q08911,Authorizer,This group will be used for testing Group Policy Objects before they are production ready,
GPO - Test Group 1,KCUS,WinNT://KCUS/GPO - Test Group 1,Q39116,Authorizer,This group will be used for testing Group Policy Objects before they are production ready,
GPO - Test Group 1,KCUS,WinNT://KCUS/GPO - Test Group 1,W48997,Delegate,This group will be used for testing Group Policy Objects before they are production ready,
VDIDriveRedirection,KCUS,WinNT://KCUS/VDIDriveRedirection,E18722,Owner,Disabling the Client Drive Redirection for VDI users,
VDIDriveRedirection,KCUS,WinNT://KCUS/VDIDriveRedirection,Q07708,Authorizer,Disabling the Client Drive Redirection for VDI users,
VDIDriveRedirection,KCUS,WinNT://KCUS/VDIDriveRedirection,Q13357,Authorizer,Disabling the Client Drive Redirection for VDI users,
VDIDriveRedirection,KCUS,WinNT://KCUS/VDIDriveRedirection,Q14616,Authorizer,Disabling the Client Drive Redirection for VDI users,
VDIDriveRedirection,KCUS,WinNT://KCUS/VDIDriveRedirection,Q14718,Authorizer,Disabling the Client Drive Redirection for VDI users,
VDIDriveRedirection,KCUS,WinNT://KCUS/VDIDriveRedirection,Q15926,Authorizer,Disabling the Client Drive Redirection for VDI users,
VDIDriveRedirection,KCUS,WinNT://KCUS/VDIDriveRedirection,W49040,Delegate,Disabling the Client Drive Redirection for VDI users,
FC-RPADevUsers,KCUS,WinNT://KCUS/FC-RPADevUsers,E18722,Owner,Robotic Process Automation Team VDI Users,
FC-RPADevUsers,KCUS,WinNT://KCUS/FC-RPADevUsers,Q07708,Authorizer,Robotic Process Automation Team VDI Users,
FC-RPADevUsers,KCUS,WinNT://KCUS/FC-RPADevUsers,Q13357,Authorizer,Robotic Process Automation Team VDI Users,
FC-RPADevUsers,KCUS,WinNT://KCUS/FC-RPADevUsers,Q14616,Authorizer,Robotic Process Automation Team VDI Users,
FC-RPADevUsers,KCUS,WinNT://KCUS/FC-RPADevUsers,Q14718,Authorizer,Robotic Process Automation Team VDI Users,
FC-RPADevUsers,KCUS,WinNT://KCUS/FC-RPADevUsers,Q15926,Authorizer,Robotic Process Automation Team VDI Users,
FC-RPADevUsers,KCUS,WinNT://KCUS/FC-RPADevUsers,W49040,Delegate,Robotic Process Automation Team VDI Users,
Admin MSX Server,KCINET,WINNT://KCINET/ADMIN MSX SERVER,E18722,Owner,Microsoft Exchange Permissions,
Admin MSX Server,KCINET,WinNT://KCINET/Admin MSX Server,W62002,Delegate,Microsoft Exchange Permissions,
SMSCliAdvAutoAuth,KCUS,WinNT://KCUS/SMSCliAdvAutoAuth,E18722,Owner,SMS Request and Reporting System,
SMSCliAdvAutoAuth,KCUS,WinNT://KCUS/SMSCliAdvAutoAuth,W66442,Delegate,SMS Request and Reporting System,
SMSModPkgAutoAuth,KCUS,WinNT://KCUS/SMSModPkgAutoAuth,E18722,Owner,SMS Request and Reporting System,
SMSModPkgAutoAuth,KCUS,WinNT://KCUS/SMSModPkgAutoAuth,W66442,Delegate,SMS Request and Reporting System,
FAXServer Admin,KCUS,WinNT://KCUS/FAXServer Admin,E18722,Owner,FAX Server Administrators,
FAXServer Admin,KCUS,WinNT://KCUS/FAXServer Admin,Q03582,Authorizer,FAX Server Administrators,
FAXServer Admin,KCUS,WinNT://KCUS/FAXServer Admin,W62002,Delegate,FAX Server Administrators,
TCS Messaging Staff,KCUS,WinNT://KCUS/TCS Messaging Staff,E18722,Owner,TCS Messaging Members,
TCS Messaging Staff,KCUS,WinNT://KCUS/TCS Messaging Staff,Q11533,Delegate,TCS Messaging Members,
DL010337,KCUS,WinNT://KCUS/DL010337,E18722,Owner,+NA_Marketing  Huggies Desktop Team,
DL010337,KCUS,WinNT://KCUS/DL010337,W66442,Delegate,+NA_Marketing  Huggies Desktop Team,
HostApp-3PL-DIST-SP,KCUS,WinNT://KCUS/HostApp-3PL-DIST-SP,E18722,Owner,Citrix Application security group for Distribution SharePoint site,
HostApp-3PL-DIST-SP,KCUS,WinNT://KCUS/HostApp-3PL-DIST-SP,Q07708,Authorizer,Citrix Application security group for Distribution SharePoint site,
HostApp-3PL-DIST-SP,KCUS,WinNT://KCUS/HostApp-3PL-DIST-SP,Q13357,Authorizer,Citrix Application security group for Distribution SharePoint site,
HostApp-3PL-DIST-SP,KCUS,WinNT://KCUS/HostApp-3PL-DIST-SP,Q14616,Authorizer,Citrix Application security group for Distribution SharePoint site,
HostApp-3PL-DIST-SP,KCUS,WinNT://KCUS/HostApp-3PL-DIST-SP,Q14718,Authorizer,Citrix Application security group for Distribution SharePoint site,
HostApp-3PL-DIST-SP,KCUS,WinNT://KCUS/HostApp-3PL-DIST-SP,Q15926,Authorizer,Citrix Application security group for Distribution SharePoint site,
HostApp-3PL-DIST-SP,KCUS,WinNT://KCUS/HostApp-3PL-DIST-SP,W49040,Delegate,Citrix Application security group for Distribution SharePoint site,
QATesters_B-id,KCUS,WinNT://KCUS/QATesters_B-id,E18722,Owner,Desktop QA Testers - B-id's,
QATesters_B-id,KCUS,WinNT://KCUS/QATesters_B-id,Q04499,Delegate,Desktop QA Testers - B-id's,
Hostappmenu-Excel64bit,KCUS,WinNT://KCUS/Hostappmenu-Excel64bit,E18722,Owner,Citrix User security group for Excel64 bit,
Hostappmenu-Excel64bit,KCUS,WinNT://KCUS/Hostappmenu-Excel64bit,Q07708,Authorizer,Citrix User security group for Excel64 bit,
Hostappmenu-Excel64bit,KCUS,WinNT://KCUS/Hostappmenu-Excel64bit,Q13357,Authorizer,Citrix User security group for Excel64 bit,
Hostappmenu-Excel64bit,KCUS,WinNT://KCUS/Hostappmenu-Excel64bit,Q14616,Authorizer,Citrix User security group for Excel64 bit,
Hostappmenu-Excel64bit,KCUS,WinNT://KCUS/Hostappmenu-Excel64bit,Q14718,Authorizer,Citrix User security group for Excel64 bit,
Hostappmenu-Excel64bit,KCUS,WinNT://KCUS/Hostappmenu-Excel64bit,Q15926,Authorizer,Citrix User security group for Excel64 bit,
Hostappmenu-Excel64bit,KCUS,WinNT://KCUS/Hostappmenu-Excel64bit,W49040,Delegate,Citrix User security group for Excel64 bit,
Hostapp-Excel64bit,KCUS,WinNT://KCUS/Hostapp-Excel64bit,E18722,Owner,Citrix Application security group for Excel64 bit,
Hostapp-Excel64bit,KCUS,WinNT://KCUS/Hostapp-Excel64bit,Q07708,Authorizer,Citrix Application security group for Excel64 bit,
Hostapp-Excel64bit,KCUS,WinNT://KCUS/Hostapp-Excel64bit,Q13357,Authorizer,Citrix Application security group for Excel64 bit,
Hostapp-Excel64bit,KCUS,WinNT://KCUS/Hostapp-Excel64bit,Q14616,Authorizer,Citrix Application security group for Excel64 bit,
Hostapp-Excel64bit,KCUS,WinNT://KCUS/Hostapp-Excel64bit,Q14718,Authorizer,Citrix Application security group for Excel64 bit,
Hostapp-Excel64bit,KCUS,WinNT://KCUS/Hostapp-Excel64bit,Q15926,Authorizer,Citrix Application security group for Excel64 bit,
Hostapp-Excel64bit,KCUS,WinNT://KCUS/Hostapp-Excel64bit,W49040,Delegate,Citrix Application security group for Excel64 bit,
IADOWKSADMIN,KCUS,WinNT://KCUS/IADOWKSADMIN,E18722,Owner,Delhi Sales Office-Site Computer Account Administrators,
IADOWKSADMIN,KCUS,WinNT://KCUS/IADOWKSADMIN,Q06085,Authorizer,Delhi Sales Office-Site Computer Account Administrators,
IADOWKSADMIN,KCUS,WinNT://KCUS/IADOWKSADMIN,Q08911,Authorizer,Delhi Sales Office-Site Computer Account Administrators,
IADOWKSADMIN,KCUS,WinNT://KCUS/IADOWKSADMIN,W48997,Delegate,Delhi Sales Office-Site Computer Account Administrators,
IT_User_Information_PROD_Users,KCUS,WinNT://KCUS/IT_User_Information_PROD_Users,B00932,Delegate, This group will control access to the IT User Information Web Application,
IT_User_Information_PROD_Users,KCUS,WinNT://KCUS/IT_User_Information_PROD_Users,E18722,Owner, This group will control access to the IT User Information Web Application,
O365 Service Administrators,KCUS,WinNT://KCUS/O365 Service Administrators,E18722,Owner,O365 Service Administrators,
O365 Service Administrators,KCUS,WinNT://KCUS/O365 Service Administrators,Q03582,Authorizer,O365 Service Administrators,
O365 Service Administrators,KCUS,WinNT://KCUS/O365 Service Administrators,W62002,Delegate,O365 Service Administrators,
ODS_C,KCUS,WinNT://KCUS/ODS_C,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Content\NA.US.kcc.com\ODS\,
ODS_C,KCUS,WinNT://KCUS/ODS_C,W62002,Delegate,\\kcc.com\webservices\WebSites\Intranet\Content\NA.US.kcc.com\ODS\,
PF Admin,KCUS,WinNT://KCUS/PF Admin,E18722,Owner,Public Folders Administration,
PF Admin,KCUS,WinNT://KCUS/PF Admin,W62002,Delegate,Public Folders Administration,
O365 Reports Readers,KCUS,WinNT://KCUS/O365 Reports Readers,B46959,Authorizer,Members of the group will have an access to O365 capabilities usage reports,
O365 Reports Readers,KCUS,WinNT://KCUS/O365 Reports Readers,e18722,Owner,Members of the group will have an access to O365 capabilities usage reports,
O365 Reports Readers,KCUS,WinNT://KCUS/O365 Reports Readers,W62002,Delegate,Members of the group will have an access to O365 capabilities usage reports,
Windows 10 1709 language Packs,KCUS,WinNT://KCUS/Windows 10 1709 language Packs,E18722,Owner,Deploy the Windows 10 Version 1709 Non-English language packs,
Windows 10 1709 language Packs,KCUS,WinNT://KCUS/Windows 10 1709 language Packs,Q04499,Delegate,Deploy the Windows 10 Version 1709 Non-English language packs,
CA-PSYNCH,KCUS,WinNT://KCUS/CA-PSYNCH,E18722,Owner,Password Wizard Support Group Assessors.,
CA-PSYNCH,KCUS,WinNT://KCUS/CA-PSYNCH,W66442,Delegate,Password Wizard Support Group Assessors.,
USTCA260_Admin,KCUS,WinNT://KCUS/USTCA260_Admin,E18722,Owner,Administrator access to USTCA260,
USTCA260_Admin,KCUS,WinNT://KCUS/USTCA260_Admin,W66442,Delegate,Administrator access to USTCA260,
IACHMedSvcTwrMessaging_C,KCUS,WinNT://KCUS/IACHMedSvcTwrMessaging_C,E18722,Owner,\\iachfn01\KC Share\Medium Sensitive\Service Towers\Messaging,
IACHMedSvcTwrMessaging_C,KCUS,WinNT://KCUS/IACHMedSvcTwrMessaging_C,W62002,Delegate,\\iachfn01\KC Share\Medium Sensitive\Service Towers\Messaging,
GPO - Test Wireless Policy,KCUS,WinNT://KCUS/GPO - Test Wireless Policy,E18722,Owner,Pilot testing for new KC wireless profile,
GPO - Test Wireless Policy,KCUS,WinNT://KCUS/GPO - Test Wireless Policy,Q04499,Delegate,Pilot testing for new KC wireless profile,
GPO - Test Wireless Policy,KCUS,WinNT://KCUS/GPO - Test Wireless Policy,W48997,Authorizer,Pilot testing for new KC wireless profile,
IACHMedSvcTwrMessaging_R,KCUS,WinNT://KCUS/IACHMedSvcTwrMessaging_R,E18722,Owner,\\iachfn01\KC Share\Medium Sensitive\Service Towers\Messaging,
IACHMedSvcTwrMessaging_R,KCUS,WinNT://KCUS/IACHMedSvcTwrMessaging_R,W62002,Delegate,\\iachfn01\KC Share\Medium Sensitive\Service Towers\Messaging,
Managed Print Solution Server Admin,KCUS,WinNT://KCUS/Managed Print Solution Server Admin,B45906,Authorizer,Provides Admin access to the MPS / FM Audit server            ,
Managed Print Solution Server Admin,KCUS,WinNT://KCUS/Managed Print Solution Server Admin,E18722,Owner,Provides Admin access to the MPS / FM Audit server            ,
Managed Print Solution Server Admin,KCUS,WinNT://KCUS/Managed Print Solution Server Admin,Q06085,Authorizer,Provides Admin access to the MPS / FM Audit server            ,
Managed Print Solution Server Admin,KCUS,WinNT://KCUS/Managed Print Solution Server Admin,Q08911,Authorizer,Provides Admin access to the MPS / FM Audit server            ,
Managed Print Solution Server Admin,KCUS,WinNT://KCUS/Managed Print Solution Server Admin,W49040,Delegate,Provides Admin access to the MPS / FM Audit server            ,
HostAppMenu-Tesco,KCUS,WinNT://KCUS/HostAppMenu-Tesco,E18722,Owner,Citrix User Security group for Tesco users,
HostAppMenu-Tesco,KCUS,WinNT://KCUS/HostAppMenu-Tesco,Q07708,Authorizer,Citrix User Security group for Tesco users,
HostAppMenu-Tesco,KCUS,WinNT://KCUS/HostAppMenu-Tesco,Q13357,Authorizer,Citrix User Security group for Tesco users,
HostAppMenu-Tesco,KCUS,WinNT://KCUS/HostAppMenu-Tesco,Q14616,Authorizer,Citrix User Security group for Tesco users,
HostAppMenu-Tesco,KCUS,WinNT://KCUS/HostAppMenu-Tesco,Q14718,Authorizer,Citrix User Security group for Tesco users,
HostAppMenu-Tesco,KCUS,WinNT://KCUS/HostAppMenu-Tesco,Q15926,Authorizer,Citrix User Security group for Tesco users,
HostAppMenu-Tesco,KCUS,WinNT://KCUS/HostAppMenu-Tesco,W49040,Delegate,Citrix User Security group for Tesco users,
Trend_TMCMdatabase_RO,KCUS,WinNT://KCUS/Trend_TMCMdatabase_RO,E18722,Owner,Trend Micro Control Manager Read Only Database Group,
Trend_TMCMdatabase_RO,KCUS,WinNT://KCUS/Trend_TMCMdatabase_RO,W66442,Delegate,Trend Micro Control Manager Read Only Database Group,
Restart required machines,KCUS,WinNT://KCUS/Restart required machines,E18722,Owner,Apply reboot as required,
Restart required machines,KCUS,WinNT://KCUS/Restart required machines,W48997,Delegate,Apply reboot as required,
O365 Global Administrators,KCUS,WinNT://KCUS/O365 Global Administrators,B46959,Authorizer,Members of this group will be granted Global Admin role in Office 365,
O365 Global Administrators,KCUS,WinNT://KCUS/O365 Global Administrators,E18722,Owner,Members of this group will be granted Global Admin role in Office 365,
O365 Global Administrators,KCUS,WinNT://KCUS/O365 Global Administrators,W62002,Delegate,Members of this group will be granted Global Admin role in Office 365,
DesktopServicesBSDValidation,KCUS,WinNT://KCUS/DesktopServicesBSDValidation,E18722,Owner,This group will maintain control over supporting documentation to validate TCS knowledge ,
DesktopServicesBSDValidation,KCUS,WinNT://KCUS/DesktopServicesBSDValidation,W66442,Delegate,This group will maintain control over supporting documentation to validate TCS knowledge ,
USDQMISVisualSourceSafe_C,KCUS,WinNT://KCUS/USDQMISVisualSourceSafe_C,E18722,Owner,\\usdqfn01\share\mis\Visual Source Safe,
USDQMISVisualSourceSafe_C,KCUS,WinNT://KCUS/USDQMISVisualSourceSafe_C,W66442,Delegate,\\usdqfn01\share\mis\Visual Source Safe,
SMSRequest,KCUS,WinNT://KCUS/SMSRequest,E18722,Owner,Test Group for SMS Request System,
SMSRequest,KCUS,WinNT://KCUS/SMSRequest,W66442,Delegate,Test Group for SMS Request System,
USDQPersonalCare_C,KCUS,WinNT://KCUS/USDQPersonalCare_C,B75781,Authorizer,\\usdqfn01\share\Personal Care,
USDQPersonalCare_C,KCUS,WinNT://KCUS/USDQPersonalCare_C,E18722,Owner,\\usdqfn01\share\Personal Care,
USDQPersonalCare_C,KCUS,WinNT://KCUS/USDQPersonalCare_C,W66442,Delegate,\\usdqfn01\share\Personal Care,
USDQPresentations_C,KCUS,WinNT://KCUS/USDQPresentations_C,E18722,Owner,\\usdqfn01\share\presentations,
USDQPresentations_C,KCUS,WinNT://KCUS/USDQPresentations_C,W66442,Delegate,\\usdqfn01\share\presentations,
MISCOMPSVCSEDT_C,KCUS,WinNT://KCUS/MISCOMPSVCSEDT_C,W66442,Delegate,\\kcc.com\webservices\WebSites\Intranet\Content\NA.US.kcc.com\MIS\COMPSVCS\EDT\,
MISCOMPSVCSEDT_C,KCUS,WinNT://KCUS/MISCOMPSVCSEDT_C,E18722,Owner,\\kcc.com\webservices\WebSites\Intranet\Content\NA.US.kcc.com\MIS\COMPSVCS\EDT\,
MISCOMPSVCSEDT_C,KCUS,WinNT://KCUS/MISCOMPSVCSEDT_C,Q73491,Authorizer,\\kcc.com\webservices\WebSites\Intranet\Content\NA.US.kcc.com\MIS\COMPSVCS\EDT\,
MisCompSvcsEdtExecSupp_rstr,KCUS,WinNT://KCUS/MisCompSvcsEdtExecSupp_rstr,E18722,Owner,Intranet\MIS\CompSvcs\Edt\Executive Support -rstr,
MisCompSvcsEdtExecSupp_rstr,KCUS,WinNT://KCUS/MisCompSvcsEdtExecSupp_rstr,W66442,Delegate,Intranet\MIS\CompSvcs\Edt\Executive Support -rstr,
FC-SupportUsers,KCUS,WinNT://KCUS/FC-SupportUsers,E18722,Owner,CTS Support VDI Users,
FC-SupportUsers,KCUS,WinNT://KCUS/FC-SupportUsers,w49040,Delegate,CTS Support VDI Users,
O365-NonUserAc-E3M,KCUS,WinNT://KCUS/O365-NonUserAc-E3M,E18722,Owner,This Group provide special service Plans access to the Non User Accounts,
O365-NonUserAc-E3M,KCUS,WinNT://KCUS/O365-NonUserAc-E3M,Q03582,Authorizer,This Group provide special service Plans access to the Non User Accounts,
O365-NonUserAc-E3M,KCUS,WinNT://KCUS/O365-NonUserAc-E3M,w62002,Delegate,This Group provide special service Plans access to the Non User Accounts,
_UMAdmin,KCUS,WinNT://KCUS/_UMAdmin,b80845,Authorizer,Grants rights to add  remove  or change Unfied Messaging Attributes,
_UMAdmin,KCUS,WinNT://KCUS/_UMAdmin,E18722,Owner,Grants rights to add  remove  or change Unfied Messaging Attributes,
_UMAdmin,KCUS,WinNT://KCUS/_UMAdmin,W62002,Delegate,Grants rights to add  remove  or change Unfied Messaging Attributes,
USDAP001,KCUS,WinNT://KCUS/USDAP001,E18722,Owner,Dallas Turtle Creek- Executive LaserJet 4200N,
USDAP001,KCUS,WinNT://KCUS/USDAP001,W66442,Delegate,Dallas Turtle Creek- Executive LaserJet 4200N,
Allow-Comp-WSUS-AutoInstall,KCUS,WinNT://KCUS/Allow-Comp-WSUS-AutoInstall,E18722,Owner,Allows the Comp-WSUS-AutoInstall policy to apply,
Allow-Comp-WSUS-AutoInstall,KCUS,WinNT://KCUS/Allow-Comp-WSUS-AutoInstall,W66442,Delegate,Allows the Comp-WSUS-AutoInstall policy to apply,
MCOE_Reporting_DB_RO_C,KCUS,WinNT://KCUS/MCOE_Reporting_DB_RO_C,E18722,Owner,,
MCOE_Reporting_DB_RO_C,KCUS,WinNT://KCUS/MCOE_Reporting_DB_RO_C,E32872,Delegate,,
MCOE_Reporting_DB_RO_C,KCUS,WinNT://KCUS/MCOE_Reporting_DB_RO_C,W48997,Authorizer,,
Allow Social Connector Exception,KCUS,WinNT://KCUS/Allow Social Connector Exception,E18722,Owner,Security exception group for User-Deny Social Connector in Outlook 2010  GPO,
Allow Social Connector Exception,KCUS,WinNT://KCUS/Allow Social Connector Exception,W66442,Delegate,Security exception group for User-Deny Social Connector in Outlook 2010  GPO,
FC-CogSupportUsers,KCUS,WinNT://KCUS/FC-CogSupportUsers,E18722,Owner,CTS Support VDI Users,
FC-CogSupportUsers,KCUS,WinNT://KCUS/FC-CogSupportUsers,Q07708,Authorizer,CTS Support VDI Users,
FC-CogSupportUsers,KCUS,WinNT://KCUS/FC-CogSupportUsers,Q13357,Authorizer,CTS Support VDI Users,
FC-CogSupportUsers,KCUS,WinNT://KCUS/FC-CogSupportUsers,Q14616,Authorizer,CTS Support VDI Users,
FC-CogSupportUsers,KCUS,WinNT://KCUS/FC-CogSupportUsers,Q14718,Authorizer,CTS Support VDI Users,
FC-CogSupportUsers,KCUS,WinNT://KCUS/FC-CogSupportUsers,Q15926,Authorizer,CTS Support VDI Users,
FC-CogSupportUsers,KCUS,WinNT://KCUS/FC-CogSupportUsers,w49040,Delegate,CTS Support VDI Users,
DL000306,KCUS,WinNT://KCUS/DL000306,E18722,Owner,+NA_EDMT  Project Teams,
DL000306,KCUS,WinNT://KCUS/DL000306,W62002,Delegate,+NA_EDMT  Project Teams,
FC-MFGUsers,KCUS,WinNT://KCUS/FC-MFGUsers,E18722,Owner,CTS MFG VDI Users,
FC-MFGUsers,KCUS,WinNT://KCUS/FC-MFGUsers,Q07708,Authorizer,CTS MFG VDI Users,
FC-MFGUsers,KCUS,WinNT://KCUS/FC-MFGUsers,Q13357,Authorizer,CTS MFG VDI Users,
FC-MFGUsers,KCUS,WinNT://KCUS/FC-MFGUsers,Q14616,Authorizer,CTS MFG VDI Users,
FC-MFGUsers,KCUS,WinNT://KCUS/FC-MFGUsers,Q14718,Authorizer,CTS MFG VDI Users,
FC-MFGUsers,KCUS,WinNT://KCUS/FC-MFGUsers,Q15926,Authorizer,CTS MFG VDI Users,
FC-MFGUsers,KCUS,WinNT://KCUS/FC-MFGUsers,w49040,Delegate,CTS MFG VDI Users,
WS-EUA-R-P,KCUS,WinNT://KCUS/WS-EUA-R-P,E18722,Owner,Provides read access to End User Agreement database,
WS-EUA-R-P,KCUS,WinNT://KCUS/WS-EUA-R-P,Q04499,Delegate,Provides read access to End User Agreement database,
WS-EUA-R-P,KCUS,WinNT://KCUS/WS-EUA-R-P,W48997,Authorizer,Provides read access to End User Agreement database,
HostApp-EUPromax,KCUS,WinNT://KCUS/HostApp-EUPromax,E18722,Owner,Citrix Application access group for EUPromax,
HostApp-EUPromax,KCUS,WinNT://KCUS/HostApp-EUPromax,Q07708,Authorizer,Citrix Application access group for EUPromax,
HostApp-EUPromax,KCUS,WinNT://KCUS/HostApp-EUPromax,Q14616,Authorizer,Citrix Application access group for EUPromax,
HostApp-EUPromax,KCUS,WinNT://KCUS/HostApp-EUPromax,W49040,Delegate,Citrix Application access group for EUPromax,
Hardware Hash Upload Automation Prod,KCUS,WinNT://KCUS/Hardware Hash Upload Automation Prod,B45906,Authorizer,This is to automate the hardware hash upload process,
Hardware Hash Upload Automation Prod,KCUS,WinNT://KCUS/Hardware Hash Upload Automation Prod,B70279,Authorizer,This is to automate the hardware hash upload process,
Hardware Hash Upload Automation Prod,KCUS,WinNT://KCUS/Hardware Hash Upload Automation Prod,C29053,Authorizer,This is to automate the hardware hash upload process,
Hardware Hash Upload Automation Prod,KCUS,WinNT://KCUS/Hardware Hash Upload Automation Prod,E18722,Owner,This is to automate the hardware hash upload process,
Hardware Hash Upload Automation Prod,KCUS,WinNT://KCUS/Hardware Hash Upload Automation Prod,E38114,Authorizer,This is to automate the hardware hash upload process,
Hardware Hash Upload Automation Prod,KCUS,WinNT://KCUS/Hardware Hash Upload Automation Prod,Q06085,Authorizer,This is to automate the hardware hash upload process,
Hardware Hash Upload Automation Prod,KCUS,WinNT://KCUS/Hardware Hash Upload Automation Prod,W45758,Authorizer,This is to automate the hardware hash upload process,
Hardware Hash Upload Automation Prod,KCUS,WinNT://KCUS/Hardware Hash Upload Automation Prod,W48997,Delegate,This is to automate the hardware hash upload process,
USTCA158_Admin,KCUS,WinNT://KCUS/USTCA158_Admin,E18722,Owner,Administrator group for USTCA158,
USTCA158_Admin,KCUS,WinNT://KCUS/USTCA158_Admin,W66442,Delegate,Administrator group for USTCA158,
EuropeanBroadbandwksAdmin,KCUS,WinNT://KCUS/EuropeanBroadbandwksAdmin,E18722,Owner,EuropeanBroadbandUsers-Site Computer Account Administrators,
EuropeanBroadbandwksAdmin,KCUS,WinNT://KCUS/EuropeanBroadbandwksAdmin,W48997,Delegate,EuropeanBroadbandUsers-Site Computer Account Administrators,
EuropeanRASwksAdmin,KCUS,WinNT://KCUS/EuropeanRASwksAdmin,E18722,Owner,EuropeanRASUsers-Site Computer Account Administrators,
EuropeanRASwksAdmin,KCUS,WinNT://KCUS/EuropeanRASwksAdmin,W48997,Delegate,EuropeanRASUsers-Site Computer Account Administrators,
Policy Pilot Computers,KCUS,WinNT://KCUS/Policy Pilot Computers,E18722,Owner,Pilot group used for policy changes,
Policy Pilot Computers,KCUS,WinNT://KCUS/Policy Pilot Computers,Q06085,Authorizer,Pilot group used for policy changes,
Policy Pilot Computers,KCUS,WinNT://KCUS/Policy Pilot Computers,Q08911,Authorizer,Pilot group used for policy changes,
Policy Pilot Computers,KCUS,WinNT://KCUS/Policy Pilot Computers,W66442,Delegate,Pilot group used for policy changes,
O365 Exchange Administrators,KCUS,WinNT://KCUS/O365 Exchange Administrators,E18722,Owner,Members of this group will be granted Exchange administrator access in Office 365.,
O365 Exchange Administrators,KCUS,WinNT://KCUS/O365 Exchange Administrators,W62002,Delegate,Members of this group will be granted Exchange administrator access in Office 365.,
DL008543,KCUS,WinNT://KCUS/DL008543,E18722,Owner,+TCS  Messaging,
DL008543,KCUS,WinNT://KCUS/DL008543,Q03582,Authorizer,+TCS  Messaging,
DL008543,KCUS,WinNT://KCUS/DL008543,Q11533,Authorizer,+TCS  Messaging,
DL008543,KCUS,WinNT://KCUS/DL008543,W62002,Delegate,+TCS  Messaging,
Intune MAM Exclusions,KCUS,WinNT://KCUS/Intune MAM Exclusions,E18722,Owner,This group will be used in exceptional cases where Intune team has to exclude certain users from MAM or CA policies,
Intune MAM Exclusions,KCUS,WinNT://KCUS/Intune MAM Exclusions,W66442,Delegate,This group will be used in exceptional cases where Intune team has to exclude certain users from MAM or CA policies,
DL008898,KCUS,WinNT://KCUS/DL008898,E18722,Owner,+LAO_ITS  INF WMS RF Team,
DL008898,KCUS,WinNT://KCUS/DL008898,W62002,Delegate,+LAO_ITS  INF WMS RF Team,
DL010340,KCUS,WinNT://KCUS/DL010340,E18722,Owner,+NA_Marketing  FC Desktop Team,
DL010340,KCUS,WinNT://KCUS/DL010340,W66442,Delegate,+NA_Marketing  FC Desktop Team,
HostApp-TMCitrixDesktop,KCUS,WinNT://KCUS/HostApp-TMCitrixDesktop,E18722,Owner,Citrix Desktop for TechM Users,
HostApp-TMCitrixDesktop,KCUS,WinNT://KCUS/HostApp-TMCitrixDesktop,Q14616,Authorizer,Citrix Desktop for TechM Users,
HostApp-TMCitrixDesktop,KCUS,WinNT://KCUS/HostApp-TMCitrixDesktop,W49040,Delegate,Citrix Desktop for TechM Users,
HostappMenu-PulseGlint,KCUS,WinNT://KCUS/HostappMenu-PulseGlint,B99187,Authorizer,Access provision group for Glint Pulse Survey published through Chrome in Citrix,
HostappMenu-PulseGlint,KCUS,WinNT://KCUS/HostappMenu-PulseGlint,E18722,Owner,Access provision group for Glint Pulse Survey published through Chrome in Citrix,
HostappMenu-PulseGlint,KCUS,WinNT://KCUS/HostappMenu-PulseGlint,W49040,Delegate,Access provision group for Glint Pulse Survey published through Chrome in Citrix,
Azure Named Location Admins,KCUS,WinNT://KCUS/Azure Named Location Admins,E18722,Owner,This security group would be used by CS&A team to manage the IP ranges in Azure Named location (configured via Azure Conditional Access Policy),
Azure Named Location Admins,KCUS,WinNT://KCUS/Azure Named Location Admins,w62002,Delegate,This security group would be used by CS&A team to manage the IP ranges in Azure Named location (configured via Azure Conditional Access Policy),
O365 Attribute Assignment Reader,KCUS,WinNT://KCUS/O365 Attribute Assignment Reader,E18722,Owner,Azure AD role [Attribute Assignment Reader],
O365 Attribute Assignment Reader,KCUS,WinNT://KCUS/O365 Attribute Assignment Reader,W62002,Delegate,Azure AD role [Attribute Assignment Reader],
Office-365-PerDevice,KCUS,WinNT://KCUS/Office-365-PerDevice,E18722,Owner,The PCs listed in this group will get O365 shared device license,
Office-365-PerDevice,KCUS,WinNT://KCUS/Office-365-PerDevice,Q04499,Authorizer,The PCs listed in this group will get O365 shared device license,
Office-365-PerDevice,KCUS,WinNT://KCUS/Office-365-PerDevice,Q06085,Authorizer,The PCs listed in this group will get O365 shared device license,
Office-365-PerDevice,KCUS,WinNT://KCUS/Office-365-PerDevice,Q06371,Authorizer,The PCs listed in this group will get O365 shared device license,
Office-365-PerDevice,KCUS,WinNT://KCUS/Office-365-PerDevice,Q06435,Authorizer,The PCs listed in this group will get O365 shared device license,
Office-365-PerDevice,KCUS,WinNT://KCUS/Office-365-PerDevice,Q07749,Authorizer,The PCs listed in this group will get O365 shared device license,
Office-365-PerDevice,KCUS,WinNT://KCUS/Office-365-PerDevice,Q39116,Authorizer,The PCs listed in this group will get O365 shared device license,
Office-365-PerDevice,KCUS,WinNT://KCUS/Office-365-PerDevice,W48997,Delegate,The PCs listed in this group will get O365 shared device license,
Office-365-PerDevice,KCUS,WinNT://KCUS/Office-365-PerDevice,W66442,Authorizer,The PCs listed in this group will get O365 shared device license,
O365 OneDrive Users,KCUS,WinNT://KCUS/O365 OneDrive Users,E18722,Owner,O365 Entitled users in this group can use OneDrive for Business in O365,
O365 OneDrive Users,KCUS,WinNT://KCUS/O365 OneDrive Users,Q03582,Authorizer,O365 Entitled users in this group can use OneDrive for Business in O365,
O365 OneDrive Users,KCUS,WinNT://KCUS/O365 OneDrive Users,W62002,Delegate,O365 Entitled users in this group can use OneDrive for Business in O365,
DL010339,KCUS,WinNT://KCUS/DL010339,E18722,Owner,+NA_Marketing  Digital Tools Desktop Team,
DL010339,KCUS,WinNT://KCUS/DL010339,W66442,Delegate,+NA_Marketing  Digital Tools Desktop Team,
HostApp-ControlM,KCUS,WinNT://KCUS/HostApp-ControlM,E18722,Owner,Citrix Application access group for Control-M,
HostApp-ControlM,KCUS,WinNT://KCUS/HostApp-ControlM,Q07708,Authorizer,Citrix Application access group for Control-M,
HostApp-ControlM,KCUS,WinNT://KCUS/HostApp-ControlM,Q13357,Authorizer,Citrix Application access group for Control-M,
HostApp-ControlM,KCUS,WinNT://KCUS/HostApp-ControlM,Q14616,Authorizer,Citrix Application access group for Control-M,
HostApp-ControlM,KCUS,WinNT://KCUS/HostApp-ControlM,Q14718,Authorizer,Citrix Application access group for Control-M,
HostApp-ControlM,KCUS,WinNT://KCUS/HostApp-ControlM,Q15926,Authorizer,Citrix Application access group for Control-M,
HostApp-ControlM,KCUS,WinNT://KCUS/HostApp-ControlM,W49040,Delegate,Citrix Application access group for Control-M,
HostAppMenu-Kantar,KCUS,WinNT://KCUS/HostAppMenu-Kantar,E18722,Owner,To provide access to HSD,
HostAppMenu-Kantar,KCUS,WinNT://KCUS/HostAppMenu-Kantar,Q07708,Authorizer,To provide access to HSD,
HostAppMenu-Kantar,KCUS,WinNT://KCUS/HostAppMenu-Kantar,Q13357,Authorizer,To provide access to HSD,
HostAppMenu-Kantar,KCUS,WinNT://KCUS/HostAppMenu-Kantar,Q14616,Authorizer,To provide access to HSD,
HostAppMenu-Kantar,KCUS,WinNT://KCUS/HostAppMenu-Kantar,Q14718,Authorizer,To provide access to HSD,
HostAppMenu-Kantar,KCUS,WinNT://KCUS/HostAppMenu-Kantar,Q15926,Authorizer,To provide access to HSD,
HostAppMenu-Kantar,KCUS,WinNT://KCUS/HostAppMenu-Kantar,W49040,Delegate,To provide access to HSD,
Endpoint Security Test 1,KCUS,WinNT://KCUS/Endpoint Security Test 1,E18722,Owner,Testing group for Endpoint Security and Password Wizard,
Endpoint Security Test 1,KCUS,WinNT://KCUS/Endpoint Security Test 1,Q06371,Delegate,Testing group for Endpoint Security and Password Wizard,
AdobeAcroProEngInstall,KCUS,WinNT://KCUS/AdobeAcroProEngInstall,E18722,Owner,Security group for Installing Adobe Acrobat Professional from SCCM,
AdobeAcroProEngInstall,KCUS,WinNT://KCUS/AdobeAcroProEngInstall,W48997,Delegate,Security group for Installing Adobe Acrobat Professional from SCCM,
O365-PrjOnl-P1,KCUS,WinNT://KCUS/O365-PrjOnl-P1,E18722,Owner,Members are licensed with MS Project Online Plan1,
O365-PrjOnl-P1,KCUS,WinNT://KCUS/O365-PrjOnl-P1,W49040,Delegate,Members are licensed with MS Project Online Plan1,
Office-2013-Exception-Requestors,KCUS,WinNT://KCUS/Office-2013-Exception-Requestors,E18722,Owner,This group is to provide an access to a request form for Office 2013 32-bit,
Office-2013-Exception-Requestors,KCUS,WinNT://KCUS/Office-2013-Exception-Requestors,W48997,Delegate,This group is to provide an access to a request form for Office 2013 32-bit,
VNC-Install-Access,KCUS,WinNT://KCUS/VNC-Install-Access,B70279,Authorizer,Provides access to Real VNC installation package,
VNC-Install-Access,KCUS,WinNT://KCUS/VNC-Install-Access,C29053,Authorizer,Provides access to Real VNC installation package,
VNC-Install-Access,KCUS,WinNT://KCUS/VNC-Install-Access,E18722,Owner,Provides access to Real VNC installation package,
VNC-Install-Access,KCUS,WinNT://KCUS/VNC-Install-Access,E38114,Authorizer,Provides access to Real VNC installation package,
VNC-Install-Access,KCUS,WinNT://KCUS/VNC-Install-Access,Q04499,Authorizer,Provides access to Real VNC installation package,
VNC-Install-Access,KCUS,WinNT://KCUS/VNC-Install-Access,Q06085,Authorizer,Provides access to Real VNC installation package,
VNC-Install-Access,KCUS,WinNT://KCUS/VNC-Install-Access,W45758,Authorizer,Provides access to Real VNC installation package,
VNC-Install-Access,KCUS,WinNT://KCUS/VNC-Install-Access,W48997,Authorizer,Provides access to Real VNC installation package,
VNC-Install-Access,KCUS,WinNT://KCUS/VNC-Install-Access,W66442,Delegate,Provides access to Real VNC installation package,
Visual Studio Professional-Download,KCUS,WinNT://KCUS/Visual Studio Professional-Download,E18722,Owner,Visual Studio Professional License and Download,
Visual Studio Professional-Download,KCUS,WinNT://KCUS/Visual Studio Professional-Download,Q06371,Authorizer,Visual Studio Professional License and Download,
Visual Studio Professional-Download,KCUS,WinNT://KCUS/Visual Studio Professional-Download,Q39116,Delegate,Visual Studio Professional License and Download,
O365_MSTEAMS_FF_POC,KCUS,WinNT://KCUS/O365_MSTEAMS_FF_POC,E18722,Owner,Security Group to Enable Restricted Teams Functions for Targeted POC users,
O365_MSTEAMS_FF_POC,KCUS,WinNT://KCUS/O365_MSTEAMS_FF_POC,W62002,Delegate,Security Group to Enable Restricted Teams Functions for Targeted POC users,
Hostapp-office M365,KCUS,WinNT://KCUS/Hostapp-office M365,Q15926,Authorizer,Citrix access group for MS365,
Hostapp-office M365,KCUS,WinNT://KCUS/Hostapp-office M365,W49040,Delegate,Citrix access group for MS365,
Hostapp-office M365,KCUS,WinNT://KCUS/Hostapp-office M365,E18722,Owner,Citrix access group for MS365,
Hostapp-office M365,KCUS,WinNT://KCUS/Hostapp-office M365,Q07708,Authorizer,Citrix access group for MS365,
Hostapp-office M365,KCUS,WinNT://KCUS/Hostapp-office M365,Q13357,Authorizer,Citrix access group for MS365,
Hostapp-office M365,KCUS,WinNT://KCUS/Hostapp-office M365,Q14616,Authorizer,Citrix access group for MS365,
Hostapp-office M365,KCUS,WinNT://KCUS/Hostapp-office M365,Q14718,Authorizer,Citrix access group for MS365,
Citrix User Acceptance Testing,KCUS,WinNT://KCUS/Citrix User Acceptance Testing,E18722,Owner,User Acceptance Testing for Citrix Applications,
Citrix User Acceptance Testing,KCUS,WinNT://KCUS/Citrix User Acceptance Testing,W49040,Delegate,User Acceptance Testing for Citrix Applications,
DesktopServices DB ReadOnly,KCUS,WinNT://KCUS/DesktopServices DB ReadOnly,E18722,Owner,Read-only access to Desktop Services DBs,
DesktopServices DB ReadOnly,KCUS,WinNT://KCUS/DesktopServices DB ReadOnly,Q73491,Delegate,Read-only access to Desktop Services DBs,
Microsoft Silverlight Removal,KCUS,WinNT://KCUS/Microsoft Silverlight Removal,Q14717,Authorizer,Contains devices from which Silverlight will be removed,
Microsoft Silverlight Removal,KCUS,WinNT://KCUS/Microsoft Silverlight Removal,W48997,Delegate,Contains devices from which Silverlight will be removed,
Microsoft Silverlight Removal,KCUS,WinNT://KCUS/Microsoft Silverlight Removal,E18722,Owner,Contains devices from which Silverlight will be removed,
Microsoft Silverlight Removal,KCUS,WinNT://KCUS/Microsoft Silverlight Removal,Q13243,Authorizer,Contains devices from which Silverlight will be removed,
DL008077,KCUS,WinNT://KCUS/DL008077,E18722,Owner,+GLOBAL  RAD TEAM,
DL008077,KCUS,WinNT://KCUS/DL008077,W62002,Delegate,+GLOBAL  RAD TEAM,
O365 Global Compliance,KCUS,WinNT://KCUS/O365 Global Compliance,B46959,Authorizer,Members of this group will be granted special compliance access in Office 365,
O365 Global Compliance,KCUS,WinNT://KCUS/O365 Global Compliance,E18722,Owner,Members of this group will be granted special compliance access in Office 365,
O365 Global Compliance,KCUS,WinNT://KCUS/O365 Global Compliance,W62002,Delegate,Members of this group will be granted special compliance access in Office 365,
Desktop-Monitoring,KCUS,WinNT://KCUS/Desktop-Monitoring,B00982,Authorizer,Contains machines names that are in pilot for Splunk monitoring tool,
Desktop-Monitoring,KCUS,WinNT://KCUS/Desktop-Monitoring,B80845,Authorizer,Contains machines names that are in pilot for Splunk monitoring tool,
Desktop-Monitoring,KCUS,WinNT://KCUS/Desktop-Monitoring,e18722,Owner,Contains machines names that are in pilot for Splunk monitoring tool,
Desktop-Monitoring,KCUS,WinNT://KCUS/Desktop-Monitoring,Q04499,Authorizer,Contains machines names that are in pilot for Splunk monitoring tool,
Desktop-Monitoring,KCUS,WinNT://KCUS/Desktop-Monitoring,Q06085,Authorizer,Contains machines names that are in pilot for Splunk monitoring tool,
Desktop-Monitoring,KCUS,WinNT://KCUS/Desktop-Monitoring,W48997,Delegate,Contains machines names that are in pilot for Splunk monitoring tool,
HostApp-XenBase,KCUS,WinNT://KCUS/HostApp-XenBase,E18722,Owner,Citrix access group for XenBase,
HostApp-XenBase,KCUS,WinNT://KCUS/HostApp-XenBase,Q07708,Authorizer,Citrix access group for XenBase,
HostApp-XenBase,KCUS,WinNT://KCUS/HostApp-XenBase,Q13357,Authorizer,Citrix access group for XenBase,
HostApp-XenBase,KCUS,WinNT://KCUS/HostApp-XenBase,Q14616,Authorizer,Citrix access group for XenBase,
HostApp-XenBase,KCUS,WinNT://KCUS/HostApp-XenBase,Q14718,Authorizer,Citrix access group for XenBase,
HostApp-XenBase,KCUS,WinNT://KCUS/HostApp-XenBase,Q15926,Authorizer,Citrix access group for XenBase,
HostApp-XenBase,KCUS,WinNT://KCUS/HostApp-XenBase,W49040,Delegate,Citrix access group for XenBase,
O365-LoopApp-Users,KCUS,WinNT://KCUS/O365-LoopApp-Users,e18722,Owner,O365-LoopApp-Users,
O365-LoopApp-Users,KCUS,WinNT://KCUS/O365-LoopApp-Users,U32685,Authorizer,O365-LoopApp-Users,
O365-LoopApp-Users,KCUS,WinNT://KCUS/O365-LoopApp-Users,w62002,Delegate,O365-LoopApp-Users,
O365-Tango-Users,KCUS,WinNT://KCUS/O365-Tango-Users,e18722,Owner,O365-Tango-Users,
O365-Tango-Users,KCUS,WinNT://KCUS/O365-Tango-Users,Q03582,Authorizer,O365-Tango-Users,
O365-Tango-Users,KCUS,WinNT://KCUS/O365-Tango-Users,w62002,Delegate,O365-Tango-Users,
DL002546,KCUS,WinNT://KCUS/DL002546,E18722,Owner,+NA_EDMT  QAA NA Site LAN Admins,
DL002546,KCUS,WinNT://KCUS/DL002546,W62002,Delegate,+NA_EDMT  QAA NA Site LAN Admins,
AdobeAcroStdEngInstall,KCUS,WinNT://KCUS/AdobeAcroStdEngInstall,E18722,Owner,Security group for Installing Adobe Acrobat Standard from SCCM,
AdobeAcroStdEngInstall,KCUS,WinNT://KCUS/AdobeAcroStdEngInstall,W48997,Delegate,Security group for Installing Adobe Acrobat Standard from SCCM,
DL003855,KCUS,WinNT://KCUS/DL003855,E18722,Owner,+NA_EDMT  QAA Critical Desktop Alert,
DL003855,KCUS,WinNT://KCUS/DL003855,Q06085,Authorizer,+NA_EDMT  QAA Critical Desktop Alert,
DL003855,KCUS,WinNT://KCUS/DL003855,Q08911,Authorizer,+NA_EDMT  QAA Critical Desktop Alert,
DL003855,KCUS,WinNT://KCUS/DL003855,W66442,Delegate,+NA_EDMT  QAA Critical Desktop Alert,
Avecto RC2 Expansion,KCUS,WinNT://KCUS/Avecto RC2 Expansion,E18722,Owner,Expanded RC2 security group for Avecto 4.4.92.0 deployment,
Avecto RC2 Expansion,KCUS,WinNT://KCUS/Avecto RC2 Expansion,W48997,Delegate,Expanded RC2 security group for Avecto 4.4.92.0 deployment,
DL004001,KCUS,WinNT://KCUS/DL004001,E18722,Owner,+EURO_ITS  Mobile UK,
DL004001,KCUS,WinNT://KCUS/DL004001,W62002,Delegate,+EURO_ITS  Mobile UK,
www.mobilityservices.kcc.com_owners,KCUS,WinNT://KCUS/www.mobilityservices.kcc.com_owners,E18722,Owner,Group for website owners,
www.mobilityservices.kcc.com_owners,KCUS,WinNT://KCUS/www.mobilityservices.kcc.com_owners,W66442,Delegate,Group for website owners,
GRP08598_C,KCUS,WinNT://KCUS/GRP08598_C,E18722,Owner,Mailbox:_Support  K2 Platforms,
GRP08598_C,KCUS,WinNT://KCUS/GRP08598_C,Q08911,Authorizer,Mailbox:_Support  K2 Platforms,
GRP08598_C,KCUS,WinNT://KCUS/GRP08598_C,W49040,Delegate,Mailbox:_Support  K2 Platforms,
O365-Bookings-E3M,KCUS,WinNT://KCUS/O365-Bookings-E3M,E18722,Owner,This Security group will be used for activating MS Booking License to E3M users,
O365-Bookings-E3M,KCUS,WinNT://KCUS/O365-Bookings-E3M,Q03582,Authorizer,This Security group will be used for activating MS Booking License to E3M users,
O365-Bookings-E3M,KCUS,WinNT://KCUS/O365-Bookings-E3M,W62002,Delegate,This Security group will be used for activating MS Booking License to E3M users,
IADOICWKSADMIN,KCUS,WinNT://KCUS/IADOICWKSADMIN,E18722,Owner,Delhi Sales Office ICW Computer Account Administrators,
IADOICWKSADMIN,KCUS,WinNT://KCUS/IADOICWKSADMIN,Q06085,Authorizer,Delhi Sales Office ICW Computer Account Administrators,
IADOICWKSADMIN,KCUS,WinNT://KCUS/IADOICWKSADMIN,Q08911,Authorizer,Delhi Sales Office ICW Computer Account Administrators,
IADOICWKSADMIN,KCUS,WinNT://KCUS/IADOICWKSADMIN,W48997,Delegate,Delhi Sales Office ICW Computer Account Administrators,
www.mobilityservices.kcc.com_authorizers,KCUS,WinNT://KCUS/www.mobilityservices.kcc.com_authorizers,E18722,Owner,Group for website Authorizers,
www.mobilityservices.kcc.com_authorizers,KCUS,WinNT://KCUS/www.mobilityservices.kcc.com_authorizers,W66442,Delegate,Group for website Authorizers,
www.mobilityservices.kcc.com_developers,KCUS,WinNT://KCUS/www.mobilityservices.kcc.com_developers,E18722,Owner,Group for website developers,
www.mobilityservices.kcc.com_developers,KCUS,WinNT://KCUS/www.mobilityservices.kcc.com_developers,W66442,Delegate,Group for website developers,
GRP05058_C,KCUS,WinNT://KCUS/GRP05058_C,E18722,Owner,Mailbox: _Support  Testmailbox,
GRP05058_C,KCUS,WinNT://KCUS/GRP05058_C,W62002,Delegate,Mailbox: _Support  Testmailbox,
Office 365 Apps Current Channel Preview,KCUS,WinNT://KCUS/Office 365 Apps Current Channel Preview,E18722,Owner,This group will be used to enable O365 Current Channel Preview and Teams Preview,
Office 365 Apps Current Channel Preview,KCUS,WinNT://KCUS/Office 365 Apps Current Channel Preview,W48997,Authorizer,This group will be used to enable O365 Current Channel Preview and Teams Preview,
Office 365 Apps Current Channel Preview,KCUS,WinNT://KCUS/Office 365 Apps Current Channel Preview,W66442,Delegate,This group will be used to enable O365 Current Channel Preview and Teams Preview,
Intune_MAM_Enabled_Exception_Users,KCUS,WinNT://KCUS/Intune_MAM_Enabled_Exception_Users,E18722,Owner,Intune_MAM_Enabled_Exception_Users,
Intune_MAM_Enabled_Exception_Users,KCUS,WinNT://KCUS/Intune_MAM_Enabled_Exception_Users,E32872,Authorizer,Intune_MAM_Enabled_Exception_Users,
Intune_MAM_Enabled_Exception_Users,KCUS,WinNT://KCUS/Intune_MAM_Enabled_Exception_Users,w62002,Delegate,Intune_MAM_Enabled_Exception_Users,
Visual Studio Professional,KCUS,WinNT://KCUS/Visual Studio Professional,E18722,Owner,Members to be provisioned with MS Visual Studio Pro license.,
Visual Studio Professional,KCUS,WinNT://KCUS/Visual Studio Professional,Q06371,Authorizer,Members to be provisioned with MS Visual Studio Pro license.,
Visual Studio Professional,KCUS,WinNT://KCUS/Visual Studio Professional,Q39116,Delegate,Members to be provisioned with MS Visual Studio Pro license.,
Hostappmenu-HSD,KCUS,WinNT://KCUS/Hostappmenu-HSD,E18722,Owner,Citrix Desktop access level group,
Hostappmenu-HSD,KCUS,WinNT://KCUS/Hostappmenu-HSD,Q07708,Authorizer,Citrix Desktop access level group,
Hostappmenu-HSD,KCUS,WinNT://KCUS/Hostappmenu-HSD,Q13357,Authorizer,Citrix Desktop access level group,
Hostappmenu-HSD,KCUS,WinNT://KCUS/Hostappmenu-HSD,Q14616,Authorizer,Citrix Desktop access level group,
Hostappmenu-HSD,KCUS,WinNT://KCUS/Hostappmenu-HSD,Q14718,Authorizer,Citrix Desktop access level group,
Hostappmenu-HSD,KCUS,WinNT://KCUS/Hostappmenu-HSD,Q15926,Authorizer,Citrix Desktop access level group,
Hostappmenu-HSD,KCUS,WinNT://KCUS/Hostappmenu-HSD,W49040,Delegate,Citrix Desktop access level group,
Security Center Read Access - Desktop,KCUS,WinNT://KCUS/Security Center Read Access - Desktop,E18722,Owner,Group for Desktop Team to get L2 level access of Security Center,
Security Center Read Access - Desktop,KCUS,WinNT://KCUS/Security Center Read Access - Desktop,Q06371,Authorizer,Group for Desktop Team to get L2 level access of Security Center,
Security Center Read Access - Desktop,KCUS,WinNT://KCUS/Security Center Read Access - Desktop,W48997,Delegate,Group for Desktop Team to get L2 level access of Security Center,
IADOPIPEADMIN,KCUS,WinNT://KCUS/IADOPIPEADMIN,E18722,Owner,Delhi Sales Office-Site Pipe Account Administrators,
IADOPIPEADMIN,KCUS,WinNT://KCUS/IADOPIPEADMIN,Q06085,Authorizer,Delhi Sales Office-Site Pipe Account Administrators,
IADOPIPEADMIN,KCUS,WinNT://KCUS/IADOPIPEADMIN,Q08911,Authorizer,Delhi Sales Office-Site Pipe Account Administrators,
IADOPIPEADMIN,KCUS,WinNT://KCUS/IADOPIPEADMIN,W48997,Delegate,Delhi Sales Office-Site Pipe Account Administrators,
O365_PrjOnlPro,KCUS,WinNT://KCUS/O365_PrjOnlPro,E18722,Owner,To manage MS Project Professional Licenses,
O365_PrjOnlPro,KCUS,WinNT://KCUS/O365_PrjOnlPro,Q06371,Authorizer,To manage MS Project Professional Licenses,
O365_PrjOnlPro,KCUS,WinNT://KCUS/O365_PrjOnlPro,Q39116,Authorizer,To manage MS Project Professional Licenses,
O365_PrjOnlPro,KCUS,WinNT://KCUS/O365_PrjOnlPro,W48997,Delegate,To manage MS Project Professional Licenses,
O365_PrjOnlPro,KCUS,WinNT://KCUS/O365_PrjOnlPro,W66442,Authorizer,To manage MS Project Professional Licenses,
Hostapp-Visio 2010,KCUS,WinNT://KCUS/Hostapp-Visio 2010,Q07708,Authorizer,Citrix access group for MS Visio 2010,
Hostapp-Visio 2010,KCUS,WinNT://KCUS/Hostapp-Visio 2010,Q13357,Authorizer,Citrix access group for MS Visio 2010,
Hostapp-Visio 2010,KCUS,WinNT://KCUS/Hostapp-Visio 2010,Q14616,Authorizer,Citrix access group for MS Visio 2010,
Hostapp-Visio 2010,KCUS,WinNT://KCUS/Hostapp-Visio 2010,Q14718,Authorizer,Citrix access group for MS Visio 2010,
Hostapp-Visio 2010,KCUS,WinNT://KCUS/Hostapp-Visio 2010,Q15926,Authorizer,Citrix access group for MS Visio 2010,
Hostapp-Visio 2010,KCUS,WinNT://KCUS/Hostapp-Visio 2010,W49040,Delegate,Citrix access group for MS Visio 2010,
Hostapp-Visio 2010,KCUS,WinNT://KCUS/Hostapp-Visio 2010,E18722,Owner,Citrix access group for MS Visio 2010,
Hostapp-ie,KCUS,WinNT://KCUS/Hostapp-ie,E18722,Owner,Citrix access group for Internet Explorer,
Hostapp-ie,KCUS,WinNT://KCUS/Hostapp-ie,Q07708,Authorizer,Citrix access group for Internet Explorer,
Hostapp-ie,KCUS,WinNT://KCUS/Hostapp-ie,Q13357,Authorizer,Citrix access group for Internet Explorer,
Hostapp-ie,KCUS,WinNT://KCUS/Hostapp-ie,Q14616,Authorizer,Citrix access group for Internet Explorer,
Hostapp-ie,KCUS,WinNT://KCUS/Hostapp-ie,Q14718,Authorizer,Citrix access group for Internet Explorer,
Hostapp-ie,KCUS,WinNT://KCUS/Hostapp-ie,Q15926,Authorizer,Citrix access group for Internet Explorer,
Hostapp-ie,KCUS,WinNT://KCUS/Hostapp-ie,W49040,Delegate,Citrix access group for Internet Explorer,
Hostapp-SAP,KCUS,WinNT://KCUS/Hostapp-SAP,W49040,Delegate,Citrix access group for SAP,
Hostapp-SAP,KCUS,WinNT://KCUS/Hostapp-SAP,E18722,Owner,Citrix access group for SAP,
Hostapp-SAP,KCUS,WinNT://KCUS/Hostapp-SAP,Q07708,Authorizer,Citrix access group for SAP,
Hostapp-SAP,KCUS,WinNT://KCUS/Hostapp-SAP,Q13357,Authorizer,Citrix access group for SAP,
Hostapp-SAP,KCUS,WinNT://KCUS/Hostapp-SAP,Q14616,Authorizer,Citrix access group for SAP,
Hostapp-SAP,KCUS,WinNT://KCUS/Hostapp-SAP,Q14718,Authorizer,Citrix access group for SAP,
Hostapp-SAP,KCUS,WinNT://KCUS/Hostapp-SAP,Q15926,Authorizer,Citrix access group for SAP,
O365-OF3,KCUS,WinNT://KCUS/O365-OF3,E18722,Owner,This security group would be used for assigning F3 license to users,
O365-OF3,KCUS,WinNT://KCUS/O365-OF3,Q03582,Authorizer,This security group would be used for assigning F3 license to users,
O365-OF3,KCUS,WinNT://KCUS/O365-OF3,w62002,Delegate,This security group would be used for assigning F3 license to users,
O365-Tango-E3,KCUS,WinNT://KCUS/O365-Tango-E3,e18722,Owner,O365-Tango-E3,
O365-Tango-E3,KCUS,WinNT://KCUS/O365-Tango-E3,Q03582,Authorizer,O365-Tango-E3,
O365-Tango-E3,KCUS,WinNT://KCUS/O365-Tango-E3,w62002,Delegate,O365-Tango-E3,
GCPS-DBDBOs,KCUS,WinNT://KCUS/GCPS-DBDBOs,E18722,Owner,Allows DBO access to GCPS' databases,
GCPS-DBDBOs,KCUS,WinNT://KCUS/GCPS-DBDBOs,Q73491,Delegate,Allows DBO access to GCPS' databases,
Hostapp-TableauDesktop,KCUS,WinNT://KCUS/Hostapp-TableauDesktop,Q13357,Authorizer,Citrix access group for TableauDesktop application,
Hostapp-TableauDesktop,KCUS,WinNT://KCUS/Hostapp-TableauDesktop,Q14616,Authorizer,Citrix access group for TableauDesktop application,
Hostapp-TableauDesktop,KCUS,WinNT://KCUS/Hostapp-TableauDesktop,Q14718,Authorizer,Citrix access group for TableauDesktop application,
Hostapp-TableauDesktop,KCUS,WinNT://KCUS/Hostapp-TableauDesktop,Q15926,Authorizer,Citrix access group for TableauDesktop application,
Hostapp-TableauDesktop,KCUS,WinNT://KCUS/Hostapp-TableauDesktop,W49040,Delegate,Citrix access group for TableauDesktop application,
Hostapp-TableauDesktop,KCUS,WinNT://KCUS/Hostapp-TableauDesktop,E18722,Owner,Citrix access group for TableauDesktop application,
Hostapp-TableauDesktop,KCUS,WinNT://KCUS/Hostapp-TableauDesktop,Q07708,Authorizer,Citrix access group for TableauDesktop application,
Delegated Setup,KCUS,WinNT://KCUS/Delegated Setup,E18722,Owner,Members of this management role group have permissions to install and uninstall Exchange on provisioned servers. This role group shouldn't be deleted.,
Delegated Setup,KCUS,WinNT://KCUS/Delegated Setup,W62002,Delegate,Members of this management role group have permissions to install and uninstall Exchange on provisioned servers. This role group shouldn't be deleted.,
O365 Legal Compliance,KCUS,WinNT://KCUS/O365 Legal Compliance,B46959,Authorizer,Members of this group will be granted special compliance access in Office 365,
O365 Legal Compliance,KCUS,WinNT://KCUS/O365 Legal Compliance,E18722,Owner,Members of this group will be granted special compliance access in Office 365,
O365 Legal Compliance,KCUS,WinNT://KCUS/O365 Legal Compliance,W62002,Delegate,Members of this group will be granted special compliance access in Office 365,
HostApp-3PL-FileShare,KCUS,WinNT://KCUS/HostApp-3PL-FileShare,E18722,Owner,Citrix Application Security group for hosting Home Drive for 3PL Business Partners,
HostApp-3PL-FileShare,KCUS,WinNT://KCUS/HostApp-3PL-FileShare,Q07708,Authorizer,Citrix Application Security group for hosting Home Drive for 3PL Business Partners,
HostApp-3PL-FileShare,KCUS,WinNT://KCUS/HostApp-3PL-FileShare,Q14616,Authorizer,Citrix Application Security group for hosting Home Drive for 3PL Business Partners,
HostApp-3PL-FileShare,KCUS,WinNT://KCUS/HostApp-3PL-FileShare,W49040,Delegate,Citrix Application Security group for hosting Home Drive for 3PL Business Partners,
Internet Explorer Exception,KCUS,WinNT://KCUS/Internet Explorer Exception,E18722,Owner,This group will be used for handling IE exception.,
Internet Explorer Exception,KCUS,WinNT://KCUS/Internet Explorer Exception,Q39116,Authorizer,This group will be used for handling IE exception.,
Internet Explorer Exception,KCUS,WinNT://KCUS/Internet Explorer Exception,W48997,Delegate,This group will be used for handling IE exception.,
Mobile Platform Engineers,KCUS,WinNT://KCUS/Mobile Platform Engineers,E18722,Owner,Mobile Platform Engineers,
Mobile Platform Engineers,KCUS,WinNT://KCUS/Mobile Platform Engineers,W66442,Delegate,Mobile Platform Engineers,
O365-Suzano-E3M,KCUS,WinNT://KCUS/O365-Suzano-E3M,e18722,Owner,O365-Suzano-E3M,
O365-Suzano-E3M,KCUS,WinNT://KCUS/O365-Suzano-E3M,w62002,Delegate,O365-Suzano-E3M,
O365-PowerApps-Administrators,KCUS,WinNT://KCUS/O365-PowerApps-Administrators,E18722,Owner,Members of this group are Power Apps Service Administrators in Azure Portal,
O365-PowerApps-Administrators,KCUS,WinNT://KCUS/O365-PowerApps-Administrators,W62002,Delegate,Members of this group are Power Apps Service Administrators in Azure Portal,
PBI_LC_PROUSER,KCUS,WinNT://KCUS/PBI_LC_PROUSER,B03885,Owner,Power BI Licensing - Pro User,
PBI_LC_PROUSER,KCUS,WinNT://KCUS/PBI_LC_PROUSER,B31216,Authorizer,Power BI Licensing - Pro User,
PBI_LC_PROUSER,KCUS,WinNT://KCUS/PBI_LC_PROUSER,E18722,Authorizer,Power BI Licensing - Pro User,
PBI_LC_PROUSER,KCUS,WinNT://KCUS/PBI_LC_PROUSER,L37902,Delegate,Power BI Licensing - Pro User,
PBI_LC_PROUSER,KCUS,WinNT://KCUS/PBI_LC_PROUSER,L90398,Authorizer,Power BI Licensing - Pro User,
PBI_LC_PROUSER,KCUS,WinNT://KCUS/PBI_LC_PROUSER,U15405,Authorizer,Power BI Licensing - Pro User,
PBI_LC_PROUSER,KCUS,WinNT://KCUS/PBI_LC_PROUSER,W48386,Authorizer,Power BI Licensing - Pro User,
PBI_LC_PROUSER,KCUS,WinNT://KCUS/PBI_LC_PROUSER,W48951,Authorizer,Power BI Licensing - Pro User,
Defendpoint_Crisis_Devices,KCUS,WinNT://KCUS/Defendpoint_Crisis_Devices,E18722,Owner,This group will have primary devices of users who are part of Crisis groups.,
Defendpoint_Crisis_Devices,KCUS,WinNT://KCUS/Defendpoint_Crisis_Devices,W48997,Delegate,This group will have primary devices of users who are part of Crisis groups.,
HostAppMenu-M365LicenceTest,KCUS,WinNT://KCUS/HostAppMenu-M365LicenceTest,E18722,Owner,HostAppMenu-M365LicenceTest,
HostAppMenu-M365LicenceTest,KCUS,WinNT://KCUS/HostAppMenu-M365LicenceTest,W49040,Delegate,HostAppMenu-M365LicenceTest,
O365-Sky-For-Business-Admin,KCUS,WinNT://KCUS/O365-Sky-For-Business-Admin,E18722,Owner,Members of this Groups are SFB & Teams administrators,
O365-Sky-For-Business-Admin,KCUS,WinNT://KCUS/O365-Sky-For-Business-Admin,W62002,Delegate,Members of this Groups are SFB & Teams administrators,
Hostapp-OneNote M365,KCUS,WinNT://KCUS/Hostapp-OneNote M365,E18722,Owner,Citrix access group for MS OneNote M365,
Hostapp-OneNote M365,KCUS,WinNT://KCUS/Hostapp-OneNote M365,W49040,Delegate,Citrix access group for MS OneNote M365,
LBBEGrpAdmin,KCUS,WinNT://KCUS/LBBEGrpAdmin,E18722,Owner,Beirut  Lebanon-Site Group Administrators ,
LBBEGrpAdmin,KCUS,WinNT://KCUS/LBBEGrpAdmin,Q06085,Authorizer,Beirut  Lebanon-Site Group Administrators ,
LBBEGrpAdmin,KCUS,WinNT://KCUS/LBBEGrpAdmin,Q08911,Authorizer,Beirut  Lebanon-Site Group Administrators ,
LBBEGrpAdmin,KCUS,WinNT://KCUS/LBBEGrpAdmin,W48997,Delegate,Beirut  Lebanon-Site Group Administrators ,
O365 Stream Report Readers,KCUS,WinNT://KCUS/O365 Stream Report Readers,E18722,Owner,This group will have access to audit logs in O365 portal to monitor and investigate actions taken in Stream.,
O365 Stream Report Readers,KCUS,WinNT://KCUS/O365 Stream Report Readers,W49040,Delegate,This group will have access to audit logs in O365 portal to monitor and investigate actions taken in Stream.,
UserSegmentationRead,KCUS,WinNT://KCUS/UserSegmentationRead,E18722,Owner,Microsoft User Licensing Segmentation Read,
UserSegmentationRead,KCUS,WinNT://KCUS/UserSegmentationRead,W62002,Delegate,Microsoft User Licensing Segmentation Read,
UserSegmentationChange,KCUS,WinNT://KCUS/UserSegmentationChange,E18722,Owner,Microsoft User Licensing Segmentation Change,
UserSegmentationChange,KCUS,WinNT://KCUS/UserSegmentationChange,W62002,Delegate,Microsoft User Licensing Segmentation Change,
Hostapp-HRportal,KCUS,WinNT://KCUS/Hostapp-HRportal,E18722,Owner,Citrix access group for MyHRportal,
Hostapp-HRportal,KCUS,WinNT://KCUS/Hostapp-HRportal,Q07708,Authorizer,Citrix access group for MyHRportal,
Hostapp-HRportal,KCUS,WinNT://KCUS/Hostapp-HRportal,Q14616,Authorizer,Citrix access group for MyHRportal,
Hostapp-HRportal,KCUS,WinNT://KCUS/Hostapp-HRportal,W49040,Delegate,Citrix access group for MyHRportal,
HostApp-Plant,KCUS,WinNT://KCUS/HostApp-Plant,Q15926,Authorizer,Citrix Application security group for Plant apps,
HostApp-Plant,KCUS,WinNT://KCUS/HostApp-Plant,W49040,Delegate,Citrix Application security group for Plant apps,
HostApp-Plant,KCUS,WinNT://KCUS/HostApp-Plant,E18722,Owner,Citrix Application security group for Plant apps,
HostApp-Plant,KCUS,WinNT://KCUS/HostApp-Plant,Q07708,Authorizer,Citrix Application security group for Plant apps,
HostApp-Plant,KCUS,WinNT://KCUS/HostApp-Plant,Q13357,Authorizer,Citrix Application security group for Plant apps,
HostApp-Plant,KCUS,WinNT://KCUS/HostApp-Plant,Q14616,Authorizer,Citrix Application security group for Plant apps,
HostApp-Plant,KCUS,WinNT://KCUS/HostApp-Plant,Q14718,Authorizer,Citrix Application security group for Plant apps,
O365 SharePoint Administrators,KCUS,WinNT://KCUS/O365 SharePoint Administrators,E18722,Owner,Members of this group will be granted SharePoint administrator access in Office 365.,
O365 SharePoint Administrators,KCUS,WinNT://KCUS/O365 SharePoint Administrators,W49040,Delegate,Members of this group will be granted SharePoint administrator access in Office 365.,
Admin Studio Development Catalog Users,KCUS,WinNT://KCUS/Admin Studio Development Catalog Users,E18722,Owner,Users who have access to the Admin Studio Development Catalog,
Admin Studio Development Catalog Users,KCUS,WinNT://KCUS/Admin Studio Development Catalog Users,W48997,Delegate,Users who have access to the Admin Studio Development Catalog,
HostApp-PublishedDesktop,KCUS,WinNT://KCUS/HostApp-PublishedDesktop,E18722,Owner,Citrix Application Security group for Published Desktop,
HostApp-PublishedDesktop,KCUS,WinNT://KCUS/HostApp-PublishedDesktop,Q07708,Authorizer,Citrix Application Security group for Published Desktop,
HostApp-PublishedDesktop,KCUS,WinNT://KCUS/HostApp-PublishedDesktop,Q14616,Authorizer,Citrix Application Security group for Published Desktop,
HostApp-PublishedDesktop,KCUS,WinNT://KCUS/HostApp-PublishedDesktop,W49040,Delegate,Citrix Application Security group for Published Desktop,
O365 ITS Compliance,KCUS,WinNT://KCUS/O365 ITS Compliance,B46959,Authorizer,Members of this group will be granted special compliance access in Office 365,
O365 ITS Compliance,KCUS,WinNT://KCUS/O365 ITS Compliance,E18722,Owner,Members of this group will be granted special compliance access in Office 365,
O365 ITS Compliance,KCUS,WinNT://KCUS/O365 ITS Compliance,W62002,Delegate,Members of this group will be granted special compliance access in Office 365,
Admin Vdi,KCUS,WinNT://KCUS/Admin Vdi,E18722,Owner,Manage VDI access in VM console,
Admin Vdi,KCUS,WinNT://KCUS/Admin Vdi,Q13357,Authorizer,Manage VDI access in VM console,
Admin Vdi,KCUS,WinNT://KCUS/Admin Vdi,Q14718,Authorizer,Manage VDI access in VM console,
Admin Vdi,KCUS,WinNT://KCUS/Admin Vdi,Q15926,Authorizer,Manage VDI access in VM console,
Admin Vdi,KCUS,WinNT://KCUS/Admin Vdi,W49040,Delegate,Manage VDI access in VM console,
KCFILESWksAdmin,KCUS,WinNT://KCUS/KCFILESWksAdmin,E18722,Owner,KCFILES -ICW Computer Account Administrators,
KCFILESWksAdmin,KCUS,WinNT://KCUS/KCFILESWksAdmin,W48997,Delegate,KCFILES -ICW Computer Account Administrators,
O365-Tango-E3M,KCUS,WinNT://KCUS/O365-Tango-E3M,e18722,Owner,O365-Tango-E3M,
O365-Tango-E3M,KCUS,WinNT://KCUS/O365-Tango-E3M,Q03582,Authorizer,O365-Tango-E3M,
O365-Tango-E3M,KCUS,WinNT://KCUS/O365-Tango-E3M,w62002,Delegate,O365-Tango-E3M,
Corporate Mobile Device Agreement - Saudi Arabia,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement - Saudi Arabia,E18722,Owner,Use this group for people who signed the PMDA in Saudi Arabia,
Corporate Mobile Device Agreement - Saudi Arabia,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement - Saudi Arabia,E32872,Delegate,Use this group for people who signed the PMDA in Saudi Arabia,
FC-FullGenUsers,KCUS,WinNT://KCUS/FC-FullGenUsers,E18722,Owner,Full Clone General Users,
FC-FullGenUsers,KCUS,WinNT://KCUS/FC-FullGenUsers,Q07708,Authorizer,Full Clone General Users,
FC-FullGenUsers,KCUS,WinNT://KCUS/FC-FullGenUsers,Q13357,Authorizer,Full Clone General Users,
FC-FullGenUsers,KCUS,WinNT://KCUS/FC-FullGenUsers,Q14616,Authorizer,Full Clone General Users,
FC-FullGenUsers,KCUS,WinNT://KCUS/FC-FullGenUsers,Q14718,Authorizer,Full Clone General Users,
FC-FullGenUsers,KCUS,WinNT://KCUS/FC-FullGenUsers,Q15926,Authorizer,Full Clone General Users,
FC-FullGenUsers,KCUS,WinNT://KCUS/FC-FullGenUsers,W49040,Delegate,Full Clone General Users,
GPO - Test Group 3,KCUS,WinNT://KCUS/GPO - Test Group 3,E18722,Owner,This group will be used for testing Group Policy Objects in DEV/QA before they are production ready,
GPO - Test Group 3,KCUS,WinNT://KCUS/GPO - Test Group 3,M20354,Authorizer,This group will be used for testing Group Policy Objects in DEV/QA before they are production ready,
GPO - Test Group 3,KCUS,WinNT://KCUS/GPO - Test Group 3,Q04499,Authorizer,This group will be used for testing Group Policy Objects in DEV/QA before they are production ready,
GPO - Test Group 3,KCUS,WinNT://KCUS/GPO - Test Group 3,Q39116,Authorizer,This group will be used for testing Group Policy Objects in DEV/QA before they are production ready,
GPO - Test Group 3,KCUS,WinNT://KCUS/GPO - Test Group 3,W48997,Delegate,This group will be used for testing Group Policy Objects in DEV/QA before they are production ready,
FC-SAPSupportUsers,KCUS,WinNT://KCUS/FC-SAPSupportUsers,E18722,Owner,SAP Support team VDI users,
FC-SAPSupportUsers,KCUS,WinNT://KCUS/FC-SAPSupportUsers,Q07708,Authorizer,SAP Support team VDI users,
FC-SAPSupportUsers,KCUS,WinNT://KCUS/FC-SAPSupportUsers,Q13357,Authorizer,SAP Support team VDI users,
FC-SAPSupportUsers,KCUS,WinNT://KCUS/FC-SAPSupportUsers,Q14616,Authorizer,SAP Support team VDI users,
FC-SAPSupportUsers,KCUS,WinNT://KCUS/FC-SAPSupportUsers,Q14718,Authorizer,SAP Support team VDI users,
FC-SAPSupportUsers,KCUS,WinNT://KCUS/FC-SAPSupportUsers,Q15926,Authorizer,SAP Support team VDI users,
FC-SAPSupportUsers,KCUS,WinNT://KCUS/FC-SAPSupportUsers,W49040,Delegate,SAP Support team VDI users,
Admin ECCM Support_sids,KCUS,WinNT://KCUS/Admin ECCM Support_sids,E18722,Owner,Sid group for ECCM Support,
Admin ECCM Support_sids,KCUS,WinNT://KCUS/Admin ECCM Support_sids,W48997,Delegate,Sid group for ECCM Support,
DL006046,KCUS,WinNT://KCUS/DL006046,E18722,Owner,+ITS  TCS_Desktop Services_Neenah,
DL006046,KCUS,WinNT://KCUS/DL006046,Q06085,Authorizer,+ITS  TCS_Desktop Services_Neenah,
DL006046,KCUS,WinNT://KCUS/DL006046,Q08911,Authorizer,+ITS  TCS_Desktop Services_Neenah,
DL006046,KCUS,WinNT://KCUS/DL006046,W66442,Delegate,+ITS  TCS_Desktop Services_Neenah,
DL006047,KCUS,WinNT://KCUS/DL006047,E18722,Owner,+ITS  TCS_Desktop Services_ODC,
DL006047,KCUS,WinNT://KCUS/DL006047,Q06085,Authorizer,+ITS  TCS_Desktop Services_ODC,
DL006047,KCUS,WinNT://KCUS/DL006047,Q06371,Authorizer,+ITS  TCS_Desktop Services_ODC,
DL006047,KCUS,WinNT://KCUS/DL006047,Q08911,Authorizer,+ITS  TCS_Desktop Services_ODC,
DL006047,KCUS,WinNT://KCUS/DL006047,W66442,Delegate,+ITS  TCS_Desktop Services_ODC,
Admin ECCM Engineers,KCUS,WinNT://KCUS/Admin ECCM Engineers,E18722,Owner,Administration for PCC Engineers,
Admin ECCM Engineers,KCUS,WinNT://KCUS/Admin ECCM Engineers,W48997,Delegate,Administration for PCC Engineers,
DL010352,KCUS,WinNT://KCUS/DL010352,E18722,Owner,+NA_Marketing  Web Analytics Team,
DL010352,KCUS,WinNT://KCUS/DL010352,W62002,Delegate,+NA_Marketing  Web Analytics Team,
IT_User_Information_DEV _Agents,KCUS,WinNT://KCUS/IT_User_Information_DEV _Agents,B00932,Delegate,Perform lookups of general user information plus additional service desk activities - dev,
IT_User_Information_DEV _Agents,KCUS,WinNT://KCUS/IT_User_Information_DEV _Agents,E18722,Owner,Perform lookups of general user information plus additional service desk activities - dev,
DESKTOP-ENGINEERING-TCS,KCUS,WinNT://KCUS/DESKTOP-ENGINEERING-TCS,E18722,Owner,Service now group for handling Engineering related change/problem/incident for Desktop services,
DESKTOP-ENGINEERING-TCS,KCUS,WinNT://KCUS/DESKTOP-ENGINEERING-TCS,Q06085,Delegate,Service now group for handling Engineering related change/problem/incident for Desktop services,
DESKTOP-ENGINEERING-TCS,KCUS,WinNT://KCUS/DESKTOP-ENGINEERING-TCS,Q08911,Authorizer,Service now group for handling Engineering related change/problem/incident for Desktop services,
DESKTOP-SERVICES-KC,KCUS,WinNT://KCUS/DESKTOP-SERVICES-KC,E18722,Owner,KC Desktop Services Group,
DESKTOP-SERVICES-KC,KCUS,WinNT://KCUS/DESKTOP-SERVICES-KC,W66442,Delegate,KC Desktop Services Group,
O365 Skype Administrators,KCUS,WinNT://KCUS/O365 Skype Administrators,B46959,Authorizer,Members of this group will be Skype Administrators in Office 365,
O365 Skype Administrators,KCUS,WinNT://KCUS/O365 Skype Administrators,B91148,Authorizer,Members of this group will be Skype Administrators in Office 365,
O365 Skype Administrators,KCUS,WinNT://KCUS/O365 Skype Administrators,E18722,Owner,Members of this group will be Skype Administrators in Office 365,
O365 Skype Administrators,KCUS,WinNT://KCUS/O365 Skype Administrators,W62002,Delegate,Members of this group will be Skype Administrators in Office 365,
MCOE_Reporting_DB_Public_Access,KCUS,WinNT://KCUS/MCOE_Reporting_DB_Public_Access,E18722,Owner,MCOE_Reporting_DB_Public_Access,
MCOE_Reporting_DB_Public_Access,KCUS,WinNT://KCUS/MCOE_Reporting_DB_Public_Access,E32872,Delegate,MCOE_Reporting_DB_Public_Access,
MCOE_Reporting_DB_Public_Access,KCUS,WinNT://KCUS/MCOE_Reporting_DB_Public_Access,W48997,Authorizer,MCOE_Reporting_DB_Public_Access,
Admin ECCM Engineers_sids,KCUS,WinNT://KCUS/Admin ECCM Engineers_sids,B46959,Authorizer,Administration for PCC Engineers SIDs,
Admin ECCM Engineers_sids,KCUS,WinNT://KCUS/Admin ECCM Engineers_sids,E18722,Owner,Administration for PCC Engineers SIDs,
Admin ECCM Engineers_sids,KCUS,WinNT://KCUS/Admin ECCM Engineers_sids,W48997,Delegate,Administration for PCC Engineers SIDs,
Corporate Mobile Device Agreement - South Africa,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement - South Africa,E18722,Owner,Use this group for people who have signed the PMDA in South Africa,
Corporate Mobile Device Agreement - South Africa,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement - South Africa,E32872,Delegate,Use this group for people who have signed the PMDA in South Africa,
AP_MY_CorporateMobile_Users,KCUS,WinNT://KCUS/AP_MY_CorporateMobile_Users,E18722,Owner,Use for BYOD users in AP,
AP_MY_CorporateMobile_Users,KCUS,WinNT://KCUS/AP_MY_CorporateMobile_Users,E32872,Delegate,Use for BYOD users in AP,
ACC Citrix Admins,KCINET,WinNT://KCINET/ACC Citrix Admins,E18722,Owner,Service Desk L1 Engineer Citrix Access,
ACC Citrix Admins,KCINET,WinNT://KCINET/ACC Citrix Admins,W49040,Delegate,Service Desk L1 Engineer Citrix Access,
IT_User_Information_DEV_Admins,KCUS,WinNT://KCUS/IT_User_Information_DEV_Admins,B00932,Delegate,Perform lookups of general user information plus additional administration of the application - dev,
IT_User_Information_DEV_Admins,KCUS,WinNT://KCUS/IT_User_Information_DEV_Admins,E18722,Owner,Perform lookups of general user information plus additional administration of the application - dev,
Mobile Email Access Corporate,KCUS,WinNT://KCUS/Mobile Email Access Corporate,E18722,Owner,Use this group for BYOD users,
Mobile Email Access Corporate,KCUS,WinNT://KCUS/Mobile Email Access Corporate,W62002,Delegate,Use this group for BYOD users,
AP_SG_CorporateMobile_Users,KCUS,WinNT://KCUS/AP_SG_CorporateMobile_Users,E18722,Owner,Use this group fro BYOD users in AP_SG,
AP_SG_CorporateMobile_Users,KCUS,WinNT://KCUS/AP_SG_CorporateMobile_Users,E32872,Delegate,Use this group fro BYOD users in AP_SG,
Corporate Mobile Device Agreement - Ukraine,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement - Ukraine,E18722,Owner,Use this group for people who have signed the PMDA in Ukraine,
Corporate Mobile Device Agreement - Ukraine,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement - Ukraine,E32872,Delegate,Use this group for people who have signed the PMDA in Ukraine,
O365-VisioPro,KCUS,WinNT://KCUS/O365-VisioPro,E18722,Owner,O365 Visio Plan 2 Subscription Users,
O365-VisioPro,KCUS,WinNT://KCUS/O365-VisioPro,Q04499,Authorizer,O365 Visio Plan 2 Subscription Users,
O365-VisioPro,KCUS,WinNT://KCUS/O365-VisioPro,Q06371,Authorizer,O365 Visio Plan 2 Subscription Users,
O365-VisioPro,KCUS,WinNT://KCUS/O365-VisioPro,Q39116,Authorizer,O365 Visio Plan 2 Subscription Users,
O365-VisioPro,KCUS,WinNT://KCUS/O365-VisioPro,W48997,Delegate,O365 Visio Plan 2 Subscription Users,
O365-VisioPro,KCUS,WinNT://KCUS/O365-VisioPro,W66442,Authorizer,O365 Visio Plan 2 Subscription Users,
MachineAccountCreation-VirtualDesktop,KCUS,WinNT://KCUS/MachineAccountCreation-VirtualDesktop,E18722,Owner,Create Virtual Desktop Machine accounts,
MachineAccountCreation-VirtualDesktop,KCUS,WinNT://KCUS/MachineAccountCreation-VirtualDesktop,W49040,Delegate,Create Virtual Desktop Machine accounts,
DL001637,KCUS,WinNT://KCUS/DL001637,E18722,Owner,+NA_EDMT  QAA Non-Application Owners,
DL001637,KCUS,WinNT://KCUS/DL001637,Q06085,Authorizer,+NA_EDMT  QAA Non-Application Owners,
DL001637,KCUS,WinNT://KCUS/DL001637,Q08911,Authorizer,+NA_EDMT  QAA Non-Application Owners,
DL001637,KCUS,WinNT://KCUS/DL001637,W62002,Delegate,+NA_EDMT  QAA Non-Application Owners,
DL001704,KCUS,WinNT://KCUS/DL001704,E18722,Owner,+NA_ITS  Tip of the Week,
DL001704,KCUS,WinNT://KCUS/DL001704,Q06085,Authorizer,+NA_ITS  Tip of the Week,
DL001704,KCUS,WinNT://KCUS/DL001704,Q08911,Authorizer,+NA_ITS  Tip of the Week,
DL001704,KCUS,WinNT://KCUS/DL001704,W62002,Delegate,+NA_ITS  Tip of the Week,
DL001720,KCUS,WinNT://KCUS/DL001720,E18722,Owner,+NA_EDMT  QRB,
DL001720,KCUS,WinNT://KCUS/DL001720,Q06085,Authorizer,+NA_EDMT  QRB,
DL001720,KCUS,WinNT://KCUS/DL001720,Q08911,Authorizer,+NA_EDMT  QRB,
DL001720,KCUS,WinNT://KCUS/DL001720,W62002,Delegate,+NA_EDMT  QRB,
DL001770,KCUS,WinNT://KCUS/DL001770,E18722,Owner,+NA_EDMT  This Wk at Desktop,
DL001770,KCUS,WinNT://KCUS/DL001770,Q06085,Authorizer,+NA_EDMT  This Wk at Desktop,
DL001770,KCUS,WinNT://KCUS/DL001770,Q08911,Authorizer,+NA_EDMT  This Wk at Desktop,
DL001770,KCUS,WinNT://KCUS/DL001770,W66442,Delegate,+NA_EDMT  This Wk at Desktop,
DL001805,KCUS,WinNT://KCUS/DL001805,b10717,Authorizer,+NA_ITS  Logon Script Changes,
DL001805,KCUS,WinNT://KCUS/DL001805,E18722,Owner,+NA_ITS  Logon Script Changes,
DL001805,KCUS,WinNT://KCUS/DL001805,W62002,Delegate,+NA_ITS  Logon Script Changes,
DL001806,KCUS,WinNT://KCUS/DL001806,E18722,Owner,+NA_ITS  GPO Changes,
DL001806,KCUS,WinNT://KCUS/DL001806,W62002,Delegate,+NA_ITS  GPO Changes,
DL001916,KCUS,WinNT://KCUS/DL001916,E18722,Owner,+NA_EDMT  QAA PC Coordinators,
DL001916,KCUS,WinNT://KCUS/DL001916,Q06085,Authorizer,+NA_EDMT  QAA PC Coordinators,
DL001916,KCUS,WinNT://KCUS/DL001916,Q08911,Authorizer,+NA_EDMT  QAA PC Coordinators,
DL001916,KCUS,WinNT://KCUS/DL001916,W62002,Delegate,+NA_EDMT  QAA PC Coordinators,
DL001973,KCUS,WinNT://KCUS/DL001973,E18722,Owner,+NA_EDMT  QAA Desktop Update,
DL001973,KCUS,WinNT://KCUS/DL001973,W66442,Delegate,+NA_EDMT  QAA Desktop Update,
Corporate Mobile Device Agreement – Russia,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement – Russia,E18722,Owner,Use this group for people in Russia who have signed the PMDA,
Corporate Mobile Device Agreement – Russia,KCUS,WinNT://KCUS/Corporate Mobile Device Agreement – Russia,E32872,Delegate,Use this group for people in Russia who have signed the PMDA,
DL006621,KCUS,WinNT://KCUS/DL006621,E18722,Owner,+NA_ITS  Desktop App Testing Comm,
DL006621,KCUS,WinNT://KCUS/DL006621,W66442,Delegate,+NA_ITS  Desktop App Testing Comm,
HostAppMenu-ClarityWorkbench,KCUS,WinNT://KCUS/HostAppMenu-ClarityWorkbench,E18722,Owner,Citrix Application Security Group for Clarity Open Workbench,
HostAppMenu-ClarityWorkbench,KCUS,WinNT://KCUS/HostAppMenu-ClarityWorkbench,Q07708,Authorizer,Citrix Application Security Group for Clarity Open Workbench,
HostAppMenu-ClarityWorkbench,KCUS,WinNT://KCUS/HostAppMenu-ClarityWorkbench,Q13357,Authorizer,Citrix Application Security Group for Clarity Open Workbench,
HostAppMenu-ClarityWorkbench,KCUS,WinNT://KCUS/HostAppMenu-ClarityWorkbench,Q14616,Authorizer,Citrix Application Security Group for Clarity Open Workbench,
HostAppMenu-ClarityWorkbench,KCUS,WinNT://KCUS/HostAppMenu-ClarityWorkbench,Q14718,Authorizer,Citrix Application Security Group for Clarity Open Workbench,
HostAppMenu-ClarityWorkbench,KCUS,WinNT://KCUS/HostAppMenu-ClarityWorkbench,Q15926,Authorizer,Citrix Application Security Group for Clarity Open Workbench,
HostAppMenu-ClarityWorkbench,KCUS,WinNT://KCUS/HostAppMenu-ClarityWorkbench,w49040,Delegate,Citrix Application Security Group for Clarity Open Workbench,
HostAppMenu-TMCitrixDesktop,KCUS,WinNT://KCUS/HostAppMenu-TMCitrixDesktop,E18722,Owner,Citrix Desktop access for TechM users,
HostAppMenu-TMCitrixDesktop,KCUS,WinNT://KCUS/HostAppMenu-TMCitrixDesktop,Q14616,Authorizer,Citrix Desktop access for TechM users,
HostAppMenu-TMCitrixDesktop,KCUS,WinNT://KCUS/HostAppMenu-TMCitrixDesktop,W49040,Delegate,Citrix Desktop access for TechM users,
O365-Apps-AddOn,KCUS,WinNT://KCUS/O365-Apps-AddOn,E18722,Owner,To be assigned to E3M users who require Office Full client,
O365-Apps-AddOn,KCUS,WinNT://KCUS/O365-Apps-AddOn,w49040,Delegate,To be assigned to E3M users who require Office Full client,
O365-Apps-AddOn,KCUS,WinNT://KCUS/O365-Apps-AddOn,W62002,Authorizer,To be assigned to E3M users who require Office Full client,
WS-PACKCCCOM-D,KCUS,WinNT://KCUS/WS-PACKCCCOM-D,W66442,Delegate,The group is used for Pac.Kcc.Com change access,
WS-PACKCCCOM-D,KCUS,WinNT://KCUS/WS-PACKCCCOM-D,E18722,Owner,The group is used for Pac.Kcc.Com change access,
WS-PACKCCCOM-D,KCUS,WinNT://KCUS/WS-PACKCCCOM-D,Q04499,Authorizer,The group is used for Pac.Kcc.Com change access,
WS-PACKCCCOM-Q,KCUS,WinNT://KCUS/WS-PACKCCCOM-Q,E18722,Owner,The group is used for Pac.Kcc.Com change access,
WS-PACKCCCOM-Q,KCUS,WinNT://KCUS/WS-PACKCCCOM-Q,Q04499,Authorizer,The group is used for Pac.Kcc.Com change access,
WS-PACKCCCOM-Q,KCUS,WinNT://KCUS/WS-PACKCCCOM-Q,W66442,Delegate,The group is used for Pac.Kcc.Com change access,
Release management - Ring 1,KCUS,WinNT://KCUS/Release management - Ring 1,E18722,Owner,This group include Desktop Team (TCS & KC),
Release management - Ring 1,KCUS,WinNT://KCUS/Release management - Ring 1,Q04499,Delegate,This group include Desktop Team (TCS & KC),
Release management - Ring 1,KCUS,WinNT://KCUS/Release management - Ring 1,Q06085,Authorizer,This group include Desktop Team (TCS & KC),
Release management - Ring 1,KCUS,WinNT://KCUS/Release management - Ring 1,Q08911,Authorizer,This group include Desktop Team (TCS & KC),
VMware_Vcenter_DesktopAdmin,KCUS,WinNT://KCUS/VMware_Vcenter_DesktopAdmin,E18722,Owner,Used for accessesing the VMware central console for managmeent.,
VMware_Vcenter_DesktopAdmin,KCUS,WinNT://KCUS/VMware_Vcenter_DesktopAdmin,W49040,Delegate,Used for accessesing the VMware central console for managmeent.,
VCenter Access for Desktop team,KCUS,WinNT://KCUS/VCenter Access for Desktop team,E18722,Owner,To gain access for VMware Cluster for Desktop team.,
VCenter Access for Desktop team,KCUS,WinNT://KCUS/VCenter Access for Desktop team,Q06085,Delegate,To gain access for VMware Cluster for Desktop team.,
dt9ARA.kcmkt.com,KCINET,WinNT://KCINET/dt9ARA.kcmkt.com,B57752,Delegate,Secure data transport with (WS_FTP) Arrow,
dt9ARA.kcmkt.com,KCINET,WinNT://KCINET/dt9ARA.kcmkt.com,B98619,Owner,Secure data transport with (WS_FTP) Arrow,
dt9ARA.kcmkt.com,KCINET,WinNT://KCINET/dt9ARA.kcmkt.com,E18722,Authorizer,Secure data transport with (WS_FTP) Arrow,
dt9ARA.kcmkt.com,KCINET,WinNT://KCINET/dt9ARA.kcmkt.com,Q08911,Authorizer,Secure data transport with (WS_FTP) Arrow,
GPO - PAC QA,KCUS,WinNT://KCUS/GPO - PAC QA,E18722,Owner,Secuirty group for testing PAC file in QA environment,
GPO - PAC QA,KCUS,WinNT://KCUS/GPO - PAC QA,Q04499,Authorizer,Secuirty group for testing PAC file in QA environment,
GPO - PAC QA,KCUS,WinNT://KCUS/GPO - PAC QA,Q06085,Authorizer,Secuirty group for testing PAC file in QA environment,
GPO - PAC QA,KCUS,WinNT://KCUS/GPO - PAC QA,Q08911,Authorizer,Secuirty group for testing PAC file in QA environment,
GPO - PAC QA,KCUS,WinNT://KCUS/GPO - PAC QA,W48997,Delegate,Secuirty group for testing PAC file in QA environment,
KC-Desktop-VPro-Admins,KCUS,WinNT://KCUS/KC-Desktop-VPro-Admins,E18722,Owner,This group will manage the server owners for Vpro EMA servers,
KC-Desktop-VPro-Admins,KCUS,WinNT://KCUS/KC-Desktop-VPro-Admins,w48997,Delegate,This group will manage the server owners for Vpro EMA servers,
DormantUsers-PowerAppsFormAccess,KCUS,WinNT://KCUS/DormantUsers-PowerAppsFormAccess,E18722,Owner,This security Group would be used for providing PowerApps Form access to Dormant users,
DormantUsers-PowerAppsFormAccess,KCUS,WinNT://KCUS/DormantUsers-PowerAppsFormAccess,W62002,Delegate,This security Group would be used for providing PowerApps Form access to Dormant users,
O365-AzureLicenseMonitor,KCUS,WinNT://KCUS/O365-AzureLicenseMonitor,E18722,Delegate,The security group will be used to access Azure Key vault.,
O365-AzureLicenseMonitor,KCUS,WinNT://KCUS/O365-AzureLicenseMonitor,W62642,Owner,The security group will be used to access Azure Key vault.,
OneDrive Shared Distribution,KCUS,WinNT://KCUS/OneDrive Shared Distribution,E18722,Owner,Group used for deploying OneDrive migration tool to GD shared PC users. Members added or removed by OneDrive for shared distribution program.,
OneDrive Shared Distribution,KCUS,WinNT://KCUS/OneDrive Shared Distribution,Q07749,Authorizer,Group used for deploying OneDrive migration tool to GD shared PC users. Members added or removed by OneDrive for shared distribution program.,
OneDrive Shared Distribution,KCUS,WinNT://KCUS/OneDrive Shared Distribution,Q13243,Authorizer,Group used for deploying OneDrive migration tool to GD shared PC users. Members added or removed by OneDrive for shared distribution program.,
OneDrive Shared Distribution,KCUS,WinNT://KCUS/OneDrive Shared Distribution,Q14717,Authorizer,Group used for deploying OneDrive migration tool to GD shared PC users. Members added or removed by OneDrive for shared distribution program.,
OneDrive Shared Distribution,KCUS,WinNT://KCUS/OneDrive Shared Distribution,Q39116,Authorizer,Group used for deploying OneDrive migration tool to GD shared PC users. Members added or removed by OneDrive for shared distribution program.,
OneDrive Shared Distribution,KCUS,WinNT://KCUS/OneDrive Shared Distribution,W66442,Delegate,Group used for deploying OneDrive migration tool to GD shared PC users. Members added or removed by OneDrive for shared distribution program.,
OneDrive Shared Migration Admin,KCUS,WinNT://KCUS/OneDrive Shared Migration Admin,E18722,Owner,To assign as admin of OneDrive migration tool distribution program for GD shared who can change tool settings,
OneDrive Shared Migration Admin,KCUS,WinNT://KCUS/OneDrive Shared Migration Admin,W48997,Authorizer,To assign as admin of OneDrive migration tool distribution program for GD shared who can change tool settings,
OneDrive Shared Migration Admin,KCUS,WinNT://KCUS/OneDrive Shared Migration Admin,W66442,Delegate,To assign as admin of OneDrive migration tool distribution program for GD shared who can change tool settings,
Endpoint Security Test 2,KCUS,WinNT://KCUS/Endpoint Security Test 2,E18722,Owner,Testing group for Endpoint security and password wizard,
Endpoint Security Test 2,KCUS,WinNT://KCUS/Endpoint Security Test 2,Q06371,Delegate,Testing group for Endpoint security and password wizard,
O365-ME3-SME,KCUS,WinNT://KCUS/O365-ME3-SME,B46959,Authorizer,Members of this group are enabled with extended plans,
O365-ME3-SME,KCUS,WinNT://KCUS/O365-ME3-SME,E18722,Owner,Members of this group are enabled with extended plans,
O365-ME3-SME,KCUS,WinNT://KCUS/O365-ME3-SME,W62002,Delegate,Members of this group are enabled with extended plans,
HostServerAdmin-SAPDS,KCUS,WinNT://KCUS/HostServerAdmin-SAPDS,E18722,Owner,Local administrator privilege SAP DS Citrix Servers,
HostServerAdmin-SAPDS,KCUS,WinNT://KCUS/HostServerAdmin-SAPDS,W49040,Delegate,Local administrator privilege SAP DS Citrix Servers,
PBI_WS_GL_tyGraph_Default_Restricted_VIEWER_P,KCUS,WinNT://KCUS/PBI_WS_GL_tyGraph_Default_Restricted_VIEWER_P,B46959,Delegate,Members of this group will be granted access to the Power BI Workspace_ GL tyGraph Default–Restricted–View–PROD,
PBI_WS_GL_tyGraph_Default_Restricted_VIEWER_P,KCUS,WinNT://KCUS/PBI_WS_GL_tyGraph_Default_Restricted_VIEWER_P,E18722,Owner,Members of this group will be granted access to the Power BI Workspace_ GL tyGraph Default–Restricted–View–PROD,
PBI_WS_GL_tyGraph_Default_Restricted_VIEWER_N,KCUS,WinNT://KCUS/PBI_WS_GL_tyGraph_Default_Restricted_VIEWER_N,B46959,Delegate,GL tyGraph Default–Restricted–View–DEV,
PBI_WS_GL_tyGraph_Default_Restricted_VIEWER_N,KCUS,WinNT://KCUS/PBI_WS_GL_tyGraph_Default_Restricted_VIEWER_N,E18722,Owner,GL tyGraph Default–Restricted–View–DEV,
GPO - PAC Development,KCUS,WinNT://KCUS/GPO - PAC Development,E18722,Owner,Security group for PAC file testing for development environment,
GPO - PAC Development,KCUS,WinNT://KCUS/GPO - PAC Development,Q04499,Authorizer,Security group for PAC file testing for development environment,
GPO - PAC Development,KCUS,WinNT://KCUS/GPO - PAC Development,Q06085,Authorizer,Security group for PAC file testing for development environment,
GPO - PAC Development,KCUS,WinNT://KCUS/GPO - PAC Development,Q08911,Authorizer,Security group for PAC file testing for development environment,
GPO - PAC Development,KCUS,WinNT://KCUS/GPO - PAC Development,W48997,Delegate,Security group for PAC file testing for development environment,
Foxit PDF Editor Std,KCUS,WinNT://KCUS/Foxit PDF Editor Std,E18722,Owner,Foxit PhantomPDF Standard is a PDF solution. It is a replacement of Adobe Acrobat DC Standard,
Foxit PDF Editor Std,KCUS,WinNT://KCUS/Foxit PDF Editor Std,Q13243,Authorizer,Foxit PhantomPDF Standard is a PDF solution. It is a replacement of Adobe Acrobat DC Standard,
Foxit PDF Editor Std,KCUS,WinNT://KCUS/Foxit PDF Editor Std,W48997,Delegate,Foxit PhantomPDF Standard is a PDF solution. It is a replacement of Adobe Acrobat DC Standard,
SHAREPOINT_RPT_PROD_READ,KCUS,WinNT://KCUS/SHAREPOINT_RPT_PROD_READ,E18722,Owner,SHAREPOINT_RPT_PROD_READ,
SHAREPOINT_RPT_PROD_READ,KCUS,WinNT://KCUS/SHAREPOINT_RPT_PROD_READ,Q08911,Authorizer,SHAREPOINT_RPT_PROD_READ,
SHAREPOINT_RPT_PROD_READ,KCUS,WinNT://KCUS/SHAREPOINT_RPT_PROD_READ,W49040,Delegate,SHAREPOINT_RPT_PROD_READ,
DesktopWIPReplicate,KCUS,WinNT://KCUS/DesktopWIPReplicate,E18722,Owner,This group will be provided access to replicate in WIP/GDScript Admin tools,
DesktopWIPReplicate,KCUS,WinNT://KCUS/DesktopWIPReplicate,Q06085,Authorizer,This group will be provided access to replicate in WIP/GDScript Admin tools,
DesktopWIPReplicate,KCUS,WinNT://KCUS/DesktopWIPReplicate,Q08911,Authorizer,This group will be provided access to replicate in WIP/GDScript Admin tools,
DesktopWIPReplicate,KCUS,WinNT://KCUS/DesktopWIPReplicate,W66442,Delegate,This group will be provided access to replicate in WIP/GDScript Admin tools,
BitLockerRecoveryPasswordAdmin,KCUS,WinNT://KCUS/BitLockerRecoveryPasswordAdmin,E18722,Owner,Provides read-only access to the BitLocker Recovery Passwords stored in Active Directory,
BitLockerRecoveryPasswordAdmin,KCUS,WinNT://KCUS/BitLockerRecoveryPasswordAdmin,Q06085,Authorizer,Provides read-only access to the BitLocker Recovery Passwords stored in Active Directory,
BitLockerRecoveryPasswordAdmin,KCUS,WinNT://KCUS/BitLockerRecoveryPasswordAdmin,Q08911,Authorizer,Provides read-only access to the BitLocker Recovery Passwords stored in Active Directory,
BitLockerRecoveryPasswordAdmin,KCUS,WinNT://KCUS/BitLockerRecoveryPasswordAdmin,W48997,Delegate,Provides read-only access to the BitLocker Recovery Passwords stored in Active Directory,
BitLockerRecoveryPasswordAdmin,KCUS,WinNT://KCUS/BitLockerRecoveryPasswordAdmin,W66442,Authorizer,Provides read-only access to the BitLocker Recovery Passwords stored in Active Directory,
O365-DynRemoteA-User,KCUS,WinNT://KCUS/O365-DynRemoteA-User,E18722,Owner,O365-DynRemoteA-User,
O365-DynRemoteA-User,KCUS,WinNT://KCUS/O365-DynRemoteA-User,U15359,Authorizer,O365-DynRemoteA-User,
O365-DynRemoteA-User,KCUS,WinNT://KCUS/O365-DynRemoteA-User,W62002,Delegate,O365-DynRemoteA-User,
O365-ADAttribute-SiteAdministrators,KCUS,WinNT://KCUS/O365-ADAttribute-SiteAdministrators,E18722,Owner,O365-ADAttribute-SiteAdministrators,
O365-ADAttribute-SiteAdministrators,KCUS,WinNT://KCUS/O365-ADAttribute-SiteAdministrators,w62002,Delegate,O365-ADAttribute-SiteAdministrators,
O365 Preview Compliance,KCUS,WinNT://KCUS/O365 Preview Compliance,e18722,Owner,O365 Preview Compliance,
O365 Preview Compliance,KCUS,WinNT://KCUS/O365 Preview Compliance,w62002,Delegate,O365 Preview Compliance,
Office-365-PerDevice-Reclaim-Exception,KCUS,WinNT://KCUS/Office-365-PerDevice-Reclaim-Exception,Q06435,Authorizer,Devices in this group will be excluded from M365 Apps device license reclaim automation,
Office-365-PerDevice-Reclaim-Exception,KCUS,WinNT://KCUS/Office-365-PerDevice-Reclaim-Exception,Q39116,Authorizer,Devices in this group will be excluded from M365 Apps device license reclaim automation,
Office-365-PerDevice-Reclaim-Exception,KCUS,WinNT://KCUS/Office-365-PerDevice-Reclaim-Exception,W66442,Delegate,Devices in this group will be excluded from M365 Apps device license reclaim automation,
Office-365-PerDevice-Reclaim-Exception,KCUS,WinNT://KCUS/Office-365-PerDevice-Reclaim-Exception,E18722,Owner,Devices in this group will be excluded from M365 Apps device license reclaim automation,
HostApp-Access M365,KCUS,WinNT://KCUS/HostApp-Access M365,E18722,Owner,Citrix access group for M365,
HostApp-Access M365,KCUS,WinNT://KCUS/HostApp-Access M365,Q07708,Authorizer,Citrix access group for M365,
HostApp-Access M365,KCUS,WinNT://KCUS/HostApp-Access M365,Q13357,Authorizer,Citrix access group for M365,
HostApp-Access M365,KCUS,WinNT://KCUS/HostApp-Access M365,Q14616,Authorizer,Citrix access group for M365,
HostApp-Access M365,KCUS,WinNT://KCUS/HostApp-Access M365,Q14718,Authorizer,Citrix access group for M365,
HostApp-Access M365,KCUS,WinNT://KCUS/HostApp-Access M365,Q15926,Authorizer,Citrix access group for M365,
HostApp-Access M365,KCUS,WinNT://KCUS/HostApp-Access M365,W49040,Delegate,Citrix access group for M365,
Hostapp-Visio M365,KCUS,WinNT://KCUS/Hostapp-Visio M365,E18722,Owner,Citrix access group for M365,
Hostapp-Visio M365,KCUS,WinNT://KCUS/Hostapp-Visio M365,Q07708,Authorizer,Citrix access group for M365,
Hostapp-Visio M365,KCUS,WinNT://KCUS/Hostapp-Visio M365,Q13357,Authorizer,Citrix access group for M365,
Hostapp-Visio M365,KCUS,WinNT://KCUS/Hostapp-Visio M365,Q14616,Authorizer,Citrix access group for M365,
Hostapp-Visio M365,KCUS,WinNT://KCUS/Hostapp-Visio M365,Q14718,Authorizer,Citrix access group for M365,
Hostapp-Visio M365,KCUS,WinNT://KCUS/Hostapp-Visio M365,Q15926,Authorizer,Citrix access group for M365,
Hostapp-Visio M365,KCUS,WinNT://KCUS/Hostapp-Visio M365,W49040,Delegate,Citrix access group for M365,
HostAppmenu-Visio M365,KCUS,WinNT://KCUS/HostAppmenu-Visio M365,Q07708,Authorizer,Citrix User Security group for MS Visio,
HostAppmenu-Visio M365,KCUS,WinNT://KCUS/HostAppmenu-Visio M365,Q13357,Authorizer,Citrix User Security group for MS Visio,
HostAppmenu-Visio M365,KCUS,WinNT://KCUS/HostAppmenu-Visio M365,Q14616,Authorizer,Citrix User Security group for MS Visio,
HostAppmenu-Visio M365,KCUS,WinNT://KCUS/HostAppmenu-Visio M365,Q14718,Authorizer,Citrix User Security group for MS Visio,
HostAppmenu-Visio M365,KCUS,WinNT://KCUS/HostAppmenu-Visio M365,Q15926,Authorizer,Citrix User Security group for MS Visio,
HostAppmenu-Visio M365,KCUS,WinNT://KCUS/HostAppmenu-Visio M365,W49040,Delegate,Citrix User Security group for MS Visio,
HostAppmenu-Visio M365,KCUS,WinNT://KCUS/HostAppmenu-Visio M365,E18722,Owner,Citrix User Security group for MS Visio,
HostAppMenu-Access M365,KCUS,WinNT://KCUS/HostAppMenu-Access M365,E18722,Owner,Citrix User Security group for MS Access,
HostAppMenu-Access M365,KCUS,WinNT://KCUS/HostAppMenu-Access M365,Q07708,Authorizer,Citrix User Security group for MS Access,
HostAppMenu-Access M365,KCUS,WinNT://KCUS/HostAppMenu-Access M365,Q13357,Authorizer,Citrix User Security group for MS Access,
HostAppMenu-Access M365,KCUS,WinNT://KCUS/HostAppMenu-Access M365,Q14616,Authorizer,Citrix User Security group for MS Access,
HostAppMenu-Access M365,KCUS,WinNT://KCUS/HostAppMenu-Access M365,Q14718,Authorizer,Citrix User Security group for MS Access,
HostAppMenu-Access M365,KCUS,WinNT://KCUS/HostAppMenu-Access M365,Q15926,Authorizer,Citrix User Security group for MS Access,
HostAppMenu-Access M365,KCUS,WinNT://KCUS/HostAppMenu-Access M365,W49040,Delegate,Citrix User Security group for MS Access,
Desktop-Automations-Settings-Admin,KCUS,WinNT://KCUS/Desktop-Automations-Settings-Admin,E18722,Owner,Members can modify settings for automations managed by the Desktop team. Automations covered are - M365Apps Device License reclaim,
Desktop-Automations-Settings-Admin,KCUS,WinNT://KCUS/Desktop-Automations-Settings-Admin,W66442,Delegate,Members can modify settings for automations managed by the Desktop team. Automations covered are - M365Apps Device License reclaim,
Hostapp-Tops,KCUS,WinNT://KCUS/Hostapp-Tops,Q14718,Authorizer,Citrix Application Security group for hosting Tops Application,
Hostapp-Tops,KCUS,WinNT://KCUS/Hostapp-Tops,Q15926,Authorizer,Citrix Application Security group for hosting Tops Application,
Hostapp-Tops,KCUS,WinNT://KCUS/Hostapp-Tops,W49040,Delegate,Citrix Application Security group for hosting Tops Application,
Hostapp-Tops,KCUS,WinNT://KCUS/Hostapp-Tops,E18722,Owner,Citrix Application Security group for hosting Tops Application,
Hostapp-Tops,KCUS,WinNT://KCUS/Hostapp-Tops,Q07708,Authorizer,Citrix Application Security group for hosting Tops Application,
Hostapp-Tops,KCUS,WinNT://KCUS/Hostapp-Tops,Q13357,Authorizer,Citrix Application Security group for hosting Tops Application,
Hostapp-Tops,KCUS,WinNT://KCUS/Hostapp-Tops,Q14616,Authorizer,Citrix Application Security group for hosting Tops Application,
O365-CoPilot-Users,KCUS,WinNT://KCUS/O365-CoPilot-Users,B46959,Authorizer,O365-CoPilot-Users,
O365-CoPilot-Users,KCUS,WinNT://KCUS/O365-CoPilot-Users,e18722,Owner,O365-CoPilot-Users,
O365-CoPilot-Users,KCUS,WinNT://KCUS/O365-CoPilot-Users,w62002,Delegate,O365-CoPilot-Users,
Hostapp-SAPGUITesting,KCUS,WinNT://KCUS/Hostapp-SAPGUITesting,E18722,Owner,Citrix access group for SAPGUITesting,
Hostapp-SAPGUITesting,KCUS,WinNT://KCUS/Hostapp-SAPGUITesting,Q07708,Authorizer,Citrix access group for SAPGUITesting,
Hostapp-SAPGUITesting,KCUS,WinNT://KCUS/Hostapp-SAPGUITesting,Q13357,Authorizer,Citrix access group for SAPGUITesting,
Hostapp-SAPGUITesting,KCUS,WinNT://KCUS/Hostapp-SAPGUITesting,Q14616,Authorizer,Citrix access group for SAPGUITesting,
Hostapp-SAPGUITesting,KCUS,WinNT://KCUS/Hostapp-SAPGUITesting,Q14718,Authorizer,Citrix access group for SAPGUITesting,
Hostapp-SAPGUITesting,KCUS,WinNT://KCUS/Hostapp-SAPGUITesting,Q15926,Authorizer,Citrix access group for SAPGUITesting,
Hostapp-SAPGUITesting,KCUS,WinNT://KCUS/Hostapp-SAPGUITesting,W49040,Delegate,Citrix access group for SAPGUITesting,
Office-365 PerDevice to PerUser conversion,KCUS,WinNT://KCUS/Office-365 PerDevice to PerUser conversion,E18722,Owner,To apply GPO policy that converts M365 Apps client from PerDevice to Per User installation,
Office-365 PerDevice to PerUser conversion,KCUS,WinNT://KCUS/Office-365 PerDevice to PerUser conversion,Q39116,Authorizer,To apply GPO policy that converts M365 Apps client from PerDevice to Per User installation,
Office-365 PerDevice to PerUser conversion,KCUS,WinNT://KCUS/Office-365 PerDevice to PerUser conversion,W48997,Delegate,To apply GPO policy that converts M365 Apps client from PerDevice to Per User installation,
CSArchivingAdministrator,KCUS,WinNT://KCUS/CSArchivingAdministrator,E18722,Owner,Members of this group can create  configure  and manage archiving related settings and policies in Lync Server 2010.,
CSArchivingAdministrator,KCUS,WinNT://KCUS/CSArchivingAdministrator,W62002,Delegate,Members of this group can create  configure  and manage archiving related settings and policies in Lync Server 2010.,
iPad_Access - Personal,KCUS,WinNT://KCUS/iPad_Access - Personal,e11888,Authorizer,Provide Access to MobileIron for personal tablets & iPads,
iPad_Access - Personal,KCUS,WinNT://KCUS/iPad_Access - Personal,E18722,Owner,Provide Access to MobileIron for personal tablets & iPads,
iPad_Access - Personal,KCUS,WinNT://KCUS/iPad_Access - Personal,W66442,Delegate,Provide Access to MobileIron for personal tablets & iPads,
HostAppMenu-SQL Mgmt Studio,KCUS,WinNT://KCUS/HostAppMenu-SQL Mgmt Studio,E18722,Owner,Citrix User Security group for SQL Management Studio,
HostAppMenu-SQL Mgmt Studio,KCUS,WinNT://KCUS/HostAppMenu-SQL Mgmt Studio,Q07708,Authorizer,Citrix User Security group for SQL Management Studio,
HostAppMenu-SQL Mgmt Studio,KCUS,WinNT://KCUS/HostAppMenu-SQL Mgmt Studio,Q13357,Authorizer,Citrix User Security group for SQL Management Studio,
HostAppMenu-SQL Mgmt Studio,KCUS,WinNT://KCUS/HostAppMenu-SQL Mgmt Studio,Q14616,Authorizer,Citrix User Security group for SQL Management Studio,
HostAppMenu-SQL Mgmt Studio,KCUS,WinNT://KCUS/HostAppMenu-SQL Mgmt Studio,Q14718,Authorizer,Citrix User Security group for SQL Management Studio,
HostAppMenu-SQL Mgmt Studio,KCUS,WinNT://KCUS/HostAppMenu-SQL Mgmt Studio,Q15926,Authorizer,Citrix User Security group for SQL Management Studio,
HostAppMenu-SQL Mgmt Studio,KCUS,WinNT://KCUS/HostAppMenu-SQL Mgmt Studio,W49040,Delegate,Citrix User Security group for SQL Management Studio,
Azure AD Connect Server Admins,KCUS,WinNT://KCUS/Azure AD Connect Server Admins,E18722,Owner,Members of this group will be members of the admin group for the Azure AD Connect server,
Azure AD Connect Server Admins,KCUS,WinNT://KCUS/Azure AD Connect Server Admins,W62002,Delegate,Members of this group will be members of the admin group for the Azure AD Connect server,
SCCM Admin for Desktops,KCUS,WinNT://KCUS/SCCM Admin for Desktops,E18722,Owner,Provides segregated SCCM access for Desktop Services,
SCCM Admin for Desktops,KCUS,WinNT://KCUS/SCCM Admin for Desktops,Q06085,Authorizer,Provides segregated SCCM access for Desktop Services,
SCCM Admin for Desktops,KCUS,WinNT://KCUS/SCCM Admin for Desktops,W48997,Delegate,Provides segregated SCCM access for Desktop Services,
HostApp-AcrobatStd,KCUS,WinNT://KCUS/HostApp-AcrobatStd,E18722,Owner,Citrix Application security group for Acrobat Standard,
HostApp-AcrobatStd,KCUS,WinNT://KCUS/HostApp-AcrobatStd,Q13357,Authorizer,Citrix Application security group for Acrobat Standard,
HostApp-AcrobatStd,KCUS,WinNT://KCUS/HostApp-AcrobatStd,Q14718,Authorizer,Citrix Application security group for Acrobat Standard,
HostApp-AcrobatStd,KCUS,WinNT://KCUS/HostApp-AcrobatStd,Q15926,Authorizer,Citrix Application security group for Acrobat Standard,
HostApp-AcrobatStd,KCUS,WinNT://KCUS/HostApp-AcrobatStd,W49040,Delegate,Citrix Application security group for Acrobat Standard,
MCOE_Reporting_DB_DBO_C,KCUS,WinNT://KCUS/MCOE_Reporting_DB_DBO_C,E18722,Owner,,
MCOE_Reporting_DB_DBO_C,KCUS,WinNT://KCUS/MCOE_Reporting_DB_DBO_C,E32872,Delegate,,
MCOE_Reporting_DB_DBO_C,KCUS,WinNT://KCUS/MCOE_Reporting_DB_DBO_C,W48997,Authorizer,,
AddUserAsAdmin,KCUS,WinNT://KCUS/AddUserAsAdmin,E18722,Owner,Gives users rights to use the AddUserAsAdmin tool,
AddUserAsAdmin,KCUS,WinNT://KCUS/AddUserAsAdmin,Q04499,Authorizer,Gives users rights to use the AddUserAsAdmin tool,
AddUserAsAdmin,KCUS,WinNT://KCUS/AddUserAsAdmin,W48997,Delegate,Gives users rights to use the AddUserAsAdmin tool,
ED SQL Developers,KCUS,WinNT://KCUS/ED SQL Developers,E18722,Owner,Enterprise Desktop SQL Developers,
ED SQL Developers,KCUS,WinNT://KCUS/ED SQL Developers,Q73491,Delegate,Enterprise Desktop SQL Developers,
ED SQL Applications,KCUS,WinNT://KCUS/ED SQL Applications,E18722,Owner,Enterprise Desktop SQL Applications,
ED SQL Applications,KCUS,WinNT://KCUS/ED SQL Applications,Q73491,Delegate,Enterprise Desktop SQL Applications,
ED SQL Readonly,KCUS,WinNT://KCUS/ED SQL Readonly,E18722,Owner,Enterprise Desktop SQL Readonly ,
ED SQL Readonly,KCUS,WinNT://KCUS/ED SQL Readonly,W48997,Delegate,Enterprise Desktop SQL Readonly ,
VDI approver kc,KCUS,WinNT://KCUS/VDI approver kc,E18722,Owner,Group to approve Virtual Machine,
VDI approver kc,KCUS,WinNT://KCUS/VDI approver kc,W49040,Delegate,Group to approve Virtual Machine,
O365 DLP Administrators,KCUS,WinNT://KCUS/O365 DLP Administrators,E18722,Owner,Grants members access to manage DLP policies within Office 365,
O365 DLP Administrators,KCUS,WinNT://KCUS/O365 DLP Administrators,W62002,Delegate,Grants members access to manage DLP policies within Office 365,
O365 Addin_InsertHTML DesignModo,KCUS,WinNT://KCUS/O365 Addin_InsertHTML DesignModo,E18722,Owner,O365 Addin_InsertHTML DesignModo,
O365 Addin_InsertHTML DesignModo,KCUS,WinNT://KCUS/O365 Addin_InsertHTML DesignModo,Q03582,Authorizer,O365 Addin_InsertHTML DesignModo,
O365 Addin_InsertHTML DesignModo,KCUS,WinNT://KCUS/O365 Addin_InsertHTML DesignModo,W62002,Delegate,O365 Addin_InsertHTML DesignModo,
GPO - PAC Exceptions,KCUS,WinNT://KCUS/GPO - PAC Exceptions,E18722,Owner,Group for PAC file exception,
GPO - PAC Exceptions,KCUS,WinNT://KCUS/GPO - PAC Exceptions,q04499,Authorizer,Group for PAC file exception,
GPO - PAC Exceptions,KCUS,WinNT://KCUS/GPO - PAC Exceptions,W48997,Delegate,Group for PAC file exception,
eCIA Performance Monitoring Users,KCUS,WinNT://KCUS/eCIA Performance Monitoring Users,E18722,Owner,eCIA Performance Monitoring Users,
eCIA Performance Monitoring Users,KCUS,WinNT://KCUS/eCIA Performance Monitoring Users,W62002,Delegate,eCIA Performance Monitoring Users,
CSVoiceAdministrator,KCUS,WinNT://KCUS/CSVoiceAdministrator,E18722,Owner,Members of this group can create  configure  and manage voice-related settings and policies in Lync Server 2010.,
CSVoiceAdministrator,KCUS,WinNT://KCUS/CSVoiceAdministrator,W62002,Delegate,Members of this group can create  configure  and manage voice-related settings and policies in Lync Server 2010.,
CSAdministrator,KCUS,WinNT://KCUS/CSAdministrator,E18722,Owner, Members of this group can perform all administrative tasks in Lync Server 2010.,
CSAdministrator,KCUS,WinNT://KCUS/CSAdministrator,Q03582,Authorizer, Members of this group can perform all administrative tasks in Lync Server 2010.,
CSAdministrator,KCUS,WinNT://KCUS/CSAdministrator,W62002,Delegate, Members of this group can perform all administrative tasks in Lync Server 2010.,
HostApp-TotalTrax,KCUS,WinNT://KCUS/HostApp-TotalTrax,E18722,Owner,Application Security group for TotalTrax application for 3PL users,
HostApp-TotalTrax,KCUS,WinNT://KCUS/HostApp-TotalTrax,Q07708,Authorizer,Application Security group for TotalTrax application for 3PL users,
HostApp-TotalTrax,KCUS,WinNT://KCUS/HostApp-TotalTrax,Q13357,Authorizer,Application Security group for TotalTrax application for 3PL users,
HostApp-TotalTrax,KCUS,WinNT://KCUS/HostApp-TotalTrax,Q14616,Authorizer,Application Security group for TotalTrax application for 3PL users,
HostApp-TotalTrax,KCUS,WinNT://KCUS/HostApp-TotalTrax,Q14718,Authorizer,Application Security group for TotalTrax application for 3PL users,
HostApp-TotalTrax,KCUS,WinNT://KCUS/HostApp-TotalTrax,Q15926,Authorizer,Application Security group for TotalTrax application for 3PL users,
HostApp-TotalTrax,KCUS,WinNT://KCUS/HostApp-TotalTrax,W49040,Delegate,Application Security group for TotalTrax application for 3PL users,
O365-IntuneDB-RW,KCUS,WinNT://KCUS/O365-IntuneDB-RW,E18722,Owner,Members of this Group has Read  Write Access to the DB that reports Intune / Azure Devices,
O365-IntuneDB-RW,KCUS,WinNT://KCUS/O365-IntuneDB-RW,W62002,Delegate,Members of this Group has Read  Write Access to the DB that reports Intune / Azure Devices,
PCBUSINESSCTR-KC,KCUS,WinNT://KCUS/PCBUSINESSCTR-KC,b45906,Authorizer,PC Business Center Support,
PCBUSINESSCTR-KC,KCUS,WinNT://KCUS/PCBUSINESSCTR-KC,E18722,Owner,PC Business Center Support,
PCBUSINESSCTR-KC,KCUS,WinNT://KCUS/PCBUSINESSCTR-KC,W66442,Delegate,PC Business Center Support,
PSYNCH-TCS,KCUS,WinNT://KCUS/PSYNCH-TCS,E18722,Owner,Password Wizard support group,
PSYNCH-TCS,KCUS,WinNT://KCUS/PSYNCH-TCS,Q06085,Delegate,Password Wizard support group,
PSYNCH-TCS,KCUS,WinNT://KCUS/PSYNCH-TCS,Q06371,Authorizer,Password Wizard support group,
DL009292,KCUS,WinNT://KCUS/DL009292,B86123,Authorizer,+GLOBAL  INF Digital Workplace Platforms,
DL009292,KCUS,WinNT://KCUS/DL009292,E18722,Owner,+GLOBAL  INF Digital Workplace Platforms,
DL009292,KCUS,WinNT://KCUS/DL009292,W62002,Delegate,+GLOBAL  INF Digital Workplace Platforms,
HostServerAdmin-NAPromax,KCUS,WinNT://KCUS/HostServerAdmin-NAPromax,W49040,Delegate,Admin Access group for NA Promax Citrix servers,
HostServerAdmin-NAPromax,KCUS,WinNT://KCUS/HostServerAdmin-NAPromax,E18722,Owner,Admin Access group for NA Promax Citrix servers,
HostServerAdmin-NAPromax,KCUS,WinNT://KCUS/HostServerAdmin-NAPromax,Q07708,Authorizer,Admin Access group for NA Promax Citrix servers,
HostServerAdmin-NAPromax,KCUS,WinNT://KCUS/HostServerAdmin-NAPromax,Q13357,Authorizer,Admin Access group for NA Promax Citrix servers,
HostServerAdmin-NAPromax,KCUS,WinNT://KCUS/HostServerAdmin-NAPromax,Q14616,Authorizer,Admin Access group for NA Promax Citrix servers,
HostServerAdmin-NAPromax,KCUS,WinNT://KCUS/HostServerAdmin-NAPromax,Q14718,Authorizer,Admin Access group for NA Promax Citrix servers,
HostServerAdmin-NAPromax,KCUS,WinNT://KCUS/HostServerAdmin-NAPromax,Q15926,Authorizer,Admin Access group for NA Promax Citrix servers,
Corel WinDVD Pro V12,KCUS,WinNT://KCUS/Corel WinDVD Pro V12,E18722,Owner,K-C laptops no longer come with DVD Drives  and therefore no longer included DVD software.,
Corel WinDVD Pro V12,KCUS,WinNT://KCUS/Corel WinDVD Pro V12,Q04499,Authorizer,K-C laptops no longer come with DVD Drives  and therefore no longer included DVD software.,
Corel WinDVD Pro V12,KCUS,WinNT://KCUS/Corel WinDVD Pro V12,W48997,Authorizer,K-C laptops no longer come with DVD Drives  and therefore no longer included DVD software.,
Corel WinDVD Pro V12,KCUS,WinNT://KCUS/Corel WinDVD Pro V12,W66442,Delegate,K-C laptops no longer come with DVD Drives  and therefore no longer included DVD software.,
SCCMApp-Adobe Creative Cloud 2017 - Video,KCUS,WinNT://KCUS/SCCMApp-Adobe Creative Cloud 2017 - Video,E18722,Owner,SCCM package for Adobe Creative Cloud 2017 - Video (Custom Plan).,
SCCMApp-Adobe Creative Cloud 2017 - Video,KCUS,WinNT://KCUS/SCCMApp-Adobe Creative Cloud 2017 - Video,W48997,Delegate,SCCM package for Adobe Creative Cloud 2017 - Video (Custom Plan).,
DL008763,KCUS,WinNT://KCUS/DL008763,E18722,Owner,+GLOBAL_ITS  Desktop RC1,
DL008763,KCUS,WinNT://KCUS/DL008763,Q08911,Authorizer,+GLOBAL_ITS  Desktop RC1,
DL008763,KCUS,WinNT://KCUS/DL008763,W66442,Delegate,+GLOBAL_ITS  Desktop RC1,
DL008765,KCUS,WinNT://KCUS/DL008765,B92167,Authorizer,+GLOBAL_ITS  Desktop Pilot,
DL008765,KCUS,WinNT://KCUS/DL008765,E18722,Owner,+GLOBAL_ITS  Desktop Pilot,
DL008765,KCUS,WinNT://KCUS/DL008765,W66442,Delegate,+GLOBAL_ITS  Desktop Pilot,
DL008764,KCUS,WinNT://KCUS/DL008764,E18722,Owner,+GLOBAL_ITS  Desktop RC2,
DL008764,KCUS,WinNT://KCUS/DL008764,W66442,Delegate,+GLOBAL_ITS  Desktop RC2,
GRP07396_C,KCUS,WinNT://KCUS/GRP07396_C,E18722,Owner,Mailbox: _Desktop services  VDIs,
GRP07396_C,KCUS,WinNT://KCUS/GRP07396_C,Q06085,Authorizer,Mailbox: _Desktop services  VDIs,
GRP07396_C,KCUS,WinNT://KCUS/GRP07396_C,Q08911,Authorizer,Mailbox: _Desktop services  VDIs,
GRP07396_C,KCUS,WinNT://KCUS/GRP07396_C,Q13357,Authorizer,Mailbox: _Desktop services  VDIs,
GRP07396_C,KCUS,WinNT://KCUS/GRP07396_C,Q14718,Authorizer,Mailbox: _Desktop services  VDIs,
GRP07396_C,KCUS,WinNT://KCUS/GRP07396_C,Q15926,Authorizer,Mailbox: _Desktop services  VDIs,
GRP07396_C,KCUS,WinNT://KCUS/GRP07396_C,W49040,Delegate,Mailbox: _Desktop services  VDIs,
HostServerAdmin-EUPromax,KCUS,WinNT://KCUS/HostServerAdmin-EUPromax,Q14616,Authorizer,Admin Access group for EU Promax Citrix servers,
HostServerAdmin-EUPromax,KCUS,WinNT://KCUS/HostServerAdmin-EUPromax,Q14718,Authorizer,Admin Access group for EU Promax Citrix servers,
HostServerAdmin-EUPromax,KCUS,WinNT://KCUS/HostServerAdmin-EUPromax,Q15926,Authorizer,Admin Access group for EU Promax Citrix servers,
HostServerAdmin-EUPromax,KCUS,WinNT://KCUS/HostServerAdmin-EUPromax,W49040,Delegate,Admin Access group for EU Promax Citrix servers,
HostServerAdmin-EUPromax,KCUS,WinNT://KCUS/HostServerAdmin-EUPromax,E18722,Owner,Admin Access group for EU Promax Citrix servers,
HostServerAdmin-EUPromax,KCUS,WinNT://KCUS/HostServerAdmin-EUPromax,Q07708,Authorizer,Admin Access group for EU Promax Citrix servers,
HostServerAdmin-EUPromax,KCUS,WinNT://KCUS/HostServerAdmin-EUPromax,Q13357,Authorizer,Admin Access group for EU Promax Citrix servers,
HostServerAdmin-WMS,KCUS,WinNT://KCUS/HostServerAdmin-WMS,E18722,Owner,Admin Access group for WMS Citrix servers.,
HostServerAdmin-WMS,KCUS,WinNT://KCUS/HostServerAdmin-WMS,Q07708,Authorizer,Admin Access group for WMS Citrix servers.,
HostServerAdmin-WMS,KCUS,WinNT://KCUS/HostServerAdmin-WMS,Q13357,Authorizer,Admin Access group for WMS Citrix servers.,
HostServerAdmin-WMS,KCUS,WinNT://KCUS/HostServerAdmin-WMS,Q14616,Authorizer,Admin Access group for WMS Citrix servers.,
HostServerAdmin-WMS,KCUS,WinNT://KCUS/HostServerAdmin-WMS,Q14718,Authorizer,Admin Access group for WMS Citrix servers.,
HostServerAdmin-WMS,KCUS,WinNT://KCUS/HostServerAdmin-WMS,Q15926,Authorizer,Admin Access group for WMS Citrix servers.,
HostServerAdmin-WMS,KCUS,WinNT://KCUS/HostServerAdmin-WMS,W49040,Delegate,Admin Access group for WMS Citrix servers.,
Distribution Request System Admin,KCUS,WinNT://KCUS/Distribution Request System Admin,E18722,Owner,Pacakage Distribution through SCCM,
Distribution Request System Admin,KCUS,WinNT://KCUS/Distribution Request System Admin,W48997,Delegate,Pacakage Distribution through SCCM,
Release management - Ring 3,KCUS,WinNT://KCUS/Release management - Ring 3,E18722,Owner,This group include all Global IT and all ICD users,
Release management - Ring 3,KCUS,WinNT://KCUS/Release management - Ring 3,Q06085,Authorizer,This group include all Global IT and all ICD users,
Release management - Ring 3,KCUS,WinNT://KCUS/Release management - Ring 3,Q08911,Authorizer,This group include all Global IT and all ICD users,
Release management - Ring 3,KCUS,WinNT://KCUS/Release management - Ring 3,W66442,Delegate,This group include all Global IT and all ICD users,
iPad Access,KCUS,WinNT://KCUS/iPad Access,E18722,Owner,kcus/iPad Access,
iPad Access,KCUS,WinNT://KCUS/iPad Access,W66442,Delegate,kcus/iPad Access,
GRP07214_C,KCUS,WinNT://KCUS/GRP07214_C,B62398,Delegate,Mailbox: _Support  Mobility Services,
GRP07214_C,KCUS,WinNT://KCUS/GRP07214_C,E18722,Owner,Mailbox: _Support  Mobility Services,
INFRPT_DEV_DataReader,KCUS,WinNT://KCUS/INFRPT_DEV_DataReader,B46959,Authorizer,Provides access to the IRP0NEDB database,
INFRPT_DEV_DataReader,KCUS,WinNT://KCUS/INFRPT_DEV_DataReader,E18722,Owner,Provides access to the IRP0NEDB database,
INFRPT_DEV_DataReader,KCUS,WinNT://KCUS/INFRPT_DEV_DataReader,W62002,Delegate,Provides access to the IRP0NEDB database,
Desktop Services Azure Change,KCUS,WinNT://KCUS/Desktop Services Azure Change,E18722,Owner,Provides change access to Desktop Services Azure solutions.,
Desktop Services Azure Change,KCUS,WinNT://KCUS/Desktop Services Azure Change,Q73491,Delegate,Provides change access to Desktop Services Azure solutions.,
ENS Exception,KCUS,WinNT://KCUS/ENS Exception,E18722,Owner,To exclude users from ENS deployment,
ENS Exception,KCUS,WinNT://KCUS/ENS Exception,W48997,Delegate,To exclude users from ENS deployment,
O365 TyGraph Users,KCUS,WinNT://KCUS/O365 TyGraph Users,E18722,Owner,O365 TyGraph Users,
O365 TyGraph Users,KCUS,WinNT://KCUS/O365 TyGraph Users,Q08911,Authorizer,O365 TyGraph Users,
O365 TyGraph Users,KCUS,WinNT://KCUS/O365 TyGraph Users,W49040,Delegate,O365 TyGraph Users,
CSUserAdministrator,KCUS,WinNT://KCUS/CSUserAdministrator,E18722,Owner,Members of this group can enable and disable users for Lync Server 2010  move users  and assign existing policies to users.,
CSUserAdministrator,KCUS,WinNT://KCUS/CSUserAdministrator,W62002,Delegate,Members of this group can enable and disable users for Lync Server 2010  move users  and assign existing policies to users.,
GRP08068_C,KCUS,WinNT://KCUS/GRP08068_C,E18722,Owner,Mailbox : _Support  Video Streamings,
GRP08068_C,KCUS,WinNT://KCUS/GRP08068_C,Q08911,Authorizer,Mailbox : _Support  Video Streamings,
GRP08068_C,KCUS,WinNT://KCUS/GRP08068_C,W49040,Delegate,Mailbox : _Support  Video Streamings,
SCCM_Citrix_RO,KCUS,WinNT://KCUS/SCCM_Citrix_RO,Q15926,Authorizer,SCCM Citrix groups,
SCCM_Citrix_RO,KCUS,WinNT://KCUS/SCCM_Citrix_RO,W49040,Delegate,SCCM Citrix groups,
SCCM_Citrix_RO,KCUS,WinNT://KCUS/SCCM_Citrix_RO,E18722,Owner,SCCM Citrix groups,
SCCM_Citrix_RO,KCUS,WinNT://KCUS/SCCM_Citrix_RO,Q13357,Authorizer,SCCM Citrix groups,
SCCM_Citrix_RO,KCUS,WinNT://KCUS/SCCM_Citrix_RO,Q14718,Authorizer,SCCM Citrix groups,
GRP14124_C,KCUS,WinNT://KCUS/GRP14124_C,E18722,Owner,Mailbox: _Support  Microsoft Defender,
GRP14124_C,KCUS,WinNT://KCUS/GRP14124_C,Q06085,Authorizer,Mailbox: _Support  Microsoft Defender,
GRP14124_C,KCUS,WinNT://KCUS/GRP14124_C,Q06371,Authorizer,Mailbox: _Support  Microsoft Defender,
GRP14124_C,KCUS,WinNT://KCUS/GRP14124_C,W48997,Delegate,Mailbox: _Support  Microsoft Defender,
Help at Work,KCUS,WinNT://KCUS/Help at Work,E18722,Owner,This group is used to give people access to TeamViewer for the Help@Work application,
Help at Work,KCUS,WinNT://KCUS/Help at Work,W66442,Delegate,This group is used to give people access to TeamViewer for the Help@Work application,
Hostapp-MFU,KCUS,WinNT://KCUS/Hostapp-MFU,E18722,Owner,Citrix access group for Modulo Fiscal Universal,
Hostapp-MFU,KCUS,WinNT://KCUS/Hostapp-MFU,Q07708,Authorizer,Citrix access group for Modulo Fiscal Universal,
Hostapp-MFU,KCUS,WinNT://KCUS/Hostapp-MFU,Q13357,Authorizer,Citrix access group for Modulo Fiscal Universal,
Hostapp-MFU,KCUS,WinNT://KCUS/Hostapp-MFU,Q14616,Authorizer,Citrix access group for Modulo Fiscal Universal,
Hostapp-MFU,KCUS,WinNT://KCUS/Hostapp-MFU,Q14718,Authorizer,Citrix access group for Modulo Fiscal Universal,
Hostapp-MFU,KCUS,WinNT://KCUS/Hostapp-MFU,Q15926,Authorizer,Citrix access group for Modulo Fiscal Universal,
Hostapp-MFU,KCUS,WinNT://KCUS/Hostapp-MFU,W49040,Delegate,Citrix access group for Modulo Fiscal Universal,
FC-AuroraUsers,KCUS,WinNT://KCUS/FC-AuroraUsers,E18722,Owner,Eclipse Application Users Desktop Pool,
FC-AuroraUsers,KCUS,WinNT://KCUS/FC-AuroraUsers,Q07708,Authorizer,Eclipse Application Users Desktop Pool,
FC-AuroraUsers,KCUS,WinNT://KCUS/FC-AuroraUsers,Q13357,Authorizer,Eclipse Application Users Desktop Pool,
FC-AuroraUsers,KCUS,WinNT://KCUS/FC-AuroraUsers,Q14718,Authorizer,Eclipse Application Users Desktop Pool,
FC-AuroraUsers,KCUS,WinNT://KCUS/FC-AuroraUsers,Q15926,Authorizer,Eclipse Application Users Desktop Pool,
FC-AuroraUsers,KCUS,WinNT://KCUS/FC-AuroraUsers,W49040,Delegate,Eclipse Application Users Desktop Pool,
Hostapp-outlook 2013,KCUS,WinNT://KCUS/Hostapp-outlook 2013,E18722,Owner,Citrix access group for MS Outlook 2013,
Hostapp-outlook 2013,KCUS,WinNT://KCUS/Hostapp-outlook 2013,Q07708,Authorizer,Citrix access group for MS Outlook 2013,
Hostapp-outlook 2013,KCUS,WinNT://KCUS/Hostapp-outlook 2013,Q13357,Authorizer,Citrix access group for MS Outlook 2013,
Hostapp-outlook 2013,KCUS,WinNT://KCUS/Hostapp-outlook 2013,Q14616,Authorizer,Citrix access group for MS Outlook 2013,
Hostapp-outlook 2013,KCUS,WinNT://KCUS/Hostapp-outlook 2013,Q14718,Authorizer,Citrix access group for MS Outlook 2013,
Hostapp-outlook 2013,KCUS,WinNT://KCUS/Hostapp-outlook 2013,Q15926,Authorizer,Citrix access group for MS Outlook 2013,
Hostapp-outlook 2013,KCUS,WinNT://KCUS/Hostapp-outlook 2013,W49040,Delegate,Citrix access group for MS Outlook 2013,
HostApp-RPWI-Chester,KCUS,WinNT://KCUS/HostApp-RPWI-Chester,E18722,Owner,Citrix Application Security group for hosting Red Prairie Web Interface for Chester,
HostApp-RPWI-Chester,KCUS,WinNT://KCUS/HostApp-RPWI-Chester,Q07708,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Chester,
HostApp-RPWI-Chester,KCUS,WinNT://KCUS/HostApp-RPWI-Chester,Q14616,Authorizer,Citrix Application Security group for hosting Red Prairie Web Interface for Chester,
HostApp-RPWI-Chester,KCUS,WinNT://KCUS/HostApp-RPWI-Chester,W49040,Delegate,Citrix Application Security group for hosting Red Prairie Web Interface for Chester,
Admin NS,KCUS,WinNT://KCUS/Admin NS,E18722,Owner,Admin Group for managing Citrix NetScaler,
Admin NS,KCUS,WinNT://KCUS/Admin NS,W49040,Delegate,Admin Group for managing Citrix NetScaler,
SCCM_Citrix_admin,KCUS,WinNT://KCUS/SCCM_Citrix_admin,E18722,Owner,SCCM Citrix groups,
SCCM_Citrix_admin,KCUS,WinNT://KCUS/SCCM_Citrix_admin,Q13357,Authorizer,SCCM Citrix groups,
SCCM_Citrix_admin,KCUS,WinNT://KCUS/SCCM_Citrix_admin,Q15926,Authorizer,SCCM Citrix groups,
SCCM_Citrix_admin,KCUS,WinNT://KCUS/SCCM_Citrix_admin,W49040,Delegate,SCCM Citrix groups,
Hybrid Search Usage Readers,KCUS,WinNT://KCUS/Hybrid Search Usage Readers,E18722,Owner,Hybrid Search Usage Readers,
Hybrid Search Usage Readers,KCUS,WinNT://KCUS/Hybrid Search Usage Readers,Q08911,Authorizer,Hybrid Search Usage Readers,
Hybrid Search Usage Readers,KCUS,WinNT://KCUS/Hybrid Search Usage Readers,W49040,Delegate,Hybrid Search Usage Readers,
FC-ModUsers,KCUS,WinNT://KCUS/FC-ModUsers,Q14616,Authorizer,Modeling Team VDI Users,
FC-ModUsers,KCUS,WinNT://KCUS/FC-ModUsers,Q14718,Authorizer,Modeling Team VDI Users,
FC-ModUsers,KCUS,WinNT://KCUS/FC-ModUsers,Q15926,Authorizer,Modeling Team VDI Users,
FC-ModUsers,KCUS,WinNT://KCUS/FC-ModUsers,W49040,Delegate,Modeling Team VDI Users,
FC-ModUsers,KCUS,WinNT://KCUS/FC-ModUsers,E18722,Owner,Modeling Team VDI Users,
FC-ModUsers,KCUS,WinNT://KCUS/FC-ModUsers,Q07708,Authorizer,Modeling Team VDI Users,
FC-ModUsers,KCUS,WinNT://KCUS/FC-ModUsers,Q13357,Authorizer,Modeling Team VDI Users,
FC-KCITUsers,KCUS,WinNT://KCUS/FC-KCITUsers,E18722,Owner,KC IT Team VDI Users,
FC-KCITUsers,KCUS,WinNT://KCUS/FC-KCITUsers,Q07708,Authorizer,KC IT Team VDI Users,
FC-KCITUsers,KCUS,WinNT://KCUS/FC-KCITUsers,Q13357,Authorizer,KC IT Team VDI Users,
FC-KCITUsers,KCUS,WinNT://KCUS/FC-KCITUsers,Q14616,Authorizer,KC IT Team VDI Users,
FC-KCITUsers,KCUS,WinNT://KCUS/FC-KCITUsers,Q14718,Authorizer,KC IT Team VDI Users,
FC-KCITUsers,KCUS,WinNT://KCUS/FC-KCITUsers,Q15926,Authorizer,KC IT Team VDI Users,
FC-KCITUsers,KCUS,WinNT://KCUS/FC-KCITUsers,W49040,Delegate,KC IT Team VDI Users,
IPass Pilot Restricted,KCUS,WinNT://KCUS/IPass Pilot Restricted,E18722,Owner,Used to control who can authenticate through IPass,
IPass Pilot Restricted,KCUS,WinNT://KCUS/IPass Pilot Restricted,W48997,Delegate,Used to control who can authenticate through IPass,
SHAREPOINT_RPT_DEV_READ,KCUS,WinNT://KCUS/SHAREPOINT_RPT_DEV_READ,E18722,Owner,SHAREPOINT_RPT_DEV_READ,
SHAREPOINT_RPT_DEV_READ,KCUS,WinNT://KCUS/SHAREPOINT_RPT_DEV_READ,Q08911,Authorizer,SHAREPOINT_RPT_DEV_READ,
SHAREPOINT_RPT_DEV_READ,KCUS,WinNT://KCUS/SHAREPOINT_RPT_DEV_READ,W49040,Delegate,SHAREPOINT_RPT_DEV_READ,
Compliance Management,KCUS,WinNT://KCUS/Compliance Management,E18722,Owner,This role group will allow a specified user  responsible for compliance  to properly configure and manage compliance settings within Exchange in accordance with their policy.,
Compliance Management,KCUS,WinNT://KCUS/Compliance Management,W62002,Delegate,This role group will allow a specified user  responsible for compliance  to properly configure and manage compliance settings within Exchange in accordance with their policy.,
SHAREPOINT_RPT_DEV_CHANGE,KCUS,WinNT://KCUS/SHAREPOINT_RPT_DEV_CHANGE,E18722,Owner,SHAREPOINT_RPT_DEV_CHANGE,
SHAREPOINT_RPT_DEV_CHANGE,KCUS,WinNT://KCUS/SHAREPOINT_RPT_DEV_CHANGE,Q08911,Authorizer,SHAREPOINT_RPT_DEV_CHANGE,
SHAREPOINT_RPT_DEV_CHANGE,KCUS,WinNT://KCUS/SHAREPOINT_RPT_DEV_CHANGE,W49040,Delegate,SHAREPOINT_RPT_DEV_CHANGE,
GRP13777_C,KCUS,WinNT://KCUS/GRP13777_C,E18722,Owner,Mailbox: _Modern desktop  Support,
GRP13777_C,KCUS,WinNT://KCUS/GRP13777_C,W48997,Delegate,Mailbox: _Modern desktop  Support,
GRP13777_C,KCUS,WinNT://KCUS/GRP13777_C,W66442,Authorizer,Mailbox: _Modern desktop  Support,
www.messagingsa.kcc.com_owners,KCUS,WinNT://KCUS/www.messagingsa.kcc.com_owners,W62002,Delegate,Group for website owners,
www.messagingsa.kcc.com_owners,KCUS,WinNT://KCUS/www.messagingsa.kcc.com_owners,E18722,Owner,Group for website owners,
www.messagingsa.kcc.com_businesspartners,KCUS,WinNT://KCUS/www.messagingsa.kcc.com_businesspartners,E18722,Owner,Group for website buisnesspartners,
www.messagingsa.kcc.com_businesspartners,KCUS,WinNT://KCUS/www.messagingsa.kcc.com_businesspartners,W62002,Delegate,Group for website buisnesspartners,
www.messagingsa.kcc.com_authorizers,KCUS,WinNT://KCUS/www.messagingsa.kcc.com_authorizers,E18722,Owner,Group for website Authorizers,
www.messagingsa.kcc.com_authorizers,KCUS,WinNT://KCUS/www.messagingsa.kcc.com_authorizers,W62002,Delegate,Group for website Authorizers,
GRP07349_C,KCUS,WinNT://KCUS/GRP07349_C,E18722,Owner,Mailbox: _Support  Project Servers,
GRP07349_C,KCUS,WinNT://KCUS/GRP07349_C,W49040,Delegate,Mailbox: _Support  Project Servers,
SHAREPOINT_RPT_PROD_CHANGE,KCUS,WinNT://KCUS/SHAREPOINT_RPT_PROD_CHANGE,E18722,Owner,SHAREPOINT_RPT_PROD_CHANGE,
SHAREPOINT_RPT_PROD_CHANGE,KCUS,WinNT://KCUS/SHAREPOINT_RPT_PROD_CHANGE,Q08911,Authorizer,SHAREPOINT_RPT_PROD_CHANGE,
SHAREPOINT_RPT_PROD_CHANGE,KCUS,WinNT://KCUS/SHAREPOINT_RPT_PROD_CHANGE,W49040,Delegate,SHAREPOINT_RPT_PROD_CHANGE,
BeyondInsight Desktop Vulnerability Report Access,KCUS,WinNT://KCUS/BeyondInsight Desktop Vulnerability Report Access,E18722,Owner,Group will be used to grant access the Computer security vulnerability scanner reports related to desktops,
BeyondInsight Desktop Vulnerability Report Access,KCUS,WinNT://KCUS/BeyondInsight Desktop Vulnerability Report Access,W48997,Delegate,Group will be used to grant access the Computer security vulnerability scanner reports related to desktops,
Exchange All Hosted Organizations,KCUS,WinNT://KCUS/Exchange All Hosted Organizations,E18722,Owner,This group contains all the Exchange Hosted Organization Mailboxes groups. It is used for applying Password Setting Objects to all hosted mailboxes.  This group should not be deleted.,
Exchange All Hosted Organizations,KCUS,WinNT://KCUS/Exchange All Hosted Organizations,W62002,Delegate,This group contains all the Exchange Hosted Organization Mailboxes groups. It is used for applying Password Setting Objects to all hosted mailboxes.  This group should not be deleted.,
O365-Bookings,KCUS,WinNT://KCUS/O365-Bookings,E18722,Owner,This is used for managing licenses for Bookings in O365,
O365-Bookings,KCUS,WinNT://KCUS/O365-Bookings,Q03582,Authorizer,This is used for managing licenses for Bookings in O365,
O365-Bookings,KCUS,WinNT://KCUS/O365-Bookings,W62002,Delegate,This is used for managing licenses for Bookings in O365,
OneDrive for GD Shared,KCUS,WinNT://KCUS/OneDrive for GD Shared,E18722,Owner,This group is for enabling OneDrive for users in GD Shared devices,
OneDrive for GD Shared,KCUS,WinNT://KCUS/OneDrive for GD Shared,Q07749,Authorizer,This group is for enabling OneDrive for users in GD Shared devices,
OneDrive for GD Shared,KCUS,WinNT://KCUS/OneDrive for GD Shared,Q39116,Authorizer,This group is for enabling OneDrive for users in GD Shared devices,
OneDrive for GD Shared,KCUS,WinNT://KCUS/OneDrive for GD Shared,W66442,Delegate,This group is for enabling OneDrive for users in GD Shared devices,
VMAll,KCUS,WinNT://KCUS/VMAll,E18722,Owner,Master Security group fro VM deployments,
VMAll,KCUS,WinNT://KCUS/VMAll,W48997,Delegate,Master Security group fro VM deployments,
Exchange Install Domain Servers,KCUS,WinNT://KCUS/Exchange Install Domain Servers,E18722,Owner,This group is used during Exchange setup and is not intended to be used for other purposes.,
Exchange Install Domain Servers,KCUS,WinNT://KCUS/Exchange Install Domain Servers,W62002,Delegate,This group is used during Exchange setup and is not intended to be used for other purposes.,
Exchange Servers,KCUS,WinNT://KCUS/Exchange Servers,E18722,Owner,This group contains all the Exchange servers. This group should not be deleted.,
Exchange Servers,KCUS,WinNT://KCUS/Exchange Servers,W62002,Delegate,This group contains all the Exchange servers. This group should not be deleted.,
Exchange Windows Permissions,KCUS,WinNT://KCUS/Exchange Windows Permissions,E18722,Owner,This group contains Exchange servers that run Exchange cmdlets on behalf of users via Management service. Its members will have permission to read and modify all Windows accounts and groups. This group should not be deleted.,
Exchange Windows Permissions,KCUS,WinNT://KCUS/Exchange Windows Permissions,W62002,Delegate,This group contains Exchange servers that run Exchange cmdlets on behalf of users via Management service. Its members will have permission to read and modify all Windows accounts and groups. This group should not be deleted.,
ExchangeLegacyInterop,KCUS,WinNT://KCUS/ExchangeLegacyInterop,E18722,Owner,This group is for interoperability with Exchange 2003 servers within the same forest. This group should not be deleted.,
ExchangeLegacyInterop,KCUS,WinNT://KCUS/ExchangeLegacyInterop,W62002,Delegate,This group is for interoperability with Exchange 2003 servers within the same forest. This group should not be deleted.,
ITS Computer Security,KCUS,WinNT://KCUS/ITS Computer Security,E18722,Owner,,
ITS Computer Security,KCUS,WinNT://KCUS/ITS Computer Security,W62002,Delegate,,
VmWksAll,KCUS,WinNT://KCUS/VmWksAll,E18722,Owner,VM Workstation license –Master ,
VmWksAll,KCUS,WinNT://KCUS/VmWksAll,W49040,Delegate,VM Workstation license –Master ,
VmWksFC,KCUS,WinNT://KCUS/VmWksFC,E18722,Owner,VM Workstation license –Family Care,
VmWksFC,KCUS,WinNT://KCUS/VmWksFC,W49040,Delegate,VM Workstation license –Family Care,
VmWksPC,KCUS,WinNT://KCUS/VmWksPC,W49040,Delegate,VM Workstation license –Personal Care,
VmWksPC,KCUS,WinNT://KCUS/VmWksPC,E18722,Owner,VM Workstation license –Personal Care,
VmWksNW,KCUS,WinNT://KCUS/VmWksNW,E18722,Owner,VM Workstation license –Non Wovens,
VmWksNW,KCUS,WinNT://KCUS/VmWksNW,W49040,Delegate,VM Workstation license –Non Wovens,
VmWksKCP,KCUS,WinNT://KCUS/VmWksKCP,E18722,Owner,VM Workstation license –KC Professional,
VmWksKCP,KCUS,WinNT://KCUS/VmWksKCP,W49040,Delegate,VM Workstation license –KC Professional,
VmWksITS,KCUS,WinNT://KCUS/VmWksITS,E18722,Owner,VM Workstation license –IT Services,
VmWksITS,KCUS,WinNT://KCUS/VmWksITS,W49040,Delegate,VM Workstation license –IT Services,
VmWksOther,KCUS,WinNT://KCUS/VmWksOther,W49040,Delegate,VM Workstation license –Non EE  Other,
VmWksOther,KCUS,WinNT://KCUS/VmWksOther,E18722,Owner,VM Workstation license –Non EE  Other,
O365-Teams-Service-Administrator,KCUS,WinNT://KCUS/O365-Teams-Service-Administrator,W62002,Delegate,Teams Service Administrators will be assigned using this Group,
O365-Teams-Service-Administrator,KCUS,WinNT://KCUS/O365-Teams-Service-Administrator,E18722,Owner,Teams Service Administrators will be assigned using this Group,
Hostapp- Eupromaxprodcopy,KCUS,WinNT://KCUS/Hostapp- Eupromaxprodcopy,E18722,Owner,Citrix Security group for Eupromax prodcopy,
Hostapp- Eupromaxprodcopy,KCUS,WinNT://KCUS/Hostapp- Eupromaxprodcopy,Q07708,Authorizer,Citrix Security group for Eupromax prodcopy,
Hostapp- Eupromaxprodcopy,KCUS,WinNT://KCUS/Hostapp- Eupromaxprodcopy,Q13357,Authorizer,Citrix Security group for Eupromax prodcopy,
Hostapp- Eupromaxprodcopy,KCUS,WinNT://KCUS/Hostapp- Eupromaxprodcopy,Q14616,Authorizer,Citrix Security group for Eupromax prodcopy,
Hostapp- Eupromaxprodcopy,KCUS,WinNT://KCUS/Hostapp- Eupromaxprodcopy,Q14718,Authorizer,Citrix Security group for Eupromax prodcopy,
Hostapp- Eupromaxprodcopy,KCUS,WinNT://KCUS/Hostapp- Eupromaxprodcopy,Q15926,Authorizer,Citrix Security group for Eupromax prodcopy,
Hostapp- Eupromaxprodcopy,KCUS,WinNT://KCUS/Hostapp- Eupromaxprodcopy,W49040,Delegate,Citrix Security group for Eupromax prodcopy,
Mailbox Search and Import-Export,KCUS,WinNT://KCUS/Mailbox Search and Import-Export,E18722,Owner,Elevated role group to search mailboxes and run import/export cmlets,
Mailbox Search and Import-Export,KCUS,WinNT://KCUS/Mailbox Search and Import-Export,W62002,Delegate,Elevated role group to search mailboxes and run import/export cmlets,
Managed Availability Servers,KCUS,WinNT://KCUS/Managed Availability Servers,E18722,Owner,This group contains all the Managed Availability servers. This group shouldn't be deleted.,
Managed Availability Servers,KCUS,WinNT://KCUS/Managed Availability Servers,W62002,Delegate,This group contains all the Managed Availability servers. This group shouldn't be deleted.,
Records Management,KCUS,WinNT://KCUS/Records Management,E18722,Owner,Members of this management role group can configure compliance features such as retention policy tags  message classifications  transport rules  and more.,
Records Management,KCUS,WinNT://KCUS/Records Management,W62002,Delegate,Members of this management role group can configure compliance features such as retention policy tags  message classifications  transport rules  and more.,
www.messagingsa.kcc.com_developers,KCUS,WinNT://KCUS/www.messagingsa.kcc.com_developers,W62002,Delegate,Group for website developers,
www.messagingsa.kcc.com_developers,KCUS,WinNT://KCUS/www.messagingsa.kcc.com_developers,E18722,Owner,Group for website developers,
www.messagingsa.kcc.com_agencies,KCUS,WinNT://KCUS/www.messagingsa.kcc.com_agencies,E18722,Owner,Group for agencies,
www.messagingsa.kcc.com_agencies,KCUS,WinNT://KCUS/www.messagingsa.kcc.com_agencies,W62002,Delegate,Group for agencies,
Test Role - Computer Security,KCUS,WinNT://KCUS/Test Role - Computer Security,E18722,Owner,,
Test Role - Computer Security,KCUS,WinNT://KCUS/Test Role - Computer Security,W66442,Delegate,,
SCCMApp-Adobe Creative Cloud 2017 - Design,KCUS,WinNT://KCUS/SCCMApp-Adobe Creative Cloud 2017 - Design,E18722,Owner,package for Adobe Creative Cloud 2017 - Design (Custom Plan).,
SCCMApp-Adobe Creative Cloud 2017 - Design,KCUS,WinNT://KCUS/SCCMApp-Adobe Creative Cloud 2017 - Design,W48997,Delegate,package for Adobe Creative Cloud 2017 - Design (Custom Plan).,
SCCMApp-Adobe Creative Cloud 2017 - Web,KCUS,WinNT://KCUS/SCCMApp-Adobe Creative Cloud 2017 - Web,E18722,Owner,package for Adobe Creative Cloud 2017 - Web (Custom Plan).,
SCCMApp-Adobe Creative Cloud 2017 - Web,KCUS,WinNT://KCUS/SCCMApp-Adobe Creative Cloud 2017 - Web,W48997,Delegate,package for Adobe Creative Cloud 2017 - Web (Custom Plan).,
UM Prompt Admins,KCUS,WinNT://KCUS/UM Prompt Admins,E18722,Owner,Role group to allow certain users access to change auto attendant prompts in UM,
UM Prompt Admins,KCUS,WinNT://KCUS/UM Prompt Admins,W66442,Delegate,Role group to allow certain users access to change auto attendant prompts in UM,
SCCMApps-MSProjectProfessional2010,KCUS,WinNT://KCUS/SCCMApps-MSProjectProfessional2010,E18722,Owner,MS Project Professional 2010 Distribution Group,
SCCMApps-MSProjectProfessional2010,KCUS,WinNT://KCUS/SCCMApps-MSProjectProfessional2010,W48997,Delegate,MS Project Professional 2010 Distribution Group,
SCCMApps-MSProjectStandard2010,KCUS,WinNT://KCUS/SCCMApps-MSProjectStandard2010,E18722,Owner,MS Project Standard 2010 Distribution Group,
SCCMApps-MSProjectStandard2010,KCUS,WinNT://KCUS/SCCMApps-MSProjectStandard2010,W48997,Delegate,MS Project Standard 2010 Distribution Group,
HostAppMenu-Eclipse,KCUS,WinNT://KCUS/HostAppMenu-Eclipse,E18722,Owner,Citrix user security group for Eclipse application,
HostAppMenu-Eclipse,KCUS,WinNT://KCUS/HostAppMenu-Eclipse,W49040,Delegate,Citrix user security group for Eclipse application,
HostAppMenu-Eclipse,KCUS,WinNT://KCUS/HostAppMenu-Eclipse,W61837,Authorizer,Citrix user security group for Eclipse application,
HostAppMenu-Eclipse,KCUS,WinNT://KCUS/HostAppMenu-Eclipse,W62585,Authorizer,Citrix user security group for Eclipse application,
HostApp-Eclipse,KCUS,WinNT://KCUS/HostApp-Eclipse,E18722,Owner,Citrix user security group for Eclipse application,
HostApp-Eclipse,KCUS,WinNT://KCUS/HostApp-Eclipse,W49040,Delegate,Citrix user security group for Eclipse application,
InfoPath 2013 for Office 365,KCUS,WinNT://KCUS/InfoPath 2013 for Office 365,E18722,Owner,Add InfoPath 2013 for Office 365 ProPlus Subscription to non-core software request page.,
InfoPath 2013 for Office 365,KCUS,WinNT://KCUS/InfoPath 2013 for Office 365,W48997,Delegate,Add InfoPath 2013 for Office 365 ProPlus Subscription to non-core software request page.,
O365-PrjOnl-Ess,KCUS,WinNT://KCUS/O365-PrjOnl-Ess,E18722,Owner,To manage MS Project Online essentials Licenses,
O365-PrjOnl-Ess,KCUS,WinNT://KCUS/O365-PrjOnl-Ess,W49040,Delegate,To manage MS Project Online essentials Licenses,
O365-PrjOnl-Ess,KCUS,WinNT://KCUS/O365-PrjOnl-Ess,W66442,Authorizer,To manage MS Project Online essentials Licenses,
Help Desk,KCUS,WinNT://KCUS/Help Desk,E18722,Owner,Members of this management role group can view and manage the configuration for individual recipients and view recipients in an Exchange organization. Members of this role group can only manage the configuration each user can manage on his or her own mailbox. Additional  permissions can be added by assigning additional management roles to this role group.,
Help Desk,KCUS,WinNT://KCUS/Help Desk,W62002,Delegate,Members of this management role group can view and manage the configuration for individual recipients and view recipients in an Exchange organization. Members of this role group can only manage the configuration each user can manage on his or her own mailbox. Additional  permissions can be added by assigning additional management roles to this role group.,
O365-E3M-Dynamic-Restricted Access,KCUS,WinNT://KCUS/O365-E3M-Dynamic-Restricted Access,E11888,Owner,kcc.com/Accounts/GlobalGroups/Other/O365-E3M-Dynamic-Restricted Access,
O365-E3M-Dynamic-Restricted Access,KCUS,WinNT://KCUS/O365-E3M-Dynamic-Restricted Access,E18722,Delegate,kcc.com/Accounts/GlobalGroups/Other/O365-E3M-Dynamic-Restricted Access,
HostAppMenu-RAD,KCUS,WinNT://KCUS/HostAppMenu-RAD,E18722,Owner,HostAppMenu-RAD,
HostAppMenu-RAD,KCUS,WinNT://KCUS/HostAppMenu-RAD,Q07708,Authorizer,HostAppMenu-RAD,
HostAppMenu-RAD,KCUS,WinNT://KCUS/HostAppMenu-RAD,Q14616,Authorizer,HostAppMenu-RAD,
HostAppMenu-RAD,KCUS,WinNT://KCUS/HostAppMenu-RAD,W49040,Delegate,HostAppMenu-RAD,
CRLWGrpAdmin,KCUS,WinNT://KCUS/CRLWGrpAdmin,E18722,Owner,FTZ Warehouse Desamparados -Site Group Administrators ,
CRLWGrpAdmin,KCUS,WinNT://KCUS/CRLWGrpAdmin,Q08911,Authorizer,FTZ Warehouse Desamparados -Site Group Administrators ,
CRLWGrpAdmin,KCUS,WinNT://KCUS/CRLWGrpAdmin,W48997,Delegate,FTZ Warehouse Desamparados -Site Group Administrators ,
SCCMApps-MSProjectProfessional2013,KCUS,WinNT://KCUS/SCCMApps-MSProjectProfessional2013,W48997,Delegate,Project Professional 2013 Distribution Group,
SCCMApps-MSProjectProfessional2013,KCUS,WinNT://KCUS/SCCMApps-MSProjectProfessional2013,E18722,Owner,Project Professional 2013 Distribution Group,
SCCMApps-MSProjectProfessional2013,KCUS,WinNT://KCUS/SCCMApps-MSProjectProfessional2013,Q04499,Authorizer,Project Professional 2013 Distribution Group,
SCCMApps-MSProjectProfessional2013,KCUS,WinNT://KCUS/SCCMApps-MSProjectProfessional2013,Q39116,Authorizer,Project Professional 2013 Distribution Group,
SCCMApps-MSProjectStandard2013,KCUS,WinNT://KCUS/SCCMApps-MSProjectStandard2013,E18722,Owner,MS Project Standard 2013 Distribution Group,
SCCMApps-MSProjectStandard2013,KCUS,WinNT://KCUS/SCCMApps-MSProjectStandard2013,Q04499,Authorizer,MS Project Standard 2013 Distribution Group,
SCCMApps-MSProjectStandard2013,KCUS,WinNT://KCUS/SCCMApps-MSProjectStandard2013,Q06085,Authorizer,MS Project Standard 2013 Distribution Group,
SCCMApps-MSProjectStandard2013,KCUS,WinNT://KCUS/SCCMApps-MSProjectStandard2013,W48997,Delegate,MS Project Standard 2013 Distribution Group,
SCCMApps-MSVisioProfessional2013,KCUS,WinNT://KCUS/SCCMApps-MSVisioProfessional2013,E18722,Owner,MS Visio Professional 2013 Distribution Group,
SCCMApps-MSVisioProfessional2013,KCUS,WinNT://KCUS/SCCMApps-MSVisioProfessional2013,Q04499,Delegate,MS Visio Professional 2013 Distribution Group,
SCCMApps-MSVisioProfessional2013,KCUS,WinNT://KCUS/SCCMApps-MSVisioProfessional2013,Q06085,Authorizer,MS Visio Professional 2013 Distribution Group,
SCCMApps-MSVisioProfessional2013,KCUS,WinNT://KCUS/SCCMApps-MSVisioProfessional2013,W48997,Delegate,MS Visio Professional 2013 Distribution Group,
SCCMApps-MSVisioStandard2013,KCUS,WinNT://KCUS/SCCMApps-MSVisioStandard2013,E18722,Owner,MS Visio Standard 2013 Distribution Group,
SCCMApps-MSVisioStandard2013,KCUS,WinNT://KCUS/SCCMApps-MSVisioStandard2013,Q04499,Authorizer,MS Visio Standard 2013 Distribution Group,
SCCMApps-MSVisioStandard2013,KCUS,WinNT://KCUS/SCCMApps-MSVisioStandard2013,Q06085,Authorizer,MS Visio Standard 2013 Distribution Group,
SCCMApps-MSVisioStandard2013,KCUS,WinNT://KCUS/SCCMApps-MSVisioStandard2013,W48997,Delegate,MS Visio Standard 2013 Distribution Group,
SCCMApps-MSAccess2013,KCUS,WinNT://KCUS/SCCMApps-MSAccess2013,E18722,Owner,MS Access 2013 Distribution Group,
SCCMApps-MSAccess2013,KCUS,WinNT://KCUS/SCCMApps-MSAccess2013,Q04499,Authorizer,MS Access 2013 Distribution Group,
SCCMApps-MSAccess2013,KCUS,WinNT://KCUS/SCCMApps-MSAccess2013,Q06085,Authorizer,MS Access 2013 Distribution Group,
SCCMApps-MSAccess2013,KCUS,WinNT://KCUS/SCCMApps-MSAccess2013,W48997,Delegate,MS Access 2013 Distribution Group,
Citrix Venafi Admins,KCUS,WinNT://KCUS/Citrix Venafi Admins,e18722,Owner,n/a,
Citrix Venafi Admins,KCUS,WinNT://KCUS/Citrix Venafi Admins,W49040,Delegate,n/a,
ReInstall_Avecto,KCUS,WinNT://KCUS/ReInstall_Avecto,E18722,Owner,Group used to reinstall Avecto to users in ILTA & ILTZ site,
ReInstall_Avecto,KCUS,WinNT://KCUS/ReInstall_Avecto,W48997,Delegate,Group used to reinstall Avecto to users in ILTA & ILTZ site,
DL000512,KCUS,WinNT://KCUS/DL000512,E18722,Owner,+Z 00 Recipients Alternate 37,
DL000512,KCUS,WinNT://KCUS/DL000512,W62002,Delegate,+Z 00 Recipients Alternate 37,
FC-SiteCoreUsers,KCUS,WinNT://KCUS/FC-SiteCoreUsers,E18722,Owner,Sitecore Web Development VDI Users,
FC-SiteCoreUsers,KCUS,WinNT://KCUS/FC-SiteCoreUsers,Q07708,Authorizer,Sitecore Web Development VDI Users,
FC-SiteCoreUsers,KCUS,WinNT://KCUS/FC-SiteCoreUsers,Q13357,Authorizer,Sitecore Web Development VDI Users,
FC-SiteCoreUsers,KCUS,WinNT://KCUS/FC-SiteCoreUsers,Q14616,Authorizer,Sitecore Web Development VDI Users,
FC-SiteCoreUsers,KCUS,WinNT://KCUS/FC-SiteCoreUsers,Q14718,Authorizer,Sitecore Web Development VDI Users,
FC-SiteCoreUsers,KCUS,WinNT://KCUS/FC-SiteCoreUsers,Q15926,Authorizer,Sitecore Web Development VDI Users,
FC-SiteCoreUsers,KCUS,WinNT://KCUS/FC-SiteCoreUsers,W49040,Delegate,Sitecore Web Development VDI Users,
USTCFF73_PSApprovers,KCUS,WinNT://KCUS/USTCFF73_PSApprovers,B10717,Delegate,Approvers - US KC North  SCCM Server Firefight,
USTCFF73_PSApprovers,KCUS,WinNT://KCUS/USTCFF73_PSApprovers,E18722,Owner,Approvers - US KC North  SCCM Server Firefight,
USTCFF74_PSApprovers,KCUS,WinNT://KCUS/USTCFF74_PSApprovers,B10717,Delegate,Approvers - US KC North  SCCM Server Firefight,
USTCFF74_PSApprovers,KCUS,WinNT://KCUS/USTCFF74_PSApprovers,E18722,Owner,Approvers - US KC North  SCCM Server Firefight,
OrchestratorUsersGroup,KCUS,WinNT://KCUS/OrchestratorUsersGroup,E18722,Owner,,
OrchestratorUsersGroup,KCUS,WinNT://KCUS/OrchestratorUsersGroup,W66442,Delegate,,
O365-OF3-NonUser,KCUS,WinNT://KCUS/O365-OF3-NonUser,E18722,Owner,O365 F3 License for non-user accounts  default plan,
O365-OF3-NonUser,KCUS,WinNT://KCUS/O365-OF3-NonUser,Q03582,Authorizer,O365 F3 License for non-user accounts  default plan,
O365-OF3-NonUser,KCUS,WinNT://KCUS/O365-OF3-NonUser,Q08525,Authorizer,O365 F3 License for non-user accounts  default plan,
O365-OF3-NonUser,KCUS,WinNT://KCUS/O365-OF3-NonUser,Q09038,Authorizer,O365 F3 License for non-user accounts  default plan,
O365-OF3-NonUser,KCUS,WinNT://KCUS/O365-OF3-NonUser,Q10438,Authorizer,O365 F3 License for non-user accounts  default plan,
O365-OF3-NonUser,KCUS,WinNT://KCUS/O365-OF3-NonUser,Q11533,Authorizer,O365 F3 License for non-user accounts  default plan,
O365-OF3-NonUser,KCUS,WinNT://KCUS/O365-OF3-NonUser,W62002,Delegate,O365 F3 License for non-user accounts  default plan,
HostApp-ETQ,KCUS,WinNT://KCUS/HostApp-ETQ,E18722,Owner,n/a,
HostApp-ETQ,KCUS,WinNT://KCUS/HostApp-ETQ,Q07708,Authorizer,n/a,
HostApp-ETQ,KCUS,WinNT://KCUS/HostApp-ETQ,Q14616,Authorizer,n/a,
HostApp-ETQ,KCUS,WinNT://KCUS/HostApp-ETQ,W49040,Delegate,n/a,
K2 Database Write,KCUS,WinNT://KCUS/K2 Database Write,E18722,Owner,Read access to K2 databases,
K2 Database Write,KCUS,WinNT://KCUS/K2 Database Write,Q08911,Authorizer,Read access to K2 databases,
K2 Database Write,KCUS,WinNT://KCUS/K2 Database Write,W49040,Delegate,Read access to K2 databases,
Hostapp-HANA,KCUS,WinNT://KCUS/Hostapp-HANA,E18722,Owner,Citrix Application Security group for hosting HANA,
Hostapp-HANA,KCUS,WinNT://KCUS/Hostapp-HANA,Q07708,Authorizer,Citrix Application Security group for hosting HANA,
Hostapp-HANA,KCUS,WinNT://KCUS/Hostapp-HANA,Q13357,Authorizer,Citrix Application Security group for hosting HANA,
Hostapp-HANA,KCUS,WinNT://KCUS/Hostapp-HANA,Q14616,Authorizer,Citrix Application Security group for hosting HANA,
Hostapp-HANA,KCUS,WinNT://KCUS/Hostapp-HANA,Q14718,Authorizer,Citrix Application Security group for hosting HANA,
Hostapp-HANA,KCUS,WinNT://KCUS/Hostapp-HANA,Q15926,Authorizer,Citrix Application Security group for hosting HANA,
Hostapp-HANA,KCUS,WinNT://KCUS/Hostapp-HANA,W49040,Delegate,Citrix Application Security group for hosting HANA,
TCS VDI staff,KCUS,WinNT://KCUS/TCS VDI staff,E18722,Owner,VDI Team Members,
TCS VDI staff,KCUS,WinNT://KCUS/TCS VDI staff,Q13357,Authorizer,VDI Team Members,
TCS VDI staff,KCUS,WinNT://KCUS/TCS VDI staff,Q14718,Authorizer,VDI Team Members,
TCS VDI staff,KCUS,WinNT://KCUS/TCS VDI staff,Q15926,Authorizer,VDI Team Members,
TCS VDI staff,KCUS,WinNT://KCUS/TCS VDI staff,W49040,Delegate,VDI Team Members,
Enable_RDP_Autologon_Thinclient_Kiosk,KCUS,WinNT://KCUS/Enable_RDP_Autologon_Thinclient_Kiosk,E18722,Owner,This group will have the list of Thinclient Kiosk PC and will apply the GPO to this group to enable autologon using RDP,
Enable_RDP_Autologon_Thinclient_Kiosk,KCUS,WinNT://KCUS/Enable_RDP_Autologon_Thinclient_Kiosk,W48997,Delegate,This group will have the list of Thinclient Kiosk PC and will apply the GPO to this group to enable autologon using RDP,
GRP08939_C,KCUS,WinNT://KCUS/GRP08939_C,E18722,Owner,Mailbox:_Support  SharePoint Platformts,
GRP08939_C,KCUS,WinNT://KCUS/GRP08939_C,Q08911,Authorizer,Mailbox:_Support  SharePoint Platformts,
GRP08939_C,KCUS,WinNT://KCUS/GRP08939_C,W49040,Delegate,Mailbox:_Support  SharePoint Platformts,
HostApp-Naviswork,KCUS,WinNT://KCUS/HostApp-Naviswork,E18722,Owner,Citrix Application Security group for hosting Naviswork,
HostApp-Naviswork,KCUS,WinNT://KCUS/HostApp-Naviswork,Q07708,Authorizer,Citrix Application Security group for hosting Naviswork,
HostApp-Naviswork,KCUS,WinNT://KCUS/HostApp-Naviswork,Q13357,Authorizer,Citrix Application Security group for hosting Naviswork,
HostApp-Naviswork,KCUS,WinNT://KCUS/HostApp-Naviswork,Q14616,Authorizer,Citrix Application Security group for hosting Naviswork,
HostApp-Naviswork,KCUS,WinNT://KCUS/HostApp-Naviswork,Q14718,Authorizer,Citrix Application Security group for hosting Naviswork,
HostApp-Naviswork,KCUS,WinNT://KCUS/HostApp-Naviswork,Q15926,Authorizer,Citrix Application Security group for hosting Naviswork,
HostApp-Naviswork,KCUS,WinNT://KCUS/HostApp-Naviswork,W49040,Delegate,Citrix Application Security group for hosting Naviswork,
Hostapp-KCandME,KCUS,WinNT://KCUS/Hostapp-KCandME,W49040,Delegate,Citrix access group for KCandME,
Hostapp-KCandME,KCUS,WinNT://KCUS/Hostapp-KCandME,E18722,Owner,Citrix access group for KCandME,
Hostapp-KCandME,KCUS,WinNT://KCUS/Hostapp-KCandME,Q07708,Authorizer,Citrix access group for KCandME,
Hostapp-KCandME,KCUS,WinNT://KCUS/Hostapp-KCandME,Q13357,Authorizer,Citrix access group for KCandME,
Hostapp-KCandME,KCUS,WinNT://KCUS/Hostapp-KCandME,Q14616,Authorizer,Citrix access group for KCandME,
Hostapp-KCandME,KCUS,WinNT://KCUS/Hostapp-KCandME,Q14718,Authorizer,Citrix access group for KCandME,
Hostapp-KCandME,KCUS,WinNT://KCUS/Hostapp-KCandME,Q15926,Authorizer,Citrix access group for KCandME,
Intune CA Exclusion,KCUS,WinNT://KCUS/Intune CA Exclusion,E18722,Owner,Excluding users from CA (Disabling Native client),
Intune CA Exclusion,KCUS,WinNT://KCUS/Intune CA Exclusion,W66442,Delegate,Excluding users from CA (Disabling Native client),
KCMsg_Video,KCUS,WinNT://KCUS/KCMsg_Video,E18722,Owner,users that have viewed an executive KCMsg video,
KCMsg_Video,KCUS,WinNT://KCUS/KCMsg_Video,Q06085,Authorizer,users that have viewed an executive KCMsg video,
KCMsg_Video,KCUS,WinNT://KCUS/KCMsg_Video,Q08911,Authorizer,users that have viewed an executive KCMsg video,
KCMsg_Video,KCUS,WinNT://KCUS/KCMsg_Video,W48997,Delegate,users that have viewed an executive KCMsg video,
Office 365 Semi Annual Channel,KCUS,WinNT://KCUS/Office 365 Semi Annual Channel,E18722,Owner,This group will be used to enable the O365 Semi annual Channel updates through GPO,
Office 365 Semi Annual Channel,KCUS,WinNT://KCUS/Office 365 Semi Annual Channel,q04499,Delegate,This group will be used to enable the O365 Semi annual Channel updates through GPO,
Office 365 Semi Annual Channel,KCUS,WinNT://KCUS/Office 365 Semi Annual Channel,W62002,Authorizer,This group will be used to enable the O365 Semi annual Channel updates through GPO,
Search Crawlers,KCUS,WinNT://KCUS/Search Crawlers,E18722,Owner,Search Crawlers,
Search Crawlers,KCUS,WinNT://KCUS/Search Crawlers,W49040,Delegate,Search Crawlers,
SCCM Application Dev QA,KCUS,WinNT://KCUS/SCCM Application Dev QA,E18722,Owner,SCCM Workstation Application Authors,
SCCM Application Dev QA,KCUS,WinNT://KCUS/SCCM Application Dev QA,Q06085,Authorizer,SCCM Workstation Application Authors,
SCCM Application Dev QA,KCUS,WinNT://KCUS/SCCM Application Dev QA,W48997,Delegate,SCCM Workstation Application Authors,
SCCM Application Dev QA,KCUS,WinNT://KCUS/SCCM Application Dev QA,W66442,Authorizer,SCCM Workstation Application Authors,
Exchange Services,KCUS,WinNT://KCUS/Exchange Services,E18722,Owner,Exchange Services -- DO NOT move or rename,
Exchange Services,KCUS,WinNT://KCUS/Exchange Services,W62002,Delegate,Exchange Services -- DO NOT move or rename,
GRP09286_C,KCUS,WinNT://KCUS/GRP09286_C,E18722,Owner,Mailbox:_Desktop  Engineering,
GRP09286_C,KCUS,WinNT://KCUS/GRP09286_C,Q08911,Authorizer,Mailbox:_Desktop  Engineering,
GRP09286_C,KCUS,WinNT://KCUS/GRP09286_C,W48997,Delegate,Mailbox:_Desktop  Engineering,
O365_Intune_mobile,KCUS,WinNT://KCUS/O365_Intune_mobile,E18722,Owner,Default acces for Intune mobile users,
O365_Intune_mobile,KCUS,WinNT://KCUS/O365_Intune_mobile,E32872,Authorizer,Default acces for Intune mobile users,
O365_Intune_mobile,KCUS,WinNT://KCUS/O365_Intune_mobile,w48997,Authorizer,Default acces for Intune mobile users,
O365_Intune_mobile,KCUS,WinNT://KCUS/O365_Intune_mobile,W62002,Delegate,Default acces for Intune mobile users,
O365_Add_in_SmartCloudConnect,KCUS,WinNT://KCUS/O365_Add_in_SmartCloudConnect,E18722,Owner,Users of SmartCloud Connect SFDC add-in to Outlook,
O365_Add_in_SmartCloudConnect,KCUS,WinNT://KCUS/O365_Add_in_SmartCloudConnect,W62002,Delegate,Users of SmartCloud Connect SFDC add-in to Outlook,
MSG_EXOBJ_C_D,KCUS,WinNT://KCUS/MSG_EXOBJ_C_D,E18722,Owner,Provides db_writer access to development devmsgdb for creating exchange objects,
MSG_EXOBJ_C_D,KCUS,WinNT://KCUS/MSG_EXOBJ_C_D,W62002,Delegate,Provides db_writer access to development devmsgdb for creating exchange objects,
HostApp-A4O,KCUS,WinNT://KCUS/HostApp-A4O,E18722,Owner,Citrix Security group for Analysis for Office apps,
HostApp-A4O,KCUS,WinNT://KCUS/HostApp-A4O,W49040,Delegate,Citrix Security group for Analysis for Office apps,
O365_E3M,KCUS,WinNT://KCUS/O365_E3M,E18722,Owner,O365 E3M License,
O365_E3M,KCUS,WinNT://KCUS/O365_E3M,Q03582,Authorizer,O365 E3M License,
O365_E3M,KCUS,WinNT://KCUS/O365_E3M,Q08525,Authorizer,O365 E3M License,
O365_E3M,KCUS,WinNT://KCUS/O365_E3M,Q09038,Authorizer,O365 E3M License,
O365_E3M,KCUS,WinNT://KCUS/O365_E3M,Q10438,Authorizer,O365 E3M License,
O365_E3M,KCUS,WinNT://KCUS/O365_E3M,Q11533,Authorizer,O365 E3M License,
O365_E3M,KCUS,WinNT://KCUS/O365_E3M,W62002,Delegate,O365 E3M License,
O365_E3_PROJECTPROFESSIONAL,KCUS,WinNT://KCUS/O365_E3_PROJECTPROFESSIONAL,W62002,Delegate,O365 E3 Project Professional,
O365_E3_PROJECTPROFESSIONAL,KCUS,WinNT://KCUS/O365_E3_PROJECTPROFESSIONAL,E18722,Owner,O365 E3 Project Professional,
Hostapp-MicrosoftEdge,KCUS,WinNT://KCUS/Hostapp-MicrosoftEdge,E18722,Owner,Citrix Security Groups for Microsoft Edge for Business,
Hostapp-MicrosoftEdge,KCUS,WinNT://KCUS/Hostapp-MicrosoftEdge,Q07708,Authorizer,Citrix Security Groups for Microsoft Edge for Business,
Hostapp-MicrosoftEdge,KCUS,WinNT://KCUS/Hostapp-MicrosoftEdge,Q14616,Authorizer,Citrix Security Groups for Microsoft Edge for Business,
Hostapp-MicrosoftEdge,KCUS,WinNT://KCUS/Hostapp-MicrosoftEdge,w49040,Delegate,Citrix Security Groups for Microsoft Edge for Business,
O365_E3,KCUS,WinNT://KCUS/O365_E3,E18722,Owner,O365 E3 license,
O365_E3,KCUS,WinNT://KCUS/O365_E3,Q08525,Authorizer,O365 E3 license,
O365_E3,KCUS,WinNT://KCUS/O365_E3,Q09038,Authorizer,O365 E3 license,
O365_E3,KCUS,WinNT://KCUS/O365_E3,W62002,Delegate,O365 E3 license,
HostAppMenu-Desktop-Services,KCUS,WinNT://KCUS/HostAppMenu-Desktop-Services,E18722,Owner,Citrix User security group for Citrix L2 Desktop Services Team,
HostAppMenu-Desktop-Services,KCUS,WinNT://KCUS/HostAppMenu-Desktop-Services,Q07708,Authorizer,Citrix User security group for Citrix L2 Desktop Services Team,
HostAppMenu-Desktop-Services,KCUS,WinNT://KCUS/HostAppMenu-Desktop-Services,Q14616,Authorizer,Citrix User security group for Citrix L2 Desktop Services Team,
HostAppMenu-Desktop-Services,KCUS,WinNT://KCUS/HostAppMenu-Desktop-Services,W49040,Delegate,Citrix User security group for Citrix L2 Desktop Services Team,
O365-E3-Dynamic-Restricted Access,KCUS,WinNT://KCUS/O365-E3-Dynamic-Restricted Access,E11888,Owner,kcc.com/Accounts/GlobalGroups/Other/O365-E3-Dynamic-Restricted Access,
O365-E3-Dynamic-Restricted Access,KCUS,WinNT://KCUS/O365-E3-Dynamic-Restricted Access,E18722,Delegate,kcc.com/Accounts/GlobalGroups/Other/O365-E3-Dynamic-Restricted Access,
SCCMApp - Microsoft Whiteboard,KCUS,WinNT://KCUS/SCCMApp - Microsoft Whiteboard,E18722,Owner,package for Microsoft Whiteboard,
SCCMApp - Microsoft Whiteboard,KCUS,WinNT://KCUS/SCCMApp - Microsoft Whiteboard,Q13243,Authorizer,package for Microsoft Whiteboard,
SCCMApp - Microsoft Whiteboard,KCUS,WinNT://KCUS/SCCMApp - Microsoft Whiteboard,W48997,Delegate,package for Microsoft Whiteboard,
O365 Stream Administrators,KCUS,WinNT://KCUS/O365 Stream Administrators,E18722,Owner,Members of the group will have an access to O365 Stream administrator portal,
O365 Stream Administrators,KCUS,WinNT://KCUS/O365 Stream Administrators,W49040,Delegate,Members of the group will have an access to O365 Stream administrator portal,
Critical Application Testers,KCUS,WinNT://KCUS/Critical Application Testers,E18722,Owner,,
Critical Application Testers,KCUS,WinNT://KCUS/Critical Application Testers,W48997,Delegate,,
Security Administrator,KCUS,WinNT://KCUS/Security Administrator,E18722,Owner,Membership in this role group is synchronized across services and managed centrally,
Security Administrator,KCUS,WinNT://KCUS/Security Administrator,W62002,Delegate,Membership in this role group is synchronized across services and managed centrally,
Adobe Proxy Bypass,KCUS,WinNT://KCUS/Adobe Proxy Bypass,E18722,Owner,This will be used in a proxy bypass rule so that users can install the software,
Adobe Proxy Bypass,KCUS,WinNT://KCUS/Adobe Proxy Bypass,W48997,Delegate,This will be used in a proxy bypass rule so that users can install the software,
GRP12462_C,KCUS,WinNT://KCUS/GRP12462_C,E18722,Owner,Mailbox: _Desktop Services  Automation,
GRP12462_C,KCUS,WinNT://KCUS/GRP12462_C,q73491,Authorizer,Mailbox: _Desktop Services  Automation,
GRP12462_C,KCUS,WinNT://KCUS/GRP12462_C,W48997,Delegate,Mailbox: _Desktop Services  Automation,
HostAppMenu-OfficeProPlus,KCUS,WinNT://KCUS/HostAppMenu-OfficeProPlus,E18722,Owner,Citrix access group for MS OfficeProPlus Applications,
HostAppMenu-OfficeProPlus,KCUS,WinNT://KCUS/HostAppMenu-OfficeProPlus,q07708,Authorizer,Citrix access group for MS OfficeProPlus Applications,
HostAppMenu-OfficeProPlus,KCUS,WinNT://KCUS/HostAppMenu-OfficeProPlus,Q13357,Authorizer,Citrix access group for MS OfficeProPlus Applications,
HostAppMenu-OfficeProPlus,KCUS,WinNT://KCUS/HostAppMenu-OfficeProPlus,Q14616,Authorizer,Citrix access group for MS OfficeProPlus Applications,
HostAppMenu-OfficeProPlus,KCUS,WinNT://KCUS/HostAppMenu-OfficeProPlus,Q14718,Authorizer,Citrix access group for MS OfficeProPlus Applications,
HostAppMenu-OfficeProPlus,KCUS,WinNT://KCUS/HostAppMenu-OfficeProPlus,Q15926,Authorizer,Citrix access group for MS OfficeProPlus Applications,
HostAppMenu-OfficeProPlus,KCUS,WinNT://KCUS/HostAppMenu-OfficeProPlus,W49040,Delegate,Citrix access group for MS OfficeProPlus Applications,
SCCMApp-Adobe Creative Cloud - All Apps,KCUS,WinNT://KCUS/SCCMApp-Adobe Creative Cloud - All Apps,e18722,Owner,package for Adobe Creative Cloud - All Apps.,
SCCMApp-Adobe Creative Cloud - All Apps,KCUS,WinNT://KCUS/SCCMApp-Adobe Creative Cloud - All Apps,W48997,Delegate,package for Adobe Creative Cloud - All Apps.,
HostApp-OfficeProPlus,KCUS,WinNT://KCUS/HostApp-OfficeProPlus,E18722,Owner,Citrix access group for MS OfficeProPlus Applications,
HostApp-OfficeProPlus,KCUS,WinNT://KCUS/HostApp-OfficeProPlus,q07708,Authorizer,Citrix access group for MS OfficeProPlus Applications,
HostApp-OfficeProPlus,KCUS,WinNT://KCUS/HostApp-OfficeProPlus,Q13357,Authorizer,Citrix access group for MS OfficeProPlus Applications,
HostApp-OfficeProPlus,KCUS,WinNT://KCUS/HostApp-OfficeProPlus,Q14616,Authorizer,Citrix access group for MS OfficeProPlus Applications,
HostApp-OfficeProPlus,KCUS,WinNT://KCUS/HostApp-OfficeProPlus,Q14718,Authorizer,Citrix access group for MS OfficeProPlus Applications,
HostApp-OfficeProPlus,KCUS,WinNT://KCUS/HostApp-OfficeProPlus,Q15926,Authorizer,Citrix access group for MS OfficeProPlus Applications,
HostApp-OfficeProPlus,KCUS,WinNT://KCUS/HostApp-OfficeProPlus,W49040,Delegate,Citrix access group for MS OfficeProPlus Applications,
O365 Reports Reader Administrators,KCUS,WinNT://KCUS/O365 Reports Reader Administrators,e18722,Owner,This group will be used to manage as Reports Reader in the O365 tenant,
O365 Reports Reader Administrators,KCUS,WinNT://KCUS/O365 Reports Reader Administrators,Q03582,Authorizer,This group will be used to manage as Reports Reader in the O365 tenant,
O365 Reports Reader Administrators,KCUS,WinNT://KCUS/O365 Reports Reader Administrators,W62002,Delegate,This group will be used to manage as Reports Reader in the O365 tenant,
CNKTWksadmin,KCUS,WinNT://KCUS/CNKTWksadmin,E18722,Owner,Kimtech East Mill-Site Computer Account Administrators,
CNKTWksadmin,KCUS,WinNT://KCUS/CNKTWksadmin,Q06085,Authorizer,Kimtech East Mill-Site Computer Account Administrators,
CNKTWksadmin,KCUS,WinNT://KCUS/CNKTWksadmin,Q08911,Authorizer,Kimtech East Mill-Site Computer Account Administrators,
CNKTWksadmin,KCUS,WinNT://KCUS/CNKTWksadmin,W48997,Delegate,Kimtech East Mill-Site Computer Account Administrators,
FC-CASDevUsers,KCUS,WinNT://KCUS/FC-CASDevUsers,W49040,Delegate,Full Clone CAS Team Development users,
FC-CASDevUsers,KCUS,WinNT://KCUS/FC-CASDevUsers,E18722,Owner,Full Clone CAS Team Development users,
FC-CASDevUsers,KCUS,WinNT://KCUS/FC-CASDevUsers,Q07708,Authorizer,Full Clone CAS Team Development users,
FC-CASDevUsers,KCUS,WinNT://KCUS/FC-CASDevUsers,Q13357,Authorizer,Full Clone CAS Team Development users,
FC-CASDevUsers,KCUS,WinNT://KCUS/FC-CASDevUsers,Q14616,Authorizer,Full Clone CAS Team Development users,
FC-CASDevUsers,KCUS,WinNT://KCUS/FC-CASDevUsers,Q14718,Authorizer,Full Clone CAS Team Development users,
FC-CASDevUsers,KCUS,WinNT://KCUS/FC-CASDevUsers,Q15926,Authorizer,Full Clone CAS Team Development users,
Citrix Team - Core,KCUS,WinNT://KCUS/Citrix Team - Core,e18722,Owner,Group for Citrix core team administrators regular IDs,
Citrix Team - Core,KCUS,WinNT://KCUS/Citrix Team - Core,Q13357,Authorizer,Group for Citrix core team administrators regular IDs,
Citrix Team - Core,KCUS,WinNT://KCUS/Citrix Team - Core,Q14718,Authorizer,Group for Citrix core team administrators regular IDs,
Citrix Team - Core,KCUS,WinNT://KCUS/Citrix Team - Core,Q15926,Authorizer,Group for Citrix core team administrators regular IDs,
Citrix Team - Core,KCUS,WinNT://KCUS/Citrix Team - Core,W49040,Delegate,Group for Citrix core team administrators regular IDs,
Citrix Team - Level 2 Support,KCUS,WinNT://KCUS/Citrix Team - Level 2 Support,E18722,Owner,Citrix L2 support team  regular IDs,
Citrix Team - Level 2 Support,KCUS,WinNT://KCUS/Citrix Team - Level 2 Support,Q13357,Authorizer,Citrix L2 support team  regular IDs,
Citrix Team - Level 2 Support,KCUS,WinNT://KCUS/Citrix Team - Level 2 Support,Q14718,Authorizer,Citrix L2 support team  regular IDs,
Citrix Team - Level 2 Support,KCUS,WinNT://KCUS/Citrix Team - Level 2 Support,Q15926,Authorizer,Citrix L2 support team  regular IDs,
Citrix Team - Level 2 Support,KCUS,WinNT://KCUS/Citrix Team - Level 2 Support,W49040,Delegate,Citrix L2 support team  regular IDs,
Windows 11 22H2 Upgrade_Ring 1,KCUS,WinNT://KCUS/Windows 11 22H2 Upgrade_Ring 1,E18722,Owner,This Group is to Deploy Windows 11 22H2 update for Ring 1,
Windows 11 22H2 Upgrade_Ring 1,KCUS,WinNT://KCUS/Windows 11 22H2 Upgrade_Ring 1,E32872,Authorizer,This Group is to Deploy Windows 11 22H2 update for Ring 1,
Windows 11 22H2 Upgrade_Ring 1,KCUS,WinNT://KCUS/Windows 11 22H2 Upgrade_Ring 1,Q13243,Authorizer,This Group is to Deploy Windows 11 22H2 update for Ring 1,
Windows 11 22H2 Upgrade_Ring 1,KCUS,WinNT://KCUS/Windows 11 22H2 Upgrade_Ring 1,Q14717,Authorizer,This Group is to Deploy Windows 11 22H2 update for Ring 1,
Windows 11 22H2 Upgrade_Ring 1,KCUS,WinNT://KCUS/Windows 11 22H2 Upgrade_Ring 1,W48997,Delegate,This Group is to Deploy Windows 11 22H2 update for Ring 1,
O365 Teams Service Administrators,KCUS,WinNT://KCUS/O365 Teams Service Administrators,E18722,Owner,Can manage the Microsoft Teams service,
O365 Teams Service Administrators,KCUS,WinNT://KCUS/O365 Teams Service Administrators,W62002,Delegate,Can manage the Microsoft Teams service,
Windows 7 Internet Access,KCUS,WinNT://KCUS/Windows 7 Internet Access,W48997,Delegate,Windows 7 PCs added in this group will get internet access . A sonc approval is needed to add the PCs to this group,
Windows 7 Internet Access,KCUS,WinNT://KCUS/Windows 7 Internet Access,E18722,Owner,Windows 7 PCs added in this group will get internet access . A sonc approval is needed to add the PCs to this group,
MS Visio Standard 2013 - 64 bit,KCUS,WinNT://KCUS/MS Visio Standard 2013 - 64 bit,E18722,Owner,MS Visio Standard 2013 - 64 bit package will be pushed to the users in this group,
MS Visio Standard 2013 - 64 bit,KCUS,WinNT://KCUS/MS Visio Standard 2013 - 64 bit,Q13243,Authorizer,MS Visio Standard 2013 - 64 bit package will be pushed to the users in this group,
MS Visio Standard 2013 - 64 bit,KCUS,WinNT://KCUS/MS Visio Standard 2013 - 64 bit,W48997,Delegate,MS Visio Standard 2013 - 64 bit package will be pushed to the users in this group,
O365 Licensing Exempt Users,KCUS,WinNT://KCUS/O365 Licensing Exempt Users,e18722,Owner,Users excluded from being processed by Licensing Script,
O365 Licensing Exempt Users,KCUS,WinNT://KCUS/O365 Licensing Exempt Users,Q03582,Authorizer,Users excluded from being processed by Licensing Script,
O365 Licensing Exempt Users,KCUS,WinNT://KCUS/O365 Licensing Exempt Users,W62002,Delegate,Users excluded from being processed by Licensing Script,
HostApp-WMS-WFM-LADC,KCUS,WinNT://KCUS/HostApp-WMS-WFM-LADC,E18722,Owner,Citrix Application Security group for hosting Los Angeles Distrbution Center,
HostApp-WMS-WFM-LADC,KCUS,WinNT://KCUS/HostApp-WMS-WFM-LADC,q07708,Authorizer,Citrix Application Security group for hosting Los Angeles Distrbution Center,
HostApp-WMS-WFM-LADC,KCUS,WinNT://KCUS/HostApp-WMS-WFM-LADC,Q14616,Authorizer,Citrix Application Security group for hosting Los Angeles Distrbution Center,
HostApp-WMS-WFM-LADC,KCUS,WinNT://KCUS/HostApp-WMS-WFM-LADC,W49040,Delegate,Citrix Application Security group for hosting Los Angeles Distrbution Center,
O365_E3M_Temp_LitHold,KCUS,WinNT://KCUS/O365_E3M_Temp_LitHold,E18722,Owner,Provides E3M license to turn litigation hold,
O365_E3M_Temp_LitHold,KCUS,WinNT://KCUS/O365_E3M_Temp_LitHold,W62002,Delegate,Provides E3M license to turn litigation hold,
O365_PowerAppsPerUser,KCUS,WinNT://KCUS/O365_PowerAppsPerUser,e18722,Owner,O365 PowerApps Per User Access,
O365_PowerAppsPerUser,KCUS,WinNT://KCUS/O365_PowerAppsPerUser,W62002,Delegate,O365 PowerApps Per User Access,
USNOAP190_C,KCUS,WinNT://KCUS/USNOAP190_C,E18722,Owner,Mailbox: _Apps_PPPOC  Quarantine Search,
USNOAP190_C,KCUS,WinNT://KCUS/USNOAP190_C,W62002,Delegate,Mailbox: _Apps_PPPOC  Quarantine Search,
USNOAP189_C,KCUS,WinNT://KCUS/USNOAP189_C,E18722,Owner,Mailbox: _Apps_PPPOC  Quarantine,
USNOAP189_C,KCUS,WinNT://KCUS/USNOAP189_C,W62002,Delegate,Mailbox: _Apps_PPPOC  Quarantine,
USNOAP188_C,KCUS,WinNT://KCUS/USNOAP188_C,E18722,Owner,Mailbox: _Apps_POC  ProofPoint,
USNOAP188_C,KCUS,WinNT://KCUS/USNOAP188_C,W62002,Delegate,Mailbox: _Apps_POC  ProofPoint,
INFRPT_DEV_DataWriter,KCUS,WinNT://KCUS/INFRPT_DEV_DataWriter,B46959,Authorizer,Provides access to the IRP0NEDB database,
INFRPT_DEV_DataWriter,KCUS,WinNT://KCUS/INFRPT_DEV_DataWriter,E18722,Owner,Provides access to the IRP0NEDB database,
INFRPT_DEV_DataWriter,KCUS,WinNT://KCUS/INFRPT_DEV_DataWriter,W62002,Delegate,Provides access to the IRP0NEDB database,
INFRPT_PROD_DataReader,KCUS,WinNT://KCUS/INFRPT_PROD_DataReader,B46959,Authorizer,Provides access to the IRP0NEDB database,
INFRPT_PROD_DataReader,KCUS,WinNT://KCUS/INFRPT_PROD_DataReader,E18722,Owner,Provides access to the IRP0NEDB database,
INFRPT_PROD_DataReader,KCUS,WinNT://KCUS/INFRPT_PROD_DataReader,W62002,Delegate,Provides access to the IRP0NEDB database,
INFRPT_PROD_DataWriter,KCUS,WinNT://KCUS/INFRPT_PROD_DataWriter,B46959,Authorizer,Provides access to the IRP0NEDB database,
INFRPT_PROD_DataWriter,KCUS,WinNT://KCUS/INFRPT_PROD_DataWriter,E18722,Owner,Provides access to the IRP0NEDB database,
INFRPT_PROD_DataWriter,KCUS,WinNT://KCUS/INFRPT_PROD_DataWriter,W62002,Delegate,Provides access to the IRP0NEDB database,
DL013963,KCUS,WinNT://KCUS/DL013963,E18722,Owner,+TCS  Desktop,
DL013963,KCUS,WinNT://KCUS/DL013963,Q06085,Authorizer,+TCS  Desktop,
DL013963,KCUS,WinNT://KCUS/DL013963,W48997,Delegate,+TCS  Desktop,
RDrive Exception Users,KCUS,WinNT://KCUS/RDrive Exception Users,Q14718,Authorizer,Users who must have R drive Exception,
RDrive Exception Users,KCUS,WinNT://KCUS/RDrive Exception Users,Q15926,Authorizer,Users who must have R drive Exception,
RDrive Exception Users,KCUS,WinNT://KCUS/RDrive Exception Users,W48997,Delegate,Users who must have R drive Exception,
RDrive Exception Users,KCUS,WinNT://KCUS/RDrive Exception Users,W49040,Authorizer,Users who must have R drive Exception,
RDrive Exception Users,KCUS,WinNT://KCUS/RDrive Exception Users,E18722,Owner,Users who must have R drive Exception,
RDrive Exception Users,KCUS,WinNT://KCUS/RDrive Exception Users,Q13357,Authorizer,Users who must have R drive Exception,
Win10_v20H2_Exclusion,KCUS,WinNT://KCUS/Win10_v20H2_Exclusion,E18722,Owner,Short term exception group to manage Windows 10 version 20H2 upgrade,
Win10_v20H2_Exclusion,KCUS,WinNT://KCUS/Win10_v20H2_Exclusion,Q13243,Authorizer,Short term exception group to manage Windows 10 version 20H2 upgrade,
Win10_v20H2_Exclusion,KCUS,WinNT://KCUS/Win10_v20H2_Exclusion,W48997,Delegate,Short term exception group to manage Windows 10 version 20H2 upgrade,
Citrix Desk Admins,KCUS,WinNT://KCUS/Citrix Desk Admins,E18722,Owner,Manage access to the Citrix Director for Desk team,
Citrix Desk Admins,KCUS,WinNT://KCUS/Citrix Desk Admins,Q13357,Authorizer,Manage access to the Citrix Director for Desk team,
Citrix Desk Admins,KCUS,WinNT://KCUS/Citrix Desk Admins,Q14718,Authorizer,Manage access to the Citrix Director for Desk team,
Citrix Desk Admins,KCUS,WinNT://KCUS/Citrix Desk Admins,Q15926,Authorizer,Manage access to the Citrix Director for Desk team,
Citrix Desk Admins,KCUS,WinNT://KCUS/Citrix Desk Admins,W49040,Delegate,Manage access to the Citrix Director for Desk team,
Application Migration,KCUS,WinNT://KCUS/Application Migration,E18722,Owner,This is for Application migration assessment,
Application Migration,KCUS,WinNT://KCUS/Application Migration,W48997,Delegate,This is for Application migration assessment,
O365-WinAutoP-Device,KCUS,WinNT://KCUS/O365-WinAutoP-Device,E18722,Owner,The security group will contain devices that are requested in Mobility portal for AutoPilot access,
O365-WinAutoP-Device,KCUS,WinNT://KCUS/O365-WinAutoP-Device,E32872,Authorizer,The security group will contain devices that are requested in Mobility portal for AutoPilot access,
O365-WinAutoP-Device,KCUS,WinNT://KCUS/O365-WinAutoP-Device,Q04499,Authorizer,The security group will contain devices that are requested in Mobility portal for AutoPilot access,
O365-WinAutoP-Device,KCUS,WinNT://KCUS/O365-WinAutoP-Device,W48997,Authorizer,The security group will contain devices that are requested in Mobility portal for AutoPilot access,
O365-WinAutoP-Device,KCUS,WinNT://KCUS/O365-WinAutoP-Device,W66442,Delegate,The security group will contain devices that are requested in Mobility portal for AutoPilot access,
DevOpsAgent_ClientPlatforms,KCUS,WinNT://KCUS/DevOpsAgent_ClientPlatforms,E18722,Owner,Group used to share folders for use by Azure DevOps Agent for Client Services,
DevOpsAgent_ClientPlatforms,KCUS,WinNT://KCUS/DevOpsAgent_ClientPlatforms,W62002,Delegate,Group used to share folders for use by Azure DevOps Agent for Client Services,
O365 Search Editors,KCUS,WinNT://KCUS/O365 Search Editors,E18722,Owner,Grants members a Microsoft Search Editor role,
O365 Search Editors,KCUS,WinNT://KCUS/O365 Search Editors,W49040,Delegate,Grants members a Microsoft Search Editor role,
O365 Search Editors,KCUS,WinNT://KCUS/O365 Search Editors,W62002,Authorizer,Grants members a Microsoft Search Editor role,
O365 Search Administrators,KCUS,WinNT://KCUS/O365 Search Administrators,E18722,Owner,Grants members full access to manage Microsoft Search,
O365 Search Administrators,KCUS,WinNT://KCUS/O365 Search Administrators,Q08911,Authorizer,Grants members full access to manage Microsoft Search,
O365 Search Administrators,KCUS,WinNT://KCUS/O365 Search Administrators,W49040,Delegate,Grants members full access to manage Microsoft Search,
O365 Search Administrators,KCUS,WinNT://KCUS/O365 Search Administrators,W62002,Authorizer,Grants members full access to manage Microsoft Search,
O365-Common-Area-Phone,KCUS,WinNT://KCUS/O365-Common-Area-Phone,W62002,Delegate,MS Teams common area phones licensed users,
O365-Common-Area-Phone,KCUS,WinNT://KCUS/O365-Common-Area-Phone,B80845,Authorizer,MS Teams common area phones licensed users,
O365-Common-Area-Phone,KCUS,WinNT://KCUS/O365-Common-Area-Phone,B91148,Authorizer,MS Teams common area phones licensed users,
O365-Common-Area-Phone,KCUS,WinNT://KCUS/O365-Common-Area-Phone,e18722,Owner,MS Teams common area phones licensed users,
O365-Meeting-Room,KCUS,WinNT://KCUS/O365-Meeting-Room,B80845,Authorizer,MS Teams Meeting Room licensed accounts,
O365-Meeting-Room,KCUS,WinNT://KCUS/O365-Meeting-Room,B91148,Authorizer,MS Teams Meeting Room licensed accounts,
O365-Meeting-Room,KCUS,WinNT://KCUS/O365-Meeting-Room,E18722,Owner,MS Teams Meeting Room licensed accounts,
O365-Meeting-Room,KCUS,WinNT://KCUS/O365-Meeting-Room,W62002,Delegate,MS Teams Meeting Room licensed accounts,
O365-Phone-System,KCUS,WinNT://KCUS/O365-Phone-System,W62002,Delegate,MS Teams Phone System users,
O365-Phone-System,KCUS,WinNT://KCUS/O365-Phone-System,B80845,Authorizer,MS Teams Phone System users,
O365-Phone-System,KCUS,WinNT://KCUS/O365-Phone-System,B91148,Authorizer,MS Teams Phone System users,
O365-Phone-System,KCUS,WinNT://KCUS/O365-Phone-System,e18722,Owner,MS Teams Phone System users,
office365_azure_reports,KCUS,WinNT://KCUS/office365_azure_reports,E18722,Owner,This will fetch the data from Office 365 and Azure AD,
office365_azure_reports,KCUS,WinNT://KCUS/office365_azure_reports,W62002,Delegate,This will fetch the data from Office 365 and Azure AD,
HostAppmenu-OneNote M365,KCUS,WinNT://KCUS/HostAppmenu-OneNote M365,E18722,Owner,Citrix User Security group for MS OneNote M365,
HostAppmenu-OneNote M365,KCUS,WinNT://KCUS/HostAppmenu-OneNote M365,W49040,Delegate,Citrix User Security group for MS OneNote M365,
HostServerAdmin-Plant,KCUS,WinNT://KCUS/HostServerAdmin-Plant,E18722,Owner,Admin Access group for Plant Citrix servers,
HostServerAdmin-Plant,KCUS,WinNT://KCUS/HostServerAdmin-Plant,Q07708,Authorizer,Admin Access group for Plant Citrix servers,
HostServerAdmin-Plant,KCUS,WinNT://KCUS/HostServerAdmin-Plant,Q13357,Authorizer,Admin Access group for Plant Citrix servers,
HostServerAdmin-Plant,KCUS,WinNT://KCUS/HostServerAdmin-Plant,Q14616,Authorizer,Admin Access group for Plant Citrix servers,
HostServerAdmin-Plant,KCUS,WinNT://KCUS/HostServerAdmin-Plant,Q14718,Authorizer,Admin Access group for Plant Citrix servers,
HostServerAdmin-Plant,KCUS,WinNT://KCUS/HostServerAdmin-Plant,Q15926,Authorizer,Admin Access group for Plant Citrix servers,
HostServerAdmin-Plant,KCUS,WinNT://KCUS/HostServerAdmin-Plant,W49040,Delegate,Admin Access group for Plant Citrix servers,
PBI_WS_GL_Desktop_Services_Adhoc_Contrib_P,KCUS,WinNT://KCUS/PBI_WS_GL_Desktop_Services_Adhoc_Contrib_P,E18722,Owner,Power BI CONTRIBUTOR Role for GL Desktop Services  ADHOC Workspace,
PBI_WS_GL_Desktop_Services_Adhoc_Contrib_P,KCUS,WinNT://KCUS/PBI_WS_GL_Desktop_Services_Adhoc_Contrib_P,Q73491,Delegate,Power BI CONTRIBUTOR Role for GL Desktop Services  ADHOC Workspace,
PBI_WS_GL_Desktop_Services_Adhoc_Viewer_P,KCUS,WinNT://KCUS/PBI_WS_GL_Desktop_Services_Adhoc_Viewer_P,E18722,Owner,Power BI VIEWER Role for GL Desktop Services  ADHOC Workspace,
PBI_WS_GL_Desktop_Services_Adhoc_Viewer_P,KCUS,WinNT://KCUS/PBI_WS_GL_Desktop_Services_Adhoc_Viewer_P,Q73491,Delegate,Power BI VIEWER Role for GL Desktop Services  ADHOC Workspace,
PBI_WS_GL_Desktop_Services_Viewer_P,KCUS,WinNT://KCUS/PBI_WS_GL_Desktop_Services_Viewer_P,E18722,Owner,Power BI VIEWER Role for GL Desktop Services  PROD Workspace,
PBI_WS_GL_Desktop_Services_Viewer_P,KCUS,WinNT://KCUS/PBI_WS_GL_Desktop_Services_Viewer_P,Q73491,Delegate,Power BI VIEWER Role for GL Desktop Services  PROD Workspace,
PBI_WS_GL_Desktop_Services_Contrib_N,KCUS,WinNT://KCUS/PBI_WS_GL_Desktop_Services_Contrib_N,E18722,Owner,Power BI CONTRIBUTOR Role for GL Desktop Services D Workspace and VIEWER Role for GL Desktop Services  Q Workspace,
PBI_WS_GL_Desktop_Services_Contrib_N,KCUS,WinNT://KCUS/PBI_WS_GL_Desktop_Services_Contrib_N,Q73491,Delegate,Power BI CONTRIBUTOR Role for GL Desktop Services D Workspace and VIEWER Role for GL Desktop Services  Q Workspace,
PBI_WS_GL_Desktop_Services_Viewer_N,KCUS,WinNT://KCUS/PBI_WS_GL_Desktop_Services_Viewer_N,E18722,Owner,Power BI VIEWER Role for GL Desktop Services  D Workspace and VIEWER Role for GL Desktop Services  Q Workspace,
PBI_WS_GL_Desktop_Services_Viewer_N,KCUS,WinNT://KCUS/PBI_WS_GL_Desktop_Services_Viewer_N,Q73491,Delegate,Power BI VIEWER Role for GL Desktop Services  D Workspace and VIEWER Role for GL Desktop Services  Q Workspace,
HostServerAdmin-PowerBI,KCUS,WinNT://KCUS/HostServerAdmin-PowerBI,E18722,Owner,Admin Access group for Power BICitrix servers,
HostServerAdmin-PowerBI,KCUS,WinNT://KCUS/HostServerAdmin-PowerBI,q07708,Authorizer,Admin Access group for Power BICitrix servers,
HostServerAdmin-PowerBI,KCUS,WinNT://KCUS/HostServerAdmin-PowerBI,Q13357,Authorizer,Admin Access group for Power BICitrix servers,
HostServerAdmin-PowerBI,KCUS,WinNT://KCUS/HostServerAdmin-PowerBI,Q14616,Authorizer,Admin Access group for Power BICitrix servers,
HostServerAdmin-PowerBI,KCUS,WinNT://KCUS/HostServerAdmin-PowerBI,Q14718,Authorizer,Admin Access group for Power BICitrix servers,
HostServerAdmin-PowerBI,KCUS,WinNT://KCUS/HostServerAdmin-PowerBI,Q15926,Authorizer,Admin Access group for Power BICitrix servers,
HostServerAdmin-PowerBI,KCUS,WinNT://KCUS/HostServerAdmin-PowerBI,W49040,Delegate,Admin Access group for Power BICitrix servers,
HostApp-PowerBI,KCUS,WinNT://KCUS/HostApp-PowerBI,E18722,Owner,Citrix Application Security group for PowerBI,
HostApp-PowerBI,KCUS,WinNT://KCUS/HostApp-PowerBI,Q07708,Authorizer,Citrix Application Security group for PowerBI,
HostApp-PowerBI,KCUS,WinNT://KCUS/HostApp-PowerBI,Q13357,Authorizer,Citrix Application Security group for PowerBI,
HostApp-PowerBI,KCUS,WinNT://KCUS/HostApp-PowerBI,Q14616,Authorizer,Citrix Application Security group for PowerBI,
HostApp-PowerBI,KCUS,WinNT://KCUS/HostApp-PowerBI,Q14718,Authorizer,Citrix Application Security group for PowerBI,
HostApp-PowerBI,KCUS,WinNT://KCUS/HostApp-PowerBI,Q15926,Authorizer,Citrix Application Security group for PowerBI,
HostApp-PowerBI,KCUS,WinNT://KCUS/HostApp-PowerBI,W49040,Delegate,Citrix Application Security group for PowerBI,
Splunk Access for Desktop Engineering,KCUS,WinNT://KCUS/Splunk Access for Desktop Engineering,E18722,Owner,To give an access to Splunk for Desktop Engineering Team,
Splunk Access for Desktop Engineering,KCUS,WinNT://KCUS/Splunk Access for Desktop Engineering,Q06085,Authorizer,To give an access to Splunk for Desktop Engineering Team,
Splunk Access for Desktop Engineering,KCUS,WinNT://KCUS/Splunk Access for Desktop Engineering,W48997,Delegate,To give an access to Splunk for Desktop Engineering Team,
O365 AddIn_PP Phishing Test,KCUS,WinNT://KCUS/O365 AddIn_PP Phishing Test,E18722,Owner,This group will be used for testing ProofPoint phishing add-on,
O365 AddIn_PP Phishing Test,KCUS,WinNT://KCUS/O365 AddIn_PP Phishing Test,W62002,Delegate,This group will be used for testing ProofPoint phishing add-on,
Win10_v1903_Exclusion,KCUS,WinNT://KCUS/Win10_v1903_Exclusion,E18722,Owner,Short term exception group to manage Windows 10 version 1903 upgrade,
Win10_v1903_Exclusion,KCUS,WinNT://KCUS/Win10_v1903_Exclusion,Q06085,Authorizer,Short term exception group to manage Windows 10 version 1903 upgrade,
Win10_v1903_Exclusion,KCUS,WinNT://KCUS/Win10_v1903_Exclusion,Q13243,Authorizer,Short term exception group to manage Windows 10 version 1903 upgrade,
Win10_v1903_Exclusion,KCUS,WinNT://KCUS/Win10_v1903_Exclusion,W48997,Delegate,Short term exception group to manage Windows 10 version 1903 upgrade,
DL014145,KCUS,WinNT://KCUS/DL014145,E18722,Owner,+GLOBAL_O365  Communication Alerts,
DL014145,KCUS,WinNT://KCUS/DL014145,q03582,Authorizer,+GLOBAL_O365  Communication Alerts,
DL014145,KCUS,WinNT://KCUS/DL014145,W62002,Delegate,+GLOBAL_O365  Communication Alerts,
HostApp-WorkDay,KCUS,WinNT://KCUS/HostApp-WorkDay,E18722,Owner,HostApp-WorkDay,
HostApp-WorkDay,KCUS,WinNT://KCUS/HostApp-WorkDay,Q07708,Authorizer,HostApp-WorkDay,
HostApp-WorkDay,KCUS,WinNT://KCUS/HostApp-WorkDay,Q13357,Authorizer,HostApp-WorkDay,
HostApp-WorkDay,KCUS,WinNT://KCUS/HostApp-WorkDay,Q14616,Authorizer,HostApp-WorkDay,
HostApp-WorkDay,KCUS,WinNT://KCUS/HostApp-WorkDay,Q14718,Authorizer,HostApp-WorkDay,
HostApp-WorkDay,KCUS,WinNT://KCUS/HostApp-WorkDay,Q15926,Authorizer,HostApp-WorkDay,
HostApp-WorkDay,KCUS,WinNT://KCUS/HostApp-WorkDay,W49040,Delegate,HostApp-WorkDay,
HostApp-WMS-WFM-NEDC,KCUS,WinNT://KCUS/HostApp-WMS-WFM-NEDC,E18722,Owner,Citrix Application Security group for hosting from North West Distrbution Center,
HostApp-WMS-WFM-NEDC,KCUS,WinNT://KCUS/HostApp-WMS-WFM-NEDC,Q07708,Authorizer,Citrix Application Security group for hosting from North West Distrbution Center,
HostApp-WMS-WFM-NEDC,KCUS,WinNT://KCUS/HostApp-WMS-WFM-NEDC,Q14616,Authorizer,Citrix Application Security group for hosting from North West Distrbution Center,
HostApp-WMS-WFM-NEDC,KCUS,WinNT://KCUS/HostApp-WMS-WFM-NEDC,W49040,Delegate,Citrix Application Security group for hosting from North West Distrbution Center,
HostApp-WMS-RBW,KCUS,WinNT://KCUS/HostApp-WMS-RBW,W49040,Delegate,Citrix access group for WMS Applications,
HostApp-WMS-RBW,KCUS,WinNT://KCUS/HostApp-WMS-RBW,E18722,Owner,Citrix access group for WMS Applications,
HostApp-WMS-RBW,KCUS,WinNT://KCUS/HostApp-WMS-RBW,q07708,Authorizer,Citrix access group for WMS Applications,
HostApp-WMS-RBW,KCUS,WinNT://KCUS/HostApp-WMS-RBW,Q13357,Authorizer,Citrix access group for WMS Applications,
HostApp-WMS-RBW,KCUS,WinNT://KCUS/HostApp-WMS-RBW,Q14616,Authorizer,Citrix access group for WMS Applications,
HostApp-WMS-RBW,KCUS,WinNT://KCUS/HostApp-WMS-RBW,Q14718,Authorizer,Citrix access group for WMS Applications,
HostApp-WMS-RBW,KCUS,WinNT://KCUS/HostApp-WMS-RBW,Q15926,Authorizer,Citrix access group for WMS Applications,
HostAppMenu-ITS-Apps,KCUS,WinNT://KCUS/HostAppMenu-ITS-Apps,Q15926,Authorizer,Citrix access group for ITS-Apps,
HostAppMenu-ITS-Apps,KCUS,WinNT://KCUS/HostAppMenu-ITS-Apps,W49040,Delegate,Citrix access group for ITS-Apps,
HostAppMenu-ITS-Apps,KCUS,WinNT://KCUS/HostAppMenu-ITS-Apps,E18722,Owner,Citrix access group for ITS-Apps,
HostAppMenu-ITS-Apps,KCUS,WinNT://KCUS/HostAppMenu-ITS-Apps,Q07708,Authorizer,Citrix access group for ITS-Apps,
HostAppMenu-ITS-Apps,KCUS,WinNT://KCUS/HostAppMenu-ITS-Apps,Q13357,Authorizer,Citrix access group for ITS-Apps,
HostAppMenu-ITS-Apps,KCUS,WinNT://KCUS/HostAppMenu-ITS-Apps,Q14616,Authorizer,Citrix access group for ITS-Apps,
HostAppMenu-ITS-Apps,KCUS,WinNT://KCUS/HostAppMenu-ITS-Apps,Q14718,Authorizer,Citrix access group for ITS-Apps,
Removal Of Salesforce,KCUS,WinNT://KCUS/Removal Of Salesforce,E14682,Delegate,This group is used for removing the Salesforce Outlook Add-in,
Removal Of Salesforce,KCUS,WinNT://KCUS/Removal Of Salesforce,E18722,Owner,This group is used for removing the Salesforce Outlook Add-in,
Windows 10 Upgrade Remediation,KCUS,WinNT://KCUS/Windows 10 Upgrade Remediation,E18722,Owner,This is a temporary group to enable fixes to achieve compliance,
Windows 10 Upgrade Remediation,KCUS,WinNT://KCUS/Windows 10 Upgrade Remediation,W48997,Delegate,This is a temporary group to enable fixes to achieve compliance,
Desktop Services Azure Read-Only,KCUS,WinNT://KCUS/Desktop Services Azure Read-Only,E18722,Owner,Read-only access to Desktop Services Azure objects.,
Desktop Services Azure Read-Only,KCUS,WinNT://KCUS/Desktop Services Azure Read-Only,Q73491,Delegate,Read-only access to Desktop Services Azure objects.,
Hostapp-KCMarketPlace,KCUS,WinNT://KCUS/Hostapp-KCMarketPlace,E18722,Owner,Citrix IE Application Hosted for KC Market Place,
Hostapp-KCMarketPlace,KCUS,WinNT://KCUS/Hostapp-KCMarketPlace,W49040,Delegate,Citrix IE Application Hosted for KC Market Place,
Desktop PowerAppFlow Owners,KCUS,WinNT://KCUS/Desktop PowerAppFlow Owners,E18722,Owner,Members of this group have ownership of all power apps and flows created for Desktop Services.,
Desktop PowerAppFlow Owners,KCUS,WinNT://KCUS/Desktop PowerAppFlow Owners,Q73491,Delegate,Members of this group have ownership of all power apps and flows created for Desktop Services.,
PBI_WS_AP_DigitalWorkPlace_ADHOC_ADMIN_P,KCUS,WinNT://KCUS/PBI_WS_AP_DigitalWorkPlace_ADHOC_ADMIN_P,E18722,Delegate,Power BI ADMIN Role for AP DigitalWorkPlace  ADHOC Workspace,
PBI_WS_AP_DigitalWorkPlace_ADHOC_ADMIN_P,KCUS,WinNT://KCUS/PBI_WS_AP_DigitalWorkPlace_ADHOC_ADMIN_P,W62642,Owner,Power BI ADMIN Role for AP DigitalWorkPlace  ADHOC Workspace,
IABOWKSADMIN,KCUS,WinNT://KCUS/IABOWKSADMIN,E18722,Owner,IT Centre Bangalore GDTC-Site Computer Account Administrator,
IABOWKSADMIN,KCUS,WinNT://KCUS/IABOWKSADMIN,Q06085,Authorizer,IT Centre Bangalore GDTC-Site Computer Account Administrator,
IABOWKSADMIN,KCUS,WinNT://KCUS/IABOWKSADMIN,Q08911,Authorizer,IT Centre Bangalore GDTC-Site Computer Account Administrator,
IABOWKSADMIN,KCUS,WinNT://KCUS/IABOWKSADMIN,W48997,Delegate,IT Centre Bangalore GDTC-Site Computer Account Administrator,
O365-Intune-Device,KCUS,WinNT://KCUS/O365-Intune-Device,E18722,Owner,Member of this groups are Device IDs that will be assigned Intune Device Licenses,
O365-Intune-Device,KCUS,WinNT://KCUS/O365-Intune-Device,E32872,Authorizer,Member of this groups are Device IDs that will be assigned Intune Device Licenses,
O365-Intune-Device,KCUS,WinNT://KCUS/O365-Intune-Device,Q03582,Authorizer,Member of this groups are Device IDs that will be assigned Intune Device Licenses,
O365-Intune-Device,KCUS,WinNT://KCUS/O365-Intune-Device,W62002,Delegate,Member of this groups are Device IDs that will be assigned Intune Device Licenses,
PBI_WS_AP_DigitalWorkPlace_VIEWER_N,KCUS,WinNT://KCUS/PBI_WS_AP_DigitalWorkPlace_VIEWER_N,E18722,Delegate,Power BI VIEWER Role for AP DigitalWorkPlace  D Workspace and VIEWER Role for AP DigitalWorkPlace  Q Workspace,
PBI_WS_AP_DigitalWorkPlace_VIEWER_N,KCUS,WinNT://KCUS/PBI_WS_AP_DigitalWorkPlace_VIEWER_N,W62642,Owner,Power BI VIEWER Role for AP DigitalWorkPlace  D Workspace and VIEWER Role for AP DigitalWorkPlace  Q Workspace,
Power BI for Desktop,KCUS,WinNT://KCUS/Power BI for Desktop,E18722,Owner,Power BI Access for the Desktop team,
Power BI for Desktop,KCUS,WinNT://KCUS/Power BI for Desktop,W48997,Delegate,Power BI Access for the Desktop team,
PBI_WS_AP_DigitalWorkPlace_ADHOC_VIEWER_P,KCUS,WinNT://KCUS/PBI_WS_AP_DigitalWorkPlace_ADHOC_VIEWER_P,E18722,Delegate,Power BI VIEWER Role for AP DigitalWorkPlace  ADHOC Workspace,
PBI_WS_AP_DigitalWorkPlace_ADHOC_VIEWER_P,KCUS,WinNT://KCUS/PBI_WS_AP_DigitalWorkPlace_ADHOC_VIEWER_P,W62642,Owner,Power BI VIEWER Role for AP DigitalWorkPlace  ADHOC Workspace,
OneDrive exception user-Scanning to R drive,KCUS,WinNT://KCUS/OneDrive exception user-Scanning to R drive,B45906,Authorizer,to manage exception users for users who are scanning document and saving it to R drive.,
OneDrive exception user-Scanning to R drive,KCUS,WinNT://KCUS/OneDrive exception user-Scanning to R drive,E18722,Owner,to manage exception users for users who are scanning document and saving it to R drive.,
OneDrive exception user-Scanning to R drive,KCUS,WinNT://KCUS/OneDrive exception user-Scanning to R drive,Q06085,Authorizer,to manage exception users for users who are scanning document and saving it to R drive.,
OneDrive exception user-Scanning to R drive,KCUS,WinNT://KCUS/OneDrive exception user-Scanning to R drive,W45758,Authorizer,to manage exception users for users who are scanning document and saving it to R drive.,
OneDrive exception user-Scanning to R drive,KCUS,WinNT://KCUS/OneDrive exception user-Scanning to R drive,W48997,Delegate,to manage exception users for users who are scanning document and saving it to R drive.,
HostApp-FileZilla,KCUS,WinNT://KCUS/HostApp-FileZilla,E18722,Owner,Citrix User Security group for FileZilla,
HostApp-FileZilla,KCUS,WinNT://KCUS/HostApp-FileZilla,q07708,Authorizer,Citrix User Security group for FileZilla,
HostApp-FileZilla,KCUS,WinNT://KCUS/HostApp-FileZilla,Q13357,Authorizer,Citrix User Security group for FileZilla,
HostApp-FileZilla,KCUS,WinNT://KCUS/HostApp-FileZilla,Q14616,Authorizer,Citrix User Security group for FileZilla,
HostApp-FileZilla,KCUS,WinNT://KCUS/HostApp-FileZilla,Q14718,Authorizer,Citrix User Security group for FileZilla,
HostApp-FileZilla,KCUS,WinNT://KCUS/HostApp-FileZilla,Q15926,Authorizer,Citrix User Security group for FileZilla,
HostApp-FileZilla,KCUS,WinNT://KCUS/HostApp-FileZilla,W49040,Delegate,Citrix User Security group for FileZilla,
HostAppMenu-Stibo,KCUS,WinNT://KCUS/HostAppMenu-Stibo,E18722,Owner,Citrix User Security group for Stibo Launcher,
HostAppMenu-Stibo,KCUS,WinNT://KCUS/HostAppMenu-Stibo,q07708,Authorizer,Citrix User Security group for Stibo Launcher,
HostAppMenu-Stibo,KCUS,WinNT://KCUS/HostAppMenu-Stibo,Q13357,Authorizer,Citrix User Security group for Stibo Launcher,
HostAppMenu-Stibo,KCUS,WinNT://KCUS/HostAppMenu-Stibo,Q14616,Authorizer,Citrix User Security group for Stibo Launcher,
HostAppMenu-Stibo,KCUS,WinNT://KCUS/HostAppMenu-Stibo,Q14718,Authorizer,Citrix User Security group for Stibo Launcher,
HostAppMenu-Stibo,KCUS,WinNT://KCUS/HostAppMenu-Stibo,Q15926,Authorizer,Citrix User Security group for Stibo Launcher,
HostAppMenu-Stibo,KCUS,WinNT://KCUS/HostAppMenu-Stibo,W49040,Delegate,Citrix User Security group for Stibo Launcher,
Hostappmenu-FileZilla,KCUS,WinNT://KCUS/Hostappmenu-FileZilla,E18722,Owner,Citrix User Security group for FileZilla Application,
Hostappmenu-FileZilla,KCUS,WinNT://KCUS/Hostappmenu-FileZilla,q07708,Authorizer,Citrix User Security group for FileZilla Application,
Hostappmenu-FileZilla,KCUS,WinNT://KCUS/Hostappmenu-FileZilla,Q13357,Authorizer,Citrix User Security group for FileZilla Application,
Hostappmenu-FileZilla,KCUS,WinNT://KCUS/Hostappmenu-FileZilla,Q14616,Authorizer,Citrix User Security group for FileZilla Application,
Hostappmenu-FileZilla,KCUS,WinNT://KCUS/Hostappmenu-FileZilla,Q14718,Authorizer,Citrix User Security group for FileZilla Application,
Hostappmenu-FileZilla,KCUS,WinNT://KCUS/Hostappmenu-FileZilla,Q15926,Authorizer,Citrix User Security group for FileZilla Application,
Hostappmenu-FileZilla,KCUS,WinNT://KCUS/Hostappmenu-FileZilla,W49040,Delegate,Citrix User Security group for FileZilla Application,
HostApp-Stibo,KCUS,WinNT://KCUS/HostApp-Stibo,Q15926,Authorizer,Citrix User Security Group for Stibo Launcher,
HostApp-Stibo,KCUS,WinNT://KCUS/HostApp-Stibo,W49040,Delegate,Citrix User Security Group for Stibo Launcher,
HostApp-Stibo,KCUS,WinNT://KCUS/HostApp-Stibo,E18722,Owner,Citrix User Security Group for Stibo Launcher,
HostApp-Stibo,KCUS,WinNT://KCUS/HostApp-Stibo,q07708,Authorizer,Citrix User Security Group for Stibo Launcher,
HostApp-Stibo,KCUS,WinNT://KCUS/HostApp-Stibo,Q13357,Authorizer,Citrix User Security Group for Stibo Launcher,
HostApp-Stibo,KCUS,WinNT://KCUS/HostApp-Stibo,Q14616,Authorizer,Citrix User Security Group for Stibo Launcher,
HostApp-Stibo,KCUS,WinNT://KCUS/HostApp-Stibo,Q14718,Authorizer,Citrix User Security Group for Stibo Launcher,
O365 AddIn_MS Translator,KCUS,WinNT://KCUS/O365 AddIn_MS Translator,E18722,Owner,This group is to push MS Translator AddIn in outlook.,
O365 AddIn_MS Translator,KCUS,WinNT://KCUS/O365 AddIn_MS Translator,Q03582,Authorizer,This group is to push MS Translator AddIn in outlook.,
O365 AddIn_MS Translator,KCUS,WinNT://KCUS/O365 AddIn_MS Translator,W48997,Authorizer,This group is to push MS Translator AddIn in outlook.,
O365 AddIn_MS Translator,KCUS,WinNT://KCUS/O365 AddIn_MS Translator,W62002,Delegate,This group is to push MS Translator AddIn in outlook.,
GRP11899_C,KCUS,WinNT://KCUS/GRP11899_C,E18722,Owner,Mailbox: _Pst Import  Request,
GRP11899_C,KCUS,WinNT://KCUS/GRP11899_C,W62002,Delegate,Mailbox: _Pst Import  Request,
PBI_WS_AP_DigitalWorkPlace_VIEWER_P,KCUS,WinNT://KCUS/PBI_WS_AP_DigitalWorkPlace_VIEWER_P,E18722,Delegate,Power BI VIEWER Role for AP DigitalWorkPlace  PROD Workspace,
PBI_WS_AP_DigitalWorkPlace_VIEWER_P,KCUS,WinNT://KCUS/PBI_WS_AP_DigitalWorkPlace_VIEWER_P,W62642,Owner,Power BI VIEWER Role for AP DigitalWorkPlace  PROD Workspace,
PBI_WS_AP_DigitalWorkPlace_ADHOC_CONTRIB_P,KCUS,WinNT://KCUS/PBI_WS_AP_DigitalWorkPlace_ADHOC_CONTRIB_P,E18722,Delegate,Power BI CONTRIBUTOR Role for AP DigitalWorkPlace  ADHOC Workspace,
PBI_WS_AP_DigitalWorkPlace_ADHOC_CONTRIB_P,KCUS,WinNT://KCUS/PBI_WS_AP_DigitalWorkPlace_ADHOC_CONTRIB_P,W62642,Owner,Power BI CONTRIBUTOR Role for AP DigitalWorkPlace  ADHOC Workspace,
Enable IPV6,KCUS,WinNT://KCUS/Enable IPV6,E18722,Owner,This group is used to enable IPV6 in the PCs,
Enable IPV6,KCUS,WinNT://KCUS/Enable IPV6,q04499,Delegate,This group is used to enable IPV6 in the PCs,
Enable IPV6,KCUS,WinNT://KCUS/Enable IPV6,Q07749,Authorizer,This group is used to enable IPV6 in the PCs,
Enable IPV6,KCUS,WinNT://KCUS/Enable IPV6,Q12209,Authorizer,This group is used to enable IPV6 in the PCs,
PBI_WS_AP_Citrix_and_Horizon_VDI_VIEWER_P,KCUS,WinNT://KCUS/PBI_WS_AP_Citrix_and_Horizon_VDI_VIEWER_P,E18722,Delegate,Power BI VIEWER Role for AP Citrix and Horizon VDI  PROD Workspace,
PBI_WS_AP_Citrix_and_Horizon_VDI_VIEWER_P,KCUS,WinNT://KCUS/PBI_WS_AP_Citrix_and_Horizon_VDI_VIEWER_P,W49040,Owner,Power BI VIEWER Role for AP Citrix and Horizon VDI  PROD Workspace,
PSynch Personal Vault,KCUS,WinNT://KCUS/PSynch Personal Vault,E18722,Owner,Hitachi ID Password Manager Personal Vault Pilot Users Group,
PSynch Personal Vault,KCUS,WinNT://KCUS/PSynch Personal Vault,Q06371,Delegate,Hitachi ID Password Manager Personal Vault Pilot Users Group,
Hostappmenu-MicrosoftEdge,KCUS,WinNT://KCUS/Hostappmenu-MicrosoftEdge,E18722,Owner,Citrix User Security Group for Microsoft Edge for Business,
Hostappmenu-MicrosoftEdge,KCUS,WinNT://KCUS/Hostappmenu-MicrosoftEdge,Q07708,Authorizer,Citrix User Security Group for Microsoft Edge for Business,
Hostappmenu-MicrosoftEdge,KCUS,WinNT://KCUS/Hostappmenu-MicrosoftEdge,Q14616,Authorizer,Citrix User Security Group for Microsoft Edge for Business,
Hostappmenu-MicrosoftEdge,KCUS,WinNT://KCUS/Hostappmenu-MicrosoftEdge,w49040,Delegate,Citrix User Security Group for Microsoft Edge for Business,
PBI_WS_AP_Citrix_and_Horizon_VDI_CONTRIB_N,KCUS,WinNT://KCUS/PBI_WS_AP_Citrix_and_Horizon_VDI_CONTRIB_N,E18722,Delegate,Power BI CONTRIBUTOR Role for AP Citrix and Horizon VDI D Workspace and CONTRIBUTOR Role for AP Citrix and Horizon VDI  Q Workspace,
PBI_WS_AP_Citrix_and_Horizon_VDI_CONTRIB_N,KCUS,WinNT://KCUS/PBI_WS_AP_Citrix_and_Horizon_VDI_CONTRIB_N,W49040,Owner,Power BI CONTRIBUTOR Role for AP Citrix and Horizon VDI D Workspace and CONTRIBUTOR Role for AP Citrix and Horizon VDI  Q Workspace,
PBI_WS_AP_Citrix_and_Horizon_VDI_VIEWER_N,KCUS,WinNT://KCUS/PBI_WS_AP_Citrix_and_Horizon_VDI_VIEWER_N,E18722,Delegate,Power BI VIEWER Role for AP Citrix and Horizon VDI  D Workspace and VIEWER Role for AP Citrix and Horizon VDI  Q Workspace,
PBI_WS_AP_Citrix_and_Horizon_VDI_VIEWER_N,KCUS,WinNT://KCUS/PBI_WS_AP_Citrix_and_Horizon_VDI_VIEWER_N,W49040,Owner,Power BI VIEWER Role for AP Citrix and Horizon VDI  D Workspace and VIEWER Role for AP Citrix and Horizon VDI  Q Workspace,
PBI_WS_AP_DigitalWorkPlace_CONTRIB_N,KCUS,WinNT://KCUS/PBI_WS_AP_DigitalWorkPlace_CONTRIB_N,E18722,Delegate,Power BI CONTRIBUTOR Role for AP DigitalWorkPlace D Workspace and CONTRIBUTOR Role for AP DigitalWorkPlace  Q Workspace,
PBI_WS_AP_DigitalWorkPlace_CONTRIB_N,KCUS,WinNT://KCUS/PBI_WS_AP_DigitalWorkPlace_CONTRIB_N,W62642,Owner,Power BI CONTRIBUTOR Role for AP DigitalWorkPlace D Workspace and CONTRIBUTOR Role for AP DigitalWorkPlace  Q Workspace,
O365 Intune Helpdesk Operator,KCUS,WinNT://KCUS/O365 Intune Helpdesk Operator,W62002,Delegate,O365 Intune Helpdesk Operator,
O365 Intune Helpdesk Operator,KCUS,WinNT://KCUS/O365 Intune Helpdesk Operator,e18722,Owner,O365 Intune Helpdesk Operator,
IT_User_Information_QA_Admins,KCUS,WinNT://KCUS/IT_User_Information_QA_Admins,B00932,Delegate,Perform lookups of general user information plus additional administration of the application - qa,
IT_User_Information_QA_Admins,KCUS,WinNT://KCUS/IT_User_Information_QA_Admins,E18722,Owner,Perform lookups of general user information plus additional administration of the application - qa,
Hybrid AD join test,KCUS,WinNT://KCUS/Hybrid AD join test,E18722,Owner,This group will be used to join PCs to Hybrid domain,
Hybrid AD join test,KCUS,WinNT://KCUS/Hybrid AD join test,W48997,Delegate,This group will be used to join PCs to Hybrid domain,
FC-WinShutUsers,KCUS,WinNT://KCUS/FC-WinShutUsers,E18722,Owner,Winshuttle VDI Users,
FC-WinShutUsers,KCUS,WinNT://KCUS/FC-WinShutUsers,Q07708,Authorizer,Winshuttle VDI Users,
FC-WinShutUsers,KCUS,WinNT://KCUS/FC-WinShutUsers,Q13357,Authorizer,Winshuttle VDI Users,
FC-WinShutUsers,KCUS,WinNT://KCUS/FC-WinShutUsers,Q14616,Authorizer,Winshuttle VDI Users,
FC-WinShutUsers,KCUS,WinNT://KCUS/FC-WinShutUsers,Q14718,Authorizer,Winshuttle VDI Users,
FC-WinShutUsers,KCUS,WinNT://KCUS/FC-WinShutUsers,Q15926,Authorizer,Winshuttle VDI Users,
FC-WinShutUsers,KCUS,WinNT://KCUS/FC-WinShutUsers,W49040,Delegate,Winshuttle VDI Users,
O365-Mobility-E3M,KCUS,WinNT://KCUS/O365-Mobility-E3M,W62002,Delegate,This Group will provide licenses to mobility to users who do not have E3 licenses,
O365-Mobility-E3M,KCUS,WinNT://KCUS/O365-Mobility-E3M,E18722,Owner,This Group will provide licenses to mobility to users who do not have E3 licenses,
O365-Mobility-E3M,KCUS,WinNT://KCUS/O365-Mobility-E3M,E32872,Authorizer,This Group will provide licenses to mobility to users who do not have E3 licenses,
O365-Mobility-E3M,KCUS,WinNT://KCUS/O365-Mobility-E3M,Q03582,Authorizer,This Group will provide licenses to mobility to users who do not have E3 licenses,
FC-HrzFcUsers,KCUS,WinNT://KCUS/FC-HrzFcUsers,E18722,Owner,Development Horizon Full Clone VDI users,
FC-HrzFcUsers,KCUS,WinNT://KCUS/FC-HrzFcUsers,w49040,Delegate,Development Horizon Full Clone VDI users,
E3-Citrix-VDI-Office2016,KCUS,WinNT://KCUS/E3-Citrix-VDI-Office2016,E18722,Owner,OfficeProplus/M365 licensed users for Use on Citrix and Horizon View,
E3-Citrix-VDI-Office2016,KCUS,WinNT://KCUS/E3-Citrix-VDI-Office2016,w49040,Delegate,OfficeProplus/M365 licensed users for Use on Citrix and Horizon View,
O365-PowerAutomatePerUserRPA,KCUS,WinNT://KCUS/O365-PowerAutomatePerUserRPA,E18722,Owner,O365 PowerAutomate Per User With Attended RPA Access,
O365-PowerAutomatePerUserRPA,KCUS,WinNT://KCUS/O365-PowerAutomatePerUserRPA,Q03582,Authorizer,O365 PowerAutomate Per User With Attended RPA Access,
O365-PowerAutomatePerUserRPA,KCUS,WinNT://KCUS/O365-PowerAutomatePerUserRPA,W62002,Delegate,O365 PowerAutomate Per User With Attended RPA Access,
FC-DNAEngUsers,KCUS,WinNT://KCUS/FC-DNAEngUsers,E18722,Owner,Data & Analytics Engineering VDI Users,
FC-DNAEngUsers,KCUS,WinNT://KCUS/FC-DNAEngUsers,Q07708,Authorizer,Data & Analytics Engineering VDI Users,
FC-DNAEngUsers,KCUS,WinNT://KCUS/FC-DNAEngUsers,Q13357,Authorizer,Data & Analytics Engineering VDI Users,
FC-DNAEngUsers,KCUS,WinNT://KCUS/FC-DNAEngUsers,Q14616,Authorizer,Data & Analytics Engineering VDI Users,
FC-DNAEngUsers,KCUS,WinNT://KCUS/FC-DNAEngUsers,Q14718,Authorizer,Data & Analytics Engineering VDI Users,
FC-DNAEngUsers,KCUS,WinNT://KCUS/FC-DNAEngUsers,Q15926,Authorizer,Data & Analytics Engineering VDI Users,
FC-DNAEngUsers,KCUS,WinNT://KCUS/FC-DNAEngUsers,W49040,Delegate,Data & Analytics Engineering VDI Users,
O365-UserAccount-Administrator,KCUS,WinNT://KCUS/O365-UserAccount-Administrator,E18722,Owner,Members of this groups have rights to create/read / delete Azure User accounts,
O365-UserAccount-Administrator,KCUS,WinNT://KCUS/O365-UserAccount-Administrator,Q03582,Authorizer,Members of this groups have rights to create/read / delete Azure User accounts,
O365-UserAccount-Administrator,KCUS,WinNT://KCUS/O365-UserAccount-Administrator,W62002,Delegate,Members of this groups have rights to create/read / delete Azure User accounts,
O365-Exception-E3,KCUS,WinNT://KCUS/O365-Exception-E3,E18722,Owner,Users added to this group are the ones who requested M365 E3 license while segmentation believes that they are E3M,
O365-Exception-E3,KCUS,WinNT://KCUS/O365-Exception-E3,W62002,Delegate,Users added to this group are the ones who requested M365 E3 license while segmentation believes that they are E3M,
AllKcSoftexUsers,KCUS,WinNT://KCUS/AllKcSoftexUsers,E18722,Owner,This security Group holds all users that are KCSoftex Employees,
AllKcSoftexUsers,KCUS,WinNT://KCUS/AllKcSoftexUsers,W62002,Delegate,This security Group holds all users that are KCSoftex Employees,
Hostapp-SAP-Genpact,KCUS,WinNT://KCUS/Hostapp-SAP-Genpact,W49040,Delegate,Citrix access group for SAP Genpact,
Hostapp-SAP-Genpact,KCUS,WinNT://KCUS/Hostapp-SAP-Genpact,E18722,Owner,Citrix access group for SAP Genpact,
Hostapp-SAP-Genpact,KCUS,WinNT://KCUS/Hostapp-SAP-Genpact,Q07708,Authorizer,Citrix access group for SAP Genpact,
Hostapp-SAP-Genpact,KCUS,WinNT://KCUS/Hostapp-SAP-Genpact,Q13357,Authorizer,Citrix access group for SAP Genpact,
Hostapp-SAP-Genpact,KCUS,WinNT://KCUS/Hostapp-SAP-Genpact,Q14616,Authorizer,Citrix access group for SAP Genpact,
Hostapp-SAP-Genpact,KCUS,WinNT://KCUS/Hostapp-SAP-Genpact,Q14718,Authorizer,Citrix access group for SAP Genpact,
Hostapp-SAP-Genpact,KCUS,WinNT://KCUS/Hostapp-SAP-Genpact,Q15926,Authorizer,Citrix access group for SAP Genpact,
HostApp-Stars12-6,KCUS,WinNT://KCUS/HostApp-Stars12-6,Q07708,Authorizer,Citrix application access group for CRM 12.6,
HostApp-Stars12-6,KCUS,WinNT://KCUS/HostApp-Stars12-6,Q13357,Authorizer,Citrix application access group for CRM 12.6,
HostApp-Stars12-6,KCUS,WinNT://KCUS/HostApp-Stars12-6,Q14616,Authorizer,Citrix application access group for CRM 12.6,
HostApp-Stars12-6,KCUS,WinNT://KCUS/HostApp-Stars12-6,Q14718,Authorizer,Citrix application access group for CRM 12.6,
HostApp-Stars12-6,KCUS,WinNT://KCUS/HostApp-Stars12-6,Q15926,Authorizer,Citrix application access group for CRM 12.6,
HostApp-Stars12-6,KCUS,WinNT://KCUS/HostApp-Stars12-6,W49040,Delegate,Citrix application access group for CRM 12.6,
HostApp-Stars12-6,KCUS,WinNT://KCUS/HostApp-Stars12-6,E18722,Owner,Citrix application access group for CRM 12.6,
Allow Cortana Users,KCUS,WinNT://KCUS/Allow Cortana Users,E18722,Owner,This AD Gorup contains the list of users for whom Cortana is Enabled.,
Allow Cortana Users,KCUS,WinNT://KCUS/Allow Cortana Users,Q06085,Authorizer,This AD Gorup contains the list of users for whom Cortana is Enabled.,
Allow Cortana Users,KCUS,WinNT://KCUS/Allow Cortana Users,W48997,Authorizer,This AD Gorup contains the list of users for whom Cortana is Enabled.,
Allow Cortana Users,KCUS,WinNT://KCUS/Allow Cortana Users,W66442,Delegate,This AD Gorup contains the list of users for whom Cortana is Enabled.,
FC-TCSSMOMUsers,KCUS,WinNT://KCUS/FC-TCSSMOMUsers,E18722,Owner,TCS SMOM VDI Users,
FC-TCSSMOMUsers,KCUS,WinNT://KCUS/FC-TCSSMOMUsers,Q07708,Authorizer,TCS SMOM VDI Users,
FC-TCSSMOMUsers,KCUS,WinNT://KCUS/FC-TCSSMOMUsers,Q13357,Authorizer,TCS SMOM VDI Users,
FC-TCSSMOMUsers,KCUS,WinNT://KCUS/FC-TCSSMOMUsers,Q14616,Authorizer,TCS SMOM VDI Users,
FC-TCSSMOMUsers,KCUS,WinNT://KCUS/FC-TCSSMOMUsers,Q14718,Authorizer,TCS SMOM VDI Users,
FC-TCSSMOMUsers,KCUS,WinNT://KCUS/FC-TCSSMOMUsers,Q15926,Authorizer,TCS SMOM VDI Users,
FC-TCSSMOMUsers,KCUS,WinNT://KCUS/FC-TCSSMOMUsers,W49040,Delegate,TCS SMOM VDI Users,
FC-EDMProusers,KCUS,WinNT://KCUS/FC-EDMProusers,E18722,Owner,EDM Roll out Project VDI users,
FC-EDMProusers,KCUS,WinNT://KCUS/FC-EDMProusers,Q07708,Authorizer,EDM Roll out Project VDI users,
FC-EDMProusers,KCUS,WinNT://KCUS/FC-EDMProusers,Q13357,Authorizer,EDM Roll out Project VDI users,
FC-EDMProusers,KCUS,WinNT://KCUS/FC-EDMProusers,Q14616,Authorizer,EDM Roll out Project VDI users,
FC-EDMProusers,KCUS,WinNT://KCUS/FC-EDMProusers,Q14718,Authorizer,EDM Roll out Project VDI users,
FC-EDMProusers,KCUS,WinNT://KCUS/FC-EDMProusers,Q15926,Authorizer,EDM Roll out Project VDI users,
FC-EDMProusers,KCUS,WinNT://KCUS/FC-EDMProusers,W49040,Delegate,EDM Roll out Project VDI users,
Enable MDATP,KCUS,WinNT://KCUS/Enable MDATP,E18722,Owner,Microsoft Defender ATP Enablement,
Enable MDATP,KCUS,WinNT://KCUS/Enable MDATP,Q04499,Authorizer,Microsoft Defender ATP Enablement,
Enable MDATP,KCUS,WinNT://KCUS/Enable MDATP,Q06203,Authorizer,Microsoft Defender ATP Enablement,
Enable MDATP,KCUS,WinNT://KCUS/Enable MDATP,Q06371,Delegate,Microsoft Defender ATP Enablement,
Enable MDATP,KCUS,WinNT://KCUS/Enable MDATP,Q07983,Authorizer,Microsoft Defender ATP Enablement,
Enable MDATP,KCUS,WinNT://KCUS/Enable MDATP,W48997,Authorizer,Microsoft Defender ATP Enablement,
FC-DigTrnUsers,KCUS,WinNT://KCUS/FC-DigTrnUsers,E18722,Owner,Digital Trn VDI Users,
FC-DigTrnUsers,KCUS,WinNT://KCUS/FC-DigTrnUsers,Q07708,Authorizer,Digital Trn VDI Users,
FC-DigTrnUsers,KCUS,WinNT://KCUS/FC-DigTrnUsers,Q13357,Authorizer,Digital Trn VDI Users,
FC-DigTrnUsers,KCUS,WinNT://KCUS/FC-DigTrnUsers,Q14616,Authorizer,Digital Trn VDI Users,
FC-DigTrnUsers,KCUS,WinNT://KCUS/FC-DigTrnUsers,Q14718,Authorizer,Digital Trn VDI Users,
FC-DigTrnUsers,KCUS,WinNT://KCUS/FC-DigTrnUsers,Q15926,Authorizer,Digital Trn VDI Users,
FC-DigTrnUsers,KCUS,WinNT://KCUS/FC-DigTrnUsers,W49040,Delegate,Digital Trn VDI Users,
GRP14197_C,KCUS,WinNT://KCUS/GRP14197_C,E18722,Owner,Mailbox: _Onedrive migration  Desktop,
GRP14197_C,KCUS,WinNT://KCUS/GRP14197_C,W48997,Delegate,Mailbox: _Onedrive migration  Desktop,
Showpad Master Group,KCUS,WinNT://KCUS/Showpad Master Group,E18722,Owner,Master group for all Showpad AD groups,
Showpad Master Group,KCUS,WinNT://KCUS/Showpad Master Group,W66442,Delegate,Master group for all Showpad AD groups,
STARS-SFA_AdminHostAppMenu-STARS-SG_R,KCUS,WinNT://KCUS/STARS-SFA_AdminHostAppMenu-STARS-SG_R,E18722,Owner,\\sgsgfn01\share\SG SIF PHOTOEXPORT,
STARS-SFA_AdminHostAppMenu-STARS-SG_R,KCUS,WinNT://KCUS/STARS-SFA_AdminHostAppMenu-STARS-SG_R,W49040,Delegate,\\sgsgfn01\share\SG SIF PHOTOEXPORT,
RSAT Users,KCUS,WinNT://KCUS/RSAT Users,E18722,Owner,Group used to publish RSAT tools in Software Center,
RSAT Users,KCUS,WinNT://KCUS/RSAT Users,Q06085,Authorizer,Group used to publish RSAT tools in Software Center,
RSAT Users,KCUS,WinNT://KCUS/RSAT Users,W48997,Delegate,Group used to publish RSAT tools in Software Center,
O365 Jump Server Access,KCUS,WinNT://KCUS/O365 Jump Server Access,B46959,Authorizer,Controls access to O365 Jump Server (USTWA421),
O365 Jump Server Access,KCUS,WinNT://KCUS/O365 Jump Server Access,E18722,Owner,Controls access to O365 Jump Server (USTWA421),
O365 Jump Server Access,KCUS,WinNT://KCUS/O365 Jump Server Access,W62002,Delegate,Controls access to O365 Jump Server (USTWA421),
pac.kcc.com_agencies,KCUS,WinNT://KCUS/pac.kcc.com_agencies,E18722,Owner,Group for agencies,
pac.kcc.com_agencies,KCUS,WinNT://KCUS/pac.kcc.com_agencies,W66442,Delegate,Group for agencies,
pac.kcc.com_authorizers,KCUS,WinNT://KCUS/pac.kcc.com_authorizers,E18722,Owner,Group for website Authorizers,
pac.kcc.com_authorizers,KCUS,WinNT://KCUS/pac.kcc.com_authorizers,W66442,Delegate,Group for website Authorizers,
pac.kcc.com_businesspartners,KCUS,WinNT://KCUS/pac.kcc.com_businesspartners,E18722,Owner,Group for website buisnesspartners,
pac.kcc.com_businesspartners,KCUS,WinNT://KCUS/pac.kcc.com_businesspartners,W66442,Delegate,Group for website buisnesspartners,
pac.kcc.com_developers,KCUS,WinNT://KCUS/pac.kcc.com_developers,E18722,Owner,Group for website developers,
pac.kcc.com_developers,KCUS,WinNT://KCUS/pac.kcc.com_developers,Q04499,Authorizer,Group for website developers,
pac.kcc.com_developers,KCUS,WinNT://KCUS/pac.kcc.com_developers,W66442,Delegate,Group for website developers,
pac.kcc.com_owners,KCUS,WinNT://KCUS/pac.kcc.com_owners,E18722,Owner,Group for website owners,
pac.kcc.com_owners,KCUS,WinNT://KCUS/pac.kcc.com_owners,W66442,Delegate,Group for website owners,
KCHC_Sales,KCUS,WinNT://KCUS/KCHC_Sales,E18722,Owner,This group will used to control access to app in Mobile Iron,
KCHC_Sales,KCUS,WinNT://KCUS/KCHC_Sales,W48997,Delegate,This group will used to control access to app in Mobile Iron,
KCHC_Sales_Test,KCUS,WinNT://KCUS/KCHC_Sales_Test,E18722,Owner,KCHC Sales - Mobile App Testing ,
KCHC_Sales_Test,KCUS,WinNT://KCUS/KCHC_Sales_Test,W48997,Delegate,KCHC Sales - Mobile App Testing ,
TMG Administrators,KCUS,WinNT://KCUS/TMG Administrators,E18722,Owner,TMG Administrators,
TMG Administrators,KCUS,WinNT://KCUS/TMG Administrators,W62002,Delegate,TMG Administrators,
Hostapp-FoxitPhantomPDF,KCUS,WinNT://KCUS/Hostapp-FoxitPhantomPDF,E18722,Owner,Citrix Application security group for Hostapp-FoxitPhantomPDF,
Hostapp-FoxitPhantomPDF,KCUS,WinNT://KCUS/Hostapp-FoxitPhantomPDF,Q07708,Authorizer,Citrix Application security group for Hostapp-FoxitPhantomPDF,
Hostapp-FoxitPhantomPDF,KCUS,WinNT://KCUS/Hostapp-FoxitPhantomPDF,Q13357,Authorizer,Citrix Application security group for Hostapp-FoxitPhantomPDF,
Hostapp-FoxitPhantomPDF,KCUS,WinNT://KCUS/Hostapp-FoxitPhantomPDF,Q14616,Authorizer,Citrix Application security group for Hostapp-FoxitPhantomPDF,
Hostapp-FoxitPhantomPDF,KCUS,WinNT://KCUS/Hostapp-FoxitPhantomPDF,Q14718,Authorizer,Citrix Application security group for Hostapp-FoxitPhantomPDF,
Hostapp-FoxitPhantomPDF,KCUS,WinNT://KCUS/Hostapp-FoxitPhantomPDF,Q15926,Authorizer,Citrix Application security group for Hostapp-FoxitPhantomPDF,
Hostapp-FoxitPhantomPDF,KCUS,WinNT://KCUS/Hostapp-FoxitPhantomPDF,W49040,Delegate,Citrix Application security group for Hostapp-FoxitPhantomPDF,
USTCFF141_PSUsers,KCUS,WinNT://KCUS/USTCFF141_PSUsers,E18722,Owner,US Neenah - North Office PowerShell Firefight ID,
USTCFF141_PSUsers,KCUS,WinNT://KCUS/USTCFF141_PSUsers,W62002,Delegate,US Neenah - North Office PowerShell Firefight ID,
USTCFF141_PSApprovers,KCUS,WinNT://KCUS/USTCFF141_PSApprovers,E18722,Owner,US Neenah - North Office PowerShell Firefight ID,
USTCFF141_PSApprovers,KCUS,WinNT://KCUS/USTCFF141_PSApprovers,W66442,Delegate,US Neenah - North Office PowerShell Firefight ID,
DBEDC-ReadWrite,KCUS,WinNT://KCUS/DBEDC-ReadWrite,E18722,Owner,Provides read/write access to the edc database.,
DBEDC-ReadWrite,KCUS,WinNT://KCUS/DBEDC-ReadWrite,Q73491,Delegate,Provides read/write access to the edc database.,
O365-Office App-Administrators,KCUS,WinNT://KCUS/O365-Office App-Administrators,E18722,Owner,Group to provide admin rights on Office Apps admin center to manage cloud policies for Office Apps  also known as M365 Apps,
O365-Office App-Administrators,KCUS,WinNT://KCUS/O365-Office App-Administrators,W48997,Authorizer,Group to provide admin rights on Office Apps admin center to manage cloud policies for Office Apps  also known as M365 Apps,
O365-Office App-Administrators,KCUS,WinNT://KCUS/O365-Office App-Administrators,W66442,Delegate,Group to provide admin rights on Office Apps admin center to manage cloud policies for Office Apps  also known as M365 Apps,
FC-TMSAPUsers,KCUS,WinNT://KCUS/FC-TMSAPUsers,E18722,Owner,TechM SAP VDI Users,
FC-TMSAPUsers,KCUS,WinNT://KCUS/FC-TMSAPUsers,Q07708,Authorizer,TechM SAP VDI Users,
FC-TMSAPUsers,KCUS,WinNT://KCUS/FC-TMSAPUsers,Q13357,Authorizer,TechM SAP VDI Users,
FC-TMSAPUsers,KCUS,WinNT://KCUS/FC-TMSAPUsers,Q15926,Authorizer,TechM SAP VDI Users,
FC-TMSAPUsers,KCUS,WinNT://KCUS/FC-TMSAPUsers,W49040,Delegate,TechM SAP VDI Users,
FC-TMDevUsers,KCUS,WinNT://KCUS/FC-TMDevUsers,E18722,Owner,TechM Dev VDI Users,
FC-TMDevUsers,KCUS,WinNT://KCUS/FC-TMDevUsers,Q07708,Authorizer,TechM Dev VDI Users,
FC-TMDevUsers,KCUS,WinNT://KCUS/FC-TMDevUsers,Q13357,Authorizer,TechM Dev VDI Users,
FC-TMDevUsers,KCUS,WinNT://KCUS/FC-TMDevUsers,Q15926,Authorizer,TechM Dev VDI Users,
FC-TMDevUsers,KCUS,WinNT://KCUS/FC-TMDevUsers,W49040,Delegate,TechM Dev VDI Users,
FC-TMMFGUsers,KCUS,WinNT://KCUS/FC-TMMFGUsers,E18722,Owner,TechM MFG VDI Users,
FC-TMMFGUsers,KCUS,WinNT://KCUS/FC-TMMFGUsers,Q07708,Authorizer,TechM MFG VDI Users,
FC-TMMFGUsers,KCUS,WinNT://KCUS/FC-TMMFGUsers,Q13357,Authorizer,TechM MFG VDI Users,
FC-TMMFGUsers,KCUS,WinNT://KCUS/FC-TMMFGUsers,Q15926,Authorizer,TechM MFG VDI Users,
FC-TMMFGUsers,KCUS,WinNT://KCUS/FC-TMMFGUsers,W49040,Delegate,TechM MFG VDI Users,
GRP10757_C,KCUS,WinNT://KCUS/GRP10757_C,E18722,Owner,Mailbox: _Access Management  INF Client Platforms,
GRP10757_C,KCUS,WinNT://KCUS/GRP10757_C,W66442,Delegate,Mailbox: _Access Management  INF Client Platforms,
FC-TMSupportUsers,KCUS,WinNT://KCUS/FC-TMSupportUsers,E18722,Owner,TechM Support VDI Users,
FC-TMSupportUsers,KCUS,WinNT://KCUS/FC-TMSupportUsers,Q07708,Authorizer,TechM Support VDI Users,
FC-TMSupportUsers,KCUS,WinNT://KCUS/FC-TMSupportUsers,Q13357,Authorizer,TechM Support VDI Users,
FC-TMSupportUsers,KCUS,WinNT://KCUS/FC-TMSupportUsers,Q14616,Authorizer,TechM Support VDI Users,
FC-TMSupportUsers,KCUS,WinNT://KCUS/FC-TMSupportUsers,Q15926,Authorizer,TechM Support VDI Users,
FC-TMSupportUsers,KCUS,WinNT://KCUS/FC-TMSupportUsers,W49040,Delegate,TechM Support VDI Users,
Foxit PDF Editor Pro,KCUS,WinNT://KCUS/Foxit PDF Editor Pro,E18722,Owner,Foxit PhantomPDF Business is a PDF solution. It is a replacement of Adobe Acrobat DC Pro,
Foxit PDF Editor Pro,KCUS,WinNT://KCUS/Foxit PDF Editor Pro,Q13243,Authorizer,Foxit PhantomPDF Business is a PDF solution. It is a replacement of Adobe Acrobat DC Pro,
Foxit PDF Editor Pro,KCUS,WinNT://KCUS/Foxit PDF Editor Pro,W48997,Delegate,Foxit PhantomPDF Business is a PDF solution. It is a replacement of Adobe Acrobat DC Pro,
CSResponseGroupAdministrator,KCUS,WinNT://KCUS/CSResponseGroupAdministrator,E18722,Owner,Members of this group can manage the configuration of the Response Group service in Lync Server 2010.,
CSResponseGroupAdministrator,KCUS,WinNT://KCUS/CSResponseGroupAdministrator,W62002,Delegate,Members of this group can manage the configuration of the Response Group service in Lync Server 2010.,
CSPersistentChatAdministrator,KCUS,WinNT://KCUS/CSPersistentChatAdministrator,E18722,Owner,Members of this group can run the persistent chat admin cmdlets for Categories/Rooms/Addins. ,
CSPersistentChatAdministrator,KCUS,WinNT://KCUS/CSPersistentChatAdministrator,W62002,Delegate,Members of this group can run the persistent chat admin cmdlets for Categories/Rooms/Addins. ,
AUYDGrpAdmin,KCUS,WinNT://KCUS/AUYDGrpAdmin,E18722,Owner,Yennora DC-Site Group Administrators,
AUYDGrpAdmin,KCUS,WinNT://KCUS/AUYDGrpAdmin,Q08911,Authorizer,Yennora DC-Site Group Administrators,
AUYDGrpAdmin,KCUS,WinNT://KCUS/AUYDGrpAdmin,W48997,Delegate,Yennora DC-Site Group Administrators,
K2 Database Read,KCUS,WinNT://KCUS/K2 Database Read,E18722,Owner,Read access to K2 databases,
K2 Database Read,KCUS,WinNT://KCUS/K2 Database Read,Q08911,Authorizer,Read access to K2 databases,
K2 Database Read,KCUS,WinNT://KCUS/K2 Database Read,W49040,Delegate,Read access to K2 databases,
HorizonVMWareDesktopsGoldenVMWksAdmin,KCUS,WinNT://KCUS/HorizonVMWareDesktopsGoldenVMWksAdmin,E18722,Owner,Horizon VMWare Desktops Computer Account Administrators,
HorizonVMWareDesktopsGoldenVMWksAdmin,KCUS,WinNT://KCUS/HorizonVMWareDesktopsGoldenVMWksAdmin,Q13357,Authorizer,Horizon VMWare Desktops Computer Account Administrators,
HorizonVMWareDesktopsGoldenVMWksAdmin,KCUS,WinNT://KCUS/HorizonVMWareDesktopsGoldenVMWksAdmin,Q14718,Authorizer,Horizon VMWare Desktops Computer Account Administrators,
HorizonVMWareDesktopsGoldenVMWksAdmin,KCUS,WinNT://KCUS/HorizonVMWareDesktopsGoldenVMWksAdmin,Q15926,Authorizer,Horizon VMWare Desktops Computer Account Administrators,
HorizonVMWareDesktopsGoldenVMWksAdmin,KCUS,WinNT://KCUS/HorizonVMWareDesktopsGoldenVMWksAdmin,W49040,Delegate,Horizon VMWare Desktops Computer Account Administrators,
HorizonVMWareDesktopsClonesWksAdmin,KCUS,WinNT://KCUS/HorizonVMWareDesktopsClonesWksAdmin,E18722,Owner,Horizon VMWare Desktops Computer Account Administrators,
HorizonVMWareDesktopsClonesWksAdmin,KCUS,WinNT://KCUS/HorizonVMWareDesktopsClonesWksAdmin,W49040,Delegate,Horizon VMWare Desktops Computer Account Administrators,
Azure-ConditionalAccessReportView,KCUS,WinNT://KCUS/Azure-ConditionalAccessReportView,E18722,Owner,This security group would be used for providing access to users for Viewing Azure Report Only logs,
Azure-ConditionalAccessReportView,KCUS,WinNT://KCUS/Azure-ConditionalAccessReportView,w62002,Delegate,This security group would be used for providing access to users for Viewing Azure Report Only logs,
