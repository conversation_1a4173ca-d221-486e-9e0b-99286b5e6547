Imports System.Data
Namespace AccountManager

    Public Class Print

        Dim strFileName, strFileLoc As String
        Dim strConn As String

        Sub New(ByVal Conn As String)
            Dim usrcur As New UserInfo

            strFileName = usrcur.GetUserID
            strFileLoc = [Global].GblServerPath & strFileName
            strConn = Conn

        End Sub

        Sub New()

            Dim usrcur As New UserInfo

            strFileName = usrcur.GetUserID
            strFileLoc = [Global].GblServerPath & strFileName
            strConn = [Global].GblUserConn

        End Sub

        Function IgnoreField(ByVal strValue As String) As String

            Select Case UCase(strValue)
                Case "ID", UCase("type"), UCase("emailflag")
                    Return ""
                Case UCase("JobRunTimeKEy"), UCase("UserLocation"), UCase("DataClassification")
                    Return ""

                Case UCase("AsMemberOf")
                    Return "Member Of"

                Case UCase("DisplayName")
                    Return "Service Name"

                Case UCase("AccountName"), UCase("AccountID")
                    Return "Account"
                Case UCase("ExpirationDAte")
                    Return "Password Past Due Date"
                Case UCase("Username")
                    Return "User ID"
                Case UCase("actDisabled")
                    Return "Disabled"
                Case UCase("UsernameDN")
                    Return "User Name"
                Case UCase("ActException")
                    Return "Exception Account"
                Case UCase("appname")
                    Return "Application Name"
                Case UCase("LastNotified")
                    Return "Last Email Notification"
                Case Else
                    Return strValue
            End Select

        End Function

        Function CreateCSV(ByVal SQL As String) As String

            Dim srData As SqlClient.SqlDataReader
            Dim dbData As New DataAccess
            Dim hlpClean As New Helper
            Dim intCount As Integer
            Dim strValue As String
            Dim strLine As String

            strFileLoc = strFileLoc & ".csv"
            Dim swWrite As New System.IO.StreamWriter(strFileLoc, False)

            srData = dbData.GetDataReaderByStringId(SQL, strConn)
            intCount = 0

            Do While intCount < srData.FieldCount

                strLine = IgnoreField(srData.GetName(intCount))
                If strLine <> "" Then
                    strLine = hlpClean.dbCleanUpString(strLine)
                    swWrite.Write(strLine & ",")
                End If
                
                intCount += 1
            Loop

            swWrite.WriteLine("")

            Do While srData.Read

                intCount = 0
                Do While intCount < srData.FieldCount

                    strValue = ""

                    If IgnoreField(srData.GetName(intCount)) <> "" Then

                        If Not srData.IsDBNull(intCount) Then
                            strLine = srData.Item(intCount)
                            strValue = hlpClean.dbCleanUpString(strLine)
                            strValue = Replace(strValue, ",", " ")
                        End If

                        swWrite.Write(strValue & ",")

                    End If
                    

                    intCount += 1
                Loop
                swWrite.WriteLine("")
            Loop
            swWrite.Close()
            swWrite = Nothing

            srData.Close()
            srData = Nothing
            dbData.CloseConnections()

            Return [Global].GblDownLoad & strFileName & ".csv"

        End Function

    End Class

End Namespace
