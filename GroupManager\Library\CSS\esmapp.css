BODY
{
    BACKGROUND-COLOR: white;
    FONT-FAMILY: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, 'MS Sans Serif';
    MARGIN: 0px 0px 50px
}
A
{
    COLOR: black;
    TEXT-DECORATION: none
}
A:hover
{
    COLOR: black;
    TEXT-DECORATION: underline
}
A:visited
{
    COLOR: black;
    TEXT-DECORATION: none
}
HR
{
    COLOR: #6699cc;
    HEIGHT: 1px
}
UL
{
    FONT-SIZE: 68.75%;
    LIST-STYLE: square;
    MARGIN-BOTTOM: 0px;
    MARGIN-LEFT: 16px;
    MARGIN-TOP: 0px
}
OL
{
    FONT-SIZE: 68.75%;
    MARGIN-BOTTOM: 0px;
    MARGIN-LEFT: 25px;
    MARGIN-TOP: 0px
}
UL OL
{
    FONT-SIZE: 100%
}
OL UL
{
    FONT-SIZE: 100%
}
LI
{
    MARGIN-BOTTOM: 0.2ex
}
.GroupInfoHeader
{
    FONT-SIZE: 68.75%;
    FONT-WEIGHT: bold
}
.GroupInfo
{
    FONT-SIZE: 68.75%
}
TABLE.BlueTitle
{
    BACKGROUND-COLOR: #6699cc;
    BORDER-BOTTOM: black 1px solid;
    FONT-SIZE: 62.5%;
    MARGIN-BOTTOM: 7px;
    WIDTH: 100%
}
.BlueTitle TD
{
    FONT-WEIGHT: bold
}
H4
{
    FONT-SIZE: 68.75%;
    FONT-WEIGHT: bold;
    MARGIN: 5px 0px 10px
}
P
{
    FONT-SIZE: 68.75%
}
.small
{
    FONT-SIZE: 68.75%
}
.FunctionStep
{
    FONT-SIZE: 68.75%;
    FONT-WEIGHT: bold
}
