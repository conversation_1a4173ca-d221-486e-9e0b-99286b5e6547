﻿Imports System.Data
Imports AccountManager.Global

Namespace AccountManager


    Partial Class PwdExemptProcess
        Inherits System.Web.UI.Page

#Region " Web Form Designer Generated Code "
        'This call is required by the Web Form Designer.
        <System.Diagnostics.DebuggerStepThrough()> Private Sub InitializeComponent()

        End Sub


        Private Sub Page_Init(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Init
            'CODEGEN: This method call is required by the Web Form Designer
            'Do not modify it using the code editor.
            InitializeComponent()
        End Sub

#End Region


        Protected Sub Page_Load(sender As Object, e As EventArgs) Handles Me.Load
            lblActID.Text = Request.QueryString("AccountID")
            If Not Page.IsPostBack Then
                getData()
                'btnRemove.Attributes.Add("onclick", " return confirm('Are you sure you want to remove Account (" & lblActID.Text & ") from Password Exempt?' );")
            End If
        End Sub

        Protected Sub getData()
            Dim strSQL As String
            Dim dbGet As New DataAccess
            Dim srRead As SqlClient.SqlDataReader
            Dim arUsers As New ArrayList
            'Dim hlpClean As New Helper
            Dim usrcur As New UserInfo
            Dim strAccountID As String = lblActID.Text
            strSQL = String.Format("SELECT vUsr.*,tbUMPwdExempt.*  FROM vUsr Left Join tbUMPwdExempt ON AccountID=PwdExemptAccountID AND PwdExemptRowStatus<>'Deleted' WHERE vUsr.AccountID = '{0}' AND UserName='{1}' AND Type IN (1,3)", strAccountID, usrcur.GetUserID)
            srRead = dbGet.GetDataReaderByStringId(strSQL)

            If srRead.Read Then
                lblActDesc.Text = Helper.IsValueNull(srRead.Item("Description"))
                Dim strActType As String = Helper.IsValueNull(srRead.Item("AccountType"))
                'lblActType.Text = Helper.IsValueNull(srRead.Item("AccountType"))
                Dim blIsExpired As Boolean = Helper.IsValueNull(srRead.Item("IsExpired"))
                lblFActID.Text = lblActID.Text
                lblFActDesc.Text = lblActDesc.Text
                'lblFActType.Text = lblActType.Text
                If Not blIsExpired Then
                    If srRead.Item("PwdExemptID") Is DBNull.Value Then
                        lblheaderDescription.Visible = True
                        MVMain.ActiveViewIndex = 1
                        btnCancel.Visible = False
                    Else
                        lblheaderDescription.Visible = False
                        Dim strPwdExemptBy As String = Helper.IsValueNull(srRead.Item("PwdExemptBy"))
                        Dim strPwdExemptOn As String = Helper.IsValueNull(srRead.Item("PwdExemptOn"))
                        Dim strPwdExemptStatus As String = Helper.IsValueNull(srRead.Item("PwdExemptStatus"))
                        Dim blIsPwdExempt As Boolean = Helper.IsValueNull(srRead.Item("IsPwdExempt"))
                        Dim blPwdExemptIsPwdExempt As Boolean = Helper.IsValueNull(srRead.Item("PwdExemptIsPwdExempt"))
                        lblFIsPwdExempt.Text = blPwdExemptIsPwdExempt
                        lblFPurpose.Text = Helper.IsValueNull(srRead.Item("PwdExemptPurpose"))
                        hfPwdExemptID.Value = srRead.Item("PwdExemptID")
                        'lblfinishMessage.Text = String.Format("Policy review has been completed by {0} on {1}.", strPwdExemptBy, strPwdExemptOn)
                        'lblfinishMessage.CssClass = "LabelRed"
                        lblFStatus.Text = strPwdExemptStatus
                        btnEdit.Visible = False
                        btnRemove.Visible = False
                        If strPwdExemptStatus = "Pending" Then
                            lblPwdExempt.Text = String.Format("Your request to add ID to the Password Exempt Process has been submitted.", lblActID.Text)
                            lblPwdExempt.CssClass = "LabelGreen"
                        ElseIf strPwdExemptStatus = "Not Qualified" Then
                            lblPwdExempt.Text = String.Format("Account {0} does not meet the requirements to qualify for the Password Exempt Process.", lblActID.Text)
                            lblPwdExempt.CssClass = "LabelRed"
                            btnEdit.Visible = True
                        ElseIf strPwdExemptStatus = "Approved" Then
                            lblPwdExempt.Text = String.Format("Account {0} has been added to Password Exempt Process.", lblActID.Text)
                            lblPwdExempt.CssClass = "LabelGreen"
                            btnRemove.Visible = True
                        ElseIf strPwdExemptStatus = "Rejected" Then
                            lblPwdExempt.Text = String.Format("Account {0} does not meet the requirements to qualify for the Password Exempt Process.", lblActID.Text)
                            lblPwdExempt.CssClass = "LabelRed"
                            btnEdit.Visible = True
                        ElseIf strPwdExemptStatus = "Removed" Then
                            lblheaderDescription.Visible = True
                            MVMain.ActiveViewIndex = 1
                            btnCancel.Visible = False
                        End If
                        If strPwdExemptStatus <> "Removed" Then
                            MVMain.ActiveViewIndex = 2
                        End If

                    End If
                Else
                    lblheaderDescription.Visible = False
                    lblMainMessage.Text = "The Password Exempt process only applies to accounts with a non-expiring password.  This account (" & lblActID.Text & ") has an expiring password."
                    lblMainMessage.CssClass = "LabelRed"
                    MVMain.ActiveViewIndex = 0
                End If
            Else
                lblheaderDescription.Visible = False
                lblMainMessage.Text = "You are not the owner or delegate of " & lblActID.Text & "."
                lblMainMessage.CssClass = "LabelRed"
                MVMain.ActiveViewIndex = 0
            End If
        End Sub

        Protected Sub btnSubmit_Click(sender As Object, e As EventArgs) Handles btnSubmit.Click
            lblsubMsg.Text = ""
            Dim IsPwdExempt As Boolean = rdoANS1.SelectedValue = "Y" And (rdoANS2.SelectedValue = "Y" Or rdoANS3.SelectedValue = "Y")
            Dim strAns As String = String.Format("{0},{1},{2}", rdoANS1.SelectedValue, rdoANS2.SelectedValue, rdoANS3.SelectedValue)
            Dim dbObj As New DataAccess
            Dim usrcur As New UserInfo
            'Dim strShortDescription As String = "Application ID Create Request"
            'Dim dataAccessObj As New DataAccess
            'Dim ID As Integer = dataAccessObj.insertTicketRequest(strShortDescription, "Testing only", "Q07233")
            Dim ID As Integer = 0
            Try
                If IsPwdExempt Then
                    'Ticket Creation entry
                    Dim newline As String = Chr(10)
                    Dim strDetails As String = "Reference:  Request/Non User Id Password Exempt Review" & newline
                    strDetails = strDetails & "Domain: " & [Global].GblDomain & newline
                    strDetails = strDetails & "Non User Id: " & lblActID.Text & newline
                    strDetails = strDetails & "Description: " & lblActDesc.Text & newline
                    strDetails = strDetails & "Answers: " & strAns & newline
                    strDetails = strDetails & "Purpose: " & txtPurpose.Text & newline
                    strDetails = strDetails & "Requestor ID: " & usrcur.GetUserID & newline
                    Dim strShortDescription As String = "Password Exempt Review"
                    Dim StrDynamicShortDesc As String = strShortDescription & ": " & lblActID.Text
                    ID = dbObj.insertTicketRequest(strShortDescription, strDetails, usrcur.GetUserID, StrDynamicShortDesc)
                End If
                Dim IsInserted As Boolean = dbObj.InsertOrUpdatePwdExempt(lblActID.Text, IsPwdExempt, strAns, txtPurpose.Text, ID, usrcur.GetUserID)
                If IsInserted Then
                    getData()
                Else
                    lblsubMsg.Text = "Error Occurred: Please try after sometime." & dbObj.ErrorMsg
                End If
            Catch ex As Exception
                lblsubMsg.Text = "Error Occurred: Please try after sometime." & ex.Message.ToString
            End Try
            

        End Sub

        Protected Sub btnBack_Click(sender As Object, e As EventArgs) Handles btnBack.Click
            Response.Redirect("../MyUsers.aspx")
        End Sub


        Protected Sub btnEdit_Click(sender As Object, e As EventArgs) Handles btnEdit.Click
            txtPurpose.Text = lblFPurpose.Text
            lblheaderDescription.Visible = True
            btnCancel.Visible = True
            MVMain.ActiveViewIndex = 1
        End Sub

        Protected Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
            getData()
            MVMain.ActiveViewIndex = 2
        End Sub

        Protected Sub btnRemove_Click(sender As Object, e As EventArgs) Handles btnRemove.Click
            If hfPwdExemptID.Value <> "" Then
                lblsubMsg.Text = ""
                Dim dbObj As New DataAccess
                Dim usrcur As New UserInfo
                Dim IsUpdated As Boolean = dbObj.RemovePwdExempt(lblActID.Text, Convert.ToInt32(hfPwdExemptID.Value), usrcur.GetUserID)
                If IsUpdated Then
                    getData()
                Else
                    lblsubMsg.Text = "Error Occurred: Please try after sometime." & dbObj.ErrorMsg
                End If
            End If
        End Sub
    End Class
End Namespace