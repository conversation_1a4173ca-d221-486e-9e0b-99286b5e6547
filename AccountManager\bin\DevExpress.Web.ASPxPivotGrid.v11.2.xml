<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DevExpress.Web.ASPxPivotGrid.v11.2</name>
    </assembly>
    <members>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridPagerStyles">

            <summary>
                <para>Provides the style settings used to paint the pivot grid's pager.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridPagerStyles.#ctor(DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid)">
            <summary>
                <para>Initializes a new instnace of the PivotGridPagerStyles class with the specified owner.
</para>
            </summary>
            <param name="grid">
		An <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid"/> object that represents the owner of the created object.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridPagerStyles.ToString">
            <summary>
                <para>Returns the textual representation of the PivotGridPagerStyles object.
</para>
            </summary>
            <returns>A <see cref="F:System.String.Empty"/> value.
</returns>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridStyles">

            <summary>
                <para>Provides the style settings used to paint the elements within the pivot grid.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.#ctor(DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid)">
            <summary>
                <para>Initializes a new instance of the PivotGridStyles class with the specified owner.
</para>
            </summary>
            <param name="pivotGrid">
		An <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid"/> object to which the created object belongs.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.AreaStyle">
            <summary>
                <para>Gets the style settings used to paint all pivot grid header areas, including the column header area, row header area, data header area, and filter header area.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotAreaStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.AreaStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.CellStyle">
            <summary>
                <para>Gets the style settings used to paint cells.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCellStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.CellStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.ColumnAreaStyle">
            <summary>
                <para>Gets the style settings used to paint the column header area.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotAreaStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.ColumnAreaStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.CustomizationFieldsCloseButtonStyle">
            <summary>
                <para>Gets the style settings used to paint the close button within the Customization Fields window.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridPopupWindowButtonStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.CustomizationFieldsCloseButtonStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.CustomizationFieldsContentStyle">
            <summary>
                <para>Gets the style settings used to paint the Customization Fields window's content.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridPopupWindowContentStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.CustomizationFieldsContentStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.CustomizationFieldsHeaderStyle">
            <summary>
                <para>Gets the style settings used to paint the Customization Fields window's header.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridPopupWindowStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.CustomizationFieldsHeaderStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.CustomizationFieldsStyle">
            <summary>
                <para>Gets the style settings used to paint the Customization Fields window.
</para>
            </summary>
            <value>An object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.CustomizationFieldsStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.CustomTotalCellStyle">
            <summary>
                <para>Gets the style settings used to paint cells within custom totals.
 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCellStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.CustomTotalCellStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.DataAreaStyle">
            <summary>
                <para>Gets the style settings used to paint the data header area.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotAreaStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.DataAreaStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.EmptyAreaStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FieldValueGrandTotalStyle">
            <summary>
                <para>Gets the style settings used to paint field values corresponding to grand totals .
 
</para>
            </summary>
            <value> A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldValueStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FieldValueGrandTotalStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FieldValueStyle">
            <summary>
                <para>Gets the style settings used to paint field values.
</para>
            </summary>
            <value> A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldValueStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FieldValueStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FieldValueTotalStyle">
            <summary>
                <para>Gets the style settings used to paint field values corresponding to totals. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldValueStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FieldValueTotalStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FilterAreaStyle">
            <summary>
                <para>Gets the style settings used to paint the filter header area.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotAreaStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FilterAreaStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FilterButtonPanelStyle">
            <summary>
                <para>Gets the style settings used to paint filter button panels.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFilterButtonPanelStyle"/> object that contains the style settings.

</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FilterButtonPanelStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FilterButtonStyle">
            <summary>
                <para>Gets the style settings used to paint the filter buttons.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFilterButtonStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FilterButtonStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FilterItemsAreaStyle">
            <summary>
                <para>Gets the style settings used to paint the items area within the filter window.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFilterStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FilterItemsAreaStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FilterItemStyle">
            <summary>
                <para>Gets the style settings used to paint items within the filter window.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFilterItemStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FilterItemStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FilterWindowStyle">
            <summary>
                <para>Gets the style settings used to paint the filter window.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFilterStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.FilterWindowStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.GrandTotalCellStyle">
            <summary>
                <para>Gets the style settings used to paint grand total cells.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCellStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.GrandTotalCellStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.HeaderStyle">
            <summary>
                <para>Gets the style settings used to paint the grid's headers (column header area, row header area, data header area).
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotHeaderStyle"/> object that contains the style settings.

</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.HeaderStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.KPICellStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.LoadingDiv">
            <summary>
                <para>Gets the style settings used to paint a rectangle displayed above the ASPxPivotGrid while waiting for a callback response.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridLoadingDivStyle"/> object that contains style settings used to paint a rectangle displayed above the ASPxPivotGrid while waiting for a callback response.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.LoadingPanel">
            <summary>
                <para>Gets the style settings used to paint the Loading Panel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridLoadingPanelStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.MenuItemStyle">
            <summary>
                <para>Gets the style settings used to paint the context menu's items.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridMenuItemStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.MenuItemStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.MenuStyle">
            <summary>
                <para>Gets the style settings used to paint the context menu.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridMenuStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.MenuStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterBuilderButtonAreaStyle">
            <summary>
                <para>Gets the style settings used to paint a prefilter's footer that displays buttons.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.SerializableStyle"/> object that contains style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterBuilderButtonAreaStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterBuilderCloseButtonStyle">
            <summary>
                <para>Gets the style settings used to paint the prefilter's <b>Close</b> button.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPopupControl.PopupWindowButtonStyle"/> object that contains style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterBuilderCloseButtonStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterBuilderHeaderStyle">
            <summary>
                <para>Gets the style settings used to paint the prefilter's header.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPopupControl.PopupWindowStyle"/> object that contains style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterBuilderHeaderStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterBuilderMainAreaStyle">
            <summary>
                <para>Gets the style settings used to paint the prefilter's content area.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.SerializableStyle"/> object that contains style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterBuilderMainAreaStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterPanelCheckBoxCellStyle">
            <summary>
                <para>Gets the style settings used to paint the Prefilter panel's cell which displays the check box.
</para>
            </summary>
            <value> A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridPrefilterPanelStyle"/> object that contains style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterPanelCheckBoxCellStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterPanelClearButtonCellStyle">
            <summary>
                <para>Gets the style settings used to paint the Prefilter panel's cell that displays the <b>Clear</b> button.
</para>
            </summary>
            <value> A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridPrefilterPanelStyle"/> object that contains style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterPanelClearButtonCellStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterPanelContainerStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterPanelExpressionCellStyle">
            <summary>
                <para>Gets the style settings used to paint the Prefilter panel's cell which displays the current filter expression.
</para>
            </summary>
            <value> A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridPrefilterPanelStyle"/> object that contains style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterPanelExpressionCellStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterPanelImageCellStyle">
            <summary>
                <para>Gets the style settings used to paint the Prefilter panel's cell which displays the filter image.
</para>
            </summary>
            <value> A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridPrefilterPanelStyle"/> object that contains style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterPanelImageCellStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterPanelLinkStyle">
            <summary>
                <para>Gets the style settings used to paint links (filter expression, clear filter command) displayed within the Prefilter panel.
</para>
            </summary>
            <value> A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridPrefilterPanelStyle"/> object that contains style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterPanelLinkStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterPanelStyle">
            <summary>
                <para>Gets the style settings used to paint the Prefilter panel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridPrefilterPanelStyle"/> object that contains style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.PrefilterPanelStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.RowAreaStyle">
            <summary>
                <para>Gets the style settings used to paint the row header area.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotAreaStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.RowAreaStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.TotalCellStyle">
            <summary>
                <para>Gets the style settings used to paint total cells.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCellStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridStyles.TotalCellStyleName">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridImages">

            <summary>
                <para>Contains the settings that define images displayed within the pivot grid's elements.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridImages.#ctor(DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid)">
            <summary>
                <para>Initializes a new instance of the PivotGridImages class with the specified owner.
</para>
            </summary>
            <param name="owner">
		An <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid"/> object that represents the owner of the created object.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.CustomizationFieldsBackground">
            <summary>
                <para>Gets the settings which define the image displayed as the Customization window's background.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.CustomizationFieldsClose">
            <summary>
                <para>Gets the settings which define the image displayed within the Customization window's Close button.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.DataHeadersPopup">
            <summary>
                <para>Gets the settings that define the image displayed within the Data Header Area when data fields are temporarily hidden.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.DragArrowDown">
            <summary>
                <para>Gets the settings which define the arrow down image displayed during drag operations. 


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.DragArrowLeft">
            <summary>
                <para>Gets the settings which define the left arrow image displayed during drag operations. 

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.DragArrowRight">
            <summary>
                <para>Gets the settings which define the right arrow image displayed during drag operations.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.DragArrowUp">
            <summary>
                <para>Gets the settings which define the arrow up image displayed during drag operations. 


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.DragHideField">
            <summary>
                <para>Gets the settings which define the hide field image displayed during drag operations. 


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.FieldValueCollapsed">
            <summary>
                <para>Gets the settings which define the image displayed within the field's expand button when the field is collapsed. 

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.FieldValueExpanded">
            <summary>
                <para>Gets the settings which define the image displayed within the field's expand button when the field is expanded. 

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.FilterWindowSizeGrip">
            <summary>
                <para>Gets the settings that define the size grip image within the Filter Window. 


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.GroupSeparator">
            <summary>
                <para>Gets the settings that define the image displayed between fields in a group.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.HeaderActiveFilter">
            <summary>
                <para>Gets the settings that define the image displayed within the filter button when filtering is applied. 


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.HeaderFilter">
            <summary>
                <para>Gets the settings that define the image displayed within the filter button. 


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.HeaderSortDown">
            <summary>
                <para>Gets the settings that define the image that indicates the descending sort order of fields. 


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.HeaderSortUp">
            <summary>
                <para>Gets the settings that define the image that indicates the ascending sort order of fields. 


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.PrefilterButton">
            <summary>
                <para>Gets the settings of an image displayed within the prefilter button.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.SortByColumn">
            <summary>
                <para>Gets the settings that define the image displayed within column or row headers. This image indicates that column or row field values are sorted by this column/row summary's values.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridImages.TreeViewNodeLoadingPanel">
            <summary>
                <para>Gets the settings that define the image displayed near the group filter node while it is expanding.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that contains image settings.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsPager">

            <summary>
                <para>Contains the pivot grid's pager settings.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsPager.#ctor(DevExpress.Web.ASPxPivotGrid.OptionsPagerChangedEventHandler,DevExpress.WebUtils.IViewBagOwner,System.String)">
            <summary>
                <para>Initializes a new instance of the PivotGridWebOptionsPager class.
</para>
            </summary>
            <param name="optionsChanged">
		 A delegate that will receive change notifications.

            </param>
            <param name="viewBagOwner">
		 

            </param>
            <param name="objectPath">
		 

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsPager.Assign(DevExpress.Web.ASPxClasses.PropertiesBase)">
            <summary>
                <para>Copies settings from the options object passed as the parameter. 
</para>
            </summary>
            <param name="source">
		A <see cref="T:DevExpress.Web.ASPxClasses.PropertiesBase"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsPager.HasPager">
            <summary>
                <para>Gets a value that indicates whether the pivot grid's pager is available.
</para>
            </summary>
            <value><b>true</b> if the pivot grid's pager is available; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsPager.PageIndex">
            <summary>
                <para>Gets or sets the index of the page currently being selected.
</para>
            </summary>
            <value>An integer value that specifies the active page's index.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsPager.PagerAlign">
            <summary>
                <para>Gets or sets the pager's horizontal alignment within the pivot grid.
</para>
            </summary>
            <value>One of the <see cref="T:DevExpress.Web.ASPxPivotGrid.PagerAlign"/> enumeration values.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsPager.RowsPerPage">
            <summary>
                <para>Gets or sets the maximum number of rows displayed within a page. Setting this property to <b>0</b> hiddes the Pager.
</para>
            </summary>
            <value>An integer value that specifies the maximum number of rows within a page.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PagerAlign">

            <summary>
                <para>Specifies the pager's position within the pivot grid.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PagerAlign.Center">
            <summary>
                <para>The pager is centered.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PagerAlign.Justify">
            <summary>
                <para>The pager is justified.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PagerAlign.Left">
            <summary>
                <para>The pager is left aligned.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PagerAlign.Right">
            <summary>
                <para>The pager is right aligned.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridClientSideEvents">

            <summary>
                <para>Contains a list of the client-side events available for the pivot grid control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridClientSideEvents.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotGridClientSideEvents class. For internal use only.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridClientSideEvents.AfterCallback">
            <summary>
                <para>Gets or sets the name of the JavaScript function or the entire code which will handle the client <see cref="E:DevExpress.Web.ASPxPivotGrid.Scripts.ASPxClientPivotGrid.AfterCallback"/> event.
</para>
            </summary>
            <value>A string that represents either the name of a JavaScript function or the entire JavaScript function code used to handle an event.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridClientSideEvents.BeforeCallback">
            <summary>
                <para>Gets or sets the name of the JavaScript function or the entire code which will handle the client <see cref="E:DevExpress.Web.ASPxPivotGrid.Scripts.ASPxClientPivotGrid.BeforeCallback"/> event.
</para>
            </summary>
            <value>A string that represents either the name of a JavaScript function or the entire JavaScript function code used to handle an event.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridClientSideEvents.BeginCallback">
            <summary>
                <para>Gets or sets the name of the JavaScript function or the entire code that will handle the client <b>BeginCallback</b> event.
</para>
            </summary>
            <value>A string that is either the name of a JavaScript function or the entire JavaScript function code used to handle an event.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridClientSideEvents.CellClick">
            <summary>
                <para>Gets or sets the name of the JavaScript function or the entire code which will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.Scripts.ASPxClientPivotGrid.CellClick"/> client event.
</para>
            </summary>
            <value>A string that represents either the name of a JavaScript function or the entire JavaScript function code used to handle an event.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridClientSideEvents.CellDblClick">
            <summary>
                <para>Gets or sets the name of the JavaScript function or the entire code which will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.Scripts.ASPxClientPivotGrid.CellDblClick"/> client event.
</para>
            </summary>
            <value>A string that represents either the name of a JavaScript function or the entire JavaScript function code used to handle an event.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridClientSideEvents.CustomizationFieldsVisibleChanged">
            <summary>
                <para>Gets or sets the name of the JavaScript function or the entire code which will handle a client pivot grid's <see cref="E:DevExpress.Web.ASPxPivotGrid.Scripts.ASPxClientPivotGrid.CustomizationFieldsVisibleChanged"/> event.
</para>
            </summary>
            <value>A string that represents either the name of a JavaScript function or the entire JavaScript function code used to handle an event.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridClientSideEvents.EndCallback">
            <summary>
                <para>Gets or sets the name of the JavaScript function or the entire code that will handle the client <b>EndCallback</b> event.
</para>
            </summary>
            <value>A string that is either the name of a JavaScript function or the entire JavaScript function code used to handle an event.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridClientSideEvents.Init">
            <summary>
                <para>Gets or sets the name of the JavaScript function or the entire code which will handle the client <b>Init</b> event.
</para>
            </summary>
            <value>A string that represents either the name of a JavaScript function or the entire JavaScript function code used to handle an event.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridClientSideEvents.PopupMenuItemClick">
            <summary>
                <para>Gets or sets the name of the JavaScript function or the entire code which will handle a client pivot grid's <see cref="E:DevExpress.Web.ASPxPivotGrid.Scripts.ASPxClientPivotGrid.PopupMenuItemClick"/> event.
</para>
            </summary>
            <value>A string that represents either the name of a JavaScript function or the entire JavaScript function code used to handle an event.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotHeaderStyle">

            <summary>
                <para>Provides style settings for field headers.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotHeaderStyle.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotHeaderStyle class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotHeaderStyle.FilterImageSpacing">
            <summary>
                <para>This property is not in effect for the PivotHeaderStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotHeaderStyle.ImageSpacing">
            <summary>
                <para>This property is not in effect for the PivotHeaderStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotHeaderStyle.SortingImageSpacing">
            <summary>
                <para>This property is not in effect for the PivotHeaderStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotHeaderStyle.Spacing">
            <summary>
                <para>This property is not in effect for the PivotHeaderStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView">

            <summary>
                <para>Provides view options for ASPxPivotGrid controls.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.#ctor(System.EventHandler,DevExpress.WebUtils.IViewBagOwner,System.String)">
            <summary>
                <para>Initializes a new instance of the PivotGridWebOptionsView class.
</para>
            </summary>
            <param name="optionsChanged">
		A delegate that will receive change notifications.

            </param>
            <param name="viewBagOwner">
		An object which implements the IViewBagOwner interface.

            </param>
            <param name="objectPath">
		A string value.


            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies settings from the options object passed as the parameter. 
</para>
            </summary>
            <param name="options">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.DataHeadersDisplayMode">
            <summary>
                <para>Gets or sets a value that specifies how data headers are displayed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotDataHeadersDisplayMode"/> enumeration value that specifies how data headers are displayed.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.DataHeadersPopupMaxColumnCount">
            <summary>
                <para>Gets or sets a value limiting the number of data field headers displayed in a row (line) of the field headers popup.
</para>
            </summary>
            <value>An integer value specifying the maximum number of data fields in a row.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.DataHeadersPopupMinCount">
            <summary>
                <para>Gets or sets the minimum number of data fields required to temporarily hide data field headers within a hidden panel.

</para>
            </summary>
            <value>An integer value that specifies the minimum number of data fields required to temporarily hide data field headers within a hidden panel.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.DrawFocusedCellRect">
            <summary>
                <para>This property is not in effect for this class. It is overridden only for the purpose of preventing it from appearing in Microsoft Visual Studio designer tools. 


</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.EnableContextMenuScrolling">
            <summary>
                <para>Gets or sets whether to enable context menu scrolling. 
</para>
            </summary>
            <value><b>true</b> to enable context menu scrolling; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.EnableFilterControlPopupMenuScrolling">
            <summary>
                <para>Gets or sets whether scrolling popup menus invoked in the Prefilter window is allowed.
</para>
            </summary>
            <value><b>true</b> to allow end-users to scroll popup menus invoked in the Prefilter window; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.RowTreeWidth">
            <summary>
                <para>This property is not in effect for this class. It is overridden only for the purpose of preventing it from appearing in Microsoft Visual Studio designer tools. 


</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.ShowContextMenus">
            <summary>
                <para>Gets or sets whether end-users can invoke the context menus.
</para>
            </summary>
            <value><b>true</b> to enable context menus; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.ShowContextMenusForAllFieldValues">
            <summary>
                <para>Gets or sets whether the context menu is shown for all field values.
</para>
            </summary>
            <value><b>true</b> to show the context menu for all field values; <b>false</b> to show the context menu only for parent field values.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.ShowHorizontalScrollBar">
            <summary>
                <para>Gets or sets whether the horizontal scrollbar is displayed.
</para>
            </summary>
            <value><b>true</b> to display the horizontal scrollbar; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.ShowHorzLines">
            <summary>
                <para>Gets or sets whether horizontal grid lines are displayed. This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value><b>true</b> to display horizontal grid lines; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.ShowVertLines">
            <summary>
                <para>Gets or sets whether vertical grid lines are displayed. This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value><b>true</b> to display vertical grid lines; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsDataField">

            <summary>
                <para>Provides options which control data fields presentation in the ASPxPivotGrid control.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsDataField.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData)">
            <summary>
                <para>Initializes a new instance of the PivotGridWebOptionsDataField class with the specified settings.
</para>
            </summary>
            <param name="data">
		 A PivotGridData object which contains the information required to initialize the created PivotGridWebOptionsDataField object.



            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsDataField.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData,DevExpress.WebUtils.IViewBagOwner,System.String)">
            <summary>
                <para>Initializes a new instance of the PivotGridWebOptionsDataField class with the specified settings.
</para>
            </summary>
            <param name="data">
		 A PivotGridData object which contains the information required to initialize the created PivotGridWebOptionsDataField object.


            </param>
            <param name="owner">
		 An IViewBagOwner object that is used to initialize the created object.


            </param>
            <param name="objectPath">
		A string value that is used to initialize the created object.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsDataField.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData,System.EventHandler,DevExpress.WebUtils.IViewBagOwner,System.String)">
            <summary>
                <para>Initializes a new instance of the PivotGridWebOptionsDataField class with the specified settings.
</para>
            </summary>
            <param name="data">
		 A PivotGridData object that contains information required to initialize the created PivotGridWebOptionsDataField object.

            </param>
            <param name="optionsChanged">
		A method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridOptionsBase.OptionsChanged"/> event.

            </param>
            <param name="owner">
		 An IViewBagOwner object used to initialize the created object.


            </param>
            <param name="objectPath">
		A string value used to initialize the created object.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsDataField.RowHeaderWidth">
            <summary>
                <para>Gets or sets the width of data field headers when they are displayed as row headers. This member supports the .NET Framework infrastructure and cannot be used directly from your code.

</para>
            </summary>
            <value>An integer which specifies the width of data field headers when they are displayed as rows.

</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsCustomization">

            <summary>
                <para>Provides customization options for an ASPxPivotGrid control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsCustomization.#ctor(DevExpress.Web.ASPxPivotGrid.OptionsCustomizationChangedEventHandler,System.EventHandler,DevExpress.WebUtils.IViewBagOwner,System.String)">
            <summary>
                <para>Initializes a new instance of the PivotGridWebOptionsCustomization class. 
</para>
            </summary>
            <param name="webOptionsChanged">
		An OptionsCustomizationChangedEventHandler delegate that will receive change notifications.

            </param>
            <param name="optionsChanged">
		An EventHandler delegate that will receive change notifications.

            </param>
            <param name="viewBagOwner">
		An object that implements the IViewBagOwner interface.

            </param>
            <param name="objectPath">
		A string value. 

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsCustomization.AllowCustomizationWindowResizing">
            <summary>
                <para>Gets or sets whether to allow end-users to resize the Customization Form.
</para>
            </summary>
            <value><b>true</b> to allow end-users to resize the Customization Form; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsCustomization.AllowHideFields">
            <summary>
                <para>Gets or sets a value which specifies when fields can be hidden by end-users. This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraPivotGrid.AllowHideFieldsType"/> enumeration value which specifies when fields can be hidden by end-users.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsCustomization.CustomizationExcelWindowHeight">
            <summary>
                <para>Gets or sets the height of the Customization Form,  when it is painted in the <b>Excel2007</b> style.
</para>
            </summary>
            <value>An integer value that specifies the height of the Customization Form, when it is painted in the <b>Excel2007</b> style, in pixels.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsCustomization.CustomizationExcelWindowWidth">
            <summary>
                <para>Gets or sets the width of the Customization Form, when it is painted in the <b>Excel2007</b> style.
</para>
            </summary>
            <value>An integer value that specifies the width of the Customization Form, when it is painted in the <b>Excel2007</b> style, in pixels.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsCustomization.CustomizationWindowHeight">
            <summary>
                <para>Gets or sets the height of the Customization Form, when it is painted in the <b>Simple</b> style.
</para>
            </summary>
            <value>An integer value that specifies the height of the Customization Form, when it is painted in the <b>Simple</b> style, in pixels.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsCustomization.CustomizationWindowWidth">
            <summary>
                <para>Gets or sets the width of the Customization Form, when it is painted in the <b>Simple</b> style.
</para>
            </summary>
            <value>An integer value that specifies the width of the Customization Form, when it is painted in the <b>Simple</b> style, in pixels.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsCustomization.FilterPopupWindowHeight">
            <summary>
                <para>Gets or sets the filter window's height.
</para>
            </summary>
            <value>An integer value that specifies the window's height. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsCustomization.FilterPopupWindowWidth">
            <summary>
                <para>Gets or sets the filter window's width.
</para>
            </summary>
            <value>An integer value that specifies the window's width. 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridHeaderTemplateContainer">

            <summary>
                <para>Represents a container for the templates used to render field headers.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridHeaderTemplateContainer.#ctor(DevExpress.Web.ASPxPivotGrid.PivotGridHeaderTemplateItem)">
            <summary>
                <para>Initializes a new instance of the PivotGridHeaderTemplateContainer class.
</para>
            </summary>
            <param name="item">
		An object containing the necessary information on the rendered field header.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridHeaderTemplateContainer.CreateHeader">
            <summary>
                <para>Creates the content of the processed field's header.
</para>
            </summary>
            <returns>A <b>PivotGridHeaderHtmlTable</b> object representing the field's header table.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridHeaderTemplateContainer.Field">
            <summary>
                <para>Gets a field for which the template's content is being instantiated.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object representing a field whose header is being rendered. 

</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateItem">

            <summary>
                <para>Contains data that can be used for data binding of a field value template's child controls.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateItem.#ctor(System.String,DevExpress.Web.ASPxPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.Data.PivotFieldValueItem,System.Collections.Generic.List`1)">
            <summary>
                <para>Initializes a new PivotGridFieldValueTemplateItem class instance with the specified settings.
</para>
            </summary>
            <param name="id">
		A string that specifies the processed field's unique indentifier.

            </param>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that specifies a field currently being processed.


            </param>
            <param name="item">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotFieldValueItem"/> object containing the necessary information on the processed field value.

            </param>
            <param name="sortedFields">
		A list of sorted fields.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateItem.CollapsedImage">
            <summary>
                <para>Gets the settings which define the image within the field's expand button when the field is collapsed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxClasses.ImageProperties"/> object which contains settings that define the image which corresponds to the field's collapsed state.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateItem.CollaspedImage">
            <summary>
                <para>Gets the settings, which define the image displayed within the corresponding field value in the collapsed state.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxClasses.ImageProperties"/> object which contains image settings. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateItem.Field">
            <summary>
                <para>Gets the field being processed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents a field currently being processed.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateItem.ImageOnClick">
            <summary>
                <para>Gets the java script code that realizes a field value's expanding/collapsing functionality.
</para>
            </summary>
            <value> A string value representing the script that expands or collapses the related field value.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateItem.IsAnyFieldSortedByThisValue">
            <summary>
                <para>Gets whether any field is sorted by the processed field value.
</para>
            </summary>
            <value><b> true</b> if any field is sorted by the value; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateItem.ValueItem">
            <summary>
                <para>Gets an object that contains the necessary information on the processed field value.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotFieldValueItem"/> object containing the necessary information on the processed field value.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateContainer">

            <summary>
                <para>Represents a container for the templates used to render field value cells.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateContainer.#ctor(DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateItem)">
            <summary>
                <para>Initializes a new instance of the PivotGridFieldValueTemplateContainer class.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateItem"/> object that contains the necessary information on the rendered field value.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateContainer.CreateFieldValue">
            <summary>
                <para>Creates the content of the processed field value cell.
</para>
            </summary>
            <returns>A <b>PivotGridFieldValueHtmlCell</b> object representing the field value cell.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateContainer.Field">
            <summary>
                <para>Gets a field for which the template's content is being instantiated.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object representing the field being rendered. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateContainer.HtmlText">
            <summary>
                <para>Gets the display text of the field value currently being rendered.
</para>
            </summary>
            <value>A string that represents the field value's display text. '<b>@nbsp;</b>' if the field's value is an empty string.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateContainer.Item">
            <summary>
                <para>Gets an object that contains the necessary information on the rendered field value.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateItem"/> object containing the necessary information on the rendered cell.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateContainer.MaxIndex">
            <summary>
                <para>Gets the maximum row index (for row fields) or column index (for column fields) that corresponds to the rendered field value.
</para>
            </summary>
            <value>An integer value that specifies the maximum row or column index that corresponds to the rendered field value.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateContainer.MinIndex">
            <summary>
                <para>Gets the minimum row index (for row fields) or column index (for column fields) that corresponds to the rendered field value.
</para>
            </summary>
            <value>An integer value that specifies the minimum row or column index that corresponds to the rendered field value.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateContainer.Text">
            <summary>
                <para>Gets the display text of the field value currently being rendered.
</para>
            </summary>
            <value>A string that represents the field value's display text.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateContainer.Value">
            <summary>
                <para>Gets the data value of the field value currently being rendered.
</para>
            </summary>
            <value>An object that represents the rendered field value's data value. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldValueTemplateContainer.ValueItem">
            <summary>
                <para>Gets an object that contains the necessary information on the rendered field value.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotFieldValueItem"/> object containing the necessary information on the rendered field value.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridFieldCollection">

            <summary>
                <para>Represents a field collection for the ASPxPivotGrid control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridFieldCollection.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData)">
            <summary>
                <para>Initializes a new instance of the PivotGridFieldCollection class.
</para>
            </summary>
            <param name="data">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridData"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridFieldCollection.Add(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Appends the specified field to the collection.
</para>
            </summary>
            <param name="field">
		The <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that will be added to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridFieldCollection.Add(System.String,DevExpress.XtraPivotGrid.PivotArea)">
            <summary>
                <para>Adds a new field with the specified field name and location to the end of the collection.
</para>
            </summary>
            <param name="fieldName">
		A string that identifies the name of the database field that will be assigned to the new PivotGridField object. 

            </param>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value that identifies the area in which the new PivotGridField object will be positioned. 

            </param>
            <returns>The <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that has been added to the collection.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridFieldCollection.AddField(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Adds the specified field to the end of the collection.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object representing the field to add to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridFieldCollection.GetFieldByClientID(System.String)">
            <summary>
                <para>This member supports the internal infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <param name="clientID">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldCollection.Item(System.Int32)">
            <summary>
                <para>Provides indexed access to individual fields in the collection.
</para>
            </summary>
            <param name="index">
		A zero-based integer specifying the desired field's position within the collection. If negative or exceeds the last available index, an exception is raised.

            </param>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents a field at the specified position.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridFieldCollection.Item(System.String)">
            <summary>
                <para>Gets the field with the specified id, name of the bound database field or caption.

</para>
            </summary>
            <param name="id_fieldName_Caption">
		A string value which specified the field's id, name of the bound database field or the field's caption.

            </param>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents a field with the specified id, name of the bound database field or caption.

</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridField">

            <summary>
                <para>Represents a field within the <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid"/> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridField.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotGridField class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridField.#ctor(System.String,DevExpress.XtraPivotGrid.PivotArea)">
            <summary>
                <para>Initializes a new instance of the PivotGridField class with the specified field name and location.
</para>
            </summary>
            <param name="fieldName">
		A string that identifies the name of the database field that will be assigned to the new PivotGridField object. 

            </param>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value that identifies the area in which the new PivotGridField object will be positioned. 

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridField.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData)">
            <summary>
                <para>Initializes a new instance of the PivotGridField class.
</para>
            </summary>
            <param name="data">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridData"/> object.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.AllowedAreas">
            <summary>
                <para>Gets or sets the areas within which the field can be positioned.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridAllowedAreas"/> value that specifies which areas the field can be positioned in.
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridField.Assign(DevExpress.XtraPivotGrid.PivotGridFieldBase)">
            <summary>
                <para>Copies the public properties from the specified object to the current one.
</para>
            </summary>
            <param name="source">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldBase"/> descendant whose properties are copied to the current object.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.CellStyle">
            <summary>
                <para>Gets style settings for cells.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCellStyle"/> object which contains style settings used to paint cells.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.CustomTotals">
            <summary>
                <para>Gets the collection of custom totals for the current field.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomTotalCollection"/> object which represent the collection of custom totals.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.ExportBestFit">
            <summary>
                <para>Gets or sets whether the columns corresponding to the current field should be resized to the minimum width required to completely display their contents when exporting the pivot grid.
</para>
            </summary>
            <value><b>true</b> to apply the Best-Fit feature to the field when exporting the pivot grid; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.FieldName">
            <summary>
                <para>Gets or sets the name of the database field that is assigned to the current PivotGridField object.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value which is the name of the data field.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.FilterButtonImage">
            <summary>
                <para>Gets the settings, which define the image within the filter button.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxClasses.ImageProperties"/> object, which contains settings that define the image within the filter button.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.GroupIndex">
            <summary>
                <para>This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.HeaderStyle">
            <summary>
                <para>Gets the field's style settings.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotHeaderStyle"/> object which contains style settings used to paint the field's header.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.HeaderTemplate">
            <summary>
                <para>Gets or sets a template used to display the content of the field's header.



</para>
            </summary>
            <value>An object that supports the <b>System.Web.UI.ITemplate</b> interface and contains the custom content for the field's header.


</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.ID">
            <summary>
                <para>Gets or sets the field's unique identifier name. 
</para>
            </summary>
            <value>A string which specifies the field's unique indentifier. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.InnerGroupIndex">
            <summary>
                <para>This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.IsTextOnly">
            <summary>
                <para>Gets a value that specifies whether both the filter button and sort glyph are displayed within the field's header.
</para>
            </summary>
            <value><b>true</b> if no filter button and sort glyph is displayed  otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.MinWidth">
            <summary>
                <para>Gets or sets the field's minimum width.
</para>
            </summary>
            <value>An integer value that specifies the field's minimum width.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.Name">
            <summary>
                <para>Gets or sets the field's unique name.
</para>
            </summary>
            <value>A string value that specifies the field's unique name.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.Options">
            <summary>
                <para>Contains the field's options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebFieldOptions"/> object which contains the field's options.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.SortImage">
            <summary>
                <para>Gets the settings, which define the sort image.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxClasses.ImageProperties"/> object which contains settings that define the sort image.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.ValueStyle">
            <summary>
                <para>Gets style settings used to paint field values.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldValueStyle"/> object that contains style settings used to paint field values.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.ValueTemplate">
            <summary>
                <para>Gets or sets a template to display the content of the field's value cells.


</para>
            </summary>
            <value>An object supporting the <b>System.Web.UI.ITemplate</b> interface that contains the custom content for the field's values.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.ValueTotalStyle">
            <summary>
                <para>Gets style settings used to paint totals.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldValueStyle"/> object that contains style settings used to paint totals.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridField.Width">
            <summary>
                <para>Gets or sets the width of columns that correspond to the current field when the pivot grid is exported.
</para>
            </summary>
            <value>An integer value specifying the width of columns that correspond to the current field when the pivot grid is exported.

</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridField.XtraClearCustomTotals(DevExpress.Utils.Serializing.XtraItemEventArgs)">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <param name="e">
		 

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridField.XtraCreateCustomTotalsItem(DevExpress.Utils.Serializing.XtraItemEventArgs)">
            <summary>
                <para>for internal use
</para>
            </summary>
            <param name="e">
		 

            </param>
            <returns> 
</returns>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridEmptyAreaTemplateContainer">

            <summary>
                <para>Represents a container for a template that is used to render the empty area.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridEmptyAreaTemplateContainer.#ctor(DevExpress.XtraPivotGrid.PivotArea)">
            <summary>
                <para>Initializes a new instance of the PivotGridEmptyAreaTemplateContainer class.
</para>
            </summary>
            <param name="area">
		One of the <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration values representing the area for which the current container is being instantiated.


            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridEmptyAreaTemplateContainer.Area">
            <summary>
                <para>Gets the area for which the template's content is being instantiated.

</para>
            </summary>
            <value>One of the <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration values. 

</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomTotalCollection">

            <summary>
                <para>Represents a collection of custom totals for a field.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCustomTotalCollection.#ctor(DevExpress.XtraPivotGrid.PivotGridFieldBase)">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomTotalCollection class.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldBase"/> object representing a field to which the current collection belongs.


            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCustomTotalCollection.#ctor(DevExpress.XtraPivotGrid.PivotGridCustomTotalBase[])">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomTotalCollection class.
</para>
            </summary>
            <param name="totals">
		An array of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotalBase"/> objects representing collections of a field's custom totals.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCustomTotalCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomTotalCollection class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCustomTotalCollection.Item(System.Int32)">
            <summary>
                <para>Provides indexed access to individual items within the PivotGridCustomTotalCollection.
</para>
            </summary>
            <param name="index">
		An integer value specifying the zero-based index of the item to be accessed.

            </param>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomTotal"/> object representing the item located at the specified index within the collection.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomTotal">

            <summary>
                <para>Represents a custom total which can be calculated for an outer column field or row field.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCustomTotal.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomTotal class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCustomTotal.#ctor(DevExpress.Data.PivotGrid.PivotSummaryType)">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomTotal class.
</para>
            </summary>
            <param name="summaryType">
		A <see cref="T:DevExpress.Data.PivotGrid.PivotSummaryType"/> enumeration value that specifies the summary function type.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomSummaryEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomSummary"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCustomSummaryEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotGridCustomSummaryEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomSummary"/> event.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomSummaryEventArgs"/> object that contains event data.


            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomSummaryEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomSummary"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCustomSummaryEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData,DevExpress.Web.ASPxPivotGrid.PivotGridField,DevExpress.Data.PivotGrid.PivotCustomSummaryInfo)">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomSummaryEventArgs class with the specified settings.
</para>
            </summary>
            <param name="data">
		A PivotGridData object which contains information required to initialize the created PivotGridCustomSummaryEventArgs object.

            </param>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents a data field. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.Data.PivotGridCustomSummaryEventArgsBase.DataField"/> property.


            </param>
            <param name="customSummaryInfo">
		A PivotCustomSummaryInfo object that contains information required to initialize the created PivotGridCustomSummaryEventArgs object.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCustomSummaryEventArgs.ColumnField">
            <summary>
                <para>Gets the column field which corresponds to the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the column field.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCustomSummaryEventArgs.RowField">
            <summary>
                <para>Gets the row field which corresponds to the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.PivotGrid.PivotGridField"/> object which represents the row field.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomFieldSort"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomFieldSort"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender. Identifies the ASPxPivotGrid control that raised an event.


            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomFieldSort"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData,DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomFieldSortEventArgs class.
</para>
            </summary>
            <param name="data">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridData"/> object which contains field data.

            </param>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the field whose values are being compared. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventArgs.Field"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventArgs.Field">
            <summary>
                <para>Gets the field whose values are being compared.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the field whose values are being compared.
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventArgs.GetListSourceColumnValue(System.Int32,System.String)">
            <summary>
                <para>Gets the specified field's value in the specified row in the control's underlying data source.
</para>
            </summary>
            <param name="listSourceRowIndex">
		An integer value which identifies the row in the data source.

            </param>
            <param name="columnName">
		A string which specifies the required field name.

            </param>
            <returns>An object which represents the field's value in the control's data source.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventArgs.Handled">
            <summary>
                <para>Gets or sets whether a comparison operation is being handled and therefore no default processing is required.
</para>
            </summary>
            <value><b>true</b> if a comparison operation is being handled; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventArgs.ListSourceRowIndex1">
            <summary>
                <para>Gets the index in the data source of the first of the two rows being compared.
</para>
            </summary>
            <value>An integer value that specifies the index of the first row in the data source.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventArgs.ListSourceRowIndex2">
            <summary>
                <para>Gets the index in the data source of the second of the two rows being compared.
</para>
            </summary>
            <value>An integer value that specifies the index of the second row in the data source.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventArgs.Result">
            <summary>
                <para>Gets or sets the result of a custom comparison.
</para>
            </summary>
            <value>An integer value that specifies the custom comparison's result.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventArgs.SortOrder">
            <summary>
                <para>Gets the sort order applied to the field.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotSortOrder"/> value that represents the field's sort order.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventArgs.Value1">
            <summary>
                <para>Gets the first value being compared.
</para>
            </summary>
            <value>An object that represents the first value being compared.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCustomFieldSortEventArgs.Value2">
            <summary>
                <para>Gets the second value being compared.
</para>
            </summary>
            <value>An object that represents the second value being compared.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem">

            <summary>
                <para>Contains data that can be used for data binding of a cell template's child controls.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridCellItem,System.String)">
            <summary>
                <para>Initializes a new instance of the PivotGridCellTemplateItem class.
</para>
            </summary>
            <param name="cellItem">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridCellItem"/> object which provides information on the processed cell.

            </param>
            <param name="text">
		A string that represents the cell's display text. 


            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.ColumnField">
            <summary>
                <para>Gets the innermost column field to which the processed cell corresponds.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the column field.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.ColumnFieldIndex">
            <summary>
                <para>Gets the index of the field to which the templated cell belongs.
</para>
            </summary>
            <value>An integer value representing the corresponding field's index.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.ColumnIndex">
            <summary>
                <para>Gets the visual index of the column that contains the processed cell.
</para>
            </summary>
            <value>An integer value which specifies the visual index of the column that contains the processed cell.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.ColumnValueType">
            <summary>
                <para>Gets the type of the column which contains the processed cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value which specifies the type of the column in which the processed cell resides.
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.CreateDrillDownDataSource">
            <summary>
                <para>Returns a list of records associated with the field value currently being processed.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains records associated with the processed field value.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.DataField">
            <summary>
                <para>Gets the data field that identifies the column to which the processed cell belongs.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that identifies the column to which the processed cell belongs.
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.GetCellValue(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns a cell value calculated for the current column and row field values, against the specified data field.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that specifies the data field, against which the required cell value is calculated.

            </param>
            <returns>Value displayed in the specified cell; <b>null</b> (<b>Nothing</b> in Visual Basic) if the cell has not been found.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.GetCellValue(System.Object[],System.Object[],DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns a cell value calculated for the specified column and row field values, against the specified data field.
</para>
            </summary>
            <param name="columnValues">
		An array of column values.

            </param>
            <param name="rowValues">
		An array of row values.

            </param>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that specifies the data field, against which the required cell value is calculated.

            </param>
            <returns>A summary value calculated for the specified column and row field values, against the specified data field.

</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.GetColumnFields">
            <summary>
                <para>Returns an array of column fields which correspond to the processed cell.
</para>
            </summary>
            <returns>An array of column fields.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.GetColumnGrandTotal(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns a Column Grand Total value calculated for the current row field values, against the specified data field.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that specifies the data field, against which the required Column Grand Total is calculated.

            </param>
            <returns>The Column Grand Total value calculated for the current row field values, against the specified data field.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.GetColumnGrandTotal(System.Object[],DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns a Column Grand Total value calculated for the specified row field values, against the specified data field.
</para>
            </summary>
            <param name="rowValues">
		An array of row field values for which the required Column Grand Total is calculated.

            </param>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that specifies the data field, against which the required Column Grand Total is calculated.

            </param>
            <returns>The column grand total value calculated against the specified data field and from the specified row values.



</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.GetFieldValue(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of the specified column or row field which identifies the column/row in which the processed cell resides.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the field whose value is returned.

            </param>
            <returns>An object which represents the value of the specified column or row field which identifies the column/row in which the processed cell resides. <b>null</b> (<b>Nothing</b> in Visual Basic) if the specified field is not a column or row field.

</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.GetGrandTotal(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Gets the grand total for the specified field.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the field whose Grand Total is returned.

            </param>
            <returns>An object which represents the Grand Total.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.GetRowFields">
            <summary>
                <para>Returns an array of row fields that correspond to the processed cell.
</para>
            </summary>
            <returns>An array of row fields.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.GetRowGrandTotal(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns a Row Grand Total value calculated for the current column field values, against the specified data field.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that specifies the data field, against which the required Row Grand Total is calculated.


            </param>
            <returns>The Row Grand Total value calculated for the current column field values, against the specified data field.

</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.GetRowGrandTotal(System.Object[],DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns a Row Grand Total value calculated for the specified column field values, against the specified data field.
</para>
            </summary>
            <param name="columnValues">
		An array of column field values for which the required Row Grand Total is calculated.

            </param>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that specifies the data field, against which the required Row Grand Total is calculated.


            </param>
            <returns>The Row Grand Total value calculated for the specified column field values, against the specified data field.

</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.IsFieldValueExpanded(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Indicates whether the specified field's value that represents a row or column header of the processed cell is expanded.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents a field in a pivot grid.


            </param>
            <returns><b>true</b> if the field value is expanded; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.IsFieldValueRetrievable(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <param name="field">
		 

            </param>
            <returns> 
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.IsOthersFieldValue(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Indicates whether the processed data cell resides within the "Others" row/column when the Top N Value feature is enabled.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents a field.


            </param>
            <returns><b>true</b> if the data cell resides within the "Others" row/column; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.RowField">
            <summary>
                <para>Gets the innermost row field which corresponds to the processed cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the row field.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.RowFieldIndex">
            <summary>
                <para>This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value>An integer value.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.RowIndex">
            <summary>
                <para>Gets the visual index of the row that contains the processed cell.
</para>
            </summary>
            <value>An integer value which specifies the visual index of the row that contains the processed cell.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.RowValueType">
            <summary>
                <para>Gets the type of the row which contains the processed cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value which specifies the type of the row in which the processed cell resides.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.SummaryValue">
            <summary>
                <para>Gets the summary value currently being processed.
</para>
            </summary>
            <value>The summary value currently being processed.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.Text">
            <summary>
                <para>Gets the display text of the cell currently being rendered.
</para>
            </summary>
            <value>A string that represents the cell's display text. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem.Value">
            <summary>
                <para>Gets the value of the cell currently being rendered.
</para>
            </summary>
            <value>An object that represents the cell's value. 

</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateContainer">

            <summary>
                <para>Represents a container for a template that is used to render cells.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateContainer.#ctor(DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem)">
            <summary>
                <para>Initializes a new instance of the PivotGridCellTemplateContainer class.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem"/> object that contains the necessary information on the rendered cell.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateContainer.ColumnField">
            <summary>
                <para>Gets the innermost column field to which the rendered cell corresponds.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the column field.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateContainer.DataField">
            <summary>
                <para>Gets the data field that identifies the column to which the rendered cell belongs.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that identifies the column to which the rendered cell belongs.

</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateContainer.GetFieldValue(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of the specified column or row field which identifies the column/row in which the rendered cell resides.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the field whose value is returned.

            </param>
            <returns>An object which represents the value of the specified column or row field which identifies the column/row in which the rendered cell resides. <b>null</b> (<b>Nothing</b> in Visual Basic) if the specified field isn't a column or row field.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateContainer.Item">
            <summary>
                <para>Gets an object that contains the necessary information on the rendered cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateItem"/> object containing the necessary information on the rendered cell.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateContainer.RowField">
            <summary>
                <para>Gets the innermost row field which corresponds to the rendered cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the row field.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateContainer.Text">
            <summary>
                <para>Gets the display text of the cell currently being rendered.
</para>
            </summary>
            <value>A string that represents the cell's display text. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCellTemplateContainer.Value">
            <summary>
                <para>Gets the data value calculated for the cell currently being rendered.
</para>
            </summary>
            <value>An object that represents the rendered cell's data value. 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFilterStyle">

            <summary>
                <para>Provides style settings used to paint the filter dropdown.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFilterStyle.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotFilterStyle class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFilterStyle.HoverStyle">
            <summary>
                <para>This property is not in effect for the PivotFilterStyle class.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.Web.ASPxClasses.AppearanceSelectedStyle"/> object.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFilterStyle.ImageSpacing">
            <summary>
                <para>This property is not in effect for the PivotFilterStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFilterStyle.Spacing">
            <summary>
                <para>This property is not in effect for the PivotFilterStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFilterItemStyle">

            <summary>
                <para>Provides style settings used to paint filter items within the filter dropdown.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFilterItemStyle.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotFilterItemStyle class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFilterItemStyle.ImageSpacing">
            <summary>
                <para>This property is not in effect for the PivotFilterItemStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFilterButtonStyle">

            <summary>
                <para>Provides style settings for filter buttons.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFilterButtonStyle.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotFilterButtonStyle class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFilterButtonStyle.ImageSpacing">
            <summary>
                <para>This property is not in effect for the PivotFilterButtonStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFilterButtonStyle.Spacing">
            <summary>
                <para>This property is not in effect for the PivotFilterButtonStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFilterButtonPanelStyle">

            <summary>
                <para>Provides style settings used to paint filter button panel.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFilterButtonPanelStyle.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotFilterButtonPanelStyle class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFilterButtonPanelStyle.ImageSpacing">
            <summary>
                <para>This property is not in effect for the PivotFilterButtonPanelStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldValueStyle">

            <summary>
                <para>Provides style settings for field values.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldValueStyle.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotFieldValueStyle class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldValueStyle.CopyFrom(System.Web.UI.WebControls.Style)">
            <summary>
                <para>Duplicates the properties of the specified object into the current instance of the PivotFieldValueStyle class.
</para>
            </summary>
            <param name="source">
		A <see cref="T:System.Web.UI.WebControls.Style"/> object from which the settings will be copied.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueStyle.HoverStyle">
            <summary>
                <para>This property is not in effect for the PivotFieldValueStyle class.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.Web.ASPxClasses.AppearanceSelectedStyle"/> object.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueStyle.Spacing">
            <summary>
                <para>This property is not in effect for the PivotFieldValueStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueStyle.TopAlignedRowValues">
            <summary>
                <para>Gets or sets whether row values are top-aligned.
</para>
            </summary>
            <value><b>true</b> if row values are top-aligned; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventHandler">

            <summary>
                <para>A method that will handle events used to customize field values.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs)">
            <summary>
                <para>A method that will handle events used to customize field values.
</para>
            </summary>
            <param name="sender">
		The event source. Identifies the ASPxPivotGrid control that raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs">

            <summary>
                <para>Serves as a base for classes that provide data for events used to customize field values.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotFieldValueItem)">
            <summary>
                <para>Initializes a new instance of the PivotFieldValueEventArgs class.
</para>
            </summary>
            <param name="fieldValueItem">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotFieldValueItem"/> object that contains information used to initialize a new object.


            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.CreateDrillDownDataSource">
            <summary>
                <para>Returns a list of records associated with the field value currently being processed.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains records associated with the processed field value.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.CreateOLAPDrillDownDataSource(System.Int32,System.Collections.Generic.List`1)">
            <summary>
                <para>In OLAP mode, returns a list of records used to calculate a summary value for the current field. Allows you to specify the columns and limit the number of records to be returned.
</para>
            </summary>
            <param name="maxRowCount">
		An integer value that specifies the maximum number of data rows to be returned. <b>-1</b> to retrieve all rows.

            </param>
            <param name="customColumns">
		A list of columns in a data source, to be returned.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.Data">
            <summary>
                <para>This member supports the .NET Framework infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A PivotGridData object.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.DataField">
            <summary>
                <para>Gets the data field which identifies the processed value.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represent the data field.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.FieldIndex">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.FieldValueItem">
            <summary>
                <para>Gets the information about the field value currently being processed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotFieldValueItem"/> object that contains information about the field value currently being processed.
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.GetCellValue(System.Int32,System.Int32)">
            <summary>
                <para>Returns a value displayed in the specified cell.
</para>
            </summary>
            <param name="columnIndex">
		A zero-based integer which identifies the visible index of the column.

            </param>
            <param name="rowIndex">
		A zero-based integer which identifies the visible index of the row.

            </param>
            <returns>A value displayed in the specified cell.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.GetFieldValue(DevExpress.Web.ASPxPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Returns the specified column or row field's value for the cell addressed by its zero-based index in the Data Area.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object representing a column field or row field, whose value is to be obtained.

            </param>
            <param name="cellIndex">
		A zero-based index of a cell in the Data Area that identifies the required field value. Indexes are numbered starting from the left edge for column fields, and from the top edge for row fields.

            </param>
            <returns>An object representing the field's value.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.GetHigherLevelFields">
            <summary>
                <para>Returns the parent field(s) for the field value being currently processed.
</para>
            </summary>
            <returns>An array of <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> objects that represent parent fields for the field value currently being processed.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.GetHigherLevelFieldValue(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of a specific parent field corresponding to the field value currently being processed.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents the parent field whose value is returned.

            </param>
            <returns>An object that represents the value of the specified parent (higher level) field.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.IsCollapsed">
            <summary>
                <para>Indicates whether the processed field value is collapsed.
</para>
            </summary>
            <value><b>true</b> if the field value is collapsed; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.IsColumn">
            <summary>
                <para>Indicates whether the processed field value corresponds to a column field.
</para>
            </summary>
            <value><b>true</b> if the processed field value corresponds to a column field; <b>false</b> if the processed field value corresponds to a row field.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.IsOthersValue">
            <summary>
                <para>Gets or sets whether the current header corresponds to the "Others" row/column.

</para>
            </summary>
            <value><b>true</b> if the current header corresponds to the "Others" row/column; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.MaxIndex">
            <summary>
                <para>Gets the maximum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.
</para>
            </summary>
            <value>An integer value that specifies the maximum row or column index that corresponds to the processed field value.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.MinIndex">
            <summary>
                <para>Gets the minimum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.
</para>
            </summary>
            <value>An integer value that specifies the minimum row or column index that corresponds to the processed field value.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.Value">
            <summary>
                <para>Gets the column field or row field value to which the processed column/row header corresponds.

</para>
            </summary>
            <value>An object that represents the field value currently being processed.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.ValueType">
            <summary>
                <para>Gets the type of the currently processed column/row header.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value which identifies the type of the currently processed column or row header.

</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueCollapsed"/> and <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueExpanded"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueCollapsed"/> and <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueExpanded"/> events.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the ASPxPivotGrid which raised an event.


            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueCollapsed"/> and <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueExpanded"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotFieldValueItem)">
            <summary>
                <para>Initializes a new instance of the PivotFieldStateChangedEventArgs class.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotFieldValueItem"/>  object which represents the item currently being processed. This value is assigned  to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.Item"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.#ctor(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Initializes a new instance of the PivotFieldStateChangedEventArgs class.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object, which is the field currently being processed. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotFieldEventArgs.Field"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.CreateDrillDownDataSource">
            <summary>
                <para>Returns a list of records associated with the field value currently being processed.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains records associated with the processed field value.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.CreateOLAPDrillDownDataSource(System.Int32,System.Collections.Generic.List`1)">
            <summary>
                <para>In OLAP mode, returns a list of records used to calculate a summary value for the current field. Allows you to specify the columns and limit the number of records to be returned.
</para>
            </summary>
            <param name="maxRowCount">
		An integer value that specifies the maximum number of data rows to be returned. <b>-1</b> to retrieve all rows.

            </param>
            <param name="customColumns">
		A list of columns in a data source, to be returned.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.CustomTotal">
            <summary>
                <para>Gets a custom total to which the processed field value corresponds.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomTotal"/> object that represents the custom total to which the processed field value corresponds. <b>null</b> (<b>Nothing</b> in Visual Basic) if the field value doesn't correspond to a custom total.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.Data">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.DataField">
            <summary>
                <para>Gets the data field which identifies the field value. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents the data field which identifies the field value. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.FieldIndex">
            <summary>
                <para>Gets the position of the processed field in the header area.
</para>
            </summary>
            <value>A zero-based integer value that specifies the position of the processed field in the header area.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.FieldValue">
            <summary>
                <para>Gets the column field or row field value which the currently processed column/row header corresponds to.
</para>
            </summary>
            <value>An object which represents the field value currently being processed. <b>null</b> (<b>Nothing</b> in Visual Basic) if a grand total header is currently being processed.
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.GetCellValue(System.Int32,System.Int32)">
            <summary>
                <para>Returns the specified cell's value.
</para>
            </summary>
            <param name="columnIndex">
		An integer value that identifies the column that contains the cell.

            </param>
            <param name="rowIndex">
		An integer value that identifies the row that owns the cell.

            </param>
            <returns>An object that represents the specified cell's value.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.GetFieldValue(DevExpress.Web.ASPxPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Returns the specified field value.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents a column or row field whose value is to be obtained.

            </param>
            <param name="cellIndex">
		A zero-based index of a cell in the Data Area that identifies the required field value. Indexes are numbered starting from the left edge for column fields, and from the top edge for row fields.

            </param>
            <returns>An object that represents the field value. 
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.GetHigherLevelFields">
            <summary>
                <para>Returns the parent field(s) for the field value being currently processed.
</para>
            </summary>
            <returns>An array of <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> objects that represent parent fields for the field value currently being processed.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.GetHigherLevelFieldValue(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of a specific parent field corresponding to the field value currently being processed.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents the parent field whose value is returned.

            </param>
            <returns>An object that represents the value of the specified parent (higher level) field.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.IsCollapsed">
            <summary>
                <para>Indicates whether the processed field value is collapsed.
</para>
            </summary>
            <value><b>true</b> if the field value is collapsed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.IsColumn">
            <summary>
                <para>Gets whether the processed field value belongs to a column or row field.
</para>
            </summary>
            <value><b>true</b> if the processed field value belongs to a column field; <b>false</b> if the processed field value belongs to a row field.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.IsOthersValue">
            <summary>
                <para>Gets whether the current header corresponds to the "Others" row/column.
</para>
            </summary>
            <value><b>true</b> if the current header corresponds to the "Others" row/column; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.Item">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.MaxIndex">
            <summary>
                <para>Gets the maximum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.
</para>
            </summary>
            <value>An integer value that specifies the maximum row or column index that corresponds to the processed field value.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.MinIndex">
            <summary>
                <para>Gets the minimum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.
</para>
            </summary>
            <value>An integer value that specifies the minimum row or column index that corresponds to the processed field value.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.Value">
            <summary>
                <para>Gets the column field or row field value to which the currently processed column/row header corresponds.
</para>
            </summary>
            <value>An object which represents the field value currently being processed.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.Values">
            <summary>
                <para>Gets the outer field values in the processed row/column.
</para>
            </summary>
            <value>An array of objects that represent outer field values in the processed row/column.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.ValueType">
            <summary>
                <para>Gets the type of the currently processed column/row header.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value which identifies the type of the currently processed column or row header.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedCancelEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueCollapsing"/> and <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueExpanding"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedCancelEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedCancelEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueCollapsing"/> and <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueExpanding"/> events.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the ASPxPivotGrid which raised an event.


            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedCancelEventArgs"/> object which contains event data.


            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedCancelEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueCollapsing"/> and <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueExpanding"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedCancelEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotFieldValueItem)">
            <summary>
                <para>Initializes a new instance of the PivotFieldStateChangedCancelEventArgs class.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotFieldValueItem"/> object which represents the item currently being processed. This value is assigned  to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedEventArgs.Item"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldStateChangedCancelEventArgs.Cancel">
            <summary>
                <para>Gets or sets whether the field value can be expanded/collapsed.
</para>
            </summary>
            <value><b>true</b> to cancel the operation; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldEventHandler">

            <summary>
                <para>A method that will handle field events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotFieldEventArgs)">
            <summary>
                <para>A method that will handle field events.
</para>
            </summary>
            <param name="sender">
		The event source. Identifies the ASPxPivotGrid control that raised an event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldEventArgs">

            <summary>
                <para>Provides data for all field handling events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldEventArgs.#ctor(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Initializes a new instance of the PivotFieldEventArgs class.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the field currently being processed. This value is assigned  to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotFieldEventArgs.Field"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldEventArgs.Field">
            <summary>
                <para>Gets the field being processed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the field currently being processed.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldDisplayTextEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueDisplayText"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldDisplayTextEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotFieldDisplayTextEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueDisplayText"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. Identifies the ASPxPivotGrid control that raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldDisplayTextEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldDisplayTextEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueDisplayText"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldDisplayTextEventArgs.#ctor(DevExpress.Web.ASPxPivotGrid.PivotGridField,System.Object)">
            <summary>
                <para>Initializes a new instance of the PivotFieldDisplayTextEventArgs class with the specified settings.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the field currently being processed. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotFieldEventArgs.Field"/> property.

            </param>
            <param name="value">
		An object which represents the field's value. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotFieldDisplayTextEventArgs.Value"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldDisplayTextEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotFieldValueItem,System.String)">
            <summary>
                <para>Initializes a new instance of the PivotFieldDisplayTextEventArgs class with the specified item and default text.
</para>
            </summary>
            <param name="fieldValueItem">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotFieldValueItem"/> specifying the field value item. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs.FieldValueItem"/> property.


            </param>
            <param name="defaultText">
		A <see cref="T:System.String"/> specifying the default display text. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotFieldDisplayTextEventArgs.DisplayText"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldDisplayTextEventArgs.DisplayText">
            <summary>
                <para>Gets or sets the item's display text.
</para>
            </summary>
            <value>A string value that specifies the item's display text. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldDisplayTextEventArgs.IsPopulatingFilterDropdown">
            <summary>
                <para>Gets whether this event is raised to populate the filter dropdown.
</para>
            </summary>
            <value><b>true</b> if the event is raised to populate the filter dropdown; <b>false</b> if the event is raised to customize the display text of column and row headers.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldDisplayTextEventArgs.Value">
            <summary>
                <para>Gets the processed item's value.
</para>
            </summary>
            <value>An object which represents the item's value.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCellStyle">

            <summary>
                <para>Provides style settings for cells.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellStyle.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotCellStyle class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellStyle.HoverStyle">
            <summary>
                <para>This property is not in effect for the PivotCellStyle class.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.Web.ASPxClasses.AppearanceSelectedStyle"/> object.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellStyle.ImageSpacing">
            <summary>
                <para>This property is not in effect for the PivotCellStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellStyle.Spacing">
            <summary>
                <para>This property is not in effect for the PivotCellStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCellDisplayTextEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCellDisplayText"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellDisplayTextEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotCellDisplayTextEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCellDisplayText"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. Identifies the ASPxPivotGrid control that raised the event. 

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCellDisplayTextEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCellDisplayTextEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCellDisplayText"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellDisplayTextEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridCellItem)">
            <summary>
                <para>Initializes a new instance of the PivotCellDisplayTextEventArgs class.
</para>
            </summary>
            <param name="cellItem">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridCellItem"/> object which provides information on the processed cell.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellDisplayTextEventArgs.DisplayText">
            <summary>
                <para>Gets or sets the display text for the cell currently being processed.
</para>
            </summary>
            <value>A string that represents the cell's display text. 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs">

            <summary>
                <para>Provides data for events which are invoked for particular cells.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridCellItem)">
            <summary>
                <para>Initializes a new instance of the PivotCellBaseEventArgs class.
</para>
            </summary>
            <param name="cellItem">
		The <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridCellItem"/> object.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.CellItem">
            <summary>
                <para>This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridCellItem"/> object.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.ColumnCustomTotal">
            <summary>
                <para>Gets the column custom total which displays the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomTotal"/> object which represents the column custom total that contains the current cell.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.ColumnField">
            <summary>
                <para>Gets the innermost column field which corresponds to the processed cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the column field.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.ColumnFieldIndex">
            <summary>
                <para>This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value>An integer value.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.ColumnIndex">
            <summary>
                <para>Gets the visual index of the column that contains the processed cell.
</para>
            </summary>
            <value>An integer value which specifies the visual index of the column that contains the processed cell.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.ColumnValueType">
            <summary>
                <para>Gets the type of the column which contains the processed cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value which specifies the type of the column in which the processed cell resides.
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.CreateDrillDownDataSource">
            <summary>
                <para>Returns a list of records used to calculate a summary value for the cell currently being processed. 
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains records used to calculate a summary value for the current cell.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.CreateSummaryDataSource">
            <summary>
                <para>Returns a <b>summary data source</b>.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotSummaryDataSource"/> object that represents the summary data source.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.Data">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.DataField">
            <summary>
                <para>Gets the data field which identifies the column where the processed cell resides.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which identifies the column where the processed cell resides.
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetAbsoluteRowIndex(System.Int32)">
            <summary>
                <para>Returns the row's absolute index by its visible index within the current page.
</para>
            </summary>
            <param name="pageRowVisibleIndex">
		An integer value that specifies the row's visible position within the current page.

            </param>
            <returns>An integer value that specifies the row's absolute index.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetCellValue(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns a cell value calculated for the current column and row field values, against the specified data field.

</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that specifies the data field.

            </param>
            <returns>The cell value. <b>null</b> (<b>Nothing</b> in Visual Basic) if no cell has been found.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetCellValue(System.Object[],System.Object[],DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns a cell value calculated for the specified column and row field values, against the specified data field.
</para>
            </summary>
            <param name="columnValues">
		An array of column values.

            </param>
            <param name="rowValues">
		An array of row values.

            </param>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that specifies the data field, against which the required cell value is calculated.


            </param>
            <returns>A summary value calculated for the specified column and row field values, against the specified data field.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetCellValue(System.Int32,System.Int32)">
            <summary>
                <para>Returns a cell value by the column and row indexes.
</para>
            </summary>
            <param name="columnIndex">
		A zero-based integer which identifies the visible index of the column.

            </param>
            <param name="rowIndex">
		A zero-based integer which identifies the visible index of the row.

            </param>
            <returns>A value displayed in the specified cell.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetColumnFields">
            <summary>
                <para>Returns an array of column fields which correspond to the processed cell.
</para>
            </summary>
            <returns>An array of column fields.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetColumnGrandTotal(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns a Column Grand Total value calculated for the current row field values, against the specified data field.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that specifies the data field, against which the required Column Grand Total is calculated.


            </param>
            <returns>The Column Grand Total value calculated for the current row field values, against the specified data field.

</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetColumnGrandTotal(System.Object[],DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns a Column Grand Total value calculated for the specified row field values, against the specified data field.
</para>
            </summary>
            <param name="rowValues">
		An array of row field values for which the required Column Grand Total is calculated.


            </param>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that specifies the data field, against which the required Column Grand Total is calculated.


            </param>
            <returns>The column grand total value calculated against the specified data field and from the specified row values.

</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetFieldValue(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of the specified column or row field which identifies the column/row in which the processed cell resides.

</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents a field whose value is returned.


            </param>
            <returns>An object which represents the value of the specified column or row field which identifies the column/row in which the processed cell resides. <b>null</b> (<b>Nothing</b> in Visual Basic) if the specified field isn't a column or row field.

</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetFieldValue(DevExpress.Web.ASPxPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Returns the specified column field or row field value for the cell, addressed by its zero-based index in the data area.

</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object representing a column field or row field, whose value is to be obtained.


            </param>
            <param name="cellIndex">
		A zero-based index of a cell in the data area that identifies the required field value. Indexes are numbered starting from the left for column fields, and from the top for row fields.


            </param>
            <returns>An object representing the field's value.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetGrandTotal(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Gets the Grand Total for the specified field.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the field whose Grand Total is returned.

            </param>
            <returns>An object which represents the Grand Total.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetNextColumnCellValue(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of the cell in the same row but in the next column.

</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which identifies the sub column in the next column.

            </param>
            <returns>An object that represents the value in the same row but in the next column.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetNextRowCellValue(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of the cell in the next row.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which identifies the sub column in the next row.

            </param>
            <returns>An object that represents the value of the cell in the next row.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetPrevColumnCellValue(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of the cell in the same row but in the previous column.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which identifies the sub column in the previous column.

            </param>
            <returns>An object that represents the value in the same row but in the previous column.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetPrevRowCellValue(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of the cell in the previous row.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which identifies the sub column in the previous row.

            </param>
            <returns>An object that represents the value of the cell in the previous row.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetRowFields">
            <summary>
                <para>Returns an array of row fields that correspond to the processed cell.
</para>
            </summary>
            <returns>An array of row fields.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetRowGrandTotal(System.Object[],DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns a Row Grand Total value calculated for the specified column field values, against the specified data field.
</para>
            </summary>
            <param name="columnValues">
		An array of column field values for which the required Row Grand Total is calculated.


            </param>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that specifies the data field, against which the required Row Grand Total is calculated.

            </param>
            <returns>The Row Grand Total value calculated for the specified column field values, against the specified data field.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.GetRowGrandTotal(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Returns a Row Grand Total value calculated for the current column field values, against the specified data field.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that specifies the data field, against which the required Row Grand Total is calculated.



            </param>
            <returns>The Row Grand Total value calculated for the current column field values, against the specified data field.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.IsFieldValueExpanded(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Indicates whether the specified field's value that represents a row or column header of the processed cell is expanded.

</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents a field in a pivot grid.


            </param>
            <returns><b>true</b> if the field value is expanded; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.IsFieldValueRetrievable(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <param name="field">
		 

            </param>
            <returns> 
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.IsOthersFieldValue(DevExpress.Web.ASPxPivotGrid.PivotGridField)">
            <summary>
                <para>Indicates whether the processed data cell resides within the "Others" row/column when the Top N Value feature is enabled.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents a field.


            </param>
            <returns><b>true</b> if the data cell resides within the "Others" row/column; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.RowCustomTotal">
            <summary>
                <para>Gets the row custom total which contains the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomTotal"/> object which represents the row custom total that contains the current cell.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.RowField">
            <summary>
                <para>Gets the innermost row field which corresponds to the processed cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the row field.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.RowFieldIndex">
            <summary>
                <para>This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value>An integer value.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.RowIndex">
            <summary>
                <para>Gets the visual index of the row that contains the processed cell.
</para>
            </summary>
            <value>An integer value which specifies the visual index of the row that contains the processed cell.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.RowValueType">
            <summary>
                <para>Gets the type of the row which contains the processed cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value which specifies the type of the row in which the processed cell resides.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.SummaryValue">
            <summary>
                <para>Gets the summary value currently being processed.
</para>
            </summary>
            <value>The summary value currently being processed.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs.Value">
            <summary>
                <para>Gets the processed cell's value.
</para>
            </summary>
            <value>An object which represents the processed cell's value.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotAreaStyle">

            <summary>
                <para>Provides style settings for header areas.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotAreaStyle.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotAreaStyle class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotAreaStyle.HoverStyle">
            <summary>
                <para>This property is not in effect for the PivotAreaStyle class.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.Web.ASPxClasses.AppearanceSelectedStyle"/> object.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotAreaStyle.ImageSpacing">
            <summary>
                <para>This property is not in effect for the PivotAreaStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotAreaStyle.Spacing">
            <summary>
                <para>This property is not in effect for the PivotAreaStyle class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.CustomFieldDataEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomUnboundFieldData"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.CustomFieldDataEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.CustomFieldDataEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomUnboundFieldData"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. Identifies the ASPxPivotGrid control that raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.CustomFieldDataEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.CustomFieldDataEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomUnboundFieldData"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.CustomFieldDataEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData,DevExpress.Web.ASPxPivotGrid.PivotGridField,System.Int32,System.Object)">
            <summary>
                <para>Initializes a new instance of the CustomFieldDataEventArgs class.
</para>
            </summary>
            <param name="data">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridData"/> object.

            </param>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents the unbound field currently being processed.

            </param>
            <param name="listSourceRow">
		An integer value which identifies the current row's index in the data source. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.CustomFieldDataEventArgs.ListSourceRowIndex"/> property.

            </param>
            <param name="_value">
		An object which represents the processed cell's value. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.CustomFieldDataEventArgs.Value"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.CustomFieldDataEventArgs.GetListSourceColumnValue(System.Int32,System.String)">
            <summary>
                <para>Gets the specified field's value in the specified row in the control's underlying data source.
</para>
            </summary>
            <param name="listSourceRowIndex">
		An integer value which identifies the required row's index in the data source. 

            </param>
            <param name="columnName">
		A string which represents the required field name.

            </param>
            <returns>An object which represents the field's value in the control's data source.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.CustomFieldDataEventArgs.GetListSourceColumnValue(System.String)">
            <summary>
                <para>Gets the specified cell's value within the current row in the control's underlying data source.
</para>
            </summary>
            <param name="columnName">
		A string which represents the required field name.

            </param>
            <returns>An object which represents a value from the control's data source.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.CustomFieldDataEventArgs.ListSourceRowIndex">
            <summary>
                <para>Gets the current row's index in the data source.
</para>
            </summary>
            <value>An integer value which identifies the current row's index in the data source.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.CustomFieldDataEventArgs.Value">
            <summary>
                <para>Sets the cell's value. 
</para>
            </summary>
            <value>An object which represents the processed cell's value.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid">

            <summary>
                <para>Represents the ASPxPivotGrid control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.#ctor">
            <summary>
                <para>Initializes a new instance of the ASPxPivotGrid class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.AccessibilityCompliant">
            <summary>
                <para>Enables support for Section 508.
</para>
            </summary>
            <value><b>true</b> to enable support for Section 508; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.AddPopupMenuItem">
            <summary>
                <para>Enables you to customize the context menu.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.AfterPerformCallback">
            <summary>
                <para>Fires after a callback initiated by the <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid"/> control has been processed on the server side.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.AreaStyle">
            <summary>
                <para>Provides style settings for header areas.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotAreaStyle"/> object that contains style settings used to paint header areas.
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.BeforeGetCallbackResult">
            <summary>
                <para>Occurs after a callback sent by the current control has been processed on the server, but prior to the time the respective callback result render is formed.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.BeforeLoadLayout">
            <summary>
                <para>Occurs before a layout is restored from a storage, allowing you to cancel this action.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.BeforePerformDataSelect">
            <summary>
                <para>Occurs before the ASPxPivotGrid control obtains data from a data source.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.BeginRefresh">
            <summary>
                <para>Occurs before the control starts recalculating its data.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.BeginUpdate">
            <summary>
                <para>Prevents the ASPxPivotGrid from being rendered until the <see cref="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.EndUpdate"/> method is called.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CalcCallbackResult">
            <summary>
                <para>Initiates a callback by the client and obtains the result processed within the server.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.Web.ASPxClasses.Internal.CallbackResult"/> object, containing the client and client's element IDs and server-processed contents.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.Caption">
            <summary>
                <para>Gets or sets the text to render in an HTML caption element in an ASPxPivotGrid.
</para>
            </summary>
            <value>A string value that specifies the text to render in an HTML caption element in an ASPxPivotGrid.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CellStyle">
            <summary>
                <para>Provides style settings for cells.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCellStyle"/> object that contains style settings used to paint cells.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CellTemplate">
            <summary>
                <para>Gets or sets a template to display the content of cells.
</para>
            </summary>
            <value>An object supporting the <b>System.Web.UI.ITemplate</b> interface that contains the custom content for cells.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ClientInstanceName">
            <summary>
                <para>Gets or sets the pivot grid's client programmatic identifier.
</para>
            </summary>
            <value>A string value that specifies the grid's client identifier.
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ClientLayout">
            <summary>
                <para>Enables you to save and restore the previously saved layout of the pivot grid. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ClientSideEvents">
            <summary>
                <para>Gets an object that lists the client-side events specific to the ASPxPivotGrid.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridClientSideEvents"/> object which allows assigning handlers to the client-side events available to the ASPxPivotGrid.

</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CollapseAll">
            <summary>
                <para>Collapses all the columns and rows in the pivot grid.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CollapseAllColumns">
            <summary>
                <para>Collapses all columns.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CollapseAllRows">
            <summary>
                <para>Collapses all rows.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CollapsedStateStoreMode">
            <summary>
                <para>Gets or sets how the grid stores information about the collapsed rows/columns.
</para>
            </summary>
            <value>One of the <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCollapsedStateStoreMode"/> enumeration values.
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CollapseValue(System.Boolean,System.Object[])">
            <summary>
                <para>Collapses the specified column or row.
</para>
            </summary>
            <param name="isColumn">
		<b>true</b> to collapse a column; <b>false</b> to collapse a row.

            </param>
            <param name="values">
		An array of field values that identifies the column/row to be collapsed.


            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ColumnCount">
            <summary>
                <para>Gets the number of columns displayed within the ASPxPivotGrid.
</para>
            </summary>
            <value>An integer value that specifies the number of columns displayed within the ASPxPivotGrid.
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ControlHierarchyCreated">
            <summary>
                <para>Occurs after the ASPxPivotGrid's table has been created.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CreateDrillDownDataSource(System.Int32,System.Int32)">
            <summary>
                <para>Returns a list of records associated with the specified cell.
</para>
            </summary>
            <param name="columnIndex">
		An integer value that specifies the zero-based index of the column where the cell resides.

            </param>
            <param name="rowIndex">
		An integer value that specifies the zero-based index of the row where the cell resides.


            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object which contains records associated with the specified cell.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CreateDrillDownDataSource(System.Int32,System.Int32,System.Int32)">
            <summary>
                <para>Returns a list of records, which are associated with the specified cell.
</para>
            </summary>
            <param name="columnIndex">
		An integer value that specifies the cell's column.

            </param>
            <param name="rowIndex">
		An integer value that identifies the row which contains the cell by its absolute index.

            </param>
            <param name="dataIndex">
		An integer value that identifies the data field by its index.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object which contains records associated with the specified cell.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CreateDrillDownDataSource">
            <summary>
                <para>Returns a list of records used to calculate summary values for all cells. 
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CreateOLAPDrillDownDataSource(System.Int32,System.Int32,System.Int32,System.Collections.Generic.List`1)">
            <summary>
                <para>In OLAP mode, returns a list of records used to calculate a summary value for the specified cell. Allows you to specify the columns, and limit the number of records to be returned.
</para>
            </summary>
            <param name="columnIndex">
		A zero-based integer which identifies the visible index of the column.

            </param>
            <param name="rowIndex">
		A zero-based integer which identifies the visible index of the row.

            </param>
            <param name="maxRowCount">
		An integer value that specifies the maximum number of data rows to be returned. <b>-1</b> to retrieve all rows.

            </param>
            <param name="customColumns">
		A list of columns in a data source, to be returned.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CreateOLAPDrillDownDataSource(System.Int32,System.Int32,System.Collections.Generic.List`1)">
            <summary>
                <para>In OLAP mode, returns a list of records used to calculate a summary value for the specified cell. Allows you to specify the columns to be returned.
</para>
            </summary>
            <param name="columnIndex">
		A zero-based integer which identifies the visible index of the column.

            </param>
            <param name="rowIndex">
		A zero-based integer which identifies the visible index of the row.

            </param>
            <param name="customColumns">
		A list of columns in a data source, to be returned.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CreateSummaryDataSource(System.Int32,System.Int32)">
            <summary>
                <para>Returns a <b>summary data source</b> for the specified row. 
</para>
            </summary>
            <param name="columnIndex">
		An integer value that specifies the column's index.

            </param>
            <param name="rowIndex">
		An integer value that specifies the row's index.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotSummaryDataSource"/> object that represents the summary data source.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CreateSummaryDataSource">
            <summary>
                <para>Returns the ASPxPivotGrid's <b>summary data source</b>. 
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotSummaryDataSource"/> object that represents the summary data source.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CssClass">
            <summary>
                <para>Gets or sets the name of the cascading style sheet (CSS) class that specifies the ASpxPivotGrid's style.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the cascading style sheet class's name.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CssFilePath">
            <summary>
                <para>Gets or sets the path to a CSS file that defines the pivot grid's appearance.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the path to the required CSS file.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CssPostfix">
            <summary>
                <para>Gets or sets a CSS class name postfix that identifies style settings to be applied to the pivot grid.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that represents the required CSS class name postfix.
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCallback">
            <summary>
                <para>Fires when a round trip to the server has been initiated by a call to the client <see cref="M:DevExpress.Web.ASPxPivotGrid.Scripts.ASPxClientPivotGrid.PerformCallback"/> method.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCellDisplayText">
            <summary>
                <para>Enables custom display text to be provided for cells displayed within the Data Area.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCellStyle">
            <summary>
                <para>Allows the appearances of cells to be dynamically customized.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCellValue">
            <summary>
                <para>Allows you to replace cell values with custom ones.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomChartDataSourceData">
            <summary>
                <para>Occurs when a ASPxPivotGrid prepares data to be displayed in a <see cref="T:DevExpress.XtraCharts.Web.WebChartControl"/>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomFieldSort">
            <summary>
                <para>Provides the ability to sort data using custom rules.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomFieldValueCells">
            <summary>
                <para>Allows you to customize field value cells.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomFilterExpressionDisplayText">
            <summary>
                <para>Enables you to display custom text within the Prefilter panel, corresponding to the current filter expression.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomFilterPopupItems">
            <summary>
                <para>Allows you to customize the filter drop-down list items.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomGroupInterval">
            <summary>
                <para>Enables grouping axis values, using your own criteria.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomizationFieldsCloseButtonStyle">
            <summary>
                <para>Provides style settings used to paint the Customization form's <b>Close</b> button.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPopupControl.PopupWindowButtonStyle"/> object that provides style settings used to paint the Customization form's <b>Close</b> button.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomizationFieldsCloseImage">
            <summary>
                <para>Gets the settings which define the image within the Customization window's Close button.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxClasses.ImageProperties"/> object that contains settings which define the image within the Customization window's Close button.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomizationFieldsContentStyle">
            <summary>
                <para>Provides style settings used to paint the Customization form's content.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPopupControl.PopupWindowContentStyle"/> object that contains style settings used to paint the Customization form's content.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomizationFieldsHeaderStyle">
            <summary>
                <para>Provides style settings used to paint the Customization form's header.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPopupControl.PopupWindowStyle"/> object that contains style settings used to paint the Customization form's header.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomizationFieldsLeft">
            <summary>
                <para>Gets or sets the X-coordinate of the Customization form's top-left corner.
</para>
            </summary>
            <value>An integer value that specifies the X-coordinate of the Customization form's top-left corner. The value is set in screen coordinates.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomizationFieldsStyle">
            <summary>
                <para>Provides style settings for the Customization form.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxClasses.AppearanceStyle"/> object that contains style settings used to paint the Customization from.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomizationFieldsTop">
            <summary>
                <para>Gets or sets the Y-coordinate of the Customization form's top-left corner.
</para>
            </summary>
            <value>An integer value that specifies the Y-coordinate of the Customization form's top-left corner. The value is set in screen coordinates.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomizationFieldsVisible">
            <summary>
                <para>Gets or sets a value that specifies whether the pivot grid's Customization form is displayed within the page.
</para>
            </summary>
            <value><b>true</b> if the Customization form is displayed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomizationFormImages">
            <summary>
                <para>Gets the settings that define images displayed in the Customization Form.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCustomizationFormImages"/> object containing the settings that define images displayed in the Customization Form.

</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomJsProperties">
            <summary>
                <para>Enables you to supply any server data that can then be parsed on the client.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomLoadCallbackState">
            <summary>
                <para>Allows you to load the ASPxPivotGrid's callback state, which was previously saved by handling the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomSaveCallbackState"/> event.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomSaveCallbackState">
            <summary>
                <para>Allows you to preserve the ASPxPivotGrid's callback state in a custom manner.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomSummary">
            <summary>
                <para>Enables summary values to be calculated manually.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomTotalCellStyle">
            <summary>
                <para>Provides style settings for custom totals.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCellStyle"/> object that contains style settings used to paint custom totals.
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomUnboundFieldData">
            <summary>
                <para>Enables data to be provided for unbound fields. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.Data">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.DataAreaPopupCreated">
            <summary>
                <para>Occurs after a panel that displays data headers has been created.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.DataSource">
            <summary>
                <para>Gets or sets the object from which the data-bound control retrieves its list of data items. 
</para>
            </summary>
            <value>An object that represents the data source from which the data-bound control retrieves its data. 
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.DataSourceChanged">
            <summary>
                <para>Fires when the pivot grid's data source changes.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.DataSourceID">
            <summary>
                <para>Gets or sets the ID of the control from which the data-bound control retrieves its list of data items. 
</para>
            </summary>
            <value>The ID of a control that represents the data source from which the data-bound control retrieves its data. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.DisabledStyle">
            <summary>
                <para>This property isn't used.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.Dispose">
            <summary>
                <para>Enables a server control to perform final clean up before it is released from memory. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.EmptyAreaTemplate">
            <summary>
                <para>Gets or sets a template to display the content of the empty area.
</para>
            </summary>
            <value>An object supporting the <b>System.Web.UI.ITemplate</b> interface that contains the custom content for the empty area.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.EnableCallbackCompression">
            <summary>
                <para>Gets or sets whether callback result compression is enabled.
</para>
            </summary>
            <value><b>true</b> to enable callback result compression; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.EnableCallBacks">
            <summary>
                <para>Gets or sets a value that specifies whether the callback or postback technology is used to manage round trips to the server.
</para>
            </summary>
            <value><b>true</b> if round trips to the server are performed using callbacks; <b>false</b> if postbacks are used.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.EnableClientSideAPI">
            <summary>
                <para>Gets or sets a value that specifies whether the grid view can be manipulated on the client side via code.
</para>
            </summary>
            <value><b>true</b> if the grid's client object model is fully available; <b>false</b> if no programmatic interface is available for the grid on the client side.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.Enabled">
            <summary>
                <para>This property isn't used.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.EnableDefaultAppearance">
            <summary>
                <para>Gets or sets a value that specifies whether the pivot grid is displayed with a predefined style or the grid's appearance has to be completely defined by a developer via either css or the appropriate style properties.
</para>
            </summary>
            <value><b>true</b> if the grid has a predefined style; <b>false</b> if the grid's appearance should be explicitly specified.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.EnableRowsCache">
            <summary>
                <para>Gets or sets whether data caching is enabled. 
</para>
            </summary>
            <value><b>true</b> to enable data caching; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.EndRefresh">
            <summary>
                <para>Occurs after the control has completed recalculating its data.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.EndUpdate">
            <summary>
                <para>Re-enables render operations after a call to the <see cref="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.BeginUpdate"/> method and forces an immediate re-rendering.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.EnsureRefreshData">
            <summary>
                <para>Refreshes the grid's data. This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ExpandAll">
            <summary>
                <para>Expands all the columns and rows in the pivot grid.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ExpandAllColumns">
            <summary>
                <para>Expands all columns.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ExpandAllRows">
            <summary>
                <para>Expands all rows.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ExpandValue(System.Boolean,System.Object[])">
            <summary>
                <para>Expands the specified column or row. 
</para>
            </summary>
            <param name="isColumn">
		<b>true</b> to expand a column; <b>false</b> to expand a row.

            </param>
            <param name="values">
		An array of values that identifies the column/row to be expanded.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ExpandValue(System.Boolean,System.Int32)">
            <summary>
                <para>Expands the specified column or row by its index.
</para>
            </summary>
            <param name="isColumn">
		<b>true</b> to expand a column; <b>false</b> to expand a row.

            </param>
            <param name="lastLevelIndex">
		An integer value that specifies the zero-based index of the column/row to be expanded.

            </param>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldAreaChanged">
            <summary>
                <para>Occurs after the field's location or visibility has been changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldAreaChanging">
            <summary>
                <para>Enables you to control whether the dragged field header can be dropped at the area it's currently located over. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldAreaIndexChanged">
            <summary>
                <para>Occurs when the field's <see cref="P:DevExpress.XtraPivotGrid.PivotGridFieldBase.AreaIndex"/> property has been changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldExpandedInFieldGroupChanged">
            <summary>
                <para>Fires when the expansion status of fields combined into a field group is changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldFilterChanged">
            <summary>
                <para>Occurs after a field's filter condition has been changed. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldFilterChanging">
            <summary>
                <para>Allows you to cancel changing the filter condition.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldPropertyChanged">
            <summary>
                <para>Occurs after a field's property has been changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.Fields">
            <summary>
                <para>Provides access to a pivot grid's field collection.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridFieldCollection"/> object that represents a collection of all the fields within a pivot grid.

</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldUnboundExpressionChanged">
            <summary>
                <para>Occurs after an unbound field expression has been changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueCollapsed">
            <summary>
                <para>Fires after a field value has been collapsed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueCollapsedImage">
            <summary>
                <para>Gets the settings which define the image within the field's expand button when the field is collapsed.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxClasses.ImageProperties"/> object which contains settings that define the image which corresponds to the field's collapsed state.

</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueCollapsing">
            <summary>
                <para>Enables you to control whether field values can be collapsed.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueDisplayText">
            <summary>
                <para>Enables the display text of individual column and row headers and filter dropdown items to be customized.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueExpanded">
            <summary>
                <para>Fires after a field value has been expanded.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueExpandedImage">
            <summary>
                <para>Gets the settings which define the image within the field's expand button when the field is expanded.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxClasses.ImageProperties"/> object which contains settings that define the image which corresponds to the field's expanded state.
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueExpanding">
            <summary>
                <para>Enables you to control whether field values can be expanded.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueGrandTotalStyle">
            <summary>
                <para>Provides style settings for Grand Total Headers.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldValueStyle"/> object that contains style settings used to paint grand total headers.
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueNotExpanded">
            <summary>
                <para>Occurs in OLAP mode, when an end-user clicks an expand button or selects <b>Expand All</b> from the context menu, and the field value cannot be expanded.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueStyle">
            <summary>
                <para>Provides style settings for field values.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldValueStyle"/> object that contains style settings used to paint field values.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueTemplate">
            <summary>
                <para>Gets or sets a template to display the content of field value cells.

</para>
            </summary>
            <value>An object supporting the <b>System.Web.UI.ITemplate</b> interface that contains the custom content for field value cells.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldValueTotalStyle">
            <summary>
                <para>Provides style settings for Total Headers.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldValueStyle"/> object that contains style settings used to paint total headers.
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldVisibleChanged">
            <summary>
                <para>Occurs after a field's visibility has been changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FilterAreaStyle">
            <summary>
                <para>Provides style settings for the Filter Header Area.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotAreaStyle"/> object that contains style settings used to paint the filter header area.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FilterButtonPanelStyle">
            <summary>
                <para>Provides style settings used to paint the Filter Button Panel.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFilterButtonPanelStyle"/> object that contains style settings used to paint the filter button panel.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FilterButtonStyle">
            <summary>
                <para>Provides style settings for buttons displayed within the Filter Window.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFilterButtonStyle"/> object that contains style settings used to paint buttons displayed within the filter window.
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FilterControlCustomValueDisplayText">
            <summary>
                <para>Enables you to specify the entered filter value's custom display text, to be displayed when the filter control's condition value editor is inactive.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FilterControlOperationVisibility">
            <summary>
                <para>Enables you to dynamically hide operation items (such as the Equals, Contains, etc.) of the Filter Control's operation dropdown menu.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FilterControlParseValue">
            <summary>
                <para>Enables you to process and modify an entered value before it is actually accepted by the filter control.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FilterItemsAreaStyle">
            <summary>
                <para>Provides style settings used to paint the client area within the Filter Window, where filter items are displayed.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFilterStyle"/> object that contains style settings for the client area within the filter window.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FilterItemStyle">
            <summary>
                <para>Provides style settings for items displayed within the Filter Window.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFilterItemStyle"/> object that contains style settings used to paint items within the filter window.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FilterWindowSizeGripImage">
            <summary>
                <para>Gets the settings, which define the size grip image within the Filter Window.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxClasses.ImageProperties"/> object which contains settings that define the size grip image within the filter window.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FilterWindowStyle">
            <summary>
                <para>Provides style settings for the Filter Window.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFilterStyle"/> object that contains style settings used to paint the filter window.

</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetAbsoluteRowIndex(System.Int32)">
            <summary>
                <para>Returns the row's absolute index by its visible index within the current page.
</para>
            </summary>
            <param name="pageRowVisibleIndex">
		An integer value that specifies the row's visible position within the current page.

            </param>
            <returns>An integer value that specifies the row's absolute index.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetCellInfo(System.Int32,System.Int32)">
            <summary>
                <para>Returns an object which contains information on the specified cell. 

</para>
            </summary>
            <param name="columnIndex">
		An integer value that specifies the zero-based index of the column where the required cell resides.

            </param>
            <param name="rowIndex">
		An integer value that specifies the zero-based index of the row where the required cell resides.

            </param>
            <returns>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCellBaseEventArgs"/> descendant which contains information on the specified cell. 
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetCellValue(System.Int32,System.Int32)">
            <summary>
                <para>Returns the value displayed in the specified cell.

</para>
            </summary>
            <param name="columnIndex">
		A zero-based index of the column that contains the cell.

            </param>
            <param name="rowIndex">
		A zero-based index of the row that contains the cell.

            </param>
            <returns>The value of the specified cell.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetColumnIndex(System.Object[])">
            <summary>
                <para>Returns the index of the specified column.
</para>
            </summary>
            <param name="values">
		An array of column field values that identify the column.

            </param>
            <returns>An integer value that specifies the column index. <b>-1</b> if the specified column has not been not found.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetFieldByArea(DevExpress.XtraPivotGrid.PivotArea,System.Int32)">
            <summary>
                <para>Returns a pivot grid field located at the specified position within the specified area.
</para>
            </summary>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value that specifies the area where the required pivot grid field is displayed.

            </param>
            <param name="areaIndex">
		An integer value that specifies the pivot grid field's position from among the other fields displayed within the same area.

            </param>
            <returns>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents the pivot grid field located at the specified position within the specified area. <b>null</b> (<b>Nothing</b>) in Visual Basic) if the file was not found.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetFieldCountByArea(DevExpress.XtraPivotGrid.PivotArea)">
            <summary>
                <para>Returns the number of fields located in the specified area.
</para>
            </summary>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration member that specifies the pivot grid area.

            </param>
            <returns>An integer value that specifies the number of fields located in the specified area.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetFieldList">
            <summary>
                <para>Returns the names of data source fields to which the pivot grid fields are bound.
</para>
            </summary>
            <returns>The names of data source fields to which the pivot grid fields are bound.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetFieldsByArea(DevExpress.XtraPivotGrid.PivotArea)">
            <summary>
                <para>Returns all visible pivot grid fields located at the specified area.
</para>
            </summary>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value that specifies the area where the required pivot grid fields are displayed.

            </param>
            <returns>The list of visible pivot grid fields located at the specified area.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetFieldValue(DevExpress.Web.ASPxPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Returns the specified field value.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents a column or row field whose value is to be obtained.

            </param>
            <param name="cellIndex">
		A zero-based index of a cell in the Data Area that identifies the required field value. Indices are numbered starting from the left edge for column fields, and from the top edge for row fields.

            </param>
            <returns>An object that represents the field's value. 
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetFieldValueByIndex(DevExpress.Web.ASPxPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Returns the specified field's value by its index.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents the field which contains the required value.

            </param>
            <param name="fieldValueIndex">
		An integer value that identifies the required field value.

            </param>
            <returns>An object that represents the specified field's value.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetFieldValueOLAPMember(DevExpress.Web.ASPxPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Returns an OLAP member for the specified field value.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Xpf.PivotGrid.PivotGridField"/> object that represents the pivot grid field. 

            </param>
            <param name="cellIndex">
		An integer value that specifies the cell's index.

            </param>
            <returns>An object that implements the <b>IOLAPMember</b> interface.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetFieldValueType(System.Boolean,System.Int32)">
            <summary>
                <para>Gets the type of a field value in the column or row area.
</para>
            </summary>
            <param name="isColumn">
		<b>true</b> to retrieve information on a field value in the column area; <b>false</b> to retrieve information on a field value in the row area.

            </param>
            <param name="cellIndex">
		A zero-based index of a column/row that identifies the required field value. Indexes are numbered starting from the left, for column fields, and from the top, for row fields.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> value that idenfifies the type of the required column/row field value.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetFieldValueType(DevExpress.Web.ASPxPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Gets the type of a specific column/row field's value.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that refers to the required field.


            </param>
            <param name="cellIndex">
		A zero-based index of a column/row that identifies the required field value. Indexes are numbered starting from the left, for column fields, and from the top, for row fields.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> value that idenfifies the type of the required column/row field value.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetKPIImage(DevExpress.XtraPivotGrid.PivotKPIGraphic,DevExpress.XtraPivotGrid.PivotKPIType,System.Int32)">
            <summary>
                <para>Returns an image that corresponds to the specified KPI value.
</para>
            </summary>
            <param name="graphic">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotKPIGraphic"/> enumeration value that specifies the KPI graphic set. You shouldn't use the 'None' and 'ServerDefined' values.

            </param>
            <param name="kpiType">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotKPIType"/> enumeration value that identifies the image set. Images can be obtained only for trend and status KPI types.

            </param>
            <param name="value">
		An integer value that specifies the KPI value (<b>-1</b>, <b>0</b> or <b>1</b>).

            </param>
            <returns>An <see cref="T:DevExpress.Web.ASPxClasses.ImageProperties"/> object that contains image settings for the specified KPI value.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetOLAPKPIList">
            <summary>
                <para>Gets the list of key performance indicators (KPI) in a cube.
</para>
            </summary>
            <returns>The list of KPI names.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetOLAPKPIMeasures(System.String)">
            <summary>
                <para>Returns the Measures dimension's members used to calculate the value, goal, status and weight of the specified Key Performance Indicator (KPI). 
</para>
            </summary>
            <param name="kpiName">
		A string value that specifies the KPI's name. 

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotOLAPKPIMeasures"/> object that contains measures used to determine the KPI's value.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetOLAPKPIServerGraphic(System.String,DevExpress.XtraPivotGrid.PivotKPIType)">
            <summary>
                <para>Returns a graphic set defined on the server for the specified KPI's trend and status.
</para>
            </summary>
            <param name="kpiName">
		A string value that specifies the KPI's name.

            </param>
            <param name="kpiType">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotKPIType"/> enumeration value that identifies the image set. Images can be obtained only for trend and status KPI types.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotKPIGraphic"/> enumeration value that specifies the KPI graphic set.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetOLAPKPIValue(System.String)">
            <summary>
                <para>Returns the specified KPI's value.
</para>
            </summary>
            <param name="kpiName">
		A string value that specifies the KPI's name.


            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotOLAPKPIValue"/> object that represents the specified KPI's value.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GetRowIndex(System.Object[])">
            <summary>
                <para>Returns the index of the specified row.

</para>
            </summary>
            <param name="values">
		An array of row field values that identify the row.

            </param>
            <returns>An integer value that specifies the row index. <b>-1</b> if the specified row has not been not found.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GrandTotalCellStyle">
            <summary>
                <para>Provides style settings for Grand Total Cells.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCellStyle"/> object that contains style settings used to paint Grand Total cells.
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GridLayout">
            <summary>
                <para>Fires immediately after the ASPxPivotGrid's layout has been changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GroupFilterChanged">
            <summary>
                <para>Occurs after a group filter condition has been changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.Groups">
            <summary>
                <para>Gets the collection of field groups.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebGroupCollection"/> object which represents a collection of field groups.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.HeaderActiveFilterImage">
            <summary>
                <para>Gets the settings, which define the image displayed within the filter button when filtering is applied.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxClasses.ImageProperties"/> object, which contains settings that define the image displayed within the filter button, when filtering is applied.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.HeaderFilterImage">
            <summary>
                <para>Gets the settings, which define the image displayed within the filter button.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxClasses.ImageProperties"/> object, which contains settings that define the image displayed within the filter button.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.HeaderSortDownImage">
            <summary>
                <para>Gets the settings, which define the image that indicates the descending sort order of fields.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxClasses.ImageProperties"/> object, which defines the image that indicates the descending sort order of fields.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.HeaderSortUpImage">
            <summary>
                <para>Gets the settings, which define the image that indicates the ascending sort order of fields.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxClasses.ImageProperties"/> object, which defines the image that indicates the ascending sort order of fields.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.HeaderStyle">
            <summary>
                <para>Provides style settings for field headers.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotHeaderStyle"/> object that contains style settings used to paint field headers.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.HeaderTemplate">
            <summary>
                <para>Gets or sets a template to display the content of field headers.

</para>
            </summary>
            <value>An object supporting the <b>System.Web.UI.ITemplate</b> interface that contains the custom content for field headers.

</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.HtmlCellPrepared">
            <summary>
                <para>Enables the settings of individual data cells to be changed.


</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.HtmlFieldValuePrepared">
            <summary>
                <para>Enables the settings of individual field value cells to be changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.Images">
            <summary>
                <para>Provides access to the settings that define images displayed within the pivot grid's elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImages"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ImagesEditors">
            <summary>
                <para>Provides access to the settings that define images displayed in the editors, used to edit filter values within the Prefilter Control (Filter Editor).

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxEditors.EditorImages"/> object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ImagesPrefilterControl">
            <summary>
                <para>Provides access to the settings that define images displayed within the Prefilter (Filter Editor).
</para>
            </summary>
            <value>The object that contains image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.IsDataBound">
            <summary>
                <para>Gets a value indicating whether the grid is data bound.
</para>
            </summary>
            <value><b>true</b> if the grid is data bound; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.IsDataSourceEmpty">
            <summary>
                <para>Gets a value that identifies whether no data source is specified for the pivot grid. For internal use only.
</para>
            </summary>
            <value><b>true</b> if no data source is specified for the pivot grid; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.IsFieldValueCollapsed(DevExpress.Web.ASPxPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Indicates whether the specified field value is collapsed.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents the row or column field.

            </param>
            <param name="cellIndex">
		A zero-based index of a cell in the Data Area that identifies the required field value. Indices are numbered starting from the left edge for column fields, and from the top edge for row fields. 

            </param>
            <returns><b>true</b> if the specified field value is collapsed; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.IsLoading">
            <summary>
                <para>Indicates that the ASPxPivotGrid is being loaded.
</para>
            </summary>
            <returns><b>true</b> if the ASPxPivotGrid is being loaded; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.IsObjectCollapsed(DevExpress.Web.ASPxPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Returns whether the specified column field value or row field value is collapsed.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that identifies a column field or row field.


            </param>
            <param name="lastLevelIndex">
		A zero-based index of a cell in the data area that identifies the required field value. Indexes are numbered starting from the left edge for column fields, and from the top edge for row fields.


            </param>
            <returns><b>true</b> if the specified column field value or row field value is collapsed; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.IsPrefilterPopupVisible">
            <summary>
                <para>Gets or sets whether the Filter Editor (Prefilter) is visible.
</para>
            </summary>
            <value><b>true</b> to show the Prefilter; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.JSProperties">
            <summary>
                <para>Enables you to supply any server data that can then be parsed on the client. 
</para>
            </summary>
            <value>The collection of property names and their values.
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.LayoutChanged">
            <summary>
                <para>Updates a pivot grid.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.LayoutUpgrade">
            <summary>
                <para>Occurs when a layout is restored from a data store, and its version is different than that of the control's current layout version.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.LoadCollapsedStateFromStream(System.IO.Stream)">
            <summary>
                <para>Restores the collapsed state of field values from the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant from which the collapsed state of field values is read. If <b>null</b> (<b>Nothing</b> in Visual Basic), an exception is raised.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.LoadCollapsedStateFromString(System.String)">
            <summary>
                <para>Restores the collapsed state of field values from the specified string.
</para>
            </summary>
            <param name="collapsedState">
		A <see cref="T:System.String"/> from which the collapsed state of field values is read.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.LoadLayout(System.String)">
            <summary>
                <para>Restores the ASPxPivotGrid's layout from the specified string.
</para>
            </summary>
            <param name="layoutState">
		A <see cref="T:System.String"/> value that contains the pivot grid's layout data.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.LoadLayoutFromStream(System.IO.Stream)">
            <summary>
                <para>Restores the pivot grid layout from the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant from which pivot grid settings are read. If <b>null</b> (<b>Nothing</b> in Visual Basic), an exception is raised.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.LoadLayoutFromStream(System.IO.Stream,DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout)">
            <summary>
                <para>Restores the pivot grid layout from the specified stream, using the specified settings.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant which contains the layout settings. If <b>null</b> (<b>Nothing</b> in Visual Basic), an exception is raised.

            </param>
            <param name="optionsLayout">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout"/> object that specifies whose options should be restored.


            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.LoadLayoutFromString(System.String)">
            <summary>
                <para>Restores the pivot grid layout from the specified string.
</para>
            </summary>
            <param name="layoutState">
		A <see cref="T:System.String"/> from which pivot grid settings are read.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.LoadLayoutFromString(System.String,DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout)">
            <summary>
                <para>Restores the pivot grid layout from the specified string, using the specified settings.
</para>
            </summary>
            <param name="layoutState">
		A <see cref="T:System.String"/> from which pivot grid settings are read.

            </param>
            <param name="optionsLayout">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout"/> object that specifies which options should be restored.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.LockDataRefreshOnCustomCallback">
            <summary>
                <para>Gets or sets whether refreshing pivot grid data is locked while handling the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCallback"/> event.
</para>
            </summary>
            <value><b>true</b> to prevent pivot grid data from being refreshed while handling the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCallback"/> event; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.MenuItemStyle">
            <summary>
                <para>Provides style settings for menu items.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxMenu.MenuItemStyle"/> object that contains style settings used to paint menu items.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.MenuStyle">
            <summary>
                <para>Provides style settings used to paint the client regions of submenus.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxMenu.MenuStyle"/> object that contains style settings used to paint the client regions of submenus.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OLAPConnectionString">
            <summary>
                <para>Specifies a connection string to a cube in an MS Analysis Services database.
</para>
            </summary>
            <value>A connection string.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OLAPDataProvider">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OLAPException">
            <summary>
                <para>Raised when a query processing error occurs on a bound OLAP server, or when the connection to this server is lost.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OLAPQueryTimeout">
            <summary>
                <para>Occurs when the query time-out has expired.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OptionsBehavior">
            <summary>
                <para>Provides access to pivot grid behavior options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsBehavior"/> object that contains behavior options for ASPxPivotGrid.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OptionsChartDataSource">
            <summary>
                <para>Provides access to the chart options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource"/> object that contains chart options.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OptionsCustomization">
            <summary>
                <para>Provides access to the pivot grid's customization options.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsCustomization"/> object, which contains customization options for an ASPxPivotGrid control.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OptionsData">
            <summary>
                <para>Provides access to the pivot grid's data specific options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsData"/> object that contains the pivot grid's data specific options.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OptionsDataField">
            <summary>
                <para>Provides access to options, which control the presentation of data fields in the ASPxPivotGrid.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsDataField"/> object that provides options, which control the data fields presentation in the ASPxPivotGrid.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OptionsFilter">
            <summary>
                <para>Provides access to options that specify the appearance and behavior of filter windows.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsFilter"/> object that specifies the appearance and behavior of filter windows.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OptionsLayout">
            <summary>
                <para>Provides access to options that control how the pivot grid layout is stored to/restored from a storage (a stream or string). 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout"/> object that provides options for controlling how the layout is stored and restored.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OptionsLoadingPanel">
            <summary>
                <para>Provides access to the loading panel's settings.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLoadingPanel"/> object that contains the loading panel's settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OptionsOLAP">
            <summary>
                <para>Provides access to the pivot grid's OLAP mode specific options. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsOLAP"/> object which contains the pivot grid's OLAP mode specific options. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OptionsPager">
            <summary>
                <para>Provides access to the pivot grid's pager options
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsPager"/> object containing the pager settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OptionsView">
            <summary>
                <para>Provides access to the ASPxPivotGrid control's view options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView"/> object that provides view options for ASPxPivotGrid controls.

</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.PageIndexChanged">
            <summary>
                <para>Fires after the selected page has been changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.PopupMenuCreated">
            <summary>
                <para>Enables you to create custom menu items.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.Prefilter">
            <summary>
                <para>Gets the <b>Prefilter</b>'s settings.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.Data.WebPrefilter"/> object that contains the <b>Prefilter</b>'s settings.
</value>


        </member>
        <member name="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.PrefilterCriteriaChanged">
            <summary>
                <para>Occurs when the Prefilter's criteria are changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.PreRendered">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ReloadData">
            <summary>
                <para>Reloads the ASPxPivotGrid's data.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.RetrieveFields">
            <summary>
                <para>Creates <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> objects for all the fields in the bound data source. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.RetrieveFields(DevExpress.XtraPivotGrid.PivotArea,System.Boolean)">
            <summary>
                <para>Creates <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> objects for all the fields in the bound data source, and moves the fields to the specified area, making them visible or hidden.
</para>
            </summary>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value that specifies the area to which the created fields are moved.

            </param>
            <param name="visible">
		<b>true</b> if the created fields are made visible; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.RowCount">
            <summary>
                <para>Gets the total number of rows displayed within the ASPxPivotGrid.
</para>
            </summary>
            <value>An interger value that specifies the total number of rows.
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.SaveCollapsedStateToStream(System.IO.Stream)">
            <summary>
                <para>Saves the collapsed state of field values to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant to which the collapsed state of field values is saved.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.SaveCollapsedStateToString">
            <summary>
                <para>Saves the collapsed state of field values to the specified string.
</para>
            </summary>
            <returns>A string to which the collapsed state of field values is saved.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.SaveLayout">
            <summary>
                <para>Returns a string that contains the ASPxPivotGrid's layout.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> value that contains the ASPxPivotGrid's layout data.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.SaveLayoutToStream(System.IO.Stream)">
            <summary>
                <para>Saves the pivot grid layout to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant to which the pivot grid layout is written.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.SaveLayoutToStream(System.IO.Stream,DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout)">
            <summary>
                <para>Saves the pivot grid layout to the specified stream, using the specified settings.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant to which the pivot grid layout is written.

            </param>
            <param name="optionsLayout">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout"/> object that specifies which options should be saved.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.SaveLayoutToString">
            <summary>
                <para>Saves the pivot grid layout to the specified string.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> that specifies the string to which the pivot grid layout is written.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.SaveLayoutToString(DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout)">
            <summary>
                <para>Saves the pivot grid layout to the specified string, using the specified settings.
</para>
            </summary>
            <param name="optionsLayout">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout"/> object that specifies which options should be saved.

            </param>
            <returns>A <see cref="T:System.String"/> that specifies the string to which the pivot grid layout has been written.
</returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ScriptHelper">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.Styles">
            <summary>
                <para>Provides access to the style settings that control the appearance of the pivot grid's elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridStyles"/> object that provides style settings for the grid's elements.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.StylesEditors">
            <summary>
                <para>Provides access to style settings that paint editors, used to edit filter values within the Filter Editor (Prefilter).

</para>
            </summary>
            <value>An <see cref="T:DevExpress.Web.ASPxEditors.EditorStyles"/> object that contains style settings used to paint editors.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.StylesFilterControl">
            <summary>
                <para>Provides access to the style settings used to paint the Filter Editor (Prefilter).
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxEditors.FilterControlStyles"/> object that contains style settings used to paint the Filter Editor.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.StylesPager">
            <summary>
                <para>Provides access to the style settings that control the appearance of the pager displayed within the pivot grid.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridPagerStyles"/> object that provides style settings used to paint a pager.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.StylesPrint">
            <summary>
                <para>Gets the style settings defining the pivot grid's export appearance.
</para>
            </summary>
            <value>A <b>WebPrintAppearance</b> object that contains export style settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.SummaryText">
            <summary>
                <para>Gets or sets a value that describes the ASPxPivotGrid's contents.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that describes the ASPxPivotGrid's contents.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ToolTip">
            <summary>
                <para>This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the tooltip.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.TotalCellStyle">
            <summary>
                <para>Provides style settings for Total Cells.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCellStyle"/> object that contains style settings used to paint Total cells.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.VisibleRowsOnPage">
            <summary>
                <para>Gets the number of rows displayed on the current page.

</para>
            </summary>
            <value>An integer value that specifies the number of rows displayed on the current page.

</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.XtraFindFieldsItem(DevExpress.Utils.Serializing.XtraItemEventArgs)">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <param name="e">
		 

            </param>
            <returns> 
</returns>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridCallbackStateEventHandler">

            <summary>
                <para>A method that will handle events related to saving/loading the pivot grid's callback state.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCallbackStateEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotGridCallbackStateEventArgs)">
            <summary>
                <para>A method that will handle events related to storing and loading the pivot grid's callback state.
</para>
            </summary>
            <param name="sender">
		A <see cref="T:System.Object"/> representing the event source. Identifies the <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid"/> control that raised the event. 

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridCallbackStateEventArgs"/> object that contains event data. 

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridCallbackStateEventArgs">

            <summary>
                <para>Provides data for the events related to saving/loading the pivot grid's callback state.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCallbackStateEventArgs.#ctor(System.String)">
            <summary>
                <para>Initializes a new PivotGridCallbackStateEventArgs object with the specified setting.
</para>
            </summary>
            <param name="callbackState">
		A string value specifying the callback state containing 'property name' / 'value' pairs.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCallbackStateEventArgs.CallbackState">
            <summary>
                <para>Gets or sets the ASPxPivotGrid's callback state.
</para>
            </summary>
            <value>A string value specifying the pivot grid's callback state.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCallbackStateEventArgs.Handled">
            <summary>
                <para>Gets or sets whether a callback state saving/loading operation is handled manually, so no default processing is required.
</para>
            </summary>
            <value><b>true</b> if no default processing is required; otherwise <b>false</b>. 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotHtmlFieldValuePreparedEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.HtmlFieldValuePrepared"/> event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotHtmlFieldValuePreparedEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotHtmlFieldValuePreparedEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.HtmlFieldValuePrepared"/> event.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotHtmlFieldValuePreparedEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotHtmlFieldValuePreparedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.HtmlFieldValuePrepared"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotHtmlFieldValuePreparedEventArgs.#ctor(DevExpress.Web.ASPxPivotGrid.HtmlControls.PivotGridHtmlFieldValueCellBase)">
            <summary>
                <para>Initializes a new instance of the PivotHtmlFieldValuePreparedEventArgs class with the specified setting.
</para>
            </summary>
            <param name="cell">
		A <b>PivotGridHtmlFieldValueCellBase</b> object representing the related cell.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotHtmlFieldValuePreparedEventArgs.Cell">
            <summary>
                <para>Gets the processed data cell. 
</para>
            </summary>
            <value>A <see cref="T:System.Web.UI.WebControls.TableCell"/> object that represents the processed data cell. 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotHtmlCellPreparedEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.HtmlCellPrepared"/> event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotHtmlCellPreparedEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotHtmlCellPreparedEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.HtmlCellPrepared"/> event.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotHtmlCellPreparedEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotHtmlCellPreparedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.HtmlCellPrepared"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotHtmlCellPreparedEventArgs.#ctor(DevExpress.Web.ASPxPivotGrid.HtmlControls.PivotGridHtmlDataCell)">
            <summary>
                <para>Initializes a new instance of the PivotHtmlCellPreparedEventArgs class with the specified setting.
</para>
            </summary>
            <param name="cell">
		A <b>PivotGridHtmlDataCell</b> object representing the related cell.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotHtmlCellPreparedEventArgs.Cell">
            <summary>
                <para>Gets the processed data cell. 
</para>
            </summary>
            <value>A <see cref="T:System.Web.UI.WebControls.TableCell"/> object that represents the processed data cell. 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCustomChartDataSourceDataEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomChartDataSourceData"/> event. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomChartDataSourceDataEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotCustomChartDataSourceDataEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomChartDataSourceData"/> event. 
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the pivot grid which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCustomChartDataSourceDataEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCustomChartDataSourceDataEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomChartDataSourceData"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomChartDataSourceDataEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotChartItemType,DevExpress.XtraPivotGrid.PivotChartItemDataMember,DevExpress.XtraPivotGrid.Data.PivotFieldValueItem,DevExpress.XtraPivotGrid.Data.PivotGridCellItem,System.Object)">
            <summary>
                <para>Initializes a new instance of the PivotCustomChartDataSourceDataEventArgs class with the specified settings.
</para>
            </summary>
            <param name="itemType">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotChartItemType"/> enumeration value specifying the type of a PivotGrid control's item to be represented in a ChartControl. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotCustomChartDataSourceDataEventArgs.ItemType"/> property. 

            </param>
            <param name="itemDataMember">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotChartItemDataMember"/> enumeration value specifying the type of a chart data member that will represent the current pivot grid item. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotCustomChartDataSourceDataEventArgs.ItemDataMember"/> property. 

            </param>
            <param name="fieldValueItem">
		A <b>DevExpress.XtraPivotGrid.Data.PivotFieldValueItem</b> object.

            </param>
            <param name="cellItem">
		A <b>DevExpress.XtraPivotGrid.Data.PivotGridCellItem</b> object. 

            </param>
            <param name="value">
		A <see cref="T:System.Object"/> specifying the value to be displayed in a chart. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotCustomChartDataSourceDataEventArgs.Value"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomChartDataSourceDataEventArgs.CellInfo">
            <summary>
                <para>Gets an object which contains information about a ASPxPivotGrid control's cell, whose value should be displayed in a WebChartControl.
</para>
            </summary>
            <value> A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCellValueEventArgs"/> object.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomChartDataSourceDataEventArgs.FieldValueInfo">
            <summary>
                <para>Gets an object which contains information about a field value to be displayed in a WebChartControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldValueEventArgs"/> object.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomChartDataSourceDataEventArgs.ItemDataMember">
            <summary>
                <para>Gets the type of a chart data member that will represent the current pivot grid item.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotChartItemDataMember"/> enumeration member that specifies the type of a chart data member that will represent the current pivot grid item.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomChartDataSourceDataEventArgs.ItemType">
            <summary>
                <para>Gets a value representing the type of an ASPxPivotGrid control's item to be represented in a WebChartControl.
</para>
            </summary>
            <value> A <see cref="T:DevExpress.XtraPivotGrid.PivotChartItemType"/> enumeration value.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomChartDataSourceDataEventArgs.Value">
            <summary>
                <para>Gets or sets a value to be displayed in a WebChartControl.
</para>
            </summary>
            <value> A <see cref="T:System.Object"/> class descendant representing the value to be displayed.

</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource">

            <summary>
                <para>Contains chart options.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource.#ctor(DevExpress.Web.ASPxPivotGrid.Data.PivotGridWebData)">
            <summary>
                <para>Initializes a new instance of the PivotGridWebOptionsChartDataSource class.
</para>
            </summary>
            <param name="data">
		A <b>DevExpress.Web.ASPxPivotGrid.Data.PivotGridWebData</b> object.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource.ChartDataVertical">
            <summary>
                <para>Gets or sets how the <b>ASPxPivotGrid</b>'s data is interpreted in a chart control.
</para>
            </summary>
            <value><b>true</b> to chart data vertically; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource.CurrentPageOnly">
            <summary>
                <para>Gets or sets whether data from the current page only is passed to the chart control.
</para>
            </summary>
            <value><b>true</b> if data from the current page only is passed to the chart control; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource.ExportCellValuesAsType">
            <summary>
                <para>Gets or sets the type to which the cell values are converted before they are exported to the chart control.
</para>
            </summary>
            <value>A <see cref="T:System.Type"/> object representing the type to which the cell values are converted.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource.ExportColumnFieldValuesAsType">
            <summary>
                <para>Gets or sets the type to which the column field values are converted before they are exported to the chart control.
</para>
            </summary>
            <value>A <see cref="T:System.Type"/> object representing the type to which the column field values are converted.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource.ExportFieldValueMode">
            <summary>
                <para>Gets or sets how field values are exported to the chart control.
</para>
            </summary>
            <value>One of the <see cref="T:DevExpress.XtraPivotGrid.PivotChartExportFieldValueMode"/> enumeration members that specifies how field values are exported to the chart control.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource.ExportRowFieldValuesAsType">
            <summary>
                <para>Gets or sets the type to which the row field values are converted before they are exported to the chart control.
</para>
            </summary>
            <value>A <see cref="T:System.Type"/> object, representing the type to which the row field values are converted.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource.ShowAllPages">
            <summary>
                <para>Gets or sets whether data from all pages is passed to the chart control.
</para>
            </summary>
            <value><b>true</b> if data from all pages is passed to the chart control; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource.ShowColumnCustomTotals">
            <summary>
                <para>Gets or sets whether column custom totals are displayed in a chart control.
</para>
            </summary>
            <value><b>true</b> to chart column custom totals; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource.ShowColumnGrandTotals">
            <summary>
                <para>Gets or sets whether column grand totals are displayed in a chart control.
</para>
            </summary>
            <value><b>true</b> to chart column grand totals; otheriwse, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource.ShowColumnTotals">
            <summary>
                <para>Gets or sets whether column totals are displayed in a chart control.
</para>
            </summary>
            <value><b>true</b> to chart column totals; otheriwse, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource.ShowRowCustomTotals">
            <summary>
                <para>Gets or sets whether row custom totals are displayed in a chart control.
</para>
            </summary>
            <value><b>true</b> to chart row custom totals; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource.ShowRowGrandTotals">
            <summary>
                <para>Gets or sets whether row grand totals are displayed in a chart control.
</para>
            </summary>
            <value><b>true</b> to chart row grand totals; otheriwse, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsChartDataSource.ShowRowTotals">
            <summary>
                <para>Gets or sets whether row totals are displayed in a chart control.
</para>
            </summary>
            <value><b>true</b> to chart row totals; otheriwse, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.HtmlControls.PivotGridDataAreaPopup">

            <summary>
                <para>Represents a popup panel that contains hidden field headers when the ASPxPivotGrid's display mode is <see cref="F:DevExpress.Web.ASPxPivotGrid.PivotDataHeadersDisplayMode.Popup"/>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.HtmlControls.PivotGridDataAreaPopup.#ctor(DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid)">
            <summary>
                <para>Initializes a new instance of the PivotGridDataAreaPopup class with the specified owner.
</para>
            </summary>
            <param name="pivotGrid">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid"/> object representing the owner of the created object.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotDataAreaPopupCreatedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.DataAreaPopupCreated"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotDataAreaPopupCreatedEventArgs.#ctor(DevExpress.Web.ASPxPivotGrid.HtmlControls.PivotGridDataAreaPopup)">
            <summary>
                <para>Initializes a new instance of the PivotDataAreaPopupCreatedEventArgs class with the specified setting.
</para>
            </summary>
            <param name="popup">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.HtmlControls.PivotGridDataAreaPopup"/> object representing the popup panel containing hidden field headers.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotDataAreaPopupCreatedEventArgs.Popup">
            <summary>
                <para>Gets the popup panel displaying hidden field headers.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.HtmlControls.PivotGridDataAreaPopup"/> object representing the popup panel containing hidden field headers.

</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCellValueEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCellValue"/> event. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCellValueEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridCellItem)">
            <summary>
                <para>Initializes a new instance of the PivotCellValueEventArgs class with the specified setting.
</para>
            </summary>
            <param name="cellItem">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridCellItem"/> object which provides information on the processed cell.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCellValueEventArgs.Value">
            <summary>
                <para>Gets or sets the processed cell's value. 
</para>
            </summary>
            <value>An object which represents the processed cell's value. 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCollapsedStateStoreMode">

            <summary>
                <para>Contains values that specify how the grid stores information about the collapsed rows/columns.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotCollapsedStateStoreMode.Indexes">
            <summary>
                <para>Collapsed rows/columns are identified by storing their indexes.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotCollapsedStateStoreMode.Values">
            <summary>
                <para>Collapsed rows/columns are identified by storing their corresponding field values.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldFilterChangingEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldFilterChanging"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldFilterChangingEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotFieldFilterChangingEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldFilterChanging"/> event.

</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldFilterChangingEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldFilterChangingEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldFilterChanging"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldFilterChangingEventArgs.#ctor(DevExpress.Web.ASPxPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotFilterType,System.Boolean,System.Collections.Generic.IList`1)">
            <summary>
                <para>Initializes a new instance of the PivotFieldFilterChangingEventArgs class.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents the field for which the event has been raised. 

            </param>
            <param name="filterType">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotFilterType"/> enumeration member that specifies the current filter type. 

            </param>
            <param name="showBlanks">
		<b>true</b> if records that contain NULL values in the current field are processed by the control; <b>false</b> if these records are ignored.

            </param>
            <param name="values">
		A collection of filter values.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldFilterChangingEventArgs.Cancel">
            <summary>
                <para>Gets or sets whether to cancel changing the filter condition.
</para>
            </summary>
            <value><b>true</b> to cancel changing the filter condition; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldFilterChangingEventArgs.FilterType">
            <summary>
                <para>Gets the current filter type.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotFilterType"/> enumeration member that specifies the filter type currently set for the field filter.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldFilterChangingEventArgs.Values">
            <summary>
                <para>Gets the collection of filter values about to be assigned to the filter.

</para>
            </summary>
            <value>A list of objects representing field filter values.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.Data.WebPrefilter">

            <summary>
                <para>Represents an ASPxPivotGrid's <b>Prefilter</b>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.Data.WebPrefilter.#ctor(DevExpress.XtraPivotGrid.IPrefilterOwnerBase)">
            <summary>
                <para>Initializes a new instance of the WebPrefilter class.
</para>
            </summary>
            <param name="owner">
		An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IPrefilterOwnerBase"/> interface.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.Data.WebPrefilter.Criteria">
            <summary>
                <para>Gets or sets the <b>Prefilter</b>'s expression.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant that represents the <b>Prefilter</b>'s expression.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.Data.WebPrefilter.CriteriaString">
            <summary>
                <para>Gets or sets the <b>Prefilter</b>'s filter string.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the <b>Prefilter</b>'s filter string.
</value>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.Data.WebPrefilter.Reset">
            <summary>
                <para>Clears the filter expression applied using the Prefilter.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.Data.WebPrefilter.ShouldSerialize">
            <summary>
                <para>Tests whether the WebPrefilter object should be persisted.
</para>
            </summary>
            <returns><b>true</b> if the object should be persisted; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotOlapExceptionEventHandler">

            <summary>
                <para>References a method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OLAPException"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotOlapExceptionEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotOlapExceptionEventArgs)">
            <summary>
                <para>References a method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OLAPException"/> event.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotOlapExceptionEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotOlapExceptionEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.OLAPException"/> event.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotOlapExceptionEventArgs.Exception">
            <summary>
                <para>Gets an exception that has been thrown.
</para>
            </summary>
            <value>A <see cref="T:System.Exception"/> that has been thrown.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotOlapExceptionEventArgs.Handled">
            <summary>
                <para>Gets or sets whether the default exception handling actions should be performed.
</para>
            </summary>
            <value><b>true</b> to perform the default exception handling actions; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGroupEventHandler">

            <summary>
                <para>References a method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GroupFilterChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGroupEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotGroupEventArgs)">
            <summary>
                <para>References a method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GroupFilterChanged"/> event.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGroupEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGroupEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.GroupFilterChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGroupEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotGridGroup)">
            <summary>
                <para>Initializes a new instance of the PivotGroupEventArgs class.
</para>
            </summary>
            <param name="group">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridGroup"/> object that specifies the group for which the event has been raised.  This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotGroupEventArgs.Group"/> property.


            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGroupEventArgs.Group">
            <summary>
                <para>Gets the group for which the event has been raised.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridGroup"/> object that specifies the group for which the event has been raised.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridResetOptions">

            <summary>
                <para>Lists values that specify which options are reset before loading the layout. 
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridResetOptions.All">
            <summary>
                <para>All options are reset.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridResetOptions.None">
            <summary>
                <para>No options reset.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridResetOptions.OptionsBehavior">
            <summary>
                <para>Behavior options are reset.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridResetOptions.OptionsChartDataSource">
            <summary>
                <para>Options related to the Chart control integration are reset.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridResetOptions.OptionsCustomization">
            <summary>
                <para>Customization options are reset.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridResetOptions.OptionsData">
            <summary>
                <para>Data options are reset.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridResetOptions.OptionsDataField">
            <summary>
                <para>Options related to the presentation of data fields are reset.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridResetOptions.OptionsFilterPopup">
            <summary>
                <para>Filter drop-down options are reset.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridResetOptions.OptionsLoadingPanel">
            <summary>
                <para>Loading Panel options are reset. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridResetOptions.OptionsOLAP">
            <summary>
                <para>OLAP options are reset.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridResetOptions.OptionsPager">
            <summary>
                <para>Pager options are reset.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldPropertyChangedEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldPropertyChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldPropertyChangedEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotFieldPropertyChangedEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldPropertyChanged"/> event.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotFieldPropertyChangedEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomCallbackEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCallback"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCustomCallbackEventArgs.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomCallbackEventArgs class.
</para>
            </summary>
            <param name="parameters">
		A string value that contains specific information collected on the client side and passed to the event for further server-side processing. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotGridCustomCallbackEventArgs.Parameters"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridCustomCallbackEventArgs.Parameters">
            <summary>
                <para>Gets a string that contains specific information (if any) passed from the client-side.
</para>
            </summary>
            <value>A string value that contains specific information collected on the client side and passed to the event for further server-side processing.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCustomCallbackEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCallback"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomCallbackEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotGridCustomCallbackEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCallback"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomCallbackEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotCustomizationControl">

            <summary>
                <para>An <b>ASPxPivotCustomizationControl</b>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotCustomizationControl.#ctor">
            <summary>
                <para>Initializes a new instance of the ASPxPivotCustomizationControl class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotCustomizationControl.AllowedLayouts">
            <summary>
                <para>Gets or sets which layouts can be applied to the ASPxPivotCustomizationControl.
</para>
            </summary>
            <value>One or several <see cref="T:DevExpress.XtraPivotGrid.Customization.CustomizationFormAllowedLayouts"/> enumeration members that specify which layouts can be applied to the ASPxPivotCustomizationControl.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotCustomizationControl.AllowFilter">
            <summary>
                <para>Gets or sets whether filtering can be applied to fields via the ASPxPivotCustomizationControl. 

</para>
            </summary>
            <value><b>true</b> if filtering can be applied to fields via the ASPxPivotCustomizationControl; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotCustomizationControl.AllowSort">
            <summary>
                <para>Gets or sets whether the sort order of field values can be toggled via the ASPxPivotCustomizationControl.

</para>
            </summary>
            <value><b>true</b> if the sort order of field values can be toggled via the ASPxPivotCustomizationControl; otherwise, <b>false</b>. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotCustomizationControl.ASPxPivotGridID">
            <summary>
                <para>Gets or sets the ID of the owner ASPxPivotGrid control.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> that represents the ID of the owner ASPxPivotGrid control.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotCustomizationControl.ClientInstanceName">
            <summary>
                <para>Gets or sets the ASPxPivotCustomizationControl's client programmatic identifier. 

</para>
            </summary>
            <value>A <see cref="T:System.String"/> that specifies the ASPxPivotCustomizationControl's client identifier. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotCustomizationControl.DeferredUpdates">
            <summary>
                <para>Gets or sets whether dragging fields within the ASPxPivotCustomizationControl immediately updates the layout of fields in the PivotGrid control. 

</para>
            </summary>
            <value><b>true</b> if the layout of fields in the PivotGrid control must not be immediately updated; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotCustomizationControl.Images">
            <summary>
                <para>Gets the settings that define images displayed in the ASPxPivotCustomizationControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCustomizationFormImages"/> object containing the settings that define images displayed in the ASPxPivotCustomizationControl.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotCustomizationControl.Layout">
            <summary>
                <para>Gets or sets the ASPxPivotCustomizationControl's layout.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.Customization.CustomizationFormLayout"/> enumeration member that specifies how fields are arranged within the ASPxPivotCustomizationControl.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.FieldValueCell">

            <summary>
                <para>Represents a field value cell.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.FieldValueCell.#ctor(DevExpress.XtraPivotGrid.Data.PivotFieldValueItem)">
            <summary>
                <para>Initializes a new instance of the FieldValueCell class.
</para>
            </summary>
            <param name="item">
		A <b>DevExpress.XtraPivotGrid.Data.PivotFieldValueItem</b> object.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.FieldValueCell.DataField">
            <summary>
                <para>Gets the data field which identifies the cell. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents the data field which identifies the cell. 

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.FieldValueCell.Field">
            <summary>
                <para>Gets the field whose value the cell represents.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents the field whose value the cell represents.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.FieldValueCell.Parent">
            <summary>
                <para>Gets the parent of the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.FieldValueCell"/> object that represents the parent cell.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotFieldPropertyChangedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldPropertyChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotFieldPropertyChangedEventArgs.#ctor(DevExpress.Web.ASPxPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotFieldPropertyName)">
            <summary>
                <para>Initializes a new instance of the PivotFieldPropertyChangedEventArgs class.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents the field whose property has been changed. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotFieldEventArgs.Field"/> property.

            </param>
            <param name="propertyName">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldPropertyName"/> enumeration member that specifies which property has been changed. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotFieldPropertyChangedEventArgs.PropertyName"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotFieldPropertyChangedEventArgs.PropertyName">
            <summary>
                <para>Gets which property has been changed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldPropertyName"/> enumeration member that identifies the property that has been changed.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsFilter">

            <summary>
                <para>Contains options that affect the appearance and behavior of filter windows. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsFilter.#ctor(DevExpress.XtraPivotGrid.PivotOptionsFilterEventHandler,DevExpress.WebUtils.IViewBagOwner,System.String)">
            <summary>
                <para>Initializes a new instance of the PivotGridWebOptionsFilter class. 
</para>
            </summary>
            <param name="optionsChanged">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotOptionsFilterEventHandler"/> delegate that will receive change notifications.

            </param>
            <param name="viewBagOwner">
		An object that implements the IViewBagOwner interface.

            </param>
            <param name="projectPath">
		A string value. 

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsFilter.GroupFilterMode">
            <summary>
                <para>Gets or sets the filtering mode used for field groups.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGroupFilterMode"/> enumeration member that specifies the filtering mode used for field groups. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsFilter.ShowHiddenItems">
            <summary>
                <para>Gets or sets whether to display hidden filter items as disabled.
</para>
            </summary>
            <value><b>true</b> to display hidden filter items as disabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsFilter.ShowOnlyAvailableItems">
            <summary>
                <para>Gets or sets whether filter items that cannot be displayed because of filtering applied to other fields should be hidden.
</para>
            </summary>
            <value><b>true</b> to hide filter items that cannot be displayed because of filtering applied to other fields; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout">

            <summary>
                <para>Contains options that specify how a control layout is stored to, and restored from a storage (a stream or string).
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotGridWebOptionsLayout class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies settings from the options object passed as the parameter. 
</para>
            </summary>
            <param name="options">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout.DefaultLayout">
            <summary>
                <para>Returns a PivotGridWebOptionsLayout object whose settings indicate that fields should be overridden when saving or restoring the control layout.
</para>
            </summary>
            <value>A PivotGridWebOptionsLayout object whose settings indicate that fields should be overridden when saving or restoring the control layout.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout.FullLayout">
            <summary>
                <para>Returns a PivotGridWebOptionsLayout object whose settings indicate that the full layout of the control should be stored to, and restored from a storage (a stream or a string). 
</para>
            </summary>
            <value>A PivotGridWebOptionsLayout object whose settings indicate that the full layout of the control should be stored to/restored from a storage.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout.ResetOptions">
            <summary>
                <para>Gets or sets which options are reset before loading the layout.
</para>
            </summary>
            <value>A combination of <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridResetOptions"/> flags, specifying which options are reset before loading the layout. 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout.StoreClientSideEvents">
            <summary>
                <para>Gets or sets whether the control client-side event handlers are stored when the layout is saved to a storage, and restored when the layout is restored from a storage.
</para>
            </summary>
            <value><b>true</b> if the control client-side event handlers are included in the layout when it is saved to a storage, and these settings are restored when the layout is restored from a storage; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLayout.StoreLayoutOptions">
            <summary>
                <para>Not used.
</para>
            </summary>
            <value>Always <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsData">

            <summary>
                <para>Provides data specific options for <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid"/>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsData.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData,System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the PivotGridWebOptionsData class.
</para>
            </summary>
            <param name="data">
		 A PivotGridData object that contains information required to initialize the created PivotGridWebOptionsData object.

            </param>
            <param name="optionsChanged">
		A method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridOptionsBase.OptionsChanged"/> event.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsData.LockDataRefreshOnCustomCallback">
            <summary>
                <para>Gets or sets whether refreshing pivot grid data is locked while handling the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCallback"/> event.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> enumeration member that specifies whether to prevent pivot grid data from being refreshed while handling the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCallback"/> event.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotAreaChangingEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldAreaChanging"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotAreaChangingEventArgs.#ctor(DevExpress.Web.ASPxPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotArea,System.Int32)">
            <summary>
                <para>Initializes a new instance of the PivotAreaChangingEventArgs class.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object which represents the field whose location is being changed. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotFieldEventArgs.Field"/> property.

            </param>
            <param name="newArea">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value which indicates the current position of the field being dragged. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotAreaChangingEventArgs.NewArea"/> property.

            </param>
            <param name="newAreaIndex">
		A zero-based integer which specifies the field's index amongst the other fields displayed within the same area. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotAreaChangingEventArgs.NewAreaIndex"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotAreaChangingEventArgs.Allow">
            <summary>
                <para>Gets or sets whether the dragged field header can be dropped on the area it's currently located over.
</para>
            </summary>
            <value><b>true</b> to allow the operation; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotAreaChangingEventArgs.NewArea">
            <summary>
                <para>Gets the current position of the field being dragged. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value which specifies the current position of the field being dragged.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotAreaChangingEventArgs.NewAreaIndex">
            <summary>
                <para>Gets the index of the field which is being dragged for the area it's currently located over among the other fields displayed within the area.

</para>
            </summary>
            <value>A zero-based integer which specifies the index of the dragged field among the other fields displayed within the area over which it's currently located.

</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsBehavior">

            <summary>
                <para>Provides behavior options for ASPxPivotGrid.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsBehavior.#ctor(System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the PivotGridWebOptionsBehavior class with the specified change notifications delegate.
</para>
            </summary>
            <param name="optionsChanged">
		A <see cref="T:System.EventHandler"/> delegate that will receive change notifications.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsBehavior.CopyToClipboardWithFieldValues">
            <summary>
                <para>This property is not in effect for this class. It is overridden only for the purpose of preventing it from appearing in Microsoft Visual Studio designer tools. 


</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsBehavior.LoadingPanelDelay">
            <summary>
                <para>This property is not in effect for this class. It is overridden only for the purpose of preventing it from appearing in Microsoft Visual Studio designer tools. 


</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsBehavior.UseAsyncMode">
            <summary>
                <para>This property is not in effect for this class. It is overridden only for the purpose of preventing it from appearing in Microsoft Visual Studio designer tools. 


</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridPopupMenuType">

            <summary>
                <para>Lists values that identify a popup menu.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridPopupMenuType.FieldListMenu">
            <summary>
                <para>Corresponds to the <i>Customization Form Layout</i> popup menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridPopupMenuType.FieldValueMenu">
            <summary>
                <para>Corresponds to the Field Value popup menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotGridPopupMenuType.HeaderMenu">
            <summary>
                <para>Corresponds to the Field Header popup menu.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotPopupMenuCreatedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.PopupMenuCreated"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotPopupMenuCreatedEventArgs.#ctor(DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu)">
            <summary>
                <para>Initializes a new instance of the PivotPopupMenuCreatedEventArgs class within the specified setting.
</para>
            </summary>
            <param name="menu">
		An <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu"/> object identifying the created context menu.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotPopupMenuCreatedEventArgs.Menu">
            <summary>
                <para>Gets the context menu.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu"/> object that represents the context menu.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotPopupMenuCreatedEventArgs.MenuType">
            <summary>
                <para>Gets the context menu's type.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridPopupMenuType"/> enumeration value that identifies the context menu.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPager">

            <summary>
                <para>This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPager.#ctor(DevExpress.Web.ASPxPivotGrid.HtmlControls.PivotGridHtmlTable,DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid)">
            <summary>
                <para>This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <param name="htmlTable">
		 

            </param>
            <param name="pivotGrid">
		 

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPager.ItemCount">
            <summary>
                <para>This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPager.ItemsPerPage">
            <summary>
                <para>This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPager.PageCount">
            <summary>
                <para>Gets the number of pages displayed within the ASPxPivotGrid.
</para>
            </summary>
            <value>An integer value that specifies the number of pages displayed within the ASPxPivotGrid.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPager.PageIndex">
            <summary>
                <para>This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotDataHeadersDisplayMode">

            <summary>
                <para>Lists values that specify how data headers are displayed within the Data Header Area.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotDataHeadersDisplayMode.Default">
            <summary>
                <para>Data headers are displayed within the Data Header Area.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.PivotDataHeadersDisplayMode.Popup">
            <summary>
                <para>The ASPxPivotGrid automatically hides data fields if there are three or more data fields. The minimum number of data fields, required to activate this feature, is specified by the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsView.DataHeadersPopupMinCount"/> property.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLoadingPanel">

            <summary>
                <para>Provides settings that affect the Loading Panel's appearance and functionality.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLoadingPanel.#ctor(DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid)">
            <summary>
                <para>Initializes a new instance of the PivotGridWebOptionsLoadingPanel class.
</para>
            </summary>
            <param name="owner">
		An <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid"/> object that owns the current object.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLoadingPanel.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies settings from the options object passed as the parameter. 
</para>
            </summary>
            <param name="source">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLoadingPanel.Enabled">
            <summary>
                <para>Gets or sets a value that specifies whether a specific loading panel is displayed during callback processing.
</para>
            </summary>
            <value><b>true</b>, if the loading panel should be displayed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLoadingPanel.Image">
            <summary>
                <para>Gets the settings of an image displayed within a Loading Panel.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Web.ASPxClasses.ImageProperties"/> object that contains image settings. 

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLoadingPanel.ImagePosition">
            <summary>
                <para>Gets or sets the position of the image displayed within a Loading Panel with respect to the panel's text content.
</para>
            </summary>
            <value>One of the <see cref="T:DevExpress.Web.ASPxClasses.ImagePosition"/> enumeration values.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLoadingPanel.Style">
            <summary>
                <para>Gets the style settings used to paint the Loading Panel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridLoadingPanelStyle"/> object that contains the style settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebOptionsLoadingPanel.Text">
            <summary>
                <para>Gets or sets the text displayed within a loading panel.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the panel's text.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebGroupCollection">

            <summary>
                <para>Represents the ASPxPivotGrid's group collection.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebGroupCollection.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData)">
            <summary>
                <para>Initializes a new instance of the PivotGridWebGroupCollection class.
</para>
            </summary>
            <param name="data">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridData"/> object.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebGroupCollection.Item(System.Int32)">
            <summary>
                <para>Provides indexed access to the groups in the collection. 

</para>
            </summary>
            <param name="index">
		An integer value specifying the zero-based index of the required group. If it's negative or exceeds the maximum available index, an exception is raised. 

            </param>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebGroup"/> object which represents the group in the collection.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebGroup">

            <summary>
                <para>Represents a group of fields.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebGroup.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotGridWebGroup class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebGroup.Fields">
            <summary>
                <para>Provides access to the group's field collection.
</para>
            </summary>
            <value>An object which implements the <see cref="T:System.Collections.IList"/> interface. 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu">

            <summary>
                <para>Represents a popup menu displayed within the ASPxPivotGrid.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.#ctor(DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid,DevExpress.Web.ASPxPivotGrid.PivotGridPopupMenuType)">
            <summary>
                <para>Initializes a new instance of the ASPxPivotGridPopupMenu class within the specified settings.
</para>
            </summary>
            <param name="pivotGrid">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid"/> object identifying the control to which the context belongs.

            </param>
            <param name="menuType">
		One of the <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridPopupMenuType"/> enumeration values.

            </param>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.ClearSortID">
            <summary>
                <para>Identifies the 'Clear Sorting' field header context menu item.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.CollapseAllID">
            <summary>
                <para>Identifies the 'Collapse All' field value context menu item.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.CollapseValueID">
            <summary>
                <para>Identifies the 'Collapse' field value context menu item.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.ExpandAllID">
            <summary>
                <para>Identifies the 'Expand All' field value context menu item.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.ExpandValueID">
            <summary>
                <para>Identifies the 'Expand' field value context menu item.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.HideFieldID">
            <summary>
                <para>Identifies the 'Hide' field header context menu item.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.HideFieldListID">
            <summary>
                <para>Identifies the 'Hide Field List' header area context menu item.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.MenuType">
            <summary>
                <para>Gets the context menu's type. 
</para>
            </summary>
            <value>One of the <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridPopupMenuType"/> enumeration values.
</value>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.RefreshID">
            <summary>
                <para>Identifies the 'Reload Data' header area context menu item.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.ShowFieldListID">
            <summary>
                <para>Identifies the 'Show Field List' header area context menu item.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.ShowPrefilterID">
            <summary>
                <para>Identifies the 'Show Prefilter' header area context menu item.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.SortAZID">
            <summary>
                <para>Identifies the 'Sort A-Z' field header context menu item.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.SortByID">
            <summary>
                <para>Identifies the field value context menu item used to apply Sorting by Summary.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.SortGroupID">
            <summary>
                <para>Identifies a context menu group that includes items used to sort field values in the OLAP mode.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.ASPxPivotGridPopupMenu.SortZAID">
            <summary>
                <para>Identifies the 'Sort Z-A' field header context menu item.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridWebFieldOptions">

            <summary>
                <para>Provides options for pivot grid fields.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridWebFieldOptions.#ctor(DevExpress.XtraPivotGrid.PivotOptionsChangedEventHandler,DevExpress.WebUtils.IViewBagOwner,System.String)">
            <summary>
                <para>Initializes a new instance of the PivotGridWebFieldOptions class with the specified settings.
</para>
            </summary>
            <param name="optionsChanged">
		A delegate that will receive change notifications.

            </param>
            <param name="viewBagOwner">
		An IViewBagOwner object that is used to initialize the created object.

            </param>
            <param name="objectPath">
		A string value that is used to initialize the created object.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebFieldOptions.AllowRunTimeSummaryChange">
            <summary>
                <para>This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotGridWebFieldOptions.ShowSummaryTypeName">
            <summary>
                <para>This member supports the .NET Framework infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.MenuItemEnum">

            <summary>
                <para>Lists values that identify menu items displayed within the <b>Field Value Popup Menu</b> and <b>Field Header Popup Menu</b>.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.MenuItemEnum.FieldValueExpand">
            <summary>
                <para>Corresponds to the menu item used to expand/collapse individual field values. This menu item is displayed within the Field Value Popup Menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.MenuItemEnum.FieldValueExpandAll">
            <summary>
                <para>Corresponds to the menu item used to expand/collapse all field values displayed within the row header area or column header area. This menu item is displayed within the Field Value Popup Menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.MenuItemEnum.FieldValueSortBySummaryFields">
            <summary>
                <para>Corresponds to the menu item used to sort row or column field values by column or row summary value, respectively. This menu item is displayed within the Field Value Popup Menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.MenuItemEnum.HeaderClearSorting">
            <summary>
                <para>Corresponds to the menu item used to clear sorting.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.MenuItemEnum.HeaderHide">
            <summary>
                <para>Corresponds to the menu item used to hide individual field headers. This menu item is displayed within the Field Header Popup Menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.MenuItemEnum.HeaderRefresh">
            <summary>
                <para><para>Corresponds to the menu item used to refresh the ASPxPivotGrid's data. This menu item is displayed within the Field Header Popup Menu.</para>

<para>
To refresh the control's data in code, use the <see cref="M:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.ReloadData"/> method.
</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.MenuItemEnum.HeaderShowList">
            <summary>
                <para>Corresponds to the menu item used to invoke the Customization Window that displays hidden fields. This menu item is displayed within the Field Header Popup Menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.MenuItemEnum.HeaderShowPrefilter">
            <summary>
                <para><para>Corresponds to the menu item used to invoke the Prefilter Editor used to build complex filter expressions. This menu item is displayed within the Field Header Popup Menu.</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.MenuItemEnum.HeaderSortAscending">
            <summary>
                <para>Corresponds to the menu item used to apply sorting in ascending order.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Web.ASPxPivotGrid.MenuItemEnum.HeaderSortDescending">
            <summary>
                <para>Corresponds to the menu item used to apply sorting in descending order.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridCustomGroupIntervalEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomGroupInterval"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridCustomGroupIntervalEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotCustomGroupIntervalEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomGroupInterval"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCustomGroupIntervalEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCustomGroupIntervalEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomGroupInterval"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomGroupIntervalEventArgs.#ctor(DevExpress.Web.ASPxPivotGrid.PivotGridField,System.Object)">
            <summary>
                <para>Initializes a new instance of the PivotCustomGroupIntervalEventArgs class.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object that represents the processed field. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotFieldEventArgs.Field"/> property.

            </param>
            <param name="value">
		An object that represents a group value. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotCustomGroupIntervalEventArgs.GroupValue"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomGroupIntervalEventArgs.GroupValue">
            <summary>
                <para>Gets or sets a group value.
</para>
            </summary>
            <value>An object that represents the group value.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomGroupIntervalEventArgs.Value">
            <summary>
                <para>Gets the processed field value.
</para>
            </summary>
            <value>An object that represents the processed field value.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotAddPopupMenuItemEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.AddPopupMenuItem"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotAddPopupMenuItemEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotAddPopupMenuItemEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.AddPopupMenuItem"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotAddPopupMenuItemEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotAddPopupMenuItemEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.AddPopupMenuItem"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotAddPopupMenuItemEventArgs.#ctor(DevExpress.Web.ASPxPivotGrid.MenuItemEnum)">
            <summary>
                <para>Initializes a new instance of the PivotAddPopupMenuItemEventArgs class.
</para>
            </summary>
            <param name="menuItem">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.MenuItemEnum"/> enumeration value that identifies the processed menu item. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotAddPopupMenuItemEventArgs.MenuItem"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotAddPopupMenuItemEventArgs.Add">
            <summary>
                <para>Gets or sets whether the processed menu item should be displayed within the menu.
</para>
            </summary>
            <value><b>true</b> to show the menu item; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotAddPopupMenuItemEventArgs.MenuItem">
            <summary>
                <para>Gets which menu item is currently being processed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.MenuItemEnum"/> enumeration value that identifies the menu item currently being processed.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties">

            <summary>
                <para>Contains the settings which define the image that can be displayed within the <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid"/> control. 

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the PivotGridImageProperties class with an image with the specified URL.
</para>
            </summary>
            <param name="url">
		A <see cref="T:System.String"/> that represents the URL of the image that will be represented by the created object. This value is assigned to the <see cref="P:DevExpress.Web.ASPxClasses.ImagePropertiesBase.Url"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties.#ctor(DevExpress.Web.ASPxClasses.IPropertiesOwner)">
            <summary>
                <para>Initializes a new instance of the PivotGridImageProperties class with the specified owner.
</para>
            </summary>
            <param name="owner">
		An object that implements the <see cref="T:DevExpress.Web.ASPxClasses.IPropertiesOwner"/> interface, representing the owner of the created object.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotGridImageProperties class with default parameters.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotAreaChangingEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldAreaChanging"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotAreaChangingEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotAreaChangingEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.FieldAreaChanging"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the pivot grid which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotAreaChangingEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCustomizationFormImages">

            <summary>
                <para>Contains the settings that define images displayed in Customization Forms and <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotCustomizationControl"/> controls.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomizationFormImages.#ctor(DevExpress.Web.ASPxClasses.Internal.ISkinOwner)">
            <summary>
                <para>Initializes a new instance of the PivotCustomizationFormImages class.
</para>
            </summary>
            <param name="owner">
		An object that implements the <b>DevExpress.Web.ASPxClasses.Internal.ISkinOwner</b> interface, representing the pivot grid or pivot customization control that owns the images.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomizationFormImages.BottomPanelOnly1by4Layout">
            <summary>
                <para>Gets the settings that define the image displayed within the 'Areas Section Only (1 by 4)' item of the <i>Customization Form Layout</i> context menu.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that represents the image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomizationFormImages.BottomPanelOnly2by2Layout">
            <summary>
                <para>Gets the settings that define the image displayed within the 'Areas Section Only (2 by 2)' item of the <i>Customization Form Layout</i> context menu.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that represents the image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomizationFormImages.ColumnAreaHeaders">
            <summary>
                <para>Gets the settings that define the image displayed above the Column Area section of the Customization Form.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that represents the image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomizationFormImages.DataAreaHeaders">
            <summary>
                <para>Gets the settings that define the image displayed above the Data Area section of the Customization Form.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that represents the image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomizationFormImages.FieldListHeaders">
            <summary>
                <para>Gets the settings that define the image displayed above the Hidden Fields section of the Customization Form.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that represents the image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomizationFormImages.FilterAreaHeaders">
            <summary>
                <para>Gets the settings that define the image displayed above the Filter Area section of the Customization Form.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that represents the image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomizationFormImages.RowAreaHeaders">
            <summary>
                <para>Gets the settings that define the image displayed above the Row Area section of the Customization Form.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that represents the image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomizationFormImages.StackedDefaultLayout">
            <summary>
                <para>Gets the settings that define the image displayed within the 'Fields Section and Areas Section Stacked' item of the <i>Customization Form Layout</i> context menu.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that represents the image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomizationFormImages.StackedSideBySideLayout">
            <summary>
                <para>Gets the settings that define the image displayed within the 'Fields Section and Areas Section Side-By-Side' item of the <i>Customization Form Layout</i> context menu.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that represents the image settings.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomizationFormImages.TopPanelOnlyLayout">
            <summary>
                <para>Gets the settings that define the image displayed within the 'Fields Section Only' item of the <i>Customization Form Layout</i> context menu.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridImageProperties"/> object that represents the image settings.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCustomFilterPopupItemsEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomFilterPopupItems"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomFilterPopupItemsEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridFilterItems)">
            <summary>
                <para>Initializes a new instance of the PivotCustomFilterPopupItemsEventArgs class.
</para>
            </summary>
            <param name="items">
		A <b>DevExpress.XtraPivotGrid.Data.PivotGridFilterItems</b> object representing a collection of filter drop-down items. This collection is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotCustomFilterPopupItemsEventArgs.Items"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomFilterPopupItemsEventArgs.CheckAllItems(System.Boolean)">
            <summary>
                <para>Checks or unchecks all filter items in the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotCustomFilterPopupItemsEventArgs.Items"/> collection.
</para>
            </summary>
            <param name="isChecked">
		<b>true</b> to check the filter items; <b>false</b> to uncheck them.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomFilterPopupItemsEventArgs.Field">
            <summary>
                <para>Gets the field for which the event has been raised.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotGridField"/> object, representing the field for which the event was raised.

</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomFilterPopupItemsEventArgs.Items">
            <summary>
                <para>Gets the collection of filter items.
</para>
            </summary>
            <value>A list of <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridFilterItem"/> objects that represent the filter items.
</value>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomFilterPopupItemsEventArgs.ShowBlanksItem">
            <summary>
                <para>Gets the 'Show Blanks' filter item.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridFilterItem"/> object representing the 'Show Blanks' filter item.
</value>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCustomFieldValueCellsEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomFieldValueCells"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomFieldValueCellsEventArgs.FindCell(System.Boolean,System.Predicate`1)">
            <summary>
                <para>Returns the header of the column/row whose summary values match the specified condition.

</para>
            </summary>
            <param name="isColumn">
		<b>true</b> to locate a column; <b>false</b> to locate a row.

            </param>
            <param name="match">
		A <b>System.Predicate</b> that specifies the condition used to locate the column/row.

            </param>
            <returns>A <see cref="T:DevExpress.Web.ASPxPivotGrid.FieldValueCell"/> object, representing the header of the column/row whose summary values match the specified predicate; <b>null</b> if no columns/rows match the predicate.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomFieldValueCellsEventArgs.GetCell(System.Boolean,System.Int32)">
            <summary>
                <para>Returns the field value cell by its index.
</para>
            </summary>
            <param name="isColumn">
		<b>true</b> to obtain the column field value cell; <b>false</b> to obtain the row field value cell.

            </param>
            <param name="index">
		An integer value that specifies the zero-based index of the cell.

            </param>
            <returns>A <see cref="T:DevExpress.Web.ASPxPivotGrid.FieldValueCell"/> object that represents the required cell; <b>null</b> if the <i>index</i> is out of bounds.
</returns>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomFieldValueCellsEventArgs.Split(System.Boolean,System.Predicate`1,DevExpress.XtraPivotGrid.Data.FieldValueSplitData[])">
            <summary>
                <para>Splits all field value cells that match the specified condition.
</para>
            </summary>
            <param name="isColumn">
		<b>true</b> to process column field value cells; <b>false</b> to process row field value cells.

            </param>
            <param name="match">
		A <b>System.Predicate</b> that represents the condition used to define which cells should be split.

            </param>
            <param name="cells">
		The <see cref="T:DevExpress.XtraPivotGrid.Data.FieldValueSplitData"/> objects that define how to split the cells.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomFieldValueCellsEventArgs.Split(System.Boolean,System.Predicate`1,System.Boolean,DevExpress.XtraPivotGrid.Data.FieldValueSplitData[])">
            <summary>
                <para>Splits all field value cells that match the specified condition, or only the first matching cell.
</para>
            </summary>
            <param name="isColumn">
		<b>true</b> to process column field value cells; <b>false</b> to process row field value cells.

            </param>
            <param name="match">
		A <b>System.Predicate</b> that represents the condition used to define which cells should be split.

            </param>
            <param name="firstCellOnly">
		<b>true</b> to split only the first cell that matches the specified condition; <b>false</b> to split all cells that match the condition. 

            </param>
            <param name="cells">
		The <see cref="T:DevExpress.XtraPivotGrid.Data.FieldValueSplitData"/> objects that define how to split the cells.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomFieldValueCellsEventArgs.Split(System.Boolean,System.Predicate`1,System.Boolean,System.Collections.Generic.IList`1)">
            <summary>
                <para>Splits all field value cells that match the specified condition, or only the first matching cell.
</para>
            </summary>
            <param name="isColumn">
		<b>true</b> to process column field value cells; <b>false</b> to process row field value cells.

            </param>
            <param name="match">
		A <b>System.Predicate</b> that represents the condition used to define which cells should be split.

            </param>
            <param name="firstCellOnly">
		<b>true</b> to split only the first cell that matches the specified condition; <b>false</b> to split all cells that match the condition. 

            </param>
            <param name="cells">
		A list of <see cref="T:DevExpress.XtraPivotGrid.Data.FieldValueSplitData"/> objects that define how to split the cells.

            </param>


        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomFieldValueCellsEventArgs.Split(System.Boolean,System.Predicate`1,System.Collections.Generic.IList`1)">
            <summary>
                <para>Splits all field value cells that match the specified condition.
</para>
            </summary>
            <param name="isColumn">
		<b>true</b> to process column field value cells; <b>false</b> to process row field value cells.

            </param>
            <param name="match">
		A <b>System.Predicate</b> that represents the condition used to define which cells should be split.

            </param>
            <param name="cells">
		A list of <see cref="T:DevExpress.XtraPivotGrid.Data.FieldValueSplitData"/> objects that define how to split the cells.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCustomCellStyleEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCellStyle"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomCellStyleEventHandler.Invoke(System.Object,DevExpress.Web.ASPxPivotGrid.PivotCustomCellStyleEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCellStyle"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid"/> control which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCustomCellStyleEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Web.ASPxPivotGrid.PivotCustomCellStyleEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Web.ASPxPivotGrid.ASPxPivotGrid.CustomCellStyle"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Web.ASPxPivotGrid.PivotCustomCellStyleEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridCellItem,DevExpress.Web.ASPxPivotGrid.PivotCellStyle)">
            <summary>
                <para>Initializes a new instance of the PivotCustomCellStyleEventArgs class.
</para>
            </summary>
            <param name="cellItem">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridCellItem"/> object that represents the processed cell.

            </param>
            <param name="cellStyle">
		A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCellStyle"/> object that contains the style settings used to paint the processed cell. This value is assigned to the <see cref="P:DevExpress.Web.ASPxPivotGrid.PivotCustomCellStyleEventArgs.CellStyle"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Web.ASPxPivotGrid.PivotCustomCellStyleEventArgs.CellStyle">
            <summary>
                <para>Gets the style settings used to paint the processed cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Web.ASPxPivotGrid.PivotCellStyle"/> object that contains the style settings used to paint the processed cell.
</value>


        </member>
    </members>
</doc>
