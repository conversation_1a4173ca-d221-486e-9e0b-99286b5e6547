﻿<%@ Register TagPrefix="dxwg" Namespace="DevExpress.Web.ASPxGrid" Assembly="DevExpress.Web.ASPxGrid.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="dxwdc" Namespace="DevExpress.Web.ASPxDataControls" Assembly="DevExpress.Web.ASPxDataControls.v7.1, Version=7.1.9.0, Culture=neutral, PublicKeyToken=79868b8147b5eae4" %>
<%@ Register TagPrefix="uc1" TagName="Menu" Src="~/Library/UserControls/Menu.ascx" %>

<%@ Page Trace="False" Language="vb" AutoEventWireup="false" Inherits="AccountManager.UpdateBackup"
    CodeFile="UpdateBackup.aspx.vb" MasterPageFile="~/MasterPage.master" %>

<%@ Register Assembly="DevExpress.Web.ASPxGridView.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxGridView" TagPrefix="dx" %>

<%@ Register Assembly="DevExpress.Web.ASPxEditors.v11.2, Version=*********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"
    Namespace="DevExpress.Web.ASPxEditors" TagPrefix="dx" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="ajaxToolkit" %>
<%@ Register Assembly="System.Web.Extensions, Version=1.0.61025.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
    Namespace="System.Web.UI" TagPrefix="asp" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <script lang="javascript" type="text/javascript">
<!--


    // -->
    </script>

    <asp:ScriptManager ID="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <table>
        <tr>
            <td>
                <%-- <strong>Instructions</strong>&nbsp;
                                                                                    <br />--%>
                <asp:Panel ID="pNoOwners" runat="server">
                    <table>
                        <tr>
                            <td>
                                <table class="DisplayTables">
                                    <tr>
                                        <td>
                                            <table>
                                                <tr>
                                                    <td colspan="3">
                                                        <strong>Replace Delegate and Add/Remove Backups</strong></td>
                                                </tr>
                                                <tr>
                                                    <td colspan="3">
                                                        <asp:Label ID="lblMessage" runat="server" CssClass="LabelGreen"></asp:Label>
                                                        <%--<asp:Label ID="lblMessage0" runat="server" CssClass="LabelGreen"></asp:Label>--%>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="3">
                                                        <asp:Label ID="lblErrorMessage" runat="server" CssClass="LabelRed"></asp:Label>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td colspan="3">
                                                        <asp:Panel ID="pBckUpd" runat="server">
                                                            <div>
                                                                <ajaxToolkit:TabContainer runat="server" ID="Tabs" ActiveTabIndex="0">
                                                                    <ajaxToolkit:TabPanel runat="server" ID="tpAddUser" HeaderText="Replace Delegate / Add Backups" Height="100%"
                                                                        Width="100%">
                                                                        <ContentTemplate>

                                                                            <table border="1" style="width: 450px;">
                                                                                <tr><td><table>
                                                                                    <colgroup>
                                                                                        <col style="width:100px;"/>
                                                                                        <col style="width:150px;"/>
                                                                                        <col style="width:100px;"/>
                                                                                        <col style="width:100px;"/>
                                                                                    </colgroup>
                                                                                <tr>
                                                                                    <td>User ID:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txtAddUser" Display="None" ErrorMessage="Please enter User ID." SetFocusOnError="True" ValidationGroup="AddUser"></asp:RequiredFieldValidator></td>
                                                                                    <td>
                                                                                        <asp:TextBox ID="txtAddUser" runat="server"></asp:TextBox></td>
                                                                                    <td>Access Type:</td>
                                                                                    <td>
                                                                                        <asp:DropDownList ID="ddlAddAccess" runat="server">
                                                                                        </asp:DropDownList><asp:RadioButton ID="rdoAddAll" runat="server" Text="Update All Accounts Shown" GroupName="Update" Checked="True" Visible="False" /></td>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td>Comments:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="txtNewOwnerComments" Display="None" ErrorMessage="Please enter comments." SetFocusOnError="True" ValidationGroup="AddUser"></asp:RequiredFieldValidator>
                                                                                    </td>
                                                                                    <td colspan="3">
                                                                                        <asp:TextBox ID="txtNewOwnerComments" runat="server" TextMode="MultiLine" Rows="4"
                                                                                            Columns="40" MaxLength="200"></asp:TextBox></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td colspan="4">
                                                                                        <dx:ASPxButton ID="cmdAddUser" runat="server" ValidationGroup="AddUser" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                                            CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                                                            Text="Add User">
                                                                                        </dx:ASPxButton>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td colspan="4">
                                                                                        <asp:ValidationSummary ID="ValidationSummary1" ValidationGroup="AddUser" runat="server" />
                                                                                        <asp:Label ID="lblNote" runat="server">
                                                                                     Note: Owners must communicate duties and responsibilities to delegates at the time delegates are assigned.  
                                                                                        </asp:Label>
                                                                                                                                                                            </td>
                                                                                </tr>
                                                                            </table></td></tr>
                                                                            </table>
                                                                        </ContentTemplate>
                                                                    </ajaxToolkit:TabPanel>
                                                                    <ajaxToolkit:TabPanel runat="server" ID="tbpDel" HeaderText="Remove Backups" ToolTip="Delete Backups From All the Accounts or Selected Accounts" Height="100%"
                                                                        Width="100%">
                                                                        <ContentTemplate>
                                                                            <%--<asp:UpdatePanel ID="upDelUser" runat="server">
                                                                            </asp:UpdatePanel>--%>
                                                                            <%--<strong>Instructions</strong>

                                                                            <br />--%>
                                                                             <table border="1" style="width: 450px;">
                                                                                <tr><td><table>
                                                                                    <colgroup>
                                                                                        <col style="width:100px;"/>
                                                                                        <col style="width:350px;"/>
                                                                                        
                                                                                    </colgroup>
                                                                                <tr>
                                                                                    <td>Backup ID:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ControlToValidate="ddlBackupList" Display="None"  ErrorMessage="Please select Backup from above dropdown list." SetFocusOnError="True" ValidationGroup="DelUser" InitialValue="0"></asp:RequiredFieldValidator></td>
                                                                                    <td>
                                                                                        <%--<asp:TextBox ID="txtDelUser" runat="server"></asp:TextBox>--%><asp:DropDownList ID="ddlBackupList" runat="server"></asp:DropDownList><asp:CheckBox ID="chkDelAll" runat="server" Text="Select All" Checked="True" /></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td>Comments:<span class="ValidationHeader">*</span><asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" ControlToValidate="txtDelComments" Display="None" ErrorMessage="Please enter comments." SetFocusOnError="True" ValidationGroup="DelUser"></asp:RequiredFieldValidator>
                                                                                    </td>
                                                                                    <td >
                                                                                        <asp:TextBox ID="txtDelComments" runat="server" TextMode="MultiLine" Rows="4"
                                                                                            Columns="40" MaxLength="200"></asp:TextBox></td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td colspan="2">
                                                                                        <dx:ASPxButton ID="cmdDelUser" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                                                                        CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                                                                        Text="Delete" ValidationGroup="DelUser">
                                                                                                    </dx:ASPxButton>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td colspan="2">
                                                                                        <asp:ValidationSummary ID="ValidationSummary2" ValidationGroup="DelUser" runat="server" />
                                                                                        </td>                                                                                                                                                                            </td>
                                                                                </tr>
                                                                            </table></td></tr>
                                                                            </table>
                                                                        </ContentTemplate>
                                                                    </ajaxToolkit:TabPanel>
                                                                </ajaxToolkit:TabContainer>
                                                            </div>
                                                        </asp:Panel>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="left">
                                                        <dx:ASPxButton ID="cmdReturn" runat="server" CssFilePath="~/App_Themes/Office2003Blue/{0}/styles.css"
                                                            CssPostfix="Office2003Blue" SpriteCssFilePath="~/App_Themes/Office2003Blue/{0}/sprite.css"
                                                            Text="Return to My Accounts">
                                                        </dx:ASPxButton>
                                                    </td>
                                                    <td align="right">
                                                        <asp:CheckBox ID="chkExpand" runat="server" Text="Expand All" AutoPostBack="true" /></td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <asp:Panel ID="pOwners" runat="server">
                                        <tr>
                                            <td colspan="3">
                                               <%-- <br />--%>
                                                <%--<asp:Label ID="lblOwners" runat="server" Font-Bold="true">Owner, Delegate and Backups of Account:-</asp:Label>--%>
                                                <%--<asp:Label ID="lblSearchMessage" runat="server" Font-Bold="true">To search based on Account ID, User and Access, you can use textboxes exist below header row.</asp:Label>--%>
                                            </td>
                                        </tr>
                                    </asp:Panel>
                                    <tr>
                                        <td>
                                            <dx:ASPxGridView ID="grdOwners" runat="server" AutoGenerateColumns="False" CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css" 
                                                CssPostfix="PlasticBlue" Width="620px" Settings-ShowHeaderFilterButton="False" SettingsText-FilterBarCreateFilter="Search">
                                                
                                                <Styles CssFilePath="~/App_Themes/PlasticBlue/{0}/styles.css" CssPostfix="PlasticBlue">
                                                    <Header ImageSpacing="10px" SortingImageSpacing="10px">
                                                    </Header>
                                                </Styles>
                                                <SettingsPager ShowDefaultImages="False">
                                                    <AllButton Text="All">
                                                    </AllButton>
                                                    <NextPageButton Text="Next &gt;">
                                                    </NextPageButton>
                                                    <PrevPageButton Text="&lt; Prev">
                                                    </PrevPageButton>
                                                </SettingsPager>
                                                <ImagesFilterControl>
                                                    <LoadingPanel Url="~/App_Themes/PlasticBlue/Editors/Loading.gif">
                                                    </LoadingPanel>
                                                </ImagesFilterControl>
                                                <Images SpriteCssFilePath="~/App_Themes/PlasticBlue/{0}/sprite.css">
                                                    <LoadingPanelOnStatusBar Url="~/App_Themes/PlasticBlue/GridView/gvLoadingOnStatusBar.gif">
                                                    </LoadingPanelOnStatusBar>
                                                    <LoadingPanel Url="~/App_Themes/PlasticBlue/GridView/Loading.gif">
                                                    </LoadingPanel>
                                                </Images>
                                                
                                                <Columns>
                                                    <dx:GridViewCommandColumn  ClearFilterButton-Visible="true" VisibleIndex="4">
                                                        
                                                                                                            </dx:GridViewCommandColumn>
                                                    <dx:GridViewDataTextColumn FieldName="AccountID" GroupIndex="0" SortIndex="0"  VisibleIndex="1">
                                                    </dx:GridViewDataTextColumn>
                                                    <dx:GridViewDataTextColumn Caption="User" FieldName="User" VisibleIndex="2">
                                                    </dx:GridViewDataTextColumn>
                                                    <dx:GridViewDataTextColumn Caption="Access" FieldName="Access" SortOrder="Descending" VisibleIndex="3">
                                                    </dx:GridViewDataTextColumn>
                                                </Columns>
                                                <Settings ShowFilterRow="false" ShowGroupPanel="True" ShowFilterRowMenu="False"  />
                                               
                                                <StylesEditors>
                                                    <CalendarHeader Spacing="11px">
                                                    </CalendarHeader>
                                                    <ProgressBar Height="25px">
                                                    </ProgressBar>
                                                </StylesEditors>
                                            </dx:ASPxGridView>
                                        </td>
                                    </tr>
                        </tr>
                </asp:Panel>

            </td>
        </tr>


    </table>



</asp:Content>
